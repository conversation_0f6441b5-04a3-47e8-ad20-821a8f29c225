#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页性能测试脚本
测试优化前后的分页响应速度和格式化性能
"""

import sys
import os
import time
from typing import Dict, List

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def test_formatting_performance():
    """测试格式化性能"""
    print("[性能测试] 开始分页格式化性能测试...")
    
    try:
        # 导入必要的模块
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 创建测试数据（模拟50行24列的页面数据）
        test_data = []
        column_names = [
            "工号", "姓名", "2025年基础性绩效", "2025年奖励性绩效预发", 
            "车补", "代扣代存养老保险", "薪级工资", "岗位工资",
            "工龄津贴", "特殊津贴", "其他津贴", "合计工资",
            "人员类别代码", "部门", "职务", "入职日期"
        ] + [f"测试字段{i}" for i in range(8)]  # 补足24列
        
        # 生成50行测试数据
        for i in range(50):
            row_data = {}
            for j, col in enumerate(column_names):
                if "绩效" in col or "工资" in col or "津贴" in col or "车补" in col or "保险" in col:
                    row_data[col] = 1000 + i * 10 + j  # 数值字段
                elif col == "工号":
                    row_data[col] = f"2025{1000 + i:04d}"
                elif col == "姓名":
                    row_data[col] = f"测试员工{i+1}"
                else:
                    row_data[col] = f"值{i}-{j}"
            test_data.append(row_data)
        
        print(f"测试数据生成完成: {len(test_data)}行 x {len(column_names)}列")
        
        # 性能测试：获取格式化管理器
        print("\n[性能测试] 测试统一格式化管理器...")
        
        start_time = time.time()
        unified_formatter = get_unified_format_manager()
        manager_init_time = (time.time() - start_time) * 1000
        print(f"  - 格式化管理器初始化: {manager_init_time:.2f}ms")
        
        # 测试字段注册系统缓存性能
        field_registry = unified_formatter.field_registry
        
        # 清空缓存统计
        field_registry.clear_cache()
        
        # 测试1：首次查找（缓存未命中）
        print("\n[性能测试] 测试字段类型查找性能...")
        
        start_time = time.time()
        for _ in range(5):  # 重复查找同一表类型
            field_types = field_registry.get_table_field_types("active_employees")
        first_lookup_time = (time.time() - start_time) * 1000
        
        # 测试2：后续查找（缓存命中）
        start_time = time.time()
        for _ in range(100):  # 大量重复查找
            field_types = field_registry.get_table_field_types("active_employees")
        cached_lookup_time = (time.time() - start_time) * 1000
        
        # 获取缓存统计
        cache_stats = field_registry.get_cache_stats()
        field_registry.log_cache_performance()
        
        print(f"  - 首次查找(5次): {first_lookup_time:.2f}ms")
        print(f"  - 缓存查找(100次): {cached_lookup_time:.2f}ms")
        print(f"  - 缓存命中率: {cache_stats['hit_rate_percent']}%")
        print(f"  - 性能提升: {(first_lookup_time/5) / (cached_lookup_time/100):.1f}x")
        
        # 测试3：单元格格式化性能
        print("\n[性能测试] 测试单元格格式化性能...")
        
        test_cells = [
            (1234, "2025年基础性绩效", "active_employees"),
            (5678, "2025年奖励性绩效预发", "active_employees"),
            ("-", "车补", "active_employees"),
            (999, "代扣代存养老保险", "active_employees"),
            ("20251001", "工号", "active_employees")
        ]
        
        # 首次格式化（包含初始化成本）
        start_time = time.time()
        for value, column_name, table_type in test_cells:
            result = unified_formatter.format_display_value(value, column_name, table_type)
        first_format_time = (time.time() - start_time) * 1000
        
        # 批量格式化（缓存优化后）
        start_time = time.time()
        for _ in range(20):  # 重复20轮
            for value, column_name, table_type in test_cells:
                result = unified_formatter.format_display_value(value, column_name, table_type)
        batch_format_time = (time.time() - start_time) * 1000
        
        print(f"  - 首次格式化(5个单元格): {first_format_time:.2f}ms")
        print(f"  - 批量格式化(100个单元格): {batch_format_time:.2f}ms")
        print(f"  - 单元格平均格式化时间: {batch_format_time/100:.3f}ms")
        
        # 测试4：模拟完整页面格式化
        print("\n📊 [性能测试] 模拟完整页面格式化性能...")
        
        start_time = time.time()
        formatted_count = 0
        for row in test_data:
            for column_name, value in row.items():
                result = unified_formatter.format_display_value(value, column_name, "active_employees")
                formatted_count += 1
        page_format_time = (time.time() - start_time) * 1000
        
        print(f"  - 完整页面格式化({formatted_count}个单元格): {page_format_time:.2f}ms")
        print(f"  - 平均每个单元格: {page_format_time/formatted_count:.3f}ms")
        print(f"  - 平均每行: {page_format_time/len(test_data):.2f}ms")
        
        # 预期性能评估
        expected_page_time = page_format_time + 50  # 加上UI渲染等开销
        print(f"\n🎯 [性能评估]")
        print(f"  - 预期分页响应时间: {expected_page_time:.0f}ms")
        
        if expected_page_time < 100:
            rating = "优秀"
            emoji = "🎉"
        elif expected_page_time < 300:
            rating = "良好"
            emoji = "✅"
        elif expected_page_time < 800:
            rating = "一般"
            emoji = "⚠️"
        else:
            rating = "需要改进"
            emoji = "❌"
            
        print(f"  - 性能评级: {emoji} {rating}")
        
        # 最终缓存统计
        final_cache_stats = field_registry.get_cache_stats()
        print(f"\n📈 [最终缓存统计]")
        print(f"  - 总请求数: {final_cache_stats['total_requests']}")
        print(f"  - 缓存命中率: {final_cache_stats['hit_rate_percent']}%")
        print(f"  - 缓存项数: 字段类型{final_cache_stats['cached_field_types']}, 配置{final_cache_stats['cached_configs']}")
        
        return {
            'page_format_time': page_format_time,
            'expected_page_time': expected_page_time,
            'cache_hit_rate': final_cache_stats['hit_rate_percent'],
            'performance_rating': rating
        }
        
    except Exception as e:
        print(f"❌ [错误] 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_table_type_extraction():
    """测试表类型提取逻辑"""
    print("\n🔧 [功能测试] 测试表类型提取...")
    
    try:
        # 模拟表格组件的表类型提取
        test_cases = [
            ("salary_data_2025_07_active_employees", "active_employees"),
            ("salary_data_2025_07_retired_employees", "retired_employees"),
            ("全部在职人员工资表", "active_employees"),
            ("离休人员工资表", "retired_employees"),
            ("unknown_table", "active_employees")  # 默认值
        ]
        
        for table_name, expected_type in test_cases:
            # 模拟 _extract_table_type_for_formatting 的逻辑
            if 'active' in table_name.lower() or '在职' in table_name:
                actual_type = 'active_employees'
            elif 'retired' in table_name.lower() or 'pension' in table_name.lower() or '离休' in table_name or '退休' in table_name:
                actual_type = 'retired_employees'
            else:
                actual_type = 'active_employees'  # 默认
            
            status = "✅" if actual_type == expected_type else "❌"
            print(f"  {status} {table_name} -> {actual_type}")
        
        print("✅ 表类型提取测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 表类型提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("启动分页性能优化验证测试\n")
    
    # 功能测试
    table_type_ok = test_table_type_extraction()
    
    # 性能测试
    perf_results = test_formatting_performance()
    
    # 测试总结
    print("\n" + "="*60)
    print("📋 [测试总结]")
    print("="*60)
    
    if table_type_ok:
        print("✅ 表类型提取功能: 正常")
    else:
        print("❌ 表类型提取功能: 异常")
    
    if perf_results:
        print(f"✅ 格式化性能测试: 完成")
        print(f"   - 页面格式化时间: {perf_results['page_format_time']:.0f}ms")
        print(f"   - 预期响应时间: {perf_results['expected_page_time']:.0f}ms")
        print(f"   - 缓存命中率: {perf_results['cache_hit_rate']}%")
        print(f"   - 性能评级: {perf_results['performance_rating']}")
        
        # 与优化前对比
        print(f"\n🎯 [优化效果预估]")
        if perf_results['expected_page_time'] < 200:
            improvement = 1200 / perf_results['expected_page_time']  # 假设优化前1.2秒
            print(f"   - 性能提升: {improvement:.1f}x")
            print(f"   - 响应时间从 1200ms 优化到 {perf_results['expected_page_time']:.0f}ms")
        else:
            print(f"   - 仍需进一步优化，目标响应时间 < 200ms")
    else:
        print("❌ 格式化性能测试: 失败")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()