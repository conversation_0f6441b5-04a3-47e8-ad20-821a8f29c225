#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复排序时出现空白列的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_sorting_blank_columns():
    """分析排序空白列问题的根本原因"""
    print("=" * 80)
    print("排序空白列问题分析")
    print("=" * 80)
    
    # 问题1: 列数不匹配
    print("\n1. 列数不匹配问题分析:")
    print("   - 表头列数与数据列数不一致")
    print("   - 排序后数据结构发生变化")
    print("   - 格式化过程中字段增减")
    
    # 问题2: 列宽计算错误
    print("\n2. 列宽计算问题分析:")
    print("   - 排序后重新计算列宽时出错")
    print("   - 列宽设置与实际列数不匹配")
    print("   - ResizeToContents模式下的计算异常")
    
    # 问题3: 数据渲染问题
    print("\n3. 数据渲染问题分析:")
    print("   - 排序后数据渲染不完整")
    print("   - 表格项设置时索引越界")
    print("   - 虚拟化表格的可见区域计算错误")

def create_column_count_validator():
    """创建列数验证器"""
    print("\n" + "=" * 80)
    print("创建列数验证修复方案")
    print("=" * 80)
    
    validation_code = '''
def validate_column_consistency(self, data, headers):
    """验证列数一致性"""
    if not data or not headers:
        return True, "数据或表头为空"
    
    # 检查数据列数
    if isinstance(data, list) and data:
        if isinstance(data[0], dict):
            data_columns = len(data[0].keys())
        else:
            data_columns = len(data[0]) if hasattr(data[0], '__len__') else 0
    else:
        data_columns = 0
    
    header_columns = len(headers)
    
    if data_columns != header_columns:
        error_msg = f"列数不匹配: 数据{data_columns}列, 表头{header_columns}列"
        return False, error_msg
    
    return True, "列数一致"

def fix_column_count_before_sort(self):
    """排序前修复列数"""
    try:
        current_data_columns = 0
        current_header_columns = self.columnCount()
        
        # 获取实际数据列数
        if hasattr(self, '_current_data') and self._current_data:
            if isinstance(self._current_data[0], dict):
                current_data_columns = len(self._current_data[0].keys())
        
        # 修复列数不匹配
        if current_data_columns != current_header_columns:
            self.logger.warning(f"排序前发现列数不匹配: 数据{current_data_columns}列, 表头{current_header_columns}列")
            
            # 调整表格列数
            self.setColumnCount(current_data_columns)
            self.logger.info(f"已调整表格列数为: {current_data_columns}")
            
        return True
    except Exception as e:
        self.logger.error(f"排序前列数修复失败: {e}")
        return False
'''
    
    print("列数验证器代码:")
    print(validation_code)
    return validation_code

def create_column_width_fix():
    """创建列宽修复方案"""
    print("\n" + "=" * 80)
    print("创建列宽计算修复方案")
    print("=" * 80)
    
    width_fix_code = '''
def safe_adjust_column_widths_after_sort(self):
    """排序后安全调整列宽"""
    try:
        col_count = self.columnCount()
        if col_count <= 0:
            self.logger.warning("表格列数为0，跳过列宽调整")
            return
        
        header = self.horizontalHeader()
        
        # 重置所有列的调整模式
        for col in range(col_count):
            header.setSectionResizeMode(col, QHeaderView.Interactive)
        
        # 重新设置列宽模式
        for col in range(col_count - 1):
            header.setSectionResizeMode(col, QHeaderView.ResizeToContents)
        
        # 最后一列拉伸
        if col_count > 0:
            header.setSectionResizeMode(col_count - 1, QHeaderView.Stretch)
        
        self.logger.info(f"排序后列宽调整完成: {col_count}列")
        
    except Exception as e:
        self.logger.error(f"排序后列宽调整失败: {e}")

def prevent_blank_columns_during_sort(self):
    """防止排序时出现空白列"""
    try:
        # 1. 保存当前状态
        original_column_count = self.columnCount()
        original_headers = []
        
        for i in range(original_column_count):
            header_item = self.horizontalHeaderItem(i)
            original_headers.append(header_item.text() if header_item else f"列{i}")
        
        # 2. 执行排序操作
        # (这里会调用实际的排序逻辑)
        
        # 3. 排序后验证
        new_column_count = self.columnCount()
        
        if new_column_count != original_column_count:
            self.logger.warning(f"排序后列数变化: {original_column_count} -> {new_column_count}")
            
            # 恢复原始列数
            self.setColumnCount(original_column_count)
            
            # 恢复表头
            for i, header_text in enumerate(original_headers):
                if i < self.columnCount():
                    self.setHorizontalHeaderItem(i, QTableWidgetItem(header_text))
        
        # 4. 重新调整列宽
        self.safe_adjust_column_widths_after_sort()
        
        return True
        
    except Exception as e:
        self.logger.error(f"防止空白列失败: {e}")
        return False
'''
    
    print("列宽修复代码:")
    print(width_fix_code)
    return width_fix_code

def create_data_consistency_check():
    """创建数据一致性检查"""
    print("\n" + "=" * 80)
    print("创建数据一致性检查方案")
    print("=" * 80)
    
    consistency_code = '''
def check_data_consistency_after_sort(self, sorted_data, original_headers):
    """排序后检查数据一致性"""
    try:
        issues = []
        
        # 检查1: 数据行数
        if not sorted_data:
            issues.append("排序后数据为空")
            return False, issues
        
        # 检查2: 数据列数
        if isinstance(sorted_data[0], dict):
            data_columns = len(sorted_data[0].keys())
            if data_columns != len(original_headers):
                issues.append(f"数据列数不匹配: {data_columns} vs {len(original_headers)}")
        
        # 检查3: 必要字段存在性
        required_fields = ['工号', 'employee_id']
        if isinstance(sorted_data[0], dict):
            data_keys = sorted_data[0].keys()
            has_required = any(field in data_keys for field in required_fields)
            if not has_required:
                issues.append("缺少必要的标识字段")
        
        # 检查4: 数据完整性
        for i, row in enumerate(sorted_data[:5]):  # 检查前5行
            if isinstance(row, dict):
                if not any(str(v).strip() for v in row.values() if v is not None):
                    issues.append(f"第{i+1}行数据全为空")
        
        if issues:
            return False, issues
        else:
            return True, ["数据一致性检查通过"]
            
    except Exception as e:
        return False, [f"一致性检查异常: {e}"]
'''
    
    print("数据一致性检查代码:")
    print(consistency_code)
    return consistency_code

def main():
    """主函数"""
    print("开始分析和修复排序空白列问题...")
    
    # 1. 分析问题
    analyze_sorting_blank_columns()
    
    # 2. 创建修复方案
    validation_code = create_column_count_validator()
    width_fix_code = create_column_width_fix()
    consistency_code = create_data_consistency_check()
    
    # 3. 总结修复策略
    print("\n" + "=" * 80)
    print("修复策略总结")
    print("=" * 80)
    
    strategies = [
        "1. 排序前验证列数一致性",
        "2. 排序过程中保持列数稳定",
        "3. 排序后重新计算列宽",
        "4. 数据一致性检查和恢复",
        "5. 异常情况的降级处理"
    ]
    
    for strategy in strategies:
        print(f"   {strategy}")
    
    print("\n✅ 修复方案已生成，需要应用到实际代码中")
    
    return True

if __name__ == "__main__":
    main()
