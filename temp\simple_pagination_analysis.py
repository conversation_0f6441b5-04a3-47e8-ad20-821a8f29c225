#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页按钮变灰问题分析脚本（简化版）
"""

import sys
import os
import re
from pathlib import Path

def main():
    """主分析函数"""
    print("分页按钮变灰问题分析")
    print("=" * 50)
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("错误: prototype_main_window.py文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n>>> 分析set_total_records调用")
        
        # 找到所有set_total_records调用
        pattern = r"\.set_total_records\(([^)]+)\)"
        matches = list(re.finditer(pattern, content))
        
        print(f"发现 {len(matches)} 个set_total_records调用:")
        
        problematic_calls = []
        
        for i, match in enumerate(matches, 1):
            param = match.group(1)
            # 查找行号
            lines = content[:match.start()].count('\n') + 1
            print(f"{i}. 行{lines}: set_total_records({param})")
            
            # 分析参数类型
            if "len(df_converted)" in param:
                print(f"   ⚠️ 可能有问题: 使用len(df_converted)")
                problematic_calls.append(lines)
            elif "total_records" in param:
                print(f"   ✓ 正常: 使用total_records变量")
            elif "len(df)" in param:
                print(f"   ? 需检查: 使用len(df)")
        
        print(f"\n>>> 发现问题调用位置: {problematic_calls}")
        
        # 检查第793行的具体情况
        lines_list = content.split('\n')
        if len(lines_list) > 792:
            line_793 = lines_list[792].strip()
            print(f"\n第793行内容: {line_793}")
            
            # 检查上下文
            print("上下文分析:")
            for i in range(max(0, 790), min(len(lines_list), 800)):
                line_num = i + 1
                line_content = lines_list[i].strip()
                if line_num == 793:
                    print(f">>> {line_num}: {line_content}")
                else:
                    print(f"    {line_num}: {line_content}")
        
        # 检查是否在非分页模式下设置分页状态
        print("\n>>> 检查逻辑问题")
        if "if self.use_pagination and len(df_converted) > 100:" in content:
            print("✓ 找到分页模式判断逻辑")
            
            # 查找非分页模式下的分页设置
            pattern2 = r"else:\s*.*?if self\.pagination_widget:"
            if re.search(pattern2, content, re.DOTALL):
                print("⚠️ 发现问题: 非分页模式下仍在设置分页组件状态")
        
        print("\n>>> 根本原因分析")
        print("问题1: 第793行在非分页模式下使用len(df_converted)设置总记录数")
        print("问题2: len(df_converted)可能只是当前页的数据量，不是总数据量")
        print("问题3: 这会导致total_pages计算错误，进而导致按钮状态错误")
        
        print("\n>>> 修复建议")
        print("1. 修复第793行，确保只在正确的上下文中设置total_records")
        print("2. 区分分页模式和非分页模式的状态设置")
        print("3. 确保total_records始终反映完整数据集大小")
        
        return True
        
    except Exception as e:
        print(f"分析异常: {e}")
        return False

if __name__ == "__main__":
    main()