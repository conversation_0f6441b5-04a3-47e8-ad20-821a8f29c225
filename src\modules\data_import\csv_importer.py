"""
CSV文件导入器

功能说明:
- 支持CSV格式文件导入
- 自动编码检测
- 分隔符自动识别
- 统一的错误处理和日志记录
"""

import pandas as pd
import chardet
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path

from src.utils.log_config import setup_logger

class CSVImporter:
    """CSV文件导入器
    
    提供CSV文件的读取、解析和验证功能
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化CSV导入器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = setup_logger(__name__)
        
        # 配置参数
        self.max_file_size_mb = self.config.get('max_file_size_mb', 100)
        self.encoding_priority = self.config.get('encoding_priority', ['utf-8', 'gbk', 'gb2312'])
        
    def import_data(self, file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """导入CSV数据
        
        Args:
            file_path: 文件路径
            **kwargs: 其他参数
            
        Returns:
            DataFrame数据
        """
        file_path = Path(file_path)
        
        # 检测编码
        encoding = self._detect_encoding(file_path)
        
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding=encoding)
        
        self.logger.info(f"CSV文件导入成功: {len(df)}行 x {len(df.columns)}列")
        return df
        
    def _detect_encoding(self, file_path: Path) -> str:
        """检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            检测到的编码
        """
        try:
            with open(file_path, 'rb') as f:
                result = chardet.detect(f.read())
                encoding = result['encoding']
                
            if encoding:
                self.logger.info(f"检测到编码: {encoding}")
                return encoding
            else:
                self.logger.warning("编码检测失败，使用UTF-8")
                return 'utf-8'
                
        except Exception as e:
            self.logger.error(f"编码检测失败: {e}")
            return 'utf-8' 