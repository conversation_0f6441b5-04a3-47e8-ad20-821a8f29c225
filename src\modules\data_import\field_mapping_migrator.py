#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射迁移器

功能：
1. 检测并修复混乱的字段映射格式
2. 将所有映射统一为：数据库字段名 → 显示名
3. 提供一键修复功能

创建时间: 2025-01-27
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger


class FieldMappingMigrator:
    """字段映射迁移器"""
    
    def __init__(self, config_path: str = "state/data/field_mappings.json"):
        self.logger = setup_logger(__name__)
        self.config_path = config_path
        self.backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 中文字段名到英文数据库字段名的映射
        self.chinese_to_db_mapping = {
            '工号': 'employee_id',
            '姓名': 'employee_name',
            '员工姓名': 'employee_name',
            '员工编号': 'employee_id',
            '部门': 'department',
            '部门名称': 'department',
            '职位': 'position',
            '岗位': 'position',
            '基本工资': 'basic_salary',
            '岗位工资': 'position_salary',
            '薪级工资': 'grade_salary',
            '绩效工资': 'performance_bonus',
            '津贴': 'allowance',
            '津贴补贴': 'allowance',
            '应发工资': 'total_salary',
            '应发合计': 'total_salary',
            '五险一金': 'social_insurance',
            '2025公积金': 'social_insurance',
            '个税': 'income_tax',
            '实发工资': 'net_salary',
            '序号': 'id',
            '人员代码': 'employee_id',
            '身份证号': 'id_card',
            '人员类别': 'position',
            '人员类别代码': 'position',
            '异动类型': 'change_type',
            '异动原因': 'change_reason',
            '生效日期': 'effective_date'
        }
    
    def detect_mapping_format(self, field_mappings: Dict[str, str]) -> str:
        """检测字段映射格式
        
        Returns:
            str: 'excel_column' | 'db_field' | 'mixed' | 'unknown'
        """
        if not field_mappings:
            return 'unknown'
        
        chinese_keys = 0
        english_keys = 0
        
        for key in field_mappings.keys():
            if re.match(r'^[a-z_][a-z0-9_]*$', key):
                english_keys += 1
            elif any('\u4e00' <= char <= '\u9fff' for char in key):
                chinese_keys += 1
        
        total_keys = len(field_mappings)
        
        if english_keys > total_keys * 0.8:
            return 'db_field'
        elif chinese_keys > total_keys * 0.8:
            return 'excel_column'
        elif english_keys > 0 and chinese_keys > 0:
            return 'mixed'
        else:
            return 'unknown'
    
    def convert_excel_to_db_mapping(self, excel_mapping: Dict[str, str]) -> Dict[str, str]:
        """将Excel列名映射转换为数据库字段名映射

        Args:
            excel_mapping: {Excel列名或混合: 显示名}

        Returns:
            Dict[str, str]: {数据库字段名: 显示名}
        """
        db_mapping = {}

        for key, display_name in excel_mapping.items():
            # 如果已经是英文数据库字段名，直接使用
            if re.match(r'^[a-z_][a-z0-9_]*$', key):
                db_mapping[key] = display_name
                continue

            # 查找对应的数据库字段名
            db_field = self.chinese_to_db_mapping.get(key)

            if db_field:
                db_mapping[db_field] = display_name
            else:
                # 如果找不到映射，尝试生成标准化字段名
                db_field = self._generate_db_field_name(key)
                db_mapping[db_field] = display_name

                self.logger.warning(f"未找到 '{key}' 的标准映射，使用原字段名: {db_field}")

        return db_mapping
    
    def _generate_db_field_name(self, field_name: str) -> str:
        """为字段名生成标准化的数据库字段名"""
        # 如果已经是英文数据库字段名，直接返回
        if re.match(r'^[a-z_][a-z0-9_]*$', field_name):
            return field_name

        # 对于中文字段名，保持原样（作为显示名使用）
        # 这样可以避免生成不准确的英文字段名
        return field_name
    
    def migrate_single_table(self, table_name: str, table_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """迁移单个表的字段映射
        
        Returns:
            Tuple[bool, Dict]: (是否需要迁移, 迁移后的数据)
        """
        field_mappings = table_data.get('field_mappings', {})
        format_type = self.detect_mapping_format(field_mappings)
        
        if format_type == 'db_field':
            # 已经是正确格式，无需迁移
            return False, table_data
        
        self.logger.info(f"表 {table_name} 需要迁移，当前格式: {format_type}")
        
        # 转换映射格式
        if format_type in ['excel_column', 'mixed']:
            new_field_mappings = self.convert_excel_to_db_mapping(field_mappings)
        else:
            # 未知格式，尝试保持原样
            new_field_mappings = field_mappings
        
        # 更新表数据
        migrated_data = table_data.copy()
        migrated_data['field_mappings'] = new_field_mappings
        
        # 更新元数据
        if 'metadata' not in migrated_data:
            migrated_data['metadata'] = {}
        
        migrated_data['metadata'].update({
            'last_modified': datetime.now().isoformat(),
            'migrated_at': datetime.now().isoformat(),
            'migration_from': format_type,
            'migration_to': 'db_field'
        })
        
        # 添加迁移历史
        if 'edit_history' not in migrated_data:
            migrated_data['edit_history'] = []
        
        migrated_data['edit_history'].append({
            'timestamp': datetime.now().isoformat(),
            'action': 'field_mapping_migration',
            'from_format': format_type,
            'to_format': 'db_field',
            'migrated_fields': len(new_field_mappings)
        })
        
        return True, migrated_data
    
    def migrate_all_mappings(self) -> Dict[str, Any]:
        """迁移所有字段映射
        
        Returns:
            Dict: 迁移报告
        """
        self.logger.info("开始字段映射迁移...")
        
        # 备份原配置文件
        if os.path.exists(self.config_path):
            import shutil
            shutil.copy2(self.config_path, self.backup_path)
            self.logger.info(f"已备份原配置文件到: {self.backup_path}")
        
        # 加载配置
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {'success': False, 'error': str(e)}
        
        # 迁移统计
        migration_stats = {
            'total_tables': 0,
            'migrated_tables': 0,
            'skipped_tables': 0,
            'error_tables': 0,
            'migrated_table_names': [],
            'error_details': []
        }
        
        table_mappings = config.get('table_mappings', {})
        migration_stats['total_tables'] = len(table_mappings)
        
        # 逐表迁移
        for table_name, table_data in table_mappings.items():
            try:
                needs_migration, migrated_data = self.migrate_single_table(table_name, table_data)
                
                if needs_migration:
                    table_mappings[table_name] = migrated_data
                    migration_stats['migrated_tables'] += 1
                    migration_stats['migrated_table_names'].append(table_name)
                    self.logger.info(f"表 {table_name} 迁移完成")
                else:
                    migration_stats['skipped_tables'] += 1
                    
            except Exception as e:
                migration_stats['error_tables'] += 1
                migration_stats['error_details'].append({
                    'table_name': table_name,
                    'error': str(e)
                })
                self.logger.error(f"表 {table_name} 迁移失败: {e}")
        
        # 更新配置文件
        config['last_updated'] = datetime.now().isoformat()
        config['migration_info'] = {
            'migrated_at': datetime.now().isoformat(),
            'migration_stats': migration_stats
        }
        
        # 保存配置
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            migration_stats['success'] = True
            self.logger.info(f"字段映射迁移完成: {migration_stats}")
            
        except Exception as e:
            migration_stats['success'] = False
            migration_stats['save_error'] = str(e)
            self.logger.error(f"保存迁移结果失败: {e}")
        
        return migration_stats


def run_field_mapping_migration():
    """运行字段映射迁移"""
    migrator = FieldMappingMigrator()
    result = migrator.migrate_all_mappings()
    
    print("🔧 字段映射迁移结果:")
    print(f"总表数: {result.get('total_tables', 0)}")
    print(f"迁移表数: {result.get('migrated_tables', 0)}")
    print(f"跳过表数: {result.get('skipped_tables', 0)}")
    print(f"错误表数: {result.get('error_tables', 0)}")
    
    if result.get('migrated_table_names'):
        print(f"迁移的表: {', '.join(result['migrated_table_names'][:5])}...")
    
    if result.get('success'):
        print("✅ 迁移成功完成！")
    else:
        print("❌ 迁移过程中出现错误")
        
    return result


if __name__ == "__main__":
    run_field_mapping_migration()
