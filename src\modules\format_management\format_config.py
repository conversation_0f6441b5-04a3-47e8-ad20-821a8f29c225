#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式配置管理器

负责管理系统的格式配置，包括数据类型格式、显示规则、验证规则等。
提供配置的读取、保存、验证和更新功能。

主要功能:
- 配置文件的读取和保存
- 格式规则的管理和验证
- 配置变更的监控和通知
- 配置的备份和恢复

配置结构:
- table_types: 表格类型定义
- default_formats: 默认格式规则
- validation_rules: 数据验证规则
- display_rules: 显示规则配置

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import json
import shutil
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime
import logging

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class FormatConfig:
    """
    格式配置管理器
    
    负责管理系统的所有格式化配置，包括数据类型格式、显示规则、
    验证规则等。提供配置的统一管理和访问接口。
    """
    
    def __init__(self, config_path: str):
        """
        初始化格式配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = setup_logger("format_management.format_config")
        
        self.config_path = Path(config_path)
        self.backup_dir = self.config_path.parent / "backups"
        
        # 配置数据
        self._config_data = {}
        self._loaded = False
        self._last_modified = None
        
        # 默认配置结构
        self._default_config = self._get_default_config()
        
        # 确保目录存在
        self._ensure_directories()
        
        # 🔧 [P3性能修复] 降低日志级别，减少噪音（从12000次INFO变为DEBUG）
        self.logger.debug(f"🔧 [格式配置] 格式配置管理器初始化: {config_path}")
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            self.backup_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 创建目录失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "metadata": {
                "version": "1.0.0",
                "created_at": datetime.now().isoformat(),
                "description": "统一格式管理系统配置文件"
            },
            "table_types": {
                "active_employees": {
                    "display_name": "全部在职人员工资表",
                    "description": "包含所有在职员工的工资数据",
                    "priority": 1,
                    "enabled": True,
                    "default_sort": ["employee_id", "asc"]
                },
                "retired_employees": {
                    "display_name": "离休人员工资表",
                    "description": "离休员工的工资数据",
                    "priority": 2,
                    "enabled": True,
                    "default_sort": ["employee_id", "asc"]
                },
                "part_time_employees": {
                    "display_name": "临时工工资表",
                    "description": "临时工和兼职员工的工资数据",
                    "priority": 3,
                    "enabled": True,
                    "default_sort": ["employee_id", "asc"]
                },
                "contract_employees": {
                    "display_name": "合同工工资表",
                    "description": "合同制员工的工资数据",
                    "priority": 4,
                    "enabled": True,
                    "default_sort": ["employee_id", "asc"]
                }
            },
            "default_formats": {
                "currency": {
                    "decimal_places": 2,
                    "thousand_separator": ",",
                    "symbol": "¥",
                    "symbol_position": "prefix",
                    "negative_format": "-{symbol}{value}",
                    "zero_display": "0.00"
                },
                "percentage": {
                    "decimal_places": 1,
                    "symbol": "%",
                    "symbol_position": "suffix",
                    "multiply_by_100": False
                },
                "integer": {
                    "thousand_separator": ",",
                    "zero_display": "0"
                },
                "float": {
                    "decimal_places": 2,
                    "thousand_separator": ",",
                    "zero_display": "0.00"
                },
                "date": {
                    "input_format": "%Y-%m-%d",
                    "display_format": "%Y年%m月%d日",
                    "short_format": "%Y-%m-%d"
                },
                "string": {
                    "max_length": 100,
                    "trim_whitespace": True,
                    # 🎯 [用户需求2] 修改备注字段的空值显示为空白（而不是"-"）
                    "empty_display": ""
                },
                "month_string": {
                    "extract_last_two": True,
                    "zero_pad": True,
                    "empty_display": ""
                },
                # 🎯 [用户需求3] 月份提取后两位并转字符串的专用配置
                "month_string_extract_last_two": {
                    "extract_last_two": True,
                    "zero_pad": False,                  # 不补零，按用户需求
                    "empty_display": "",
                    "format_as_string": True,
                    "description": "提取月份后两位并转换为字符串"
                },
                "year_string": {
                    "prefer_four_digit": True,
                    "empty_display": ""
                }
            },
            "field_type_rules": {
                "currency_fields": [
                    "position_salary", "grade_salary", "allowance", "balance_allowance",
                    "basic_performance", "performance_bonus", "provident_fund",
                    "housing_allowance", "car_allowance", "supplement", "advance",
                    "total_salary", "pension_insurance", "health_fee", "transport_allowance",
                    "property_allowance", "communication_allowance"
                ],
                "integer_fields": [
                    "employee_id", "year", "month"
                ],
                "string_fields": [
                    "employee_name", "department", "employee_type", "employee_type_code"
                ],
                "date_fields": [
                    "entry_date", "retirement_date", "birth_date"
                ]
            },
            # 🎯 [用户需求] 全部在职人员工资表专用格式化配置
            "active_employees_format_config": {
                "table_types": ["全部在职人员", "active_employees", "全部在职人员工资表"],
                "field_formats": {
                    # 浮点数字段（保留两位小数）- 用户需求的工资字段
                    "float_fields": [
                        "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效",
                        "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴",
                        "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
                    ],
                    # 字符串字段
                    "string_fields": [
                        "工号", "姓名", "部门名称", "人员类别代码", "人员类别"
                    ],
                    # 特殊字符串字段 - 年份和月份处理
                    "special_string_fields": {
                        "年份": "year_string",  # 转为字符串
                        "月份": "month_string"  # 提取后两位
                    }
                },
                "format_rules": {
                    "float_format": ":.2f",     # 两位小数
                    "string_format": "str",      # 字符串格式
                    "null_display": "0.00",      # 空值显示为"0.00"（浮点型字段）
                    "zero_display": "0.00",      # 零值显示为"0.00"（浮点型字段）
                    "string_null_display": "",   # 字符串字段空值显示为空白
                    "string_dash_display": "",   # 字符串字段"-"字符显示为空白
                    "remove_dot_zero": True      # 移除工号中的".0"后缀
                },
                "enabled": True,
                "priority": 1
            },
            # 🎯 [用户需求] 离休人员表专用格式化配置（增强版）
            "retired_staff_format_config": {
                "table_types": [
                    "离休人员", "retired_staff", "离休人员工资表", "retired_employees",
                    # 支持更多表名变体
                    "salary_data_2025_08_retired_employees", "salary_data_retired_employees"
                ],
                "field_formats": {
                    # 🎯 [用户需求1] 浮点数字段（保留两位小数）
                    "float_fields": [
                        "基本离休费", "结余津贴", "生活补贴", "住房补贴", 
                        "物业补贴", "离休补贴", "护理费", 
                        "增发一次性生活补贴",  # 🎯 用户新增需求
                        "补发",                        # 🎯 用户新增需求
                        "合计", 
                        "借支"                         # 🎯 用户新增需求
                    ],
                    # 🎯 [用户需求2] 字符串字段（包含备注）
                    "string_fields": [
                        "姓名", "部门名称", "人员代码",
                        "备注"                          # 🎯 用户新增需求（空值显示为空白）
                    ],
                    # 🎯 [用户需求3] 特殊字符串字段 - 年份和月份的特殊处理
                    "special_string_fields": {
                        "月份": "month_string_extract_last_two",  # 提取后两位并转字符串
                        "年份": "year_string"                    # 转为字符串格式
                    }
                },
                # 🎯 [用户需求] 隐藏字段列表 - 不在主界面右侧列表展示区域显示
                "hidden_fields": [
                    "创建时间", "更新时间", "自增主键", "序号",
                    "created_at", "updated_at", "id", "row_number"
                ],
                "format_rules": {
                    "float_format": ":.2f",                # 两位小数
                    "string_format": "str",                 # 字符串格式
                    "null_display": "0.00",                 # 浮点型空值显示为"0.00"
                    "zero_display": "0.00",                 # 浮点型零值显示为"0.00"
                    "string_null_display": "",               # 🎯 [用户需求2] 字符串空值显示为空白
                    "string_dash_display": "",               # 🎯 [用户需求2] 字符串"-"字符显示为空白
                    "none_values": ["None", "nan", "0", "0.0", "", " ", "-"],  # 🎯 [用户需求] 空值定义
                    "remove_dot_zero": False                # 不移除浮点数的".0"后缀
                },
                "enabled": True,
                "priority": 1
            },
            # 🎯 [新增] 退休人员工资表专用格式化配置
            "pension_employees_format_config": {
                "table_types": [
                    "退休人员工资表", "pension_employees", "退休人员", 
                    "salary_data_2025_08_pension_employees", "salary_data_pension_employees"
                ],
                "field_formats": {
                    # 🎯 [用户需求1] 浮点数字段（保留两位小数）
                    "float_fields": [
                        "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费",
                        "物业补贴", "住房补贴", "增资预付", 
                        "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整",
                        "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整",
                        "补发", "借支", "应发工资", "公积", "保险扣款"
                    ],
                    # 🎯 [用户需求2] 字符串字段
                    "string_fields": [
                        "姓名", "部门名称", "人员代码", "备注"
                    ],
                    # 🎯 [用户需求3] 特殊字符串字段 - 年份和月份的特殊处理
                    "special_string_fields": {
                        "月份": "month_string_extract_last_two",  # 提取后两位并转字符串
                        "年份": "year_string"                    # 转为字符串格式
                    }
                },
                # 🎯 [用户需求4] 隐藏字段列表 - 不在主界面右侧列表展示区域显示
                "hidden_fields": [
                    "创建时间", "更新时间", "自增主键", "序号",
                    "created_at", "updated_at", "id", "row_number"
                ],
                "format_rules": {
                    "float_format": ":.2f",                # 两位小数
                    "string_format": "str",                 # 字符串格式
                    "null_display": "0.00",                 # 浮点型空值显示为"0.00"
                    "zero_display": "0.00",                 # 浮点型零值显示为"0.00"
                    "string_null_display": "",               # 🎯 [用户需求] 字符串空值显示为空白
                    "string_dash_display": "",               # 🎯 [用户需求] 字符串"-"字符显示为空白
                    "none_values": ["None", "nan", "0", "0.0", "", " ", "-"],  # 🎯 [用户需求] 空值定义
                    "remove_dot_zero": False                # 不移除浮点数的".0"后缀
                },
                "enabled": True,
                "priority": 1
            },
            "validation_rules": {
                "required_fields": {
                    "active_employees": ["employee_id", "employee_name"],
                    "retired_employees": ["employee_id", "employee_name"],
                    "part_time_employees": ["employee_id", "employee_name"],
                    "contract_employees": ["employee_id", "employee_name"]
                },
                "data_ranges": {
                    "salary_min": 0,
                    "salary_max": 1000000,
                    "year_min": 2020,
                    "year_max": 2030
                },
                "string_patterns": {
                    "employee_id": r"^[A-Za-z0-9]{6,20}$",
                    "department": r"^[\u4e00-\u9fa5A-Za-z0-9\s]{1,50}$"
                }
            },
            "display_rules": {
                "column_width": {
                    "min_width": 80,
                    "max_width": 300,
                    "auto_resize": True
                },
                "row_height": {
                    "default_height": 25,
                    "header_height": 30
                },
                "color_scheme": {
                    "header_bg": "#f0f0f0",
                    "row_alternate": "#f9f9f9",
                    "currency_positive": "#000000",
                    "currency_negative": "#cc0000"
                },
                "font_settings": {
                    "family": "微软雅黑",
                    "size": 9,
                    "bold_headers": True
                }
            },
            "export_settings": {
                "excel": {
                    "sheet_name_template": "{table_type}_{date}",
                    "include_metadata": True,
                    "freeze_header": True
                },
                "csv": {
                    "encoding": "utf-8-sig",
                    "separator": ",",
                    "include_header": True
                }
            }
        }
    
    # ================== 配置加载和保存 ==================
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            是否加载成功
        """
        try:
            if not self.config_path.exists():
                self.logger.info("🔧 [格式配置] 配置文件不存在，创建默认配置")
                self._config_data = self._default_config.copy()
                self.save_config()
                self._loaded = True
                return True
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_data = json.load(f)
            
            # 验证配置完整性
            self._validate_config()
            
            # 合并缺失的默认配置
            self._merge_default_config()
            
            self._loaded = True
            self._last_modified = datetime.fromtimestamp(self.config_path.stat().st_mtime)
            
            self.logger.info("🔧 [格式配置] 配置文件加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 配置文件加载失败: {e}")
            # 使用默认配置
            self._config_data = self._default_config.copy()
            self._loaded = True
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            是否保存成功
        """
        try:
            # 备份现有配置
            if self.config_path.exists():
                self._backup_config()
            
            # 更新元数据
            if "metadata" in self._config_data:
                self._config_data["metadata"]["last_modified"] = datetime.now().isoformat()
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
            
            self._last_modified = datetime.now()
            
            self.logger.info("🔧 [格式配置] 配置文件保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 配置文件保存失败: {e}")
            return False
    
    def _backup_config(self):
        """备份配置文件"""
        try:
            if not self.config_path.exists():
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"format_config_{timestamp}.json"
            
            shutil.copy2(self.config_path, backup_file)
            
            # 保留最近10个备份
            self._cleanup_old_backups()
            
            self.logger.debug(f"🔧 [格式配置] 配置备份完成: {backup_file}")
            
        except Exception as e:
            self.logger.warning(f"🔧 [格式配置] 配置备份失败: {e}")
    
    def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧备份"""
        try:
            if not self.backup_dir.exists():
                return
            
            backup_files = list(self.backup_dir.glob("format_config_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for backup_file in backup_files[keep_count:]:
                backup_file.unlink()
                self.logger.debug(f"🔧 [格式配置] 删除旧备份: {backup_file}")
                
        except Exception as e:
            self.logger.warning(f"🔧 [格式配置] 清理旧备份失败: {e}")
    
    def _validate_config(self):
        """验证配置完整性"""
        try:
            required_sections = ["table_types", "default_formats", "validation_rules"]
            
            for section in required_sections:
                if section not in self._config_data:
                    self.logger.warning(f"🔧 [格式配置] 缺少必要配置段: {section}")
                    
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 配置验证失败: {e}")
    
    def _merge_default_config(self):
        """合并缺失的默认配置"""
        try:
            def merge_dict(target: dict, source: dict):
                for key, value in source.items():
                    if key not in target:
                        target[key] = value
                    elif isinstance(value, dict) and isinstance(target[key], dict):
                        merge_dict(target[key], value)
            
            merge_dict(self._config_data, self._default_config)
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 合并默认配置失败: {e}")
    
    # ================== 配置访问接口 ==================
    
    def get_config(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，支持点分隔（如 'default_formats.currency.decimal_places'）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            if not self._loaded:
                self.load_config()
            
            keys = key_path.split('.')
            current = self._config_data
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default
            
            return current
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 获取配置失败 {key_path}: {e}")
            return default
    
    def set_config(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        try:
            if not self._loaded:
                self.load_config()
            
            keys = key_path.split('.')
            current = self._config_data
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            self.logger.debug(f"🔧 [格式配置] 配置已更新: {key_path}")
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 设置配置失败 {key_path}: {e}")
    
    def has_config(self, key_path: str) -> bool:
        """
        检查配置是否存在
        
        Args:
            key_path: 配置键路径
            
        Returns:
            是否存在
        """
        try:
            value = self.get_config(key_path, None)
            return value is not None
        except:
            return False
    
    def remove_config(self, key_path: str) -> bool:
        """
        删除配置项
        
        Args:
            key_path: 配置键路径
            
        Returns:
            是否删除成功
        """
        try:
            if not self._loaded:
                self.load_config()
            
            keys = key_path.split('.')
            current = self._config_data
            
            # 导航到父级
            for key in keys[:-1]:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return False
            
            # 删除配置项
            if isinstance(current, dict) and keys[-1] in current:
                del current[keys[-1]]
                self.logger.debug(f"🔧 [格式配置] 配置已删除: {key_path}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 删除配置失败 {key_path}: {e}")
            return False
    
    # ================== 特定配置获取方法 ==================
    
    def get_table_types(self) -> Dict[str, Dict]:
        """获取所有表格类型配置"""
        return self.get_config('table_types', {})
    
    def get_table_type_config(self, table_type: str) -> Optional[Dict]:
        """获取特定表格类型配置"""
        table_types = self.get_table_types()
        return table_types.get(table_type)
    
    def get_format_rules(self, format_type: str, table_type: str = None) -> Optional[Dict]:
        """获取格式规则 - 支持表类型特定配置"""
        # 🎯 [用户需求] 优先检查全部在职人员工资表专用配置
        if table_type and table_type in ['active_employees', 'active_staff']:
            active_config = self.get_config('active_employees_format_config.format_rules')
            if active_config and format_type in ['float', 'string']:
                return active_config
        
        # 🎯 [新增] 优先检查退休人员工资表专用配置
        if table_type and table_type in ['pension_employees', 'pension_staff']:
            pension_config = self.get_config('pension_employees_format_config.format_rules')
            if pension_config and format_type in ['float', 'string']:
                return pension_config
        
        # 优先检查离休人员专用配置
        if table_type and table_type in ['retired_employees', 'retired_staff']:
            retired_config = self.get_config('retired_staff_format_config.format_rules')
            if retired_config and format_type == 'float':
                return retired_config
        
        # 回退到默认配置
        return self.get_config(f'default_formats.{format_type}')
    
    def get_field_type_rules(self) -> Dict[str, List[str]]:
        """获取字段类型规则"""
        return self.get_config('field_type_rules', {})
    
    def get_validation_rules(self, table_type: str = None) -> Dict:
        """获取验证规则"""
        all_rules = self.get_config('validation_rules', {})
        if table_type:
            return {
                'required_fields': all_rules.get('required_fields', {}).get(table_type, []),
                'data_ranges': all_rules.get('data_ranges', {}),
                'string_patterns': all_rules.get('string_patterns', {})
            }
        return all_rules
    
    def get_display_rules(self) -> Dict:
        """获取显示规则"""
        return self.get_config('display_rules', {})
    
    # ================== 状态和工具方法 ==================
    
    def is_loaded(self) -> bool:
        """检查配置是否已加载"""
        return self._loaded
    
    def get_last_modified(self) -> Optional[datetime]:
        """获取最后修改时间"""
        return self._last_modified
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            self._loaded = False
            return self.load_config()
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 重新加载配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            self._config_data = self._default_config.copy()
            success = self.save_config()
            if success:
                self.logger.info("🔧 [格式配置] 配置已重置为默认值")
            return success
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 重置配置失败: {e}")
            return False
    
    def export_config(self, export_path: str) -> bool:
        """导出配置到指定文件"""
        try:
            export_file = Path(export_path)
            export_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"🔧 [格式配置] 配置已导出到: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 配置导出失败: {e}")
            return False
    
    def get_active_employees_format_config(self) -> Dict[str, Any]:
        """
        🎯 [用户需求] 获取全部在职人员工资表专用格式化配置
        
        Returns:
            全部在职人员工资表格式化配置字典
        """
        if not self._loaded:
            self.load_config()
        
        return self._config_data.get("active_employees_format_config", {
            "table_types": ["全部在职人员", "active_employees", "全部在职人员工资表"],
            "field_formats": {
                "float_fields": [
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效",
                    "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴",
                    "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
                ],
                "string_fields": [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别"
                ],
                "special_string_fields": {
                    "年份": "year_string",
                    "月份": "month_string"
                }
            },
            "format_rules": {
                "float_format": ":.2f",
                "string_format": "str",
                "null_display": "0.00",
                "zero_display": "0.00",
                "string_null_display": "",
                "string_dash_display": "",
                "remove_dot_zero": True
            },
            "enabled": True,
            "priority": 1
        })

    def get_retired_staff_format_config(self) -> Dict[str, Any]:
        """
        获取离休人员表专用格式化配置
        
        Returns:
            离休人员表格式化配置字典
        """
        if not self._loaded:
            self.load_config()
        
        return self._config_data.get("retired_staff_format_config", {
            "table_types": ["离休人员", "retired_staff", "离休人员工资表"],
            "field_formats": {
                "float_fields": [
                    "基本离休费", "结余津贴", "生活补贴", "住房补贴", 
                    "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", 
                    "补发", "合计", "借支"
                ],
                "string_fields": [
                    "姓名", "部门名称", "备注"
                ]
            },
            "format_rules": {
                "float_format": ":.2f",
                "string_format": "str",
                "null_display": "0.00",
                "zero_display": "0.00"
            },
            "enabled": True,
            "priority": 1
        })

    def get_pension_employees_format_config(self) -> Dict[str, Any]:
        """
        🎯 [新增] 获取退休人员工资表专用格式化配置
        
        Returns:
            退休人员工资表格式化配置字典
        """
        if not self._loaded:
            self.load_config()
        
        return self._config_data.get("pension_employees_format_config", {
            "table_types": [
                "退休人员工资表", "pension_employees", "退休人员", 
                "salary_data_2025_08_pension_employees", "salary_data_pension_employees"
            ],
            "field_formats": {
                "float_fields": [
                    "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费",
                    "物业补贴", "住房补贴", "增资预付", 
                    "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整",
                    "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整",
                    "补发", "借支", "应发工资", "公积", "保险扣款"
                ],
                "string_fields": [
                    "姓名", "部门名称", "人员代码", "备注"
                ],
                "special_string_fields": {
                    "月份": "month_string_extract_last_two",
                    "年份": "year_string"
                }
            },
            "hidden_fields": [
                "创建时间", "更新时间", "自增主键", "序号",
                "created_at", "updated_at", "id", "row_number"
            ],
            "format_rules": {
                "float_format": ":.2f",
                "string_format": "str",
                "null_display": "0.00",
                "zero_display": "0.00",
                "string_null_display": "",
                "string_dash_display": "",
                "none_values": ["None", "nan", "0", "0.0", "", " ", "-"],
                "remove_dot_zero": False
            },
            "enabled": True,
            "priority": 1
        })
    
    def is_active_employees_table(self, table_name: str) -> bool:
        """
        🎯 [用户需求] 判断是否为全部在职人员工资表
        
        Args:
            table_name: 表名
            
        Returns:
            是否为全部在职人员工资表
        """
        config = self.get_active_employees_format_config()
        table_types = config.get("table_types", [])
        
        if not table_name:
            return False
            
        # 检查表名是否包含全部在职人员相关关键词
        for table_type in table_types:
            if table_type in table_name:
                return True
        
        # 额外检查常见的表名格式
        active_keywords = ["active_employees", "全部在职人员", "在职人员工资表", "active_staff"]
        for keyword in active_keywords:
            if keyword in table_name:
                return True
        
        return False
        
    def is_retired_staff_table(self, table_name: str) -> bool:
        """
        判断是否为离休人员表
        
        Args:
            table_name: 表名
            
        Returns:
            是否为离休人员表
        """
        config = self.get_retired_staff_format_config()
        table_types = config.get("table_types", [])
        
        if not table_name:
            return False
            
        # 检查表名是否包含离休人员相关关键词
        for table_type in table_types:
            if table_type in table_name:
                return True
        
        return False
    
    def is_pension_employees_table(self, table_name: str) -> bool:
        """
        🎯 [新增] 判断是否为退休人员工资表
        
        Args:
            table_name: 表名
            
        Returns:
            是否为退休人员工资表
        """
        config = self.get_pension_employees_format_config()
        table_types = config.get("table_types", [])
        
        if not table_name:
            return False
            
        # 检查表名是否包含退休人员相关关键词
        for table_type in table_types:
            if table_type in table_name:
                return True
        
        # 额外检查常见的表名格式
        pension_keywords = ["pension_employees", "退休人员工资表", "退休人员", "pension_staff"]
        for keyword in pension_keywords:
            if keyword in table_name:
                return True
        
        return False
    
    def import_config(self, import_path: str) -> bool:
        """从指定文件导入配置"""
        try:
            import_file = Path(import_path)
            if not import_file.exists():
                self.logger.error(f"🔧 [格式配置] 导入文件不存在: {import_path}")
                return False
            
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 备份当前配置
            self._backup_config()
            
            # 导入新配置
            self._config_data = imported_config
            success = self.save_config()
            
            if success:
                self.logger.info(f"🔧 [格式配置] 配置已从文件导入: {import_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"🔧 [格式配置] 配置导入失败: {e}")
            return False