#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Cell级别格式化修复效果
测试分页和导航是否使用统一格式化逻辑
"""

import sys
import os

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def test_cell_formatting_fix():
    """测试Cell级别格式化修复"""
    print("[TEST] 开始测试Cell级别格式化修复...")
    
    try:
        # 1. 测试UnifiedFormatManager的单个值格式化
        print("\n[TEST 1] 测试UnifiedFormatManager单个值格式化:")
        
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        unified_formatter = get_unified_format_manager()
        
        # 测试关键字段的格式化
        test_cases = [
            (1234, "2025年基础性绩效", "active_employees"),
            (5678, "2025年奖励性绩效预发", "active_employees"),
            ("-", "车补", "active_employees"),
            (999, "代扣代存养老保险", "active_employees"),
            ("01", "人员类别代码", "active_employees")
        ]
        
        for value, column_name, table_type in test_cases:
            try:
                formatted = unified_formatter.format_display_value(value, column_name, table_type)
                print(f"  {column_name}: {value} -> '{formatted}'")
            except Exception as e:
                print(f"  {column_name}: 格式化失败 - {e}")
        
        # 2. 测试virtualized_expandable_table的_format_cell_value方法
        print("\n[TEST 2] 测试virtualized_expandable_table的cell格式化:")
        
        # 模拟表格组件
        class MockTable:
            def __init__(self):
                from src.utils.log_config import setup_logger
                self.logger = setup_logger("MockTable")
                self.current_table_type = "salary_data_2025_07_active_employees"
                self.table_name = "salary_data_2025_07_active_employees"
                self._data_pre_formatted = False
                self._format_cache_dirty = True
                self._use_builtin_formatter = False
                
                # 初始化格式管理器
                self._format_manager = unified_formatter
                self._use_unified_format = True
            
            def _extract_table_type_for_formatting(self) -> str:
                """提取表类型用于统一格式化"""
                return 'active_employees'
            
            def _get_table_specific_format_config(self):
                """获取表配置（简化版）"""
                return {
                    'float_fields': ['2025年基础性绩效', '2025年奖励性绩效预发', '车补', '代扣代存养老保险'],
                    'string_fields': ['人员类别代码']
                }
            
            def _format_by_table_config(self, value, column_name, config):
                """基于表配置格式化（简化版）"""
                if column_name in config.get('float_fields', []):
                    try:
                        if value == '-':
                            return "0.00"
                        return f"{float(value):.2f}"
                    except:
                        return "0.00"
                return str(value) if value is not None else ""
            
            def _format_cell_value_builtin(self, value, column_name):
                """内置格式化（简化版）"""
                return str(value) if value is not None else ""
            
            def _format_cell_value(self, value, column_name: str = '') -> str:
                """Cell级别格式化方法（复制修复后的逻辑）"""
                # 检查预格式化状态
                if hasattr(self, '_data_pre_formatted') and self._data_pre_formatted:
                    return str(value) if value is not None else ""
                
                # 缓存表配置
                if not hasattr(self, '_cached_table_config') or self._format_cache_dirty:
                    self._cached_table_config = self._get_table_specific_format_config()
                    self._format_cache_dirty = False
                config = self._cached_table_config
                
                # 🔧 [新系统] 优先使用统一格式管理系统
                if self._use_unified_format and self._format_manager:
                    # 🔧 [根本修复] 获取正确的表类型并使用统一格式管理系统
                    table_type = self._extract_table_type_for_formatting()
                    return self._format_manager.format_display_value(value, column_name, table_type)
                elif config and not self._use_builtin_formatter:
                    # 回退到GUI层的表配置格式化
                    return self._format_by_table_config(value, column_name, config)
                else:
                    # 最后回退到内置格式化
                    return self._format_cell_value_builtin(value, column_name)
        
        # 创建模拟表格并测试
        mock_table = MockTable()
        
        for value, column_name, _ in test_cases:
            try:
                formatted = mock_table._format_cell_value(value, column_name)
                print(f"  Cell格式化 {column_name}: {value} -> '{formatted}'")
            except Exception as e:
                print(f"  Cell格式化 {column_name}: 失败 - {e}")
                import traceback
                traceback.print_exc()
        
        # 3. 验证修复效果
        print("\n[TEST 3] 验证修复效果:")
        
        success_cases = 0
        total_cases = len(test_cases)
        
        for value, column_name, table_type in test_cases:
            try:
                # 直接格式化
                direct_formatted = unified_formatter.format_display_value(value, column_name, table_type)
                # Cell级格式化
                cell_formatted = mock_table._format_cell_value(value, column_name)
                
                if direct_formatted == cell_formatted:
                    print(f"  [OK] {column_name}: 一致 ('{direct_formatted}')")
                    success_cases += 1
                else:
                    print(f"  [FAIL] {column_name}: 不一致")
                    print(f"     直接格式化: '{direct_formatted}'")
                    print(f"     Cell格式化: '{cell_formatted}'")
                    
            except Exception as e:
                print(f"  [ERROR] {column_name}: 测试失败 - {e}")
        
        print(f"\n[RESULT] 测试结果: {success_cases}/{total_cases} 通过")
        
        if success_cases == total_cases:
            print("[SUCCESS] 所有测试通过！Cell级别格式化已成功修复")
            return True
        else:
            print("[WARNING] 部分测试失败，需要进一步调试")
            return False
        
    except Exception as e:
        print(f"[ERROR] 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_cell_formatting_fix()