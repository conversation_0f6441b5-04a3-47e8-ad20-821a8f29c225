#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化修复验证脚本
验证浮点数格式化、车补字段处理、人员类别代码零填充功能
"""

import sys
import os
import pandas as pd

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.modules.format_management.field_registry import FieldRegistry
from src.modules.format_management.format_config import FormatConfig
from src.modules.format_management.format_renderer import FormatRenderer

def test_format_fixes():
    """测试格式化修复"""
    print("[测试] 开始验证格式化修复...")
    
    # 初始化组件
    field_registry = FieldRegistry("state/data/field_mappings.json")
    format_config = FormatConfig("src/modules/format_management/format_config.json")
    renderer = FormatRenderer(format_config, field_registry)
    
    # 测试数据
    test_data = {
        'basic_performance_2025': [1234, 5678.0, 0, "1000"],
        'car_allowance': [100.5, "150", "-", None],
        'pension_insurance': [200.75, "250.5", 0.0, None],
        'employee_type_code': ["1", "02", "3.0", "1.0"]
    }
    
    df = pd.DataFrame(test_data)
    print(f"原始数据：\n{df}")
    
    # 获取字段类型配置
    table_type = "active_employees"
    field_types = field_registry.get_table_field_types(table_type)
    print(f"\n字段类型配置: {field_types}")
    
    if not field_types:
        print("❌ 字段类型配置为空，尝试直接匹配表名...")
        # 尝试使用确切的表名
        field_types = field_registry.get_table_field_types("salary_data_2025_07_active_employees")
        print(f"直接匹配结果: {field_types}")
    
    # 逐列测试格式化
    print("\n[测试结果]")
    for column in df.columns:
        field_type = field_types.get(column, 'string')
        print(f"\n列 '{column}' (类型: {field_type}):")
        
        for i, value in enumerate(df[column]):
            try:
                formatted = renderer.render_value(value, field_type, column, table_type)
                print(f"  [{i}] {value} -> '{formatted}'")
            except Exception as e:
                print(f"  [{i}] {value} -> ERROR: {e}")
    
    # 测试DataFrame级格式化
    print("\n[DataFrame格式化测试]")
    try:
        formatted_df = renderer.render_dataframe(df, table_type)
        print("格式化后的DataFrame:")
        print(formatted_df)
    except Exception as e:
        print(f"DataFrame格式化失败: {e}")

if __name__ == "__main__":
    test_format_fixes()