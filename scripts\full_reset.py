#!/usr/bin/env python3
"""
完全重置脚本
删除所有状态和数据，从头开始
"""

import os
import shutil
from datetime import datetime
from pathlib import Path

def backup_original_data():
    """备份原始Excel数据"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup/full_reset_backup_{timestamp}"
    
    print(f"📦 创建完整备份: {backup_dir}")
    Path(backup_dir).mkdir(parents=True, exist_ok=True)
    
    # 备份所有重要目录
    dirs_to_backup = [
        "state",
        "data", 
        "config.json",
        "user_preferences.json"
    ]
    
    for item in dirs_to_backup:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(backup_dir, item))
            else:
                shutil.copy2(item, backup_dir)
            print(f"  ✅ 已备份: {item}")
    
    return backup_dir

def full_reset():
    """完全重置"""
    print("🔥 执行完全重置...")
    
    # 删除所有状态和数据
    items_to_remove = [
        "state",
        "data/db",
        "config.json", 
        "user_preferences.json",
        "logs"  # 可选：也清理日志
    ]
    
    for item in items_to_remove:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"  🗑️  已删除目录: {item}")
            else:
                os.remove(item)
                print(f"  🗑️  已删除文件: {item}")

def main():
    """主重置流程"""
    print("⚠️  警告：即将执行完全重置！")
    print("这将删除所有配置、状态和数据库数据")
    
    confirm = input("确认继续？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return
    
    # 1. 备份
    backup_dir = backup_original_data()
    
    # 2. 完全重置
    full_reset()
    
    print(f"""
🔥 完全重置完成！

📋 重置总结:
  - 所有配置文件: 已删除
  - 所有状态文件: 已删除  
  - 数据库文件: 已删除
  - 日志文件: 已删除
  - 备份位置: {backup_dir}

🚀 系统现在完全干净:
  - 下次启动将创建全新配置
  - 需要重新导入所有Excel文件
  - 所有设置恢复默认

📂 原始Excel文件仍在:
  - data/工资表/ 目录保留
  - 可以重新导入数据

⚠️  如需恢复，使用备份:
  cp -r {backup_dir}/* ./
""")

if __name__ == "__main__":
    main()