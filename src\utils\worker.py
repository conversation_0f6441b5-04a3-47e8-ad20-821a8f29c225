"""
通用工作线程 (Worker)

提供一个可重用的QRunnable实现，用于在后台线程中执行耗时操作，
并通过信号机制将结果、错误和完成状态安全地传递回主UI线程。
"""

from PyQt5.QtCore import QObject, QRunnable, pyqtSignal, pyqtSlot
import traceback
import sys

class WorkerSignals(QObject):
    """
    定义Worker线程可以发出的信号。
    - finished: 任务完成时发出。
    - error: 任务出错时发出，携带异常信息 (type, value, traceback)。
    - result: 任务成功时发出，携带执行结果。
    """
    finished = pyqtSignal()
    error = pyqtSignal(tuple)
    result = pyqtSignal(object)


class Worker(QRunnable):
    """
    通用工作线程，用于执行任何耗时函数而不阻塞UI线程。
    """
    def __init__(self, fn, *args, **kwargs):
        super(Worker, self).__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    @pyqtSlot()
    def run(self):
        """
        在QThreadPool管理的线程中执行目标函数。
        """
        try:
            result = self.fn(*self.args, **self.kwargs)
        except Exception as e:
            # 捕获异常并通过信号发送
            traceback.print_exc()
            exctype, value = sys.exc_info()[:2]
            self.signals.error.emit((exctype, value, traceback.format_exc()))
        else:
            # 发送执行结果
            self.signals.result.emit(result)
        finally:
            # 发出完成信号
            self.signals.finished.emit() 