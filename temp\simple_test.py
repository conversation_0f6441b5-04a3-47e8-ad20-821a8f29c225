# -*- coding: utf-8 -*-
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.format_management.field_registry import FieldRegistry

def main():
    print("开始验证修复效果...")
    
    registry = FieldRegistry('state/data/field_mappings.json')
    print("FieldRegistry已创建")
    
    # 测试active_employees表
    display_fields = registry.get_display_fields("active_employees")
    print(f"active_employees字段数量: {len(display_fields) if display_fields else 0}")
    
    if display_fields and len(display_fields) > 0:
        print("SUCCESS: existing_display_fields不再为空\!")
        
        if "人员类别" in display_fields and "人员类别代码" in display_fields:
            type_idx = display_fields.index("人员类别")
            code_idx = display_fields.index("人员类别代码")
            
            if type_idx < code_idx:
                print(f"SUCCESS: 表头顺序正确\! 人员类别位置{type_idx} -> 人员类别代码位置{code_idx}")
            else:
                print(f"FAILED: 表头顺序错误\! 人员类别位置{type_idx} -> 人员类别代码位置{code_idx}")
        
        print("前5个字段:", display_fields[:5])
    else:
        print("FAILED: existing_display_fields仍然为空")
    
    print("修复验证完成\!")

if __name__ == "__main__":
    main()
