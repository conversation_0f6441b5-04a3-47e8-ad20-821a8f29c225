#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序功能修复验证测试脚本

测试目标：
1. 验证排序功能是否真正工作
2. 验证排序后是否保持当前页码
3. 验证排序结果是否正确显示在UI中

使用方法：
1. 先启动系统并导入数据
2. 手动切换到第2页或第3页
3. 点击任意列头进行排序
4. 检查是否在当前页显示排序结果
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_sort_functionality():
    """测试排序功能"""
    print("🔧 [排序修复验证] 开始测试排序功能...")
    
    # 检查数据库
    db_path = project_root / "data" / "db" / "salary_system.db"
    if not db_path.exists():
        print("❌ 数据库文件不存在，请先导入数据")
        return False
    
    # 连接数据库测试排序查询
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 获取表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%'")
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ 没有找到工资数据表")
            return False
        
        table_name = tables[0][0]  # 使用第一个表
        print(f"✅ 使用表: {table_name}")
        
        # 测试排序查询
        # 1. 普通查询（无排序）
        cursor.execute(f'SELECT * FROM "{table_name}" LIMIT 5')
        normal_data = cursor.fetchall()
        print(f"📊 普通查询前5条数据的第一列值: {[row[0] if row else None for row in normal_data]}")
        
        # 2. 排序查询（假设第二列是薪级工资）
        cursor.execute(f'SELECT * FROM "{table_name}" WHERE "grade_salary_2025" IS NOT NULL ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 5')
        sorted_asc_data = cursor.fetchall()
        print(f"📈 升序排序前5条数据的第一列值: {[row[0] if row else None for row in sorted_asc_data]}")
        
        # 3. 降序排序查询
        cursor.execute(f'SELECT * FROM "{table_name}" WHERE "grade_salary_2025" IS NOT NULL ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 5')
        sorted_desc_data = cursor.fetchall()
        print(f"📉 降序排序前5条数据的第一列值: {[row[0] if row else None for row in sorted_desc_data]}")
        
        # 4. 分页排序查询（模拟第2页）
        cursor.execute(f'SELECT * FROM "{table_name}" WHERE "grade_salary_2025" IS NOT NULL ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 5 OFFSET 50')
        page2_sorted_data = cursor.fetchall()
        print(f"📄 第2页排序数据前5条的第一列值: {[row[0] if row else None for row in page2_sorted_data]}")
        
        conn.close()
        
        # 验证数据是否真的不同
        normal_first_col = [row[0] for row in normal_data if row]
        sorted_first_col = [row[0] for row in sorted_asc_data if row]
        
        if normal_first_col != sorted_first_col:
            print("✅ 数据库排序功能正常 - 排序前后数据确实不同")
            return True
        else:
            print("⚠️  数据库排序结果与原始数据相同，可能数据本身已经是有序的")
            return True
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def check_log_for_sort_evidence():
    """检查日志中的排序证据"""
    print("\n🔍 [日志分析] 检查最近的排序操作...")
    
    log_path = project_root / "logs" / "salary_system.log"
    if not log_path.exists():
        print("❌ 日志文件不存在")
        return
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近的排序操作
        sort_lines = []
        for i, line in enumerate(lines):
            if "排序调试" in line or "CRITICAL修复" in line:
                sort_lines.append(f"行{i+1}: {line.strip()}")
        
        if sort_lines:
            print(f"📋 找到 {len(sort_lines)} 条排序相关日志:")
            for line in sort_lines[-10:]:  # 显示最后10条
                print(f"  {line}")
        else:
            print("❌ 没有找到排序相关的日志")
            
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")

def print_test_instructions():
    """打印测试说明"""
    print("\n" + "="*60)
    print("🎯 排序功能修复验证测试")
    print("="*60)
    print()
    print("📋 手动测试步骤：")
    print("1. 启动系统：python main.py")
    print("2. 导入数据（如果还没有的话）")
    print("3. 在列表中切换到第2页或第3页")
    print("4. 点击任意列头（如'2025年薪级工资'）进行排序")
    print("5. 观察以下几点：")
    print("   ✅ 页码是否保持在当前页（不应该跳回第1页）")
    print("   ✅ 数据是否按点击的列进行排序")
    print("   ✅ 排序状态是否在列头显示（升序/降序箭头）")
    print("   ✅ 再次点击同一列头是否切换排序方向")
    print()
    print("🔧 关键修复点：")
    print("- 排序操作不再重置页码到第1页")
    print("- 排序结果正确显示在当前页")
    print("- 分页和排序状态正确同步")
    print()
    print("⚠️  如果排序后页面还是跳回第1页，说明修复不完整")
    print("✅ 如果排序后保持当前页并显示排序结果，说明修复成功")
    print()

if __name__ == "__main__":
    print_test_instructions()
    
    # 运行数据库测试
    if test_sort_functionality():
        print("\n✅ 数据库层面排序功能正常")
    else:
        print("\n❌ 数据库层面排序功能异常")
    
    # 检查日志
    check_log_for_sort_evidence()
    
    print("\n" + "="*60)
    print("🚀 请按照上面的手动测试步骤验证UI排序功能")
    print("="*60) 