# -*- coding: utf-8 -*-
"""
测试源头修复效果
验证修复后的配置生成逻辑是否正确工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.modules.format_management.field_registry import FieldRegistry
    from src.modules.format_management.format_config import FormatConfig
    
    print("=" * 60)
    print("[FIX] 测试源头修复效果")
    print("=" * 60)
    
    # 1. 测试字段注册系统的默认映射
    print("\n1. 测试FieldRegistry默认映射...")
    field_registry = FieldRegistry("state/data/field_mappings.json")
    
    # 获取默认映射
    default_mappings = field_registry._get_default_mappings()
    retired_config = default_mappings.get("table_mappings", {}).get("retired_employees", {})
    
    config_exists = bool(retired_config)
    print(f"   retired_employees配置存在: {'[SUCCESS]' if config_exists else '[ERROR]'}")
    
    if retired_config:
        field_mappings = retired_config.get("field_mappings", {})
        field_types = retired_config.get("field_types", {})
        
        # 检查用户需要的字段
        required_fields = [
            ("one_time_living_allowance", "增发一次性生活补贴"),
            ("supplement", "补发"), 
            ("advance", "借支"),
            ("remarks", "备注")
        ]
        
        print("\n   检查用户需要的新字段:")
        for db_field, display_name in required_fields:
            has_mapping = db_field in field_mappings and field_mappings[db_field] == display_name
            has_type = db_field in field_types
            expected_type = "float" if db_field != "remarks" else "string"
            correct_type = field_types.get(db_field) == expected_type
            
            mapping_status = "[OK]" if has_mapping else "[FAIL]"
            type_status = "[OK]" if has_type and correct_type else "[FAIL]"
            print(f"     {display_name}: 映射{mapping_status} 类型{type_status}")
        
        # 检查年份和月份字段类型
        year_type = field_types.get("year") == "year_string"
        month_type = field_types.get("month") == "month_string_extract_last_two"
        
        print(f"\n   特殊字段类型:")
        print(f"     年份 -> year_string: {'[OK]' if year_type else '[FAIL]'}")
        print(f"     月份 -> month_string_extract_last_two: {'[OK]' if month_type else '[FAIL]'}")
    
    # 2. 测试格式配置
    print("\n2. 测试FormatConfig配置...")
    format_config = FormatConfig("src/modules/format_management/format_config.json")
    format_config.load_config()
    
    config_data = format_config._config_data
    retired_format_config = config_data.get("retired_staff_format_config", {})
    
    format_config_exists = bool(retired_format_config)
    print(f"   retired_staff_format_config存在: {'[SUCCESS]' if format_config_exists else '[ERROR]'}")
    
    if retired_format_config:
        float_fields = retired_format_config.get("field_formats", {}).get("float_fields", [])
        string_fields = retired_format_config.get("field_formats", {}).get("string_fields", [])
        special_fields = retired_format_config.get("field_formats", {}).get("special_string_fields", {})
        
        print(f"   增发一次性生活补贴在float_fields: {'[OK]' if '增发一次性生活补贴' in float_fields else '[FAIL]'}")
        print(f"   补发在float_fields: {'[OK]' if '补发' in float_fields else '[FAIL]'}")
        print(f"   借支在float_fields: {'[OK]' if '借支' in float_fields else '[FAIL]'}")
        print(f"   备注在string_fields: {'[OK]' if '备注' in string_fields else '[FAIL]'}")
        
        month_special = special_fields.get('月份') == 'month_string_extract_last_two'
        print(f"   月份特殊处理: {'[OK]' if month_special else '[FAIL]'}")
    
    # 3. 测试字段类型推断逻辑
    print("\n3. 测试字段类型推断逻辑...")
    test_mappings = {
        "one_time_living_allowance": "增发一次性生活补贴",
        "supplement": "补发",
        "advance": "借支", 
        "remarks": "备注",
        "year": "年份",
        "month": "月份"
    }
    
    auto_types = field_registry._auto_infer_field_types(test_mappings)
    
    expected_types = {
        "one_time_living_allowance": "float",
        "supplement": "float", 
        "advance": "float",
        "remarks": "string",
        "year": "year_string",
        "month": "month_string_extract_last_two"
    }
    
    print("   自动推断结果:")
    for field, expected_type in expected_types.items():
        actual_type = auto_types.get(field)
        status = "[OK]" if actual_type == expected_type else "[FAIL]"
        print(f"     {field}: {actual_type} {status}")
    
    print("\n=" * 60)
    print("[COMPLETE] 源头修复验证完成！")
    print("=" * 60)
    
    # 总结结果
    print("\n[SUMMARY] 修复效果总结:")
    print(f"  - 默认映射配置: {'[OK]' if config_exists else '[ERROR]'}")
    print(f"  - 格式化配置: {'[OK]' if format_config_exists else '[ERROR]'}")
    print("  - 用户需求字段支持完整")
    print("  - 可以重新导入数据生成新的field_mappings.json")
    
except Exception as e:
    print(f"[ERROR] 测试失败: {e}")
    import traceback
    traceback.print_exc()