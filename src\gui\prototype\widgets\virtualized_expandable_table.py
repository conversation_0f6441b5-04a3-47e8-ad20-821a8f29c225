"""
虚拟化可展开表格组件 - Phase 4 核心交互功能增强

基于creative-core-algorithms.md的虚拟化展开算法设计，
实现支持大数据量的智能表格展开/折叠系统。

主要特性:
- 虚拟化渲染：只渲染可见区域，支持大数据量
- 智能展开：流畅的展开/折叠动画效果 
- 表格内编辑：双击编辑、Enter确认、Esc取消 (P4-001)
- 多选批量操作：Ctrl+Click、Shift+Click、8种批量功能 (P4-002)
- 右键菜单系统：上下文感知菜单、动态菜单项 (P4-003)
- 性能优化：展开响应时间<200ms，60fps动画目标
- Material Design：现代化视觉效果和交互反馈

技术架构:
- VirtualizedExpandableTable: 主表格组件
- ExpandableTableItem: 可展开行项
- ExpandableTableModel: 数据模型
- TableAnimationManager: 动画管理器
- TableCellEditor: 单元格编辑器 (P4-001)
- TableContextMenu: 右键菜单系统 (P4-003)

作者: PyQt5重构项目组
创建时间: 2025-06-19 16:10
更新时间: 2025-06-19 18:10 (P4-003 右键菜单系统)
"""

import sys
import math
import time
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Tuple, Callable
from enum import Enum
from dataclasses import dataclass
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QSizePolicy, QFrame, QPushButton, QLabel, QLineEdit, QSpinBox,
    QComboBox, QDateEdit, QDoubleSpinBox, QSpinBox, QCheckBox, 
    QTextEdit, QStyledItemDelegate, QMenu, QAction, QWidgetAction,
    QMessageBox, QToolTip, QShortcut
)
from PyQt5.QtCore import (
    Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, QSize,
    pyqtSignal, QObject, QVariant, QModelIndex, QAbstractTableModel,
    QEvent, QDate, QDateTime, QPoint, QMimeData, QThread
)
from PyQt5.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QPalette,
    QLinearGradient, QPainterPath, QPolygonF, QMouseEvent, QPaintEvent,
    QKeyEvent, QValidator, QIntValidator, QDoubleValidator, QIcon,
    QContextMenuEvent, QPixmap, QDrag
)

# 修复导入问题
try:
    from src.utils.log_config import setup_logger
    from src.utils.thread_safe_timer import ThreadSafeTimer
    from src.modules.data_import.header_edit_manager import HeaderEditManager
    from src.modules.data_import.config_sync_manager import ConfigSyncManager
    # 导入统一格式管理系统
    # 🎯 [统一格式管理] 使用统一格式管理系统
    
    # 🔧 [Timer修复] 已集成线程安全Timer管理，无需外部依赖
except ImportError:
    # 如果无法导入，使用标准日志
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    # 如果无法导入，创建Mock类
    class HeaderEditManager:
        def __init__(self, *args, **kwargs):
            pass
        def show_edit_dialog(self, *args, **kwargs):
            return False
    
    class ConfigSyncManager:
        def __init__(self, *args, **kwargs):
            pass
    
    # Mock类
    class UnifiedFormatManager:
        def __init__(self, *args, **kwargs):
            pass
        def format_table_complete(self, data, table_type, **kwargs):
            return list(data.columns) if hasattr(data, 'columns') else [], data
        def format_headers(self, headers, table_type, **kwargs):
            return headers

class ExpandState(Enum):
    """表格行展开状态枚举"""
    COLLAPSED = "collapsed"    # 折叠状态
    EXPANDING = "expanding"    # 展开中
    EXPANDED = "expanded"      # 已展开
    COLLAPSING = "collapsing"  # 折叠中

class ContextMenuType(Enum):
    """右键菜单类型枚举"""
    SINGLE_ROW = "single_row"          # 单行菜单
    MULTI_ROW = "multi_row"            # 多行菜单
    CELL_EDIT = "cell_edit"            # 单元格编辑菜单
    HEADER = "header"                  # 表头菜单
    EMPTY_AREA = "empty_area"          # 空白区域菜单
    EXPANDED_ROW = "expanded_row"      # 已展开行菜单
    COLLAPSED_ROW = "collapsed_row"    # 已折叠行菜单

class MenuAction(Enum):
    """菜单动作枚举"""
    # 基础操作
    COPY = "copy"
    PASTE = "paste"
    CUT = "cut"
    DELETE = "delete"
    SELECT_ALL = "select_all"
    CLEAR_SELECTION = "clear_selection"
    
    # 编辑操作
    EDIT_CELL = "edit_cell"
    BATCH_EDIT = "batch_edit"
    CANCEL_EDIT = "cancel_edit"
    
    # 展开操作
    EXPAND_ROW = "expand_row"
    COLLAPSE_ROW = "collapse_row"
    EXPAND_ALL = "expand_all"
    COLLAPSE_ALL = "collapse_all"
    
    # 批量操作
    BATCH_DELETE = "batch_delete"
    BATCH_COPY = "batch_copy"
    BATCH_EXPORT = "batch_export"
    BATCH_EXPAND = "batch_expand"
    BATCH_COLLAPSE = "batch_collapse"
    
    # 表格操作
    INSERT_ROW = "insert_row"
    SORT_COLUMN = "sort_column"
    HIDE_COLUMN = "hide_column"
    RESIZE_COLUMNS = "resize_columns"
    
    # 高级操作
    FILTER_ROWS = "filter_rows"
    REFRESH_DATA = "refresh_data"
    EXPORT_DATA = "export_data"

@dataclass
class MenuItemConfig:
    """菜单项配置数据"""
    action: MenuAction
    text: str
    icon: Optional[str] = None
    shortcut: Optional[str] = None
    enabled: bool = True
    visible: bool = True
    separator_after: bool = False
    tooltip: str = ""
    callback: Optional[Callable] = None

@dataclass
class ContextMenuContext:
    """右键菜单上下文数据"""
    menu_type: ContextMenuType
    clicked_row: int = -1
    clicked_column: int = -1
    selected_rows: List[int] = None
    selected_columns: List[int] = None
    click_position: QPoint = None
    is_editing: bool = False
    row_expanded: bool = False
    
    def __post_init__(self):
        if self.selected_rows is None:
            self.selected_rows = []
        if self.selected_columns is None:
            self.selected_columns = []

@dataclass
class TableRowData:
    """表格行数据结构"""
    id: str                           # 行唯一标识
    main_data: Dict[str, Any]        # 主行数据
    detail_data: Dict[str, Any]      # 详情数据
    expand_state: ExpandState = ExpandState.COLLAPSED
    expand_height: int = 0           # 展开高度
    animation_progress: float = 0.0  # 动画进度 (0.0-1.0)
    is_visible: bool = True          # 是否可见
    confidence_score: float = 1.0    # 数据置信度

class TableAnimationManager(QObject):
    """
    表格动画管理器
    
    负责管理表格展开/折叠的动画效果，实现60fps流畅动画。
    """
    
    # 动画完成信号
    animation_finished = pyqtSignal(str, ExpandState)  # row_id, final_state
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 动画配置
        self.animation_duration = 250  # 动画持续时间(ms)
        self.easing_curve = QEasingCurve.OutCubic
        
        # 活跃动画管理
        self.active_animations: Dict[str, QPropertyAnimation] = {}
        
        # 性能计数器
        self.animation_count = 0
        self.performance_stats = {
            'total_animations': 0,
            'average_duration': 0.0,
            'fps_target': 60.0
        }
        
        # 创建虚拟目标对象用于动画 (修复动画目标缺失警告)
        self.animation_target = QObject()
        self.animation_target.setProperty("animationValue", 0)
    
    def start_expand_animation(self, row_id: str, target_height: int, 
                             progress_callback=None) -> bool:
        """
        开始展开动画
        
        Args:
            row_id: 行标识
            target_height: 目标高度
            progress_callback: 进度回调函数
            
        Returns:
            bool: 动画启动是否成功
        """
        try:
            # 停止现有动画
            self.stop_animation(row_id)
            
            # 创建动画对象并设置目标
            animation = QPropertyAnimation(self.animation_target, b"animationValue")
            animation.setDuration(self.animation_duration)
            animation.setEasingCurve(self.easing_curve)
            animation.setStartValue(0)
            animation.setEndValue(target_height)
            
            # 连接回调
            if progress_callback:
                animation.valueChanged.connect(
                    lambda value: progress_callback(row_id, value / target_height if target_height > 0 else 0)
                )
            
            # 动画完成处理
            animation.finished.connect(
                lambda: self._on_animation_finished(row_id, ExpandState.EXPANDED)
            )
            
            # 存储并启动动画
            self.active_animations[row_id] = animation
            animation.start()
            
            self.performance_stats['total_animations'] += 1
            self.logger.debug(f"开始展开动画: {row_id}, 目标高度: {target_height}px")
            
            return True
            
        except Exception as e:
            self.logger.error(f"展开动画启动失败: {row_id}, 错误: {e}")
            return False
    
    def start_collapse_animation(self, row_id: str, current_height: int,
                               progress_callback=None) -> bool:
        """
        开始折叠动画
        
        Args:
            row_id: 行标识
            current_height: 当前高度
            progress_callback: 进度回调函数
            
        Returns:
            bool: 动画启动是否成功
        """
        try:
            # 停止现有动画
            self.stop_animation(row_id)
            
            # 创建动画对象并设置目标
            animation = QPropertyAnimation(self.animation_target, b"animationValue")
            animation.setDuration(self.animation_duration)
            animation.setEasingCurve(self.easing_curve)
            animation.setStartValue(current_height)
            animation.setEndValue(0)
            
            # 连接回调
            if progress_callback:
                animation.valueChanged.connect(
                    lambda value: progress_callback(row_id, 1.0 - (value / current_height if current_height > 0 else 0))
                )
            
            # 动画完成处理
            animation.finished.connect(
                lambda: self._on_animation_finished(row_id, ExpandState.COLLAPSED)
            )
            
            # 存储并启动动画
            self.active_animations[row_id] = animation
            animation.start()
            
            self.performance_stats['total_animations'] += 1
            self.logger.debug(f"开始折叠动画: {row_id}, 当前高度: {current_height}px")
            
            return True
            
        except Exception as e:
            self.logger.error(f"折叠动画启动失败: {row_id}, 错误: {e}")
            return False
    
    def stop_animation(self, row_id: str) -> bool:
        """
        停止指定行的动画
        
        Args:
            row_id: 行标识
            
        Returns:
            bool: 是否成功停止
        """
        if row_id in self.active_animations:
            animation = self.active_animations[row_id]
            animation.stop()
            del self.active_animations[row_id]
            return True
        return False
    
    def is_animating(self, row_id: str) -> bool:
        """检查指定行是否正在动画"""
        return row_id in self.active_animations
    
    def _on_animation_finished(self, row_id: str, final_state: ExpandState):
        """动画完成处理"""
        if row_id in self.active_animations:
            del self.active_animations[row_id]
        
        self.animation_finished.emit(row_id, final_state)
        self.logger.debug(f"动画完成: {row_id}, 最终状态: {final_state.value}")

class ExpandableTableModel(QAbstractTableModel):
    """
    可展开表格数据模型
    
    支持虚拟化数据管理和动态行高计算。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 数据存储
        self.row_data: List[TableRowData] = []
        self.headers: List[str] = []
        self.original_headers: List[str] = []  # 🔧 [修复标识] 保存原始英文字段名
        
        # 🔧 [修复标识] 字段映射：中文表头 -> 英文字段名
        self.header_to_field_mapping: Dict[str, str] = {}
        
        # 虚拟化配置
        self.visible_rows: List[int] = []  # 可见行索引
        self.viewport_start = 0
        self.viewport_end = 0
        
        # 性能优化
        self.cache_size = 100
        self.data_cache: Dict[Tuple[int, int], Any] = {}
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str], original_headers: List[str] = None):
        """
        设置表格数据
        
        Args:
            data: 行数据列表
            headers: 表头列表（可能是中文显示名）
            original_headers: 原始英文字段名列表
        """
        self.beginResetModel()
        
        try:
            # 🔧 [新架构] 遵循现有数据管理机制，简洁清理
            self.headers.clear()
            self.row_data.clear()
            
            self.headers = headers.copy()
            
            # 🔧 [修复标识] 建立字段映射关系
            if original_headers and len(original_headers) == len(headers):
                self.original_headers = original_headers.copy()
                # 建立中文表头到英文字段名的映射
                self.header_to_field_mapping = dict(zip(headers, original_headers))
                self.logger.debug(f"🔧 [修复标识] 建立字段映射: {len(self.header_to_field_mapping)} 个字段")
            else:
                # 如果没有提供原始字段名，假设表头就是字段名
                self.original_headers = headers.copy()
                self.header_to_field_mapping = {h: h for h in headers}
                self.logger.debug("🔧 [修复标识] 使用表头作为字段名")
            
            # 保持数据的原始顺序，严格按照输入数据的顺序创建行数据
            for i, row in enumerate(data):
                row_data = TableRowData(
                    id=f"row_{i}",
                    main_data=row.copy(),
                    detail_data=self._generate_detail_data(row),
                    confidence_score=row.get('confidence', 1.0)
                )
                self.row_data.append(row_data)
                
                # 记录行数据的顺序以供调试
                if i < 5:  # 只记录前5行
                    emp_id = self._get_employee_id_from_row(row, i)
                    self.logger.info(f"第{i}行数据工号: {emp_id}")
            
            # 初始化可见行
            self._update_visible_rows()
            
            self.logger.info(f"表格数据已设置: {len(self.row_data)} 行, {len(headers)} 列")
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
        finally:
            self.endResetModel()
    
    def _generate_detail_data(self, main_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据主数据生成详情数据"""
        detail = {
            '创建时间': '2025-06-19 16:00:00',
            '修改时间': '2025-06-19 16:15:30',
            '操作人': '系统管理员',
            '备注': '自动生成的详情数据',
            '数据来源': '工资异动检测系统',
            '审核状态': '待审核'
        }
        return detail
    
    def _update_visible_rows(self):
        """更新可见行索引"""
        self.visible_rows = list(range(len(self.row_data)))
        
        self.logger.info(f"🚨 [UI数据修复] _update_visible_rows调用，数据行数: {len(self.row_data)}")
        
        # 记录可见行的顺序以验证排序是否保持
        if len(self.row_data) > 0:
            # 检查第一个行数据是否有工号字段 - 修复：正确访问TableRowData的main_data属性
            first_row = self.row_data[0] if self.row_data else None
            if first_row and first_row.main_data and any(field in first_row.main_data for field in ['工号', 'employee_id', '人员代码']):
                visible_ids = []
                for i in range(min(5, len(self.row_data))):
                    row_data = self.row_data[i]
                    emp_id = self._get_employee_id_from_row(row_data.main_data, i)
                    salary = row_data.main_data.get('2025年薪级工资') or row_data.main_data.get('grade_salary_2025') or 'N/A'
                    visible_ids.append(emp_id)
                    self.logger.info(f"🚨 [UI数据修复] row_data[{i}]: 工号={emp_id}, 薪资={salary}")
                self.logger.info(f"可见行数据顺序: {visible_ids}")
            else:
                self.logger.warning(f"🚨 [UI数据修复] 无法找到工号字段，first_row存在: {first_row is not None}")
                if first_row and first_row.main_data:
                    self.logger.info(f"🚨 [UI数据修复] main_data字段: {list(first_row.main_data.keys())}")
    
    def rowCount(self, parent=QModelIndex()) -> int:
        """返回行数"""
        return len(self.row_data)
    
    def columnCount(self, parent=QModelIndex()) -> int:
        """返回列数"""
        return len(self.headers)
    
    def data(self, index: QModelIndex, role: int = Qt.DisplayRole) -> Any:
        """获取数据"""
        if not index.isValid():
            return QVariant()
        
        row, col = index.row(), index.column()
        
        if row >= len(self.row_data) or col >= len(self.headers):
            return QVariant()
        
        row_data = self.row_data[row]
        header = self.headers[col]
        
        if role == Qt.DisplayRole:
            # 🔧 [修复标识] 使用字段映射获取正确的数据
            field_name = self.header_to_field_mapping.get(header, header)
            value = row_data.main_data.get(field_name, "")
            
            # 调试日志（只在值为空时记录）
            if not value and field_name in row_data.main_data:
                value = row_data.main_data[field_name]
            elif not value:
                # 尝试其他可能的键名
                for key in row_data.main_data.keys():
                    if key.lower() == field_name.lower() or key.lower() == header.lower():
                        value = row_data.main_data[key]
                        break
                        
            return value
        elif role == Qt.BackgroundRole:
            # 根据数据置信度设置背景色
            alpha = int(255 * row_data.confidence_score)
            return QColor(255, 255, 255, alpha)
        
        return QVariant()
    
    def headerData(self, section: int, orientation: Qt.Orientation, 
                   role: int = Qt.DisplayRole) -> Any:
        """获取表头数据"""
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal and section < len(self.headers):
                return self.headers[section]
            elif orientation == Qt.Vertical:
                return str(section + 1)
        
        return QVariant()
    
    def get_row_data(self, row: int) -> Optional[TableRowData]:
        """获取指定行的完整数据"""
        if 0 <= row < len(self.row_data):
            return self.row_data[row]
        return None
    
    def update_expand_state(self, row: int, state: ExpandState, 
                          height: int = 0, progress: float = 0.0):
        """更新行展开状态"""
        try:
            if 0 <= row < len(self.row_data):
                row_data = self.row_data[row]
                row_data.expand_state = state
                row_data.expand_height = height
                row_data.animation_progress = progress
                
                # 发射数据变化信号
                start_index = self.index(row, 0)
                end_index = self.index(row, self.columnCount() - 1)
                self.dataChanged.emit(start_index, end_index)
                
        except Exception as e:
            self.logger.error(f"更新展开状态失败: {e}")
    
    def move_row_data(self, from_row: int, to_row: int) -> bool:
        """移动行数据"""
        try:
            if (from_row < 0 or to_row < 0 or 
                from_row >= len(self.row_data) or 
                to_row >= len(self.row_data) or
                from_row == to_row):
                return False
            
            # 开始移动操作
            if to_row > from_row:
                self.beginMoveRows(QModelIndex(), from_row, from_row, QModelIndex(), to_row + 1)
            else:
                self.beginMoveRows(QModelIndex(), from_row, from_row, QModelIndex(), to_row)
            
            # 移动数据
            row_data = self.row_data.pop(from_row)
            actual_target = to_row if to_row < from_row else to_row - 1
            self.row_data.insert(actual_target, row_data)
            
            # 完成移动操作
            self.endMoveRows()
            
            self.logger.info(f"行数据移动成功: {from_row} -> {actual_target}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动行数据失败: {e}")
            return False
    
    def _get_employee_id_from_row(self, row_data: dict, index: int = 0) -> str:
        """
        从行数据中获取员工ID，支持多种字段名
        
        Args:
            row_data: 行数据字典
            index: 行索引，用于生成默认值
            
        Returns:
            员工ID字符串
        """
        try:
            # 支持的员工ID字段名（按优先级排序）
            id_fields = ['工号', 'employee_id', '人员代码']
            
            for field in id_fields:
                if field in row_data and row_data[field]:
                    return str(row_data[field])
            
            # 如果都没找到，返回默认值
            return f"未知{index}"
            
        except Exception as e:
            self.logger.error(f"获取员工ID失败: {e}")
            return f"未知{index}"

class CellEditState(Enum):
    """单元格编辑状态枚举"""
    NORMAL = "normal"        # 正常显示状态
    EDITING = "editing"      # 编辑状态
    VALIDATING = "validating"  # 验证状态
    ERROR = "error"          # 错误状态

@dataclass
class CellEditData:
    """单元格编辑数据"""
    row: int
    column: int
    original_value: Any
    current_value: Any
    edit_state: CellEditState = CellEditState.NORMAL
    validator: Optional[QValidator] = None
    error_message: str = ""
    edit_widget: Optional[QWidget] = None

class TableCellEditor(QStyledItemDelegate):
    """
    表格单元格编辑器
    
    提供不同数据类型的编辑控件和验证逻辑
    """
    
    # 编辑完成信号
    edit_finished = pyqtSignal(int, int, object, bool)  # row, column, value, is_valid
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 编辑器类型映射
        self.editor_types = {
            'string': QLineEdit,
            'int': QSpinBox,
            'float': QDoubleSpinBox,
            'date': QDateEdit,
            'bool': QCheckBox,
            'text': QTextEdit,
            'choice': QComboBox
        }
        
        # 当前编辑状态
        self.current_edit: Optional[CellEditData] = None
    
    def createEditor(self, parent: QWidget, option, index: QModelIndex) -> QWidget:
        """创建编辑器控件"""
        try:
            # 获取单元格数据类型
            data_type = self._detect_data_type(index)
            
            # 创建对应的编辑器
            editor = self._create_typed_editor(data_type, parent)
            
            if editor:
                # 设置编辑器样式
                editor.setStyleSheet("""
                    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox {
                        border: 2px solid #2196F3;
                        border-radius: 4px;
                        padding: 4px;
                        background-color: white;
                        font-size: 12px;
                    }
                    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, 
                    QDateEdit:focus, QComboBox:focus {
                        border-color: #1976D2;
                        /* 移除了 box-shadow 属性，PyQt5不支持 */
                    }
                """)
                
                # 绑定事件
                self._setup_editor_events(editor, index)
                
                self.logger.debug(f"创建编辑器: 行{index.row()}, 列{index.column()}, 类型{data_type}")
            
            return editor
            
        except Exception as e:
            self.logger.error(f"创建编辑器失败: {e}")
            return super().createEditor(parent, option, index)
    
    def _detect_data_type(self, index: QModelIndex) -> str:
        """检测数据类型"""
        try:
            data = index.data(Qt.DisplayRole)
            
            if isinstance(data, bool):
                return 'bool'
            elif isinstance(data, int):
                return 'int'
            elif isinstance(data, float):
                return 'float'
            elif isinstance(data, (QDate, QDateTime)):
                return 'date'
            elif isinstance(data, str):
                if len(data) > 100:
                    return 'text'
                else:
                    return 'string'
            else:
                return 'string'
                
        except Exception as e:
            self.logger.error(f"数据类型检测失败: {e}")
            return 'string'
    
    def _create_typed_editor(self, data_type: str, parent: QWidget) -> QWidget:
        """创建特定类型的编辑器"""
        try:
            if data_type == 'string':
                editor = QLineEdit(parent)
                editor.setMaxLength(255)
                return editor
                
            elif data_type == 'int':
                editor = QSpinBox(parent)
                editor.setRange(-999999, 999999)
                return editor
                
            elif data_type == 'float':
                editor = QDoubleSpinBox(parent)
                editor.setRange(-999999.99, 999999.99)
                editor.setDecimals(2)
                return editor
                
            elif data_type == 'date':
                editor = QDateEdit(parent)
                editor.setDate(QDate.currentDate())
                editor.setCalendarPopup(True)
                return editor
                
            elif data_type == 'bool':
                editor = QCheckBox(parent)
                return editor
                
            elif data_type == 'text':
                editor = QTextEdit(parent)
                editor.setMaximumHeight(80)
                return editor
                
            elif data_type == 'choice':
                editor = QComboBox(parent)
                editor.setEditable(True)
                return editor
                
            else:
                return QLineEdit(parent)
                
        except Exception as e:
            self.logger.error(f"创建编辑器失败: {data_type}, {e}")
            return QLineEdit(parent)
    
    def _setup_editor_events(self, editor: QWidget, index: QModelIndex):
        """设置编辑器事件"""
        try:
            # 通用键盘事件
            if hasattr(editor, 'keyPressEvent'):
                original_key_event = editor.keyPressEvent
                
                def enhanced_key_event(event: QKeyEvent):
                    if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                        self._commit_edit(editor, index, True)
                    elif event.key() == Qt.Key_Escape:
                        self._commit_edit(editor, index, False)
                    else:
                        original_key_event(event)
                
                editor.keyPressEvent = enhanced_key_event
            
            # 特定类型的事件
            if isinstance(editor, QLineEdit):
                editor.editingFinished.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            elif isinstance(editor, (QSpinBox, QDoubleSpinBox)):
                editor.editingFinished.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            elif isinstance(editor, QComboBox):
                editor.currentTextChanged.connect(
                    lambda: self._validate_edit(editor, index)
                )
            elif isinstance(editor, QCheckBox):
                editor.stateChanged.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            
        except Exception as e:
            self.logger.error(f"设置编辑器事件失败: {e}")
    
    def _validate_edit(self, editor: QWidget, index: QModelIndex) -> bool:
        """验证编辑内容"""
        try:
            # 获取编辑后的值
            value = self._get_editor_value(editor)
            
            # 基础验证
            if value is None:
                return False
            
            # 数据类型验证
            data_type = self._detect_data_type(index)
            if not self._validate_data_type(value, data_type):
                return False
            
            # 自定义验证规则
            if not self._custom_validation(value, index):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"编辑验证失败: {e}")
            return False
    
    def _commit_edit(self, editor: QWidget, index: QModelIndex, save: bool):
        """提交或取消编辑"""
        try:
            if save:
                # 验证并保存
                if self._validate_edit(editor, index):
                    value = self._get_editor_value(editor)
                    self.edit_finished.emit(index.row(), index.column(), value, True)
                else:
                    self.edit_finished.emit(index.row(), index.column(), None, False)
            else:
                # 取消编辑
                self.edit_finished.emit(index.row(), index.column(), None, False)
            
        except Exception as e:
            self.logger.error(f"提交编辑失败: {e}")
    
    def _get_editor_value(self, editor: QWidget) -> Any:
        """获取编辑器的值"""
        try:
            if isinstance(editor, QLineEdit):
                return editor.text()
            elif isinstance(editor, QSpinBox):
                return editor.value()
            elif isinstance(editor, QDoubleSpinBox):
                return editor.value()
            elif isinstance(editor, QDateEdit):
                return editor.date().toPyDate()
            elif isinstance(editor, QCheckBox):
                return editor.isChecked()
            elif isinstance(editor, QTextEdit):
                return editor.toPlainText()
            elif isinstance(editor, QComboBox):
                return editor.currentText()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"获取编辑器值失败: {e}")
            return None
    
    def _validate_data_type(self, value: Any, data_type: str) -> bool:
        """验证数据类型"""
        try:
            if data_type == 'int':
                int(value)
            elif data_type == 'float':
                float(value)
            elif data_type == 'string':
                str(value)
            return True
        except ValueError:
            return False
    
    def _custom_validation(self, value: Any, index: QModelIndex) -> bool:
        """自定义验证规则"""
        # 可以在这里添加业务逻辑验证
        return True

class TableContextMenu(QMenu):
    """
    表格右键菜单系统
    
    提供上下文感知的智能菜单，根据点击位置、选择状态、编辑状态等
    动态生成合适的菜单项。
    """
    
    # 菜单动作信号
    action_triggered = pyqtSignal(str, object)  # action_name, context_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 菜单配置
        self.menu_configs = self._create_menu_configs()
        
        # 性能统计
        self.menu_stats = {
            'total_shows': 0,
            'action_counts': {},
            'average_show_time': 0.0
        }
        
    def _create_menu_configs(self) -> Dict[ContextMenuType, List[MenuItemConfig]]:
        """创建菜单配置"""
        configs = {
            # 单行菜单
            ContextMenuType.SINGLE_ROW: [
                MenuItemConfig(MenuAction.EDIT_CELL, "编辑单元格", "edit", "F2"),
                MenuItemConfig(MenuAction.COPY, "复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.CUT, "剪切", "cut", "Ctrl+X"),
                MenuItemConfig(MenuAction.DELETE, "删除行", "delete", "Delete", separator_after=True),
                MenuItemConfig(MenuAction.EXPAND_ROW, "展开行", "expand", "+"),
                MenuItemConfig(MenuAction.COLLAPSE_ROW, "折叠行", "collapse", "-", separator_after=True),
                MenuItemConfig(MenuAction.INSERT_ROW, "插入新行", "add", "Ctrl+I"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
            
            # 多行菜单
            ContextMenuType.MULTI_ROW: [
                MenuItemConfig(MenuAction.BATCH_EDIT, "批量编辑", "batch_edit", "Ctrl+B"),
                MenuItemConfig(MenuAction.BATCH_COPY, "批量复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.BATCH_DELETE, "批量删除", "delete", "Delete", separator_after=True),
                MenuItemConfig(MenuAction.BATCH_EXPAND, "批量展开", "expand_all", "Ctrl++"),
                MenuItemConfig(MenuAction.BATCH_COLLAPSE, "批量折叠", "collapse_all", "Ctrl+-", separator_after=True),
                MenuItemConfig(MenuAction.BATCH_EXPORT, "批量导出", "export", "Ctrl+E"),
                MenuItemConfig(MenuAction.CLEAR_SELECTION, "清除选择", "clear", "Esc"),
            ],
            
            # 单元格编辑菜单
            ContextMenuType.CELL_EDIT: [
                MenuItemConfig(MenuAction.COPY, "复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.PASTE, "粘贴", "paste", "Ctrl+V"),
                MenuItemConfig(MenuAction.CUT, "剪切", "cut", "Ctrl+X", separator_after=True),
                MenuItemConfig(MenuAction.CANCEL_EDIT, "取消编辑", "cancel", "Esc"),
            ],
            
            # 表头菜单
            ContextMenuType.HEADER: [
                MenuItemConfig(MenuAction.SORT_COLUMN, "排序列", "sort", ""),
                MenuItemConfig(MenuAction.HIDE_COLUMN, "隐藏列", "hide", ""),
                MenuItemConfig(MenuAction.RESIZE_COLUMNS, "调整列宽", "resize", "", separator_after=True),
                MenuItemConfig(MenuAction.SELECT_ALL, "全选", "select_all", "Ctrl+A"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
            
            # 空白区域菜单
            ContextMenuType.EMPTY_AREA: [
                MenuItemConfig(MenuAction.SELECT_ALL, "全选", "select_all", "Ctrl+A"),
                MenuItemConfig(MenuAction.CLEAR_SELECTION, "清除选择", "clear", "Esc", separator_after=True),
                MenuItemConfig(MenuAction.EXPAND_ALL, "展开所有", "expand_all", "Ctrl+Shift++"),
                MenuItemConfig(MenuAction.COLLAPSE_ALL, "折叠所有", "collapse_all", "Ctrl+Shift+-", separator_after=True),
                MenuItemConfig(MenuAction.INSERT_ROW, "插入新行", "add", "Ctrl+I"),
                MenuItemConfig(MenuAction.REFRESH_DATA, "刷新数据", "refresh", "F5"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
        }
        
        return configs
    
    def show_context_menu(self, context: ContextMenuContext, position: QPoint):
        """显示上下文菜单"""
        try:
            start_time = time.time()
            
            # 清空当前菜单
            self.clear()
            
            # 根据上下文构建菜单
            menu_items = self._build_menu_items(context)
            
            if not menu_items:
                self.logger.debug("没有可用的菜单项")
                return
            
            # 添加菜单项到菜单
            self._populate_menu(menu_items, context)
            
            # 显示菜单
            if not self.isEmpty():
                self.exec_(position)
                
                # 统计性能
                show_time = (time.time() - start_time) * 1000
                self.menu_stats['total_shows'] += 1
                self.menu_stats['average_show_time'] = (
                    (self.menu_stats['average_show_time'] * (self.menu_stats['total_shows'] - 1) + show_time) /
                    self.menu_stats['total_shows']
                )
                
                self.logger.debug(f"显示右键菜单: {context.menu_type.value}, 用时: {show_time:.1f}ms")
            
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def _build_menu_items(self, context: ContextMenuContext) -> List[MenuItemConfig]:
        """根据上下文构建菜单项"""
        try:
            # 获取基础菜单配置
            base_configs = self.menu_configs.get(context.menu_type, [])
            
            # 复制配置以便修改
            menu_items = []
            
            for config in base_configs:
                # 创建配置副本
                item_config = MenuItemConfig(
                    action=config.action,
                    text=config.text,
                    icon=config.icon,
                    shortcut=config.shortcut,
                    enabled=config.enabled,
                    visible=config.visible,
                    separator_after=config.separator_after,
                    tooltip=config.tooltip,
                    callback=config.callback
                )
                
                # 根据上下文调整菜单项
                self._adjust_menu_item(item_config, context)
                
                if item_config.visible:
                    menu_items.append(item_config)
            
            return menu_items
            
        except Exception as e:
            self.logger.error(f"构建菜单项失败: {e}")
            return []
    
    def _adjust_menu_item(self, item_config: MenuItemConfig, context: ContextMenuContext):
        """根据上下文调整菜单项"""
        try:
            action = item_config.action
            
            # 展开/折叠状态相关调整
            if action == MenuAction.EXPAND_ROW:
                item_config.visible = not context.row_expanded
            elif action == MenuAction.COLLAPSE_ROW:
                item_config.visible = context.row_expanded
            
            # 编辑状态相关调整
            elif action == MenuAction.EDIT_CELL:
                item_config.enabled = not context.is_editing and context.clicked_column > 0
            elif action == MenuAction.CANCEL_EDIT:
                item_config.visible = context.is_editing
            
            # 多选状态相关调整
            elif action in [MenuAction.BATCH_EDIT, MenuAction.BATCH_DELETE, 
                          MenuAction.BATCH_COPY, MenuAction.BATCH_EXPORT,
                          MenuAction.BATCH_EXPAND, MenuAction.BATCH_COLLAPSE]:
                item_config.enabled = len(context.selected_rows) > 1
            
            # 选择状态相关调整
            elif action == MenuAction.CLEAR_SELECTION:
                item_config.enabled = len(context.selected_rows) > 0
            
            # 动态文本调整
            if action == MenuAction.BATCH_DELETE and len(context.selected_rows) > 1:
                item_config.text = f"删除 {len(context.selected_rows)} 行"
            elif action == MenuAction.BATCH_COPY and len(context.selected_rows) > 1:
                item_config.text = f"复制 {len(context.selected_rows)} 行"
            elif action == MenuAction.BATCH_EXPORT and len(context.selected_rows) > 1:
                item_config.text = f"导出 {len(context.selected_rows)} 行"
            
        except Exception as e:
            self.logger.error(f"调整菜单项失败: {action}, 错误: {e}")
    
    def _populate_menu(self, menu_items: List[MenuItemConfig], context: ContextMenuContext):
        """填充菜单内容"""
        try:
            for item_config in menu_items:
                if not item_config.visible:
                    continue
                
                # 创建动作
                action = QAction(item_config.text, self)
                action.setEnabled(item_config.enabled)
                
                # 设置快捷键
                if item_config.shortcut:
                    action.setShortcut(item_config.shortcut)
                
                # 设置工具提示
                if item_config.tooltip:
                    action.setToolTip(item_config.tooltip)
                
                # 连接信号
                action.triggered.connect(
                    lambda checked, act=item_config.action: self._on_action_triggered(act, context)
                )
                
                # 添加到菜单
                self.addAction(action)
                
                # 添加分隔符
                if item_config.separator_after:
                    self.addSeparator()
            
        except Exception as e:
            self.logger.error(f"填充菜单失败: {e}")
    
    def _on_action_triggered(self, action: MenuAction, context: ContextMenuContext):
        """处理菜单动作触发"""
        try:
            # 统计动作使用
            action_name = action.value
            if action_name not in self.menu_stats['action_counts']:
                self.menu_stats['action_counts'][action_name] = 0
            self.menu_stats['action_counts'][action_name] += 1
            
            # 发出信号
            self.action_triggered.emit(action_name, context)
            
            self.logger.debug(f"菜单动作触发: {action_name}")
            
        except Exception as e:
            self.logger.error(f"处理菜单动作失败: {action}, 错误: {e}")
    
    def get_menu_stats(self) -> Dict[str, Any]:
        """获取菜单统计信息"""
        return {
            'total_shows': self.menu_stats['total_shows'],
            'action_counts': self.menu_stats['action_counts'].copy(),
            'average_show_time': self.menu_stats['average_show_time'],
            'most_used_action': max(
                self.menu_stats['action_counts'].items(),
                key=lambda x: x[1],
                default=("none", 0)
            )[0] if self.menu_stats['action_counts'] else "none"
        }

class KeyboardShortcutManager(QObject):
    """
    键盘快捷键管理器
    
    管理表格组件的所有快捷键，提供：
    - 快捷键注册和管理
    - 快捷键冲突检测
    - 快捷键帮助系统
    - 快捷键统计和分析
    """
    
    # 快捷键触发信号
    shortcut_triggered = pyqtSignal(str, object)  # shortcut_name, context_data
    help_requested = pyqtSignal(list)             # shortcuts_list
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 快捷键映射
        self.shortcuts = {}  # QShortcut对象映射
        self.shortcut_configs = self._create_shortcut_configs()
        
        # 快捷键统计
        self.usage_stats = {}
        self.conflict_log = []
        
        # 初始化快捷键
        self._register_shortcuts()
    
    def _create_shortcut_configs(self) -> Dict[str, Dict[str, Any]]:
        """创建快捷键配置"""
        configs = {
            # 基础编辑快捷键
            'edit_cell': {
                'key': 'F2',
                'description': '编辑当前单元格',
                'category': '编辑',
                'action': 'edit_cell',
                'enabled': True
            },
            'save_edit': {
                'key': 'Ctrl+Return',
                'description': '保存编辑并移到下一行',
                'category': '编辑',
                'action': 'save_edit',
                'enabled': True
            },
            'cancel_edit': {
                'key': 'Escape',
                'description': '取消当前编辑',
                'category': '编辑',
                'action': 'cancel_edit',
                'enabled': True
            },
            
            # 选择操作快捷键
            'select_all': {
                'key': 'Ctrl+A',
                'description': '全选所有行',
                'category': '选择',
                'action': 'select_all',
                'enabled': True
            },
            'clear_selection': {
                'key': 'Ctrl+D',
                'description': '清除所有选择',
                'category': '选择',
                'action': 'clear_selection',
                'enabled': True
            },
            'invert_selection': {
                'key': 'Ctrl+I',
                'description': '反选当前选择',
                'category': '选择',
                'action': 'invert_selection',
                'enabled': True
            },
            
            # 复制粘贴快捷键
            'copy_data': {
                'key': 'Ctrl+C',
                'description': '复制选中的数据',
                'category': '剪贴板',
                'action': 'copy_data',
                'enabled': True
            },
            'paste_data': {
                'key': 'Ctrl+V',
                'description': '粘贴数据',
                'category': '剪贴板',
                'action': 'paste_data',
                'enabled': True
            },
            'cut_data': {
                'key': 'Ctrl+X',
                'description': '剪切选中的数据',
                'category': '剪贴板',
                'action': 'cut_data',
                'enabled': True
            },
            
            # 展开折叠快捷键
            'expand_all': {
                'key': 'Ctrl+Plus',
                'description': '展开所有行',
                'category': '展开',
                'action': 'expand_all',
                'enabled': True
            },
            'collapse_all': {
                'key': 'Ctrl+Minus',
                'description': '折叠所有行',
                'category': '展开',
                'action': 'collapse_all',
                'enabled': True
            },
            'toggle_current_row': {
                'key': 'Space',
                'description': '切换当前行展开状态',
                'category': '展开',
                'action': 'toggle_current_row',
                'enabled': True
            },
            
            # 批量操作快捷键
            'batch_delete': {
                'key': 'Delete',
                'description': '删除选中的行',
                'category': '批量操作',
                'action': 'batch_delete',
                'enabled': True
            },
            'batch_export': {
                'key': 'Ctrl+E',
                'description': '导出选中的数据',
                'category': '批量操作',
                'action': 'batch_export',
                'enabled': True
            },
            
            # 导航快捷键
            'goto_first_row': {
                'key': 'Ctrl+Home',
                'description': '跳转到第一行',
                'category': '导航',
                'action': 'goto_first_row',
                'enabled': True
            },
            'goto_last_row': {
                'key': 'Ctrl+End',
                'description': '跳转到最后一行',
                'category': '导航',
                'action': 'goto_last_row',
                'enabled': True
            },
            
            # 系统快捷键
            'refresh_data': {
                'key': 'F5',
                'description': '刷新表格数据',
                'category': '系统',
                'action': 'refresh_data',
                'enabled': True
            },
            'show_help': {
                'key': 'F1',
                'description': '显示快捷键帮助',
                'category': '系统',
                'action': 'show_help',
                'enabled': True
            }
        }
        
        return configs
    
    def _register_shortcuts(self):
        """注册所有快捷键"""
        try:
            parent_widget = self.parent()
            if not parent_widget:
                self.logger.error("快捷键管理器没有父组件，无法注册快捷键")
                return
            
            for name, config in self.shortcut_configs.items():
                if config['enabled']:
                    try:
                        # 创建快捷键对象
                        shortcut = QShortcut(config['key'], parent_widget)
                        shortcut.activated.connect(
                            lambda checked=False, n=name: self._on_shortcut_activated(n)
                        )
                        
                        # 存储快捷键对象
                        self.shortcuts[name] = shortcut
                        
                        # 初始化统计
                        self.usage_stats[name] = 0
                        
                    except Exception as e:
                        self.logger.error(f"注册快捷键失败: {name} ({config['key']}), 错误: {e}")
                        self.conflict_log.append({
                            'shortcut': name,
                            'key': config['key'],
                            'error': str(e),
                            'timestamp': time.time()
                        })
            
            self.logger.info(f"快捷键注册完成: {len(self.shortcuts)}/{len(self.shortcut_configs)} 个")
            
        except Exception as e:
            self.logger.error(f"批量注册快捷键失败: {e}")
    
    def _on_shortcut_activated(self, shortcut_name: str):
        """处理快捷键激活"""
        try:
            # 更新使用统计
            if shortcut_name in self.usage_stats:
                self.usage_stats[shortcut_name] += 1
            
            # 获取快捷键配置
            config = self.shortcut_configs.get(shortcut_name)
            if not config:
                self.logger.error(f"未找到快捷键配置: {shortcut_name}")
                return
            
            # 创建上下文数据
            context_data = {
                'shortcut_name': shortcut_name,
                'key_sequence': config['key'],
                'description': config['description'],
                'category': config['category'],
                'action': config['action'],
                'timestamp': time.time()
            }
            
            # 发出信号
            self.shortcut_triggered.emit(shortcut_name, context_data)
            
            self.logger.debug(f"快捷键激活: {shortcut_name} ({config['key']})")
            
        except Exception as e:
            self.logger.error(f"处理快捷键激活失败: {shortcut_name}, 错误: {e}")
    
    def get_shortcuts_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """按分类获取快捷键列表"""
        try:
            categories = {}
            
            for name, config in self.shortcut_configs.items():
                category = config['category']
                if category not in categories:
                    categories[category] = []
                
                shortcut_info = {
                    'name': name,
                    'key': config['key'],
                    'description': config['description'],
                    'enabled': config['enabled'],
                    'usage_count': self.usage_stats.get(name, 0)
                }
                categories[category].append(shortcut_info)
            
            # 按使用频率排序
            for category in categories:
                categories[category].sort(key=lambda x: x['usage_count'], reverse=True)
            
            return categories
            
        except Exception as e:
            self.logger.error(f"获取快捷键分类失败: {e}")
            return {}
    
    def get_shortcut_help_text(self) -> str:
        """获取快捷键帮助文本"""
        try:
            categories = self.get_shortcuts_by_category()
            help_lines = ["快捷键帮助\n" + "="*50]
            
            for category, shortcuts in categories.items():
                help_lines.append(f"\n【{category}】")
                for shortcut in shortcuts:
                    if shortcut['enabled']:
                        key = shortcut['key'].ljust(15)
                        desc = shortcut['description']
                        usage = f"(使用{shortcut['usage_count']}次)" if shortcut['usage_count'] > 0 else ""
                        help_lines.append(f"  {key} - {desc} {usage}")
            
            help_lines.append(f"\n总计: {len([s for cat in categories.values() for s in cat if s['enabled']])} 个可用快捷键")
            
            return "\n".join(help_lines)
            
        except Exception as e:
            self.logger.error(f"生成快捷键帮助文本失败: {e}")
            return "快捷键帮助生成失败"
    
    def enable_shortcut(self, shortcut_name: str, enabled: bool = True):
        """启用或禁用指定快捷键"""
        try:
            if shortcut_name in self.shortcut_configs:
                self.shortcut_configs[shortcut_name]['enabled'] = enabled
                
                if shortcut_name in self.shortcuts:
                    shortcut = self.shortcuts[shortcut_name]
                    shortcut.setEnabled(enabled)
                
                self.logger.debug(f"快捷键状态更新: {shortcut_name} = {'启用' if enabled else '禁用'}")
                return True
            else:
                self.logger.error(f"未找到快捷键: {shortcut_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置快捷键状态失败: {shortcut_name}, 错误: {e}")
            return False
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取快捷键使用统计"""
        try:
            total_usage = sum(self.usage_stats.values())
            most_used = max(self.usage_stats.items(), key=lambda x: x[1]) if self.usage_stats else ("none", 0)
            
            return {
                'total_shortcuts': len(self.shortcut_configs),
                'enabled_shortcuts': len([c for c in self.shortcut_configs.values() if c['enabled']]),
                'registered_shortcuts': len(self.shortcuts),
                'total_usage_count': total_usage,
                'most_used_shortcut': most_used[0],
                'most_used_count': most_used[1],
                'usage_stats': self.usage_stats.copy(),
                'conflict_count': len(self.conflict_log),
                'average_usage': total_usage / len(self.usage_stats) if self.usage_stats else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"获取快捷键统计失败: {e}")
            return {}
    
    def detect_conflicts(self) -> List[Dict[str, Any]]:
        """检测快捷键冲突"""
        try:
            conflicts = []
            key_map = {}
            
            # 建立按键映射
            for name, config in self.shortcut_configs.items():
                if config['enabled']:
                    key = config['key']
                    if key in key_map:
                        conflicts.append({
                            'key': key,
                            'shortcuts': [key_map[key], name],
                            'descriptions': [
                                self.shortcut_configs[key_map[key]]['description'],
                                config['description']
                            ]
                        })
                    else:
                        key_map[key] = name
            
            return conflicts
            
        except Exception as e:
            self.logger.error(f"检测快捷键冲突失败: {e}")
            return []

class ColumnWidthManager(QObject):
    """
    列宽保存管理器
    
    负责保存和恢复表格列宽设置，包括：
    - 监听列宽调整事件
    - 保存列宽到配置文件
    - 恢复列宽设置
    """
    
    def __init__(self, parent_table: QTableWidget):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.parent_table = parent_table
        
        # 🔧 [列宽保存修复] 使用绝对路径避免工作目录问题
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent.parent.parent  # widgets -> prototype -> gui -> src -> project_root
        self.config_file = project_root / "state" / "column_widths.json"
        
        # 确保目录存在
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 🔧 [P2级性能优化] 初始化保存控制参数
        self._save_interval = 3.0  # 保存间隔（秒）
        self._last_save_time = {}  # 每个表的最后保存时间
        self._last_save_widths = {}  # 每个表的最后保存列宽
        
        # 连接列宽调整信号
        self.parent_table.horizontalHeader().sectionResized.connect(self._on_column_resized)
        
        self.logger.info(f"🔧 [列宽保存修复] 列宽管理器初始化完成")
        self.logger.info(f"🔧 [列宽保存修复] 配置文件绝对路径: {self.config_file.absolute()}")
        self.logger.debug(f"🔧 [P2级优化] 保存间隔设置为: {self._save_interval}秒")
        self.logger.info(f"🔧 [列宽保存修复] 配置文件存在: {self.config_file.exists()}")
        self.logger.info(f"🔧 [列宽保存修复] 父目录存在: {self.config_file.parent.exists()}")
        self.logger.info(f"🔧 [列宽保存修复] 当前工作目录: {Path.cwd()}")
    
    def _on_column_resized(self, logical_index: int, old_size: int, new_size: int):
        """
        处理列宽调整事件 - 真正的事件驱动机制
        """
        try:
            # 记录调整信息，但不立即保存
            self._pending_resize = {
                'logical_index': logical_index,
                'old_size': old_size,
                'new_size': new_size,
                'timestamp': time.time()
            }
            self.logger.debug(f"🔧 [列宽保存修复] 列 {logical_index} 宽度从 {old_size} 调整为 {new_size}")
            
            # 🔧 [列宽保存修复] 使用线程安全的延迟保存机制
            self._setup_save_timer_safe()
            
        except Exception as e:
            self.logger.error(f"列宽调整处理失败: {e}")
    
    def _setup_save_timer_safe(self):
        """🔧 [P0修复] 为ColumnWidthManager添加线程安全的保存定时器"""
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QTimer, QThread
            
            # 确保在主线程中创建Timer
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [P0修复] 非主线程，延迟到主线程创建列宽保存Timer")
                from src.utils.thread_safe_timer import safe_single_shot
                safe_single_shot(0, self._setup_save_timer_safe)
                return
            
            # 清理可能存在的旧Timer
            self._cleanup_save_timer()
            
            # 🔧 [P1-线程安全修复] 创建线程安全Timer
            self._save_timer = ThreadSafeTimer.create_timer(self, single_shot=True)
            if self._save_timer:
                ThreadSafeTimer.safe_start_timer(self._save_timer, 500, self._delayed_save)
                self._timer_destroyed = False
            else:
                self.logger.warning("⚠️ [线程安全] 列宽保存定时器创建失败，跳过延迟保存")
            
            self.logger.debug("🔧 [P0修复] 列宽保存定时器已在主线程中安全创建")
            
        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 创建列宽保存定时器失败: {e}")
    
    def _cleanup_save_timer(self):
        """🔧 [P0修复] 清理列宽保存定时器"""
        try:
            if hasattr(self, '_save_timer') and self._save_timer is not None:
                if not getattr(self, '_timer_destroyed', False):
                    self._save_timer.stop()
                    self._save_timer.deleteLater()
                self._save_timer = None
                self._timer_destroyed = True
                self.logger.debug("🔧 [P0修复] 列宽保存定时器已清理")
        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 清理列宽保存定时器失败: {e}")
    
    def _delayed_save(self):
        """
        延迟保存列宽 - 避免频繁保存
        """
        try:
            if hasattr(self, '_pending_resize') and self._pending_resize:
                self.logger.debug(f"🔧 [列宽保存修复] 延迟保存列宽配置")
                self._save_column_widths()
                self._pending_resize = None
        except Exception as e:
            self.logger.error(f"延迟保存列宽失败: {e}")
    
    def save_on_resize_end(self):
        """
        当用户停止调整列宽时调用（真正的事件驱动）
        """
        if hasattr(self, '_pending_resize') and self._pending_resize:
            self.logger.debug(f"🔧 [列宽保存修复] 检测到列宽调整完成，保存列宽配置")
            self._save_column_widths()
            self._pending_resize = None
        else:
            self.logger.debug(f"🔧 [列宽保存修复] 鼠标释放但无列宽调整，跳过保存")
    
    def _save_column_widths(self):
        """
        保存列宽设置
        """
        try:
            # 获取当前表名
            table_name = getattr(self.parent_table, 'current_table_name', 'default_table')
            
            # 🔧 [性能优化] 避免频繁保存，使用缓存机制
            import time
            current_time = time.time()
            
            # 初始化保存控制属性
            if not hasattr(self, '_last_save_time'):
                self._last_save_time = {}
                self._last_save_widths = {}
            
            # 🔧 [P2级性能优化] 检查保存频率限制（防抖机制）
            last_save = self._last_save_time.get(table_name, 0)
            save_interval = getattr(self, '_save_interval', 5.0)  # 可配置的保存间隔
            
            if current_time - last_save < save_interval:
                self.logger.debug(f"🔧 [性能优化] 列宽保存过于频繁，跳过本次保存: {table_name}, 间隔: {current_time - last_save:.1f}s")
                return
            
            # 读取现有配置
            widths_config = {}
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    widths_config = json.load(f)
            
            # 获取当前列宽
            header = self.parent_table.horizontalHeader()
            current_widths = []
            column_names = []
            
            for i in range(self.parent_table.columnCount()):
                current_widths.append(header.sectionSize(i))
                header_item = self.parent_table.horizontalHeaderItem(i)
                column_names.append(header_item.text() if header_item else f"列{i}")
            
            # 🔧 [P2级性能优化] 检查列宽是否实际发生变化
            last_widths = self._last_save_widths.get(table_name, [])
            if current_widths == last_widths:
                self.logger.debug(f"🔧 [性能优化] 列宽未发生变化，跳过保存: {table_name}")
                return
            
            # 保存配置
            widths_config[table_name] = {
                'column_widths': current_widths,
                'column_names': column_names,
                'timestamp': time.time()
            }
            
            # 写入文件
            self.logger.info(f"🔧 [列宽保存修复] 准备保存列宽到文件: {self.config_file.absolute()}")
            self.logger.info(f"🔧 [列宽保存修复] 表名: {table_name}, 列数: {len(current_widths)}")
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(widths_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"🔧 [列宽保存修复] 列宽设置保存成功！文件大小: {self.config_file.stat().st_size} 字节")
            self.logger.debug(f"🔧 [列宽保存修复] 保存的列宽: {current_widths}")
            
            # 🔧 [性能优化] 更新保存时间戳，避免频繁保存
            self._last_save_time[table_name] = current_time
            self._last_save_widths[table_name] = current_widths.copy()
            
        except Exception as e:
            self.logger.error(f"列宽保存失败: {e}")
    
    def restore_column_widths(self, table_name: str = None, force: bool = False):
        """
        恢复列宽设置

        Args:
            table_name: 表名，如果不提供则使用当前表名
            force: 是否强制恢复，忽略缓存机制
        """
        try:
            # 🔧 [P0-CRITICAL修复] 重复操作防护机制
            import time
            current_time = time.time()

            # 初始化防重复调用的时间戳记录
            if not hasattr(self, '_last_restore_time'):
                self._last_restore_time = {}

            # 🔧 [根本修复] 添加缓存和去重机制，避免频繁的磁盘IO
            if not table_name:
                table_name = getattr(self.parent_table, 'current_table_name', 'default_table')

            # 🔧 [P1-CRITICAL修复] 增强防重复机制，减少不必要的操作
            if not force and table_name in self._last_restore_time:
                time_diff = current_time - self._last_restore_time[table_name]
                if time_diff < 1.0:  # 🔧 [P1-CRITICAL修复] 增加到1秒，进一步减少重复操作
                    self.logger.debug(f"🔧 [P1-CRITICAL修复] 跳过重复的列宽恢复: {table_name} (距离上次{time_diff:.2f}s)")
                    return

            # 记录本次调用时间
            self._last_restore_time[table_name] = current_time

            # 🔧 [根本修复] 初始化缓存集合
            if not hasattr(self, '_restored_tables'):
                self._restored_tables = set()
            if not hasattr(self, '_no_config_tables'):
                self._no_config_tables = set()  # 负缓存：记录没有配置的表格

            # 🔧 [根本修复] 如果强制恢复，清除相关缓存
            if force:
                if table_name in self._restored_tables:
                    self._restored_tables.remove(table_name)
                    self.logger.debug(f"🔧 [根本修复] 强制恢复，已清除表格 {table_name} 的缓存标记")
                if table_name in self._no_config_tables:
                    self._no_config_tables.remove(table_name)
                    self.logger.debug(f"🔧 [根本修复] 强制恢复，已清除表格 {table_name} 的负缓存标记")

            # 🔧 [根本修复] 检查缓存（非强制模式下）
            if not force and table_name in self._restored_tables:
                self.logger.debug(f"🔧 [性能优化] 表格 {table_name} 的列宽已恢复过，跳过重复操作")
                return

            # 🔧 [根本修复] 负缓存检查（非强制模式下）
            if not force and table_name in self._no_config_tables:
                self.logger.debug(f"🔧 [性能优化] 表格 {table_name} 已知无列宽配置，跳过")
                return
            
            if not self.config_file.exists():
                self.logger.info(f"🔧 [列宽保存修复] 配置文件不存在: {self.config_file}，使用默认列宽")
                self._restored_tables.add(table_name)  # 标记为已处理
                return
            
            self.logger.debug(f"🔧 [列宽保存修复] 正在从 {self.config_file} 恢复列宽")
            
            # 读取配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                widths_config = json.load(f)
            
            if table_name not in widths_config:
                self.logger.debug(f"🔧 [P2级优化] 未找到表格 {table_name} 的列宽配置，添加到负缓存")
                self._no_config_tables.add(table_name)  # 🔧 [P2级性能优化] 添加到负缓存
                return
            
            saved_config = widths_config[table_name]
            saved_widths = saved_config.get('column_widths', [])
            saved_names = saved_config.get('column_names', [])
            
            if not saved_widths:
                return
            
            # 应用列宽（按列名匹配）
            header = self.parent_table.horizontalHeader()
            restored_count = 0
            
            for i in range(self.parent_table.columnCount()):
                header_item = self.parent_table.horizontalHeaderItem(i)
                if header_item:
                    column_name = header_item.text()
                    if column_name in saved_names:
                        # 找到匹配的列名
                        name_index = saved_names.index(column_name)
                        if name_index < len(saved_widths):
                            header.resizeSection(i, saved_widths[name_index])
                            restored_count += 1
            
            self.logger.info(f"列宽已恢复: {table_name} ({restored_count}/{self.parent_table.columnCount()} 列)")
            
            # 🔧 [性能优化] 标记该表的列宽已成功恢复，避免重复操作
            self._restored_tables.add(table_name)

        except Exception as e:
            self.logger.error(f"列宽恢复失败: {e}")

class DragSortManager(QObject):
    """
    拖拽排序管理器
    
    负责管理表格行的拖拽排序功能，提供：
    - 拖拽检测和状态管理
    - 拖拽预览和视觉反馈
    - 行数据重排序算法
    - 拖拽约束条件验证
    - 拖拽性能统计
    """
    
    # 拖拽事件信号
    drag_started = pyqtSignal(int)              # 拖拽开始信号 (source_row)
    drag_moved = pyqtSignal(int, int)           # 拖拽移动信号 (source_row, target_row)
    drag_completed = pyqtSignal(int, int, bool) # 拖拽完成信号 (from_row, to_row, success)
    drag_cancelled = pyqtSignal(int)            # 拖拽取消信号 (source_row)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 拖拽状态
        self.is_dragging = False
        self.drag_source_row = -1
        self.drag_target_row = -1
        self.drag_start_position = QPoint()
        self.drag_current_position = QPoint()
        
        # 拖拽配置
        self.drag_enabled = True
        self.drag_threshold = 10  # 拖拽开始阈值(像素)
        self.drag_preview_enabled = True
        
        # 拖拽约束
        self.cross_group_drag = True      # 允许跨组拖拽
        self.expanded_row_drag = True     # 允许展开行拖拽
        self.allowed_drag_rows = None     # 允许拖拽的行列表，None表示全部允许
        
        # 拖拽数据
        self.drag_data = None
        self.drag_preview_widget = None
        
        # 性能统计
        self.drag_statistics = {
            'total_drags': 0,
            'successful_drags': 0,
            'cancelled_drags': 0,
            'average_drag_time': 0.0,
            'longest_drag_distance': 0
        }
        
        self.logger.info("拖拽排序管理器初始化完成")
    
    def enable_drag_sort(self, enabled: bool):
        """启用/禁用拖拽排序功能"""
        try:
            self.drag_enabled = enabled
            
            if not enabled and self.is_dragging:
                # 如果正在拖拽，取消当前拖拽
                self.cancel_drag()
            
            self.logger.debug(f"拖拽排序功能{'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置拖拽排序状态失败: {e}")
    
    def set_drag_constraints(self, 
                           cross_group: bool = True,
                           expanded_rows: bool = True,
                           allowed_rows: Optional[List[int]] = None):
        """设置拖拽约束条件"""
        try:
            self.cross_group_drag = cross_group
            self.expanded_row_drag = expanded_rows
            self.allowed_drag_rows = allowed_rows
            
            self.logger.debug(f"拖拽约束更新: 跨组={cross_group}, 展开行={expanded_rows}, 限制行数={len(allowed_rows) if allowed_rows else '无'}")
            
        except Exception as e:
            self.logger.error(f"设置拖拽约束失败: {e}")
    
    def start_drag(self, source_row: int, start_position: QPoint, row_data: Dict[str, Any]) -> bool:
        """开始拖拽操作"""
        try:
            if not self.drag_enabled:
                self.logger.debug("拖拽功能已禁用")
                return False
            
            # 检查拖拽约束
            if not self._validate_drag_source(source_row):
                self.logger.debug(f"行 {source_row} 不允许拖拽")
                return False
            
            # 记录拖拽开始时间
            self.drag_start_time = time.time()
            
            # 设置拖拽状态
            self.is_dragging = True
            self.drag_source_row = source_row
            self.drag_target_row = source_row
            self.drag_start_position = start_position
            self.drag_current_position = start_position
            self.drag_data = row_data.copy()
            
            # 更新统计
            self.drag_statistics['total_drags'] += 1
            
            # 发出拖拽开始信号
            self.drag_started.emit(source_row)
            
            self.logger.debug(f"拖拽开始: 源行 {source_row}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始拖拽失败: 源行 {source_row}, 错误: {e}")
            return False
    
    def update_drag(self, target_row: int) -> bool:
        """更新拖拽状态"""
        try:
            if not self.is_dragging:
                return False
            
            # 检查目标行有效性
            if not self._validate_drag_target(target_row):
                return False
            
            # 更新拖拽状态
            old_target = self.drag_target_row
            self.drag_target_row = target_row
            
            # 发出拖拽移动信号
            if old_target != target_row:
                self.drag_moved.emit(self.drag_source_row, target_row)
                self.logger.debug(f"拖拽移动: {self.drag_source_row} -> {target_row}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新拖拽状态失败: 目标行 {target_row}, 错误: {e}")
            return False
    
    def complete_drag(self) -> bool:
        """完成拖拽操作"""
        try:
            if not self.is_dragging:
                return False
            
            source_row = self.drag_source_row
            target_row = self.drag_target_row
            
            # 检查是否真的发生了移动
            if source_row == target_row:
                self.cancel_drag()
                return False
            
            # 记录拖拽时间
            drag_time = time.time() - self.drag_start_time
            
            # 计算拖拽距离
            distance = (self.drag_current_position - self.drag_start_position).manhattanLength()
            
            # 更新统计
            self.drag_statistics['successful_drags'] += 1
            self.drag_statistics['average_drag_time'] = (
                (self.drag_statistics['average_drag_time'] * (self.drag_statistics['successful_drags'] - 1) + drag_time) /
                self.drag_statistics['successful_drags']
            )
            if distance > self.drag_statistics['longest_drag_distance']:
                self.drag_statistics['longest_drag_distance'] = distance
            
            # 清理拖拽状态
            self._reset_drag_state()
            
            # 发出拖拽完成信号
            self.drag_completed.emit(source_row, target_row, True)
            
            self.logger.info(f"拖拽完成: {source_row} -> {target_row}, 耗时: {drag_time:.3f}s")
            return True
            
        except Exception as e:
            self.logger.error(f"完成拖拽失败: {e}")
            self.cancel_drag()
            return False
    
    def cancel_drag(self):
        """取消拖拽操作"""
        try:
            if self.is_dragging:
                source_row = self.drag_source_row
                
                # 更新统计
                self.drag_statistics['cancelled_drags'] += 1
                
                # 清理拖拽状态
                self._reset_drag_state()
                
                # 发出拖拽取消信号
                self.drag_cancelled.emit(source_row)
                
                self.logger.debug(f"拖拽已取消: 源行 {source_row}")
                
        except Exception as e:
            self.logger.error(f"取消拖拽失败: {e}")
    
    def _validate_drag_source(self, row: int) -> bool:
        """验证拖拽源行"""
        try:
            # 检查行索引有效性
            if row < 0:
                return False
            
            # 检查是否在允许拖拽的行列表中
            if self.allowed_drag_rows is not None:
                if row not in self.allowed_drag_rows:
                    return False
            
            # 这里可以添加更多约束检查
            # 比如检查行是否展开、是否属于某个组等
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证拖拽源行失败: 行 {row}, 错误: {e}")
            return False
    
    def _validate_drag_target(self, row: int) -> bool:
        """验证拖拽目标行"""
        try:
            # 检查行索引有效性
            if row < 0:
                return False
            
            # 不能拖拽到自己
            if row == self.drag_source_row:
                return True  # 允许，但不会执行移动
            
            # 这里可以添加更多约束检查
            # 比如检查跨组拖拽、展开行约束等
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证拖拽目标行失败: 行 {row}, 错误: {e}")
            return False
    
    def _reset_drag_state(self):
        """重置拖拽状态"""
        self.is_dragging = False
        self.drag_source_row = -1
        self.drag_target_row = -1
        self.drag_start_position = QPoint()
        self.drag_current_position = QPoint()
        self.drag_data = None
        self.drag_preview_widget = None
    
    def create_drag_preview(self, parent_widget, row: int) -> QPixmap:
        """创建拖拽预览图"""
        try:
            if not hasattr(parent_widget, 'rowHeight') or not hasattr(parent_widget, 'columnCount'):
                # 创建简单的预览图
                pixmap = QPixmap(200, 30)
                pixmap.fill(QColor(100, 100, 100, 100))
                return pixmap
            
            # 获取行高和列数
            row_height = parent_widget.rowHeight(row)
            col_count = parent_widget.columnCount()
            
            # 计算预览图大小
            preview_width = min(400, parent_widget.width())
            preview_height = row_height
            
            # 创建预览图
            pixmap = QPixmap(preview_width, preview_height)
            pixmap.fill(QColor(255, 255, 255, 200))
            
            # 绘制预览内容
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 绘制边框
            painter.setPen(QPen(QColor(33, 150, 243), 2))
            painter.drawRect(pixmap.rect())
            
            # 绘制文本
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(10, 20, f"拖拽行 {row}")
            
            painter.end()
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"创建拖拽预览图失败: 行 {row}, 错误: {e}")
            # 返回默认预览图
            pixmap = QPixmap(200, 30)
            pixmap.fill(QColor(100, 100, 100, 100))
            return pixmap
    
    def get_drag_statistics(self) -> Dict[str, Any]:
        """获取拖拽统计信息"""
        return {
            'enabled': self.drag_enabled,
            'currently_dragging': self.is_dragging,
            'drag_threshold': self.drag_threshold,
            'constraints': {
                'cross_group_drag': self.cross_group_drag,
                'expanded_row_drag': self.expanded_row_drag,
                'allowed_rows_count': len(self.allowed_drag_rows) if self.allowed_drag_rows else None
            },
            'statistics': self.drag_statistics.copy(),
            'success_rate': (
                self.drag_statistics['successful_drags'] / self.drag_statistics['total_drags'] * 100
                if self.drag_statistics['total_drags'] > 0 else 0.0
            )
        }

class VirtualizedExpandableTable(QTableWidget):
    """
    虚拟化可展开表格主组件
    
    基于虚拟化算法实现的高性能可展开表格，支持：
    - 大数据量渲染优化 (1000+行)
    - 流畅的展开/折叠动画 (60fps目标)
    - 智能内存管理和缓存策略
    - Material Design视觉效果
    - 表格内编辑：双击编辑、Enter确认、Esc取消 (P4-001)
    - 多选批量操作：Ctrl+Click、Shift+Click、8种批量功能 (P4-002)
    - 右键菜单系统：上下文感知菜单、动态菜单项 (P4-003)
    - 快捷键支持系统：10+快捷键、帮助系统、冲突检测 (P4-004)
    """
    
    # 信号定义
    row_expanded = pyqtSignal(int, dict)    # 行展开信号
    row_collapsed = pyqtSignal(int, dict)   # 行折叠信号
    selection_changed = pyqtSignal(list)    # 选择变化信号
    cell_edited = pyqtSignal(int, int, object, object)  # 单元格编辑信号 (row, column, old_value, new_value)
    edit_mode_changed = pyqtSignal(bool)    # 编辑模式变化信号
    batch_operation_completed = pyqtSignal(str, int, list)  # 批量操作完成信号 (operation, count, affected_rows)
    context_menu_requested = pyqtSignal(object, object)  # 右键菜单请求信号 (context, position)
    context_action_triggered = pyqtSignal(str, object)   # 右键菜单动作触发信号 (action_name, context)
    shortcut_triggered = pyqtSignal(str, object)         # 快捷键触发信号 (shortcut_name, context)
    shortcut_help_requested = pyqtSignal()               # 快捷键帮助请求信号
    # P4-005 拖拽排序信号
    row_drag_started = pyqtSignal(int)                   # 行拖拽开始信号 (source_row)
    row_drag_moved = pyqtSignal(int, int)                # 行拖拽移动信号 (source_row, target_row)
    row_reordered = pyqtSignal(int, int)                 # 行重排序完成信号 (from_row, to_row)
    drag_operation_completed = pyqtSignal(str, bool)     # 拖拽操作完成信号 (operation_type, success)
    # 字段映射编辑信号
    header_edited = pyqtSignal(int, str, str)           # 表头编辑信号 (column, old_name, new_name)
    field_mapping_updated = pyqtSignal(str, dict)       # 字段映射更新信号 (table_name, mapping)
    
    def __init__(self, parent=None, max_visible_rows: int = 1000, config_sync_manager=None):
        """
        初始化虚拟化可展开表格
        
        Args:
            parent: 父组件
            max_visible_rows: 最大可见行数，用于性能优化，默认1000行
            config_sync_manager: 配置同步管理器实例（依赖注入）
        """
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 🔧 [Timer修复] 线程安全Timer管理
        self._save_timer: Optional[QTimer] = None
        self._delayed_update_timer: Optional[QTimer] = None
        self._timer_destroyed = False
        
        # 🔧 [重影修复] 初始化统一的表头/行号更新管理器
        from .header_update_manager import HeaderUpdateManager
        self.header_update_manager = HeaderUpdateManager(self)
        
        # 数据模型
        self.model = ExpandableTableModel(self)
        
        # 🚨 [架构紧急修复] 确保自定义模型正确关联
        if hasattr(self, 'model') and self.model:
            # 不能直接setModel，因为QTableWidget不支持
            # 但要确保数据同步
            self._custom_model = self.model
            self._ensure_model_data_sync = True
            self.logger.info("🚨 [架构修复] 启用模型数据同步机制")
        
        # 组件管理器初始化
        self.animation_manager = TableAnimationManager(self)
        self.cell_editor = TableCellEditor(self)
        self.context_menu = TableContextMenu(self)
        self.shortcut_manager = KeyboardShortcutManager(self)
        self.drag_sort_manager = DragSortManager(self)  # P4-005: 拖拽排序管理器
        
        # 表格状态管理
        self.visible_rows = []
        self.selected_rows = set()
        self.selected_rows_set = set()  # 多选状态管理
        self.selection_anchor = -1      # 选择锚点
        self.last_selected_row = -1
        self.multi_select_enabled = True
        self.batch_operation_mode = False
        self.expanded_rows = set()      # 展开的行集合
        
        # 表类型感知机制
        self.current_table_type = None  # 当前表类型
        self._table_format_config = None  # 表格式化配置缓存
        self._format_cache_dirty = True  # 格式化缓存状态
        
        # 🔧 [P0性能修复] FormatConfig单例缓存，解决12000次重复初始化问题
        self._format_config_instance = None  # FormatConfig实例缓存
        self._is_retired_staff_cache = {}  # 离休人员表判断结果缓存
        self._cache_table_name = None  # 缓存的表名，用于失效检测

        # 🔧 [排序修复] 数据流验证器和状态管理器
        try:
            from src.modules.data_management.data_flow_validator import DataFlowValidator, ValidationLevel
            from src.modules.state_management.table_state_manager import TableStateManager
            self.data_flow_validator = DataFlowValidator(ValidationLevel.MODERATE)
            self.state_manager = TableStateManager()
            self.logger.info("🔧 [排序修复] 数据流验证器和状态管理器初始化成功")
        except ImportError as e:
            self.logger.warning(f"🔧 [排序修复] 无法导入验证器: {e}")
            self.data_flow_validator = None
            self.state_manager = None
        
        # 编辑状态管理 (P4-001)
        self.edit_mode_enabled = True
        self.current_edit_data = None
        self.current_edit_cell = None   # 当前编辑的单元格 (row, column)
        self.edit_history = []
        self.edit_trigger_delay = 500   # 双击编辑触发延迟(ms)
        self.last_click_time = 0        # 上次点击时间
        self.last_click_pos = (-1, -1)  # 上次点击位置
        
        # 右键菜单状态 (P4-003)
        self.context_menu_enabled = True
        self.last_context_position = QPoint()
        self.last_right_click_pos = QPoint()
        self.last_right_click_time = 0
        
        # 快捷键状态 (P4-004)
        self.shortcuts_enabled = True
        
        # 拖拽排序状态 (P4-005)
        self.drag_sort_enabled = True
        self.drag_start_position = QPoint()
        self.is_potential_drag = False
        
        # 字段映射编辑状态（依赖注入）
        if config_sync_manager is not None:
            self.config_sync_manager = config_sync_manager
        else:
            # 🆕 [新架构] 必须通过依赖注入提供，不再使用兜底方案
            self.logger.error("🆕 [新架构] ConfigSyncManager必须通过依赖注入提供")
            raise ValueError("ConfigSyncManager必须通过依赖注入提供，不再支持兜底创建")
        self.header_edit_manager = HeaderEditManager(self.config_sync_manager)
        self.current_table_name = "default_table"  # 默认表名
        self.header_edit_enabled = True
        
        # 初始化统一格式管理器（新架构版本）
        try:
            # 依赖注入：使用全局事件总线和状态管理器
            from src.core.event_bus import get_event_bus
            # 🔧 [修复导入] 使用统一状态管理器
            from src.core.unified_state_manager import UnifiedStateManager
            
            # 创建状态管理器实例
            state_manager = UnifiedStateManager()
            
            # 🎯 [统一格式管理] 使用统一格式管理器
            # 格式化功能通过统一格式管理器处理
            self.logger.info("🎯 [统一格式管理] 使用统一格式管理器")
        except Exception as e:
            self.logger.error(f"🧹 [代码清理] 组件初始化过程中出现问题: {e}")
        
        # 🔧 [循环防护] 数据重新加载标志
        self._is_data_reloading = False
        
        # 🔧 [修复] 排序应用标志，防止Qt绘制冲突
        self._is_applying_sort = False
        
        # 🔧 [P0-CRITICAL] 绘制状态标志和递归防护
        self._is_painting = False
        self._is_repainting = False
        self._repaint_depth = 0
        self._max_repaint_depth = 3
        
        # 🔧 [P0-致命修复] 初始化排序处理标志，防止异常退出
        self._processing_header_click = False
        self._processing_sort_request = False
        self._is_sorting_in_progress = False
        self._is_paging_in_progress = False
        
        # 🔧 [P0-紧急修复] 初始化时间戳防护
        self._last_header_click_time = 0.0
        
        # 🔧 [P0-紧急修复] 初始化事件处理计数器
        self._header_click_count = 0

        # 🔧 [P1修复] 连接HeaderEditManager信号（带安全检查）
        try:
            if hasattr(self.header_edit_manager, 'mapping_updated'):
                self.header_edit_manager.mapping_updated.connect(self._on_mapping_updated)
            if hasattr(self.header_edit_manager, 'edit_completed'):
                self.header_edit_manager.edit_completed.connect(self._on_edit_completed)
        except Exception as e:
            self.logger.warning(f"⚠️ [信号连接] HeaderEditManager信号连接失败: {e}")
        
        # 🆕 [新架构排序] 初始化新架构多列排序管理器
        try:
            from .column_sort_manager import NewArchitectureColumnSortManager
            self.sort_manager = NewArchitectureColumnSortManager(self, max_sort_columns=3)
            
            # 连接信号
            self.sort_manager.sort_state_changed.connect(self._on_sort_state_changed)
            self.sort_manager.sort_request.connect(self._on_sort_request)
            self.sort_manager.sort_cleared.connect(self._on_sort_cleared)
            
            self.logger.info("🆕 [新架构多列排序] 初始化完成，支持最多3列排序")
        except ImportError as e:
            self.logger.error(f"🆕 [新架构多列排序] 初始化失败: {e}")
            self.sort_manager = None
        
        # 🆕 [P1-功能增强] 初始化列宽保存管理器
        self.column_width_manager = ColumnWidthManager(self)
        self.logger.info("列宽管理器初始化完成")

        # 🔧 [P0-CRITICAL修复] 初始化格式化相关属性，确保属性始终存在
        self._format_manager = None
        self._use_unified_format = False
        self._data_pre_formatted = False
        self._format_cache_dirty = True
        self._table_format_config = None

        # 尝试初始化统一格式管理器
        try:
            from src.modules.format_management.unified_format_manager import get_unified_format_manager
            self._format_manager = get_unified_format_manager()
            self._use_unified_format = True
            self.logger.info("🔧 [P0-CRITICAL修复] 统一格式管理器初始化成功")
        except ImportError as e:
            self.logger.warning(f"🔧 [P0-CRITICAL修复] 统一格式管理器导入失败，使用内置格式化: {e}")
            self._format_manager = None
            self._use_unified_format = False
        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL修复] 统一格式管理器初始化失败: {e}")
            self._format_manager = None
            self._use_unified_format = False

        # 性能配置
        self.scroll_buffer_size = 10
        self.animation_fps = 60
        self.render_batch_size = 50
        self.max_visible_rows = max_visible_rows  # 最大可见行数，用于性能优化
        self.default_row_height = 30             # 默认行高
        self.expanded_row_height = 120           # 展开行的额外高度

        # UI初始化
        self.setup_ui()
        self.setup_connections()

        self.logger.info(f"虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: {self.max_visible_rows}")
    
    def _setup_save_timer_safe(self):
        """线程安全地设置列宽保存定时器"""
        try:
            # 确保在主线程中创建Timer
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [Timer修复] 非主线程，延迟到主线程创建保存Timer")
                from src.utils.thread_safe_timer import safe_single_shot
                safe_single_shot(0, self._setup_save_timer_safe)
                return
            
            # 清理可能存在的旧Timer
            self._cleanup_save_timer()
            
            # 创建新的线程安全Timer
            self._save_timer = QTimer(self)  # 明确指定parent确保生命周期管理
            self._save_timer.setSingleShot(True)
            self._save_timer.timeout.connect(self._delayed_save)
            self._save_timer.start(300)  # 300ms延迟，避免频繁触发
            
            self.logger.debug("🔧 [Timer修复] 列宽保存定时器已在主线程中安全创建")
            
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 创建保存定时器失败: {e}")
    
    def _setup_delayed_update_timer_safe(self):
        """线程安全地设置延迟更新定时器"""
        try:
            # 确保在主线程中创建Timer
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [Timer修复] 非主线程，延迟到主线程创建更新Timer")
                from src.utils.thread_safe_timer import safe_single_shot
                safe_single_shot(0, self._setup_delayed_update_timer_safe)
                return
            
            # 清理可能存在的旧Timer
            self._cleanup_delayed_update_timer()
            
            # 🔧 [P1-线程安全修复] 创建线程安全Timer
            self._delayed_update_timer = ThreadSafeTimer.create_timer(self, single_shot=True)
            if self._delayed_update_timer:
                ThreadSafeTimer.safe_start_timer(self._delayed_update_timer, 100, self._safe_delayed_update)
            else:
                self.logger.warning("⚠️ [线程安全] 延迟更新定时器创建失败，跳过延迟更新")
            
            self.logger.debug("🔧 [Timer修复] 延迟更新定时器已在主线程中安全创建")
            
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 创建延迟更新定时器失败: {e}")
    
    def _cleanup_save_timer(self):
        """安全清理保存定时器"""
        if self._save_timer and not self._timer_destroyed:
            try:
                if self._save_timer.isActive():
                    self._save_timer.stop()
                self._save_timer.deleteLater()
                self._save_timer = None
                self.logger.debug("🔧 [Timer修复] 保存定时器已安全清理")
            except Exception as e:
                self.logger.warning(f"🔧 [Timer修复] 清理保存定时器时出现异常: {e}")
    
    def _cleanup_delayed_update_timer(self):
        """安全清理延迟更新定时器"""
        if self._delayed_update_timer and not self._timer_destroyed:
            try:
                if self._delayed_update_timer.isActive():
                    self._delayed_update_timer.stop()
                self._delayed_update_timer.deleteLater()
                self._delayed_update_timer = None
                self.logger.debug("🔧 [Timer修复] 延迟更新定时器已安全清理")
            except Exception as e:
                self.logger.warning(f"🔧 [Timer修复] 清理延迟更新定时器时出现异常: {e}")
    
    def closeEvent(self, event):
        """表格关闭时的清理工作"""
        try:
            self.logger.debug("🔧 [Timer修复] 虚拟化表格正在关闭，清理Timer资源")
            self._timer_destroyed = True
            self._cleanup_save_timer()
            self._cleanup_delayed_update_timer()
            super().closeEvent(event)
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 关闭清理失败: {e}")
            super().closeEvent(event)
    
    def setup_ui(self):
        """设置用户界面"""
        # 基本配置
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.MultiSelection)
        self.setShowGrid(True)
        # 🔧 [架构修复] 保持自定义排序功能，但修复数据显示问题
        self.setSortingEnabled(False)  # 保持禁用Qt默认排序，使用自定义排序逻辑
        
        # 🔧 [架构修复] 保持自定义排序启用，但修复数据流
        self._custom_sort_enabled = True  # 保持自定义排序启用
        self._current_sort_column = -1
        self._current_sort_order = Qt.AscendingOrder
        
        self.logger.info("🔧 [架构修复] 保持自定义排序功能，修复数据显示问题")
        
        # 拖拽排序配置 (P4-005)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
        
        # 表头配置
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.horizontalHeader().setStretchLastSection(True)
        
        # 启用垂直表头显示行号
        self.verticalHeader().setVisible(True)
        self.verticalHeader().setDefaultSectionSize(30)  # 设置行高
        
        # 🆕 [新架构排序] 水平表头排序功能 - 使用自定义排序循环
        header = self.horizontalHeader()
        header.setSortIndicatorShown(True)
        header.setSectionsClickable(True)

        # 🆕 [新架构排序] 连接表头点击信号到自定义处理器
        # 🔧 [P2-信号时序修复] 改用QueuedConnection避免并发更新冲突
        header.sectionClicked.connect(
            self._on_header_clicked,
            Qt.QueuedConnection
        )
        
        # 🔧 [P2-信号时序修复] 表头双击编辑配置 - 改用QueuedConnection
        header.sectionDoubleClicked.connect(
            self._on_header_double_clicked,
            Qt.QueuedConnection
        )
        
        self.logger.debug("✅ 表头排序信号连接完成，只连接一次")
        
        # Material Design样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #E0E0E0;
                background-color: #FAFAFA;
                alternate-background-color: #F5F5F5;
                selection-background-color: #E3F2FD;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }
            
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3, stop: 1 #1976D2
                );
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1565C0;
            }
            
            QTableWidget::item:hover {
                background-color: #F3E5F5;
            }
            
            /* 拖拽状态样式 (P4-005) */
            QTableWidget[dragging="true"] {
                border: 2px solid #2196F3;
                background-color: #F3E5F5;
            }
            
            QTableWidget::item[drag_target="true"] {
                background-color: #BBDEFB;
                border: 2px dashed #2196F3;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        # 动画管理器连接
        self.animation_manager.animation_finished.connect(
            self._on_animation_finished
        )
        
        # 单元格编辑器连接 (P4-001)
        self.cell_editor.edit_finished.connect(self._on_edit_finished)
        
        # 右键菜单连接 (P4-003)
        self.context_menu.action_triggered.connect(self._on_context_action_triggered)
        
        # 快捷键管理器连接 (P4-004)
        self.shortcut_manager.shortcut_triggered.connect(self._on_shortcut_triggered)
        self.shortcut_manager.help_requested.connect(self._on_shortcut_help_requested)
        
        # 拖拽排序管理器连接 (P4-005)
        self.drag_sort_manager.drag_started.connect(self._on_drag_started)
        self.drag_sort_manager.drag_moved.connect(self._on_drag_moved)
        self.drag_sort_manager.drag_completed.connect(self._on_drag_completed)
        self.drag_sort_manager.drag_cancelled.connect(self._on_drag_cancelled)
        
        # 表格事件连接
        # 🔧 [Qt类型修复] 表格信号连接 - 改用DirectConnection避免类型注册问题
        self.cellClicked.connect(self._on_cell_clicked, Qt.DirectConnection)
        self.cellDoubleClicked.connect(self._on_cell_double_clicked, Qt.DirectConnection)
        self.itemSelectionChanged.connect(self._on_selection_changed, Qt.DirectConnection)
    
    def set_loading_state(self, is_loading: bool, message: str = "数据加载中..."):
        """
        🚫 [用户要求] 彻底禁用加载状态显示
        """
        # 用户明确要求删除所有加载条，此方法不执行任何操作
        self.logger.info(f"🚫 [用户要求] 加载状态调用被禁用: {message}")
        return
    
    def show_loading_with_progress(self, current: int, total: int, message: str = ""):
        """
        🚫 [用户要求] 彻底禁用带进度的加载状态显示
        """
        # 用户明确要求删除所有加载条，此方法不执行任何操作
        self.logger.info(f"🚫 [用户要求] 进度加载状态调用被禁用: {message}")
        return
    
    def update_loading_message(self, message: str, detail: str = ""):
        """
        🚫 [用户要求] 彻底禁用加载消息更新
        """
        # 用户明确要求删除所有加载条，此方法不执行任何操作
        self.logger.info(f"🚫 [用户要求] 加载消息更新被禁用: {message}")
        return
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str], auto_adjust_visible_rows: bool = True, current_table_name: str = None, force_table_type: str = None):
        """
        设置表格数据并应用字段映射 - 防重影增强版 + 数据流一致性修复

        Args:
            data: 行数据列表
            headers: 表头列表
            auto_adjust_visible_rows: 是否根据数据量自动调整最大可见行数，默认为True
            current_table_name: 当前表名，用于应用字段映射
            force_table_type: 🔧 [P1修复] 强制使用指定的表类型，用于排序时保持类型一致性
        """
        # 🔧 [P1-CRITICAL修复] 增强数据设置锁，避免重复调用导致的重影和闪动
        if hasattr(self, '_data_setting_lock') and self._data_setting_lock:
            self.logger.warning("🔧 [P1-CRITICAL修复] 数据设置正在进行中，跳过重复调用")
            return

        # 🔧 [P1-CRITICAL修复] 检查数据是否真的发生了变化，避免不必要的更新
        if hasattr(self, '_last_data_hash') and data is not None:
            import hashlib
            current_data_str = str(len(data)) + str(headers) if data else "empty"
            current_hash = hashlib.md5(current_data_str.encode()).hexdigest()
            if current_hash == self._last_data_hash:
                self.logger.debug("🔧 [P1-CRITICAL修复] 数据未变化，跳过重复更新")
                return
            self._last_data_hash = current_hash

        # 🔧 [闪动修复] 暂时禁用重绘，减少闪动
        self.setUpdatesEnabled(False)

        # 🔧 [排序修复] 数据流一致性验证和修复
        data, headers = self._validate_and_fix_data_consistency(data, headers, current_table_name)

        start_time = time.time()
        step_times = {}  # 记录各步骤耗时

        try:
            # 【防重影】设置数据设置锁
            self._data_setting_lock = True
            # 🔧 [P2-优化] 简化数据验证和日志
            self.logger.debug(f"set_data调用开始，数据行数: {len(data) if data else 0}")

            # 🔧 [排序修复] 保存当前数据用于一致性检查
            self._current_data = data
            self._current_headers = headers

            # 🔧 [P0-CRITICAL修复] 只在DEBUG模式记录详细数据，安全处理DataFrame
            import pandas as pd
            if data is not None:
                if isinstance(data, pd.DataFrame) and data.shape[0] > 0:
                    # DataFrame处理
                    emp_id = self._get_employee_id_from_row(data.iloc[0].to_dict(), 0)
                    self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame数据样本（首行）: 工号={emp_id}")
                elif isinstance(data, list) and len(data) > 0:
                    # 列表处理
                    emp_id = self._get_employee_id_from_row(data[0], 0)
                    self.logger.debug(f"🔧 [P0-CRITICAL修复] 列表数据样本（首行）: 工号={emp_id}")
            
            # 🔧 [P0-CRITICAL] 增强绘制冲突检查
            # 🔧 [P2-优化] 简化绘制状态检查
            if getattr(self, '_is_painting', False) or getattr(self, '_is_repainting', False):
                self.logger.debug("检测到正在绘制，重置状态继续")
                self._is_painting = False
                self._is_repainting = False
            
            if getattr(self, '_repaint_depth', 0) > 0:
                self.logger.debug(f"重置重绘深度: {self._repaint_depth}")
                self._repaint_depth = 0

            if hasattr(self, '_setting_data_in_progress') and self._setting_data_in_progress:
                self.logger.debug("重置数据设置状态")
                self._setting_data_in_progress = False

            self._setting_data_in_progress = True
            # 🔧 [P0-CRITICAL] 设置分页进行标志
            self._is_paging_in_progress = True

            # 🔧 [修复] 设置绘制锁，防止在数据更新期间进行重绘
            self._is_painting = True
            
            # 🔧 [P2-优化] 检测数据是否已经被格式化，避免重复格式化
            self._data_pre_formatted = False
            
            # 🔧 [P0-CRITICAL修复] 安全检查数据是否带有格式化标记
            import pandas as pd
            if data is not None:
                if isinstance(data, pd.DataFrame) and data.shape[0] > 0:
                    # DataFrame处理
                    if hasattr(data, 'attrs') and data.attrs.get('formatted', False):
                        self._data_pre_formatted = True
                        self.logger.debug("🔧 [P0-CRITICAL修复] DataFrame数据已标记为格式化完成")
                elif isinstance(data, list) and len(data) > 0:
                    # 列表处理
                    if hasattr(data, 'attrs') and data.attrs.get('formatted', False):
                        self._data_pre_formatted = True
                        self.logger.debug("🔧 [P0-CRITICAL修复] 列表数据已标记为格式化完成")
                else:
                    # 后备检查：检查第一行的货币字段是否已格式化
                    first_row = data[0] if isinstance(data, list) else data.iloc[0] if hasattr(data, 'iloc') else {}
                    for header in headers:
                        if '工资' in header or '津贴' in header or '补贴' in header:
                            sample_value = first_row.get(header) if hasattr(first_row, 'get') else None
                            if isinstance(sample_value, str) and ('¥' in str(sample_value) or '￥' in str(sample_value)):
                                self._data_pre_formatted = True
                                self.logger.debug(f"检测到数据已格式化: {header}")
                                break
            
            # 步骤1：初始化和状态检查
            step_start = time.time()
            
            # 【关键修复】检查是否处于分页模式，分页模式下跳过表头重设
            is_pagination_mode = getattr(self, '_pagination_mode', False)
            
            if not is_pagination_mode:
                # 【第二层防护】非分页模式下强制清理表头状态，防止重影
                self._force_clear_header_state()
                
                # 🔧 [修复标识] 设置新数据时重置分页状态，防止跨表分页状态污染
                self.pagination_state = None
                self.logger.debug("🔧 [修复标识] 分页状态已重置，避免跨表状态污染")
            else:
                self.logger.debug("🔧 [修复标识] 分页模式：跳过表头清理和重置")

            # 保存当前表名
            if current_table_name:
                self.current_table_name = current_table_name

            # 保存原始数据和表头
            self.original_data = data
            self.original_headers = headers.copy()
            
            # 🔧 [P0-CRITICAL修复] 验证数据保存是否正确，安全处理DataFrame
            import pandas as pd
            if self.original_data is not None:
                if isinstance(self.original_data, pd.DataFrame) and self.original_data.shape[0] > 0:
                    # DataFrame处理
                    for i in range(min(2, self.original_data.shape[0])):
                        emp_id = self._get_employee_id_from_row(self.original_data.iloc[i].to_dict(), i)
                        self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame原始数据验证[{i}]: 工号={emp_id}")
                elif isinstance(self.original_data, list) and len(self.original_data) > 0:
                    # 列表处理
                    for i in range(min(2, len(self.original_data))):
                        emp_id = self._get_employee_id_from_row(self.original_data[i], i)
                        self.logger.debug(f"🔧 [P0-CRITICAL修复] 列表原始数据验证[{i}]: 工号={emp_id}")
                    salary = self.original_data[i].get('2025年薪级工资') or self.original_data[i].get('grade_salary_2025') or 'N/A'
                    self.logger.info(f"🚨 [UI数据修复] 保存到original_data第{i}行: 工号={emp_id}, 薪资={salary}")

            # 自动调整最大可见行数
            if auto_adjust_visible_rows:
                self.auto_adjust_max_visible_rows(len(data))
            
            step_times['初始化和状态'] = (time.time() - step_start) * 1000

            # 步骤2：字段映射
            step_start = time.time()
            displayed_headers = self._apply_field_mapping(headers)
            step_times['字段映射'] = (time.time() - step_start) * 1000

            # 步骤2.5：🔧 [P2-优化] 条件格式化处理 - 避免重复格式化
            step_start = time.time()
            
            # 🆕 [修复] 在try块外部定义table_type，确保在整个方法中可用
            # 🔧 [P1修复] 优先使用强制指定的表类型，确保排序时类型一致性
            if force_table_type:
                table_type = force_table_type
                self.logger.info(f"🔧 [P1修复] 使用强制指定的表类型: {force_table_type}")
            else:
                # 🔧 [P1修复] 使用统一的表类型识别入口
                table_type = self._get_unified_table_type(self.current_table_name) if self.current_table_name else "unknown"
            
            try:
                # 🔧 [P0-CRITICAL修复] 只有当数据未被格式化时才进行格式化，安全处理DataFrame
                import pandas as pd
                data_exists = False
                if data is not None:
                    if isinstance(data, pd.DataFrame):
                        data_exists = data.shape[0] > 0
                    elif isinstance(data, list):
                        data_exists = len(data) > 0
                    else:
                        data_exists = bool(data)

                if not self._data_pre_formatted and data_exists:
                        
                    # 将数据转换为DataFrame用于格式化
                    import pandas as pd
                    df = pd.DataFrame(data)
                    
                    # 🔧 [P1优化] 使用已缓存的格式化管理器，避免重复获取
                    try:
                        if not hasattr(self, '_format_manager') or self._format_manager is None:
                            from src.modules.format_management.unified_format_manager import get_unified_format_manager
                            self._format_manager = get_unified_format_manager()
                        formatted_df = self._format_manager.format_table_data(df, table_type)
                        formatted_headers = displayed_headers  # 保持已映射的表头
                    except Exception as e:
                        self.logger.warning(f"主格式化管理器调用失败: {e}")
                        formatted_df = df
                        formatted_headers = displayed_headers
                    
                    # 🚨 [排序修复] 更新数据和表头，但验证数据一致性
                    formatted_data = formatted_df.to_dict('records')
                    
                    # 🔧 [P0-CRITICAL修复] 验证格式化后的数据顺序是否正确，安全处理DataFrame
                    import pandas as pd
                    formatted_has_data = False
                    original_has_data = False

                    if formatted_data is not None:
                        if isinstance(formatted_data, pd.DataFrame):
                            formatted_has_data = formatted_data.shape[0] > 0
                        elif isinstance(formatted_data, list):
                            formatted_has_data = len(formatted_data) > 0

                    if data is not None:
                        if isinstance(data, pd.DataFrame):
                            original_has_data = data.shape[0] > 0
                        elif isinstance(data, list):
                            original_has_data = len(data) > 0

                    if formatted_has_data and original_has_data:
                        # 安全获取第一行数据
                        if isinstance(data, pd.DataFrame):
                            original_first_row = data.iloc[0].to_dict()
                        else:
                            original_first_row = data[0]

                        if isinstance(formatted_data, pd.DataFrame):
                            formatted_first_row = formatted_data.iloc[0].to_dict()
                        else:
                            formatted_first_row = formatted_data[0]

                        original_first_id = self._get_employee_id_from_row(original_first_row, 0)
                        formatted_first_id = self._get_employee_id_from_row(formatted_first_row, 0)
                        
                        # 🔧 [修复] 比较ID的数值而不是字符串格式，允许20061463.0 -> 20061463的转换
                        def normalize_id(id_value):
                            """标准化ID值用于比较，去除浮点数后缀"""
                            if id_value is None:
                                return None
                            id_str = str(id_value)
                            # 如果是浮点数格式（如20061463.0），转换为整数格式
                            if '.' in id_str and id_str.replace('.', '').replace('-', '').isdigit():
                                try:
                                    return str(int(float(id_str)))
                                except (ValueError, TypeError):
                                    return id_str
                            return id_str
                        
                        normalized_original = normalize_id(original_first_id)
                        normalized_formatted = normalize_id(formatted_first_id)
                        
                        if normalized_original != normalized_formatted:
                            self.logger.warning(f"格式化可能影响数据顺序，跳过格式化")
                            # 不更新data，保持原始排序顺序
                        else:
                            # 格式化没有破坏顺序，安全更新
                            data = formatted_data
                            self.logger.debug(f"数据格式化完成: {self.current_table_name}")
                    else:
                        data = formatted_data
                    
                    displayed_headers = formatted_headers
                    
                    # 标记数据已格式化
                    if hasattr(data, 'attrs'):
                        data.attrs['formatted'] = True
                else:
                    self.logger.debug("数据已格式化，跳过重复处理")
                    
                    self.logger.info(f"表格格式化完成: {self.current_table_name}, 类型: {table_type}")
                    
            except Exception as format_error:
                self.logger.error(f"表格格式化失败: {format_error}")
                # 格式化失败不影响数据显示，继续使用原始数据
            
            step_times['统一格式化'] = (time.time() - step_start) * 1000

            # 🚨 [架构紧急修复] 完全绕过复杂的模型机制，直接设置QTableWidget数据
            step_start = time.time()
            
            # 强制清除表格内容，确保干净的数据设置
            self.clearContents()
            
            # 🔧 [P0-CRITICAL修复] 数据和表头一致性验证，安全处理DataFrame
            import pandas as pd
            if data is not None and displayed_headers:
                data_col_count = 0
                if isinstance(data, pd.DataFrame) and data.shape[0] > 0:
                    # DataFrame处理
                    data_col_count = data.shape[1]
                    self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame列数: {data_col_count}")
                elif isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                    # 列表处理
                    data_col_count = len(data[0])
                    self.logger.debug(f"🔧 [P0-CRITICAL修复] 列表列数: {data_col_count}")

                if data_col_count > 0:
                    header_col_count = len(displayed_headers)
                    if data_col_count != header_col_count:
                        self.logger.warning(f"🔧 [P0-1修复] 设置数据时发现列数不匹配: 数据{data_col_count}列, 表头{header_col_count}列")
                        # 截取数据列以匹配表头
                        if data_col_count > header_col_count:
                            self.logger.info(f"🔧 [P0-1修复] 截取数据列以匹配表头: {data_col_count} -> {header_col_count}")
                            # 保留前N列数据
                            data = [{k: v for i, (k, v) in enumerate(row.items()) if i < header_col_count} for row in data]
            
            # 设置行数和列数
            self.setRowCount(len(data))
            self.setColumnCount(len(displayed_headers))
            
            # 🔧 [P0-关键修复] 缓存当前表头状态，供后续行号更新使用
            self.current_headers = displayed_headers.copy() if displayed_headers else []
            
            # 设置表头
            if not is_pagination_mode:
                # 🔧 [重影修复] 使用统一管理器更新表头，防止重影
                self.header_update_manager.update_headers_safe(displayed_headers)
                self.logger.debug("🚨 [架构修复] 非分页模式：直接设置表头")
            else:
                # 保持排序状态的表头设置
                self._safe_update_headers_with_sort_state(displayed_headers)
                self.logger.debug("🚨 [架构修复] 分页模式：更新表头并保持排序状态")
            
            # 使用优化的表格渲染器 - 替换原有的逐行渲染逻辑
            try:
                from src.gui.prototype.widgets.optimized_table_renderer import OptimizedTableRenderer
                
                # 预处理数据 - 应用字段映射和格式化
                processed_data = []
                for row_data in data:
                    processed_row = {}
                    for header in displayed_headers:
                        # 获取数据值 - 支持多种字段名格式
                        value = row_data.get(header, "")
                        if value == "" and header in ['工号', 'employee_id', '人员代码']:
                            # 特殊处理员工ID字段
                            value = self._get_employee_id_from_row(row_data, 0) if row_data else ""
                        
                        # 使用统一格式化
                        formatted_value = self._format_cell_value(value, header)
                        processed_row[header] = formatted_value
                    
                    processed_data.append(processed_row)
                
                # 🔧 [P0-CRITICAL修复] 记录第一行样本（用于调试），安全处理DataFrame
                import pandas as pd
                if processed_data is not None:
                    if isinstance(processed_data, pd.DataFrame) and processed_data.shape[0] > 0:
                        # DataFrame处理
                        first_row = processed_data.iloc[0].to_dict()
                        self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame第一行样本准备完成")
                    elif isinstance(processed_data, list) and len(processed_data) > 0:
                        # 列表处理
                        first_row = processed_data[0]
                        self.logger.debug(f"🔧 [P0-CRITICAL修复] 列表第一行样本准备完成")
                    else:
                        first_row = None

                    if first_row:
                        for header in ['工号', '姓名']:
                            if header in first_row:
                                self.logger.debug(f"[数据流追踪] 数据设置样本: {header} = {first_row[header]}")
                
                # 使用优化渲染器进行高性能渲染
                render_result = OptimizedTableRenderer.render_with_auto_strategy(
                    table_widget=self,
                    data=processed_data,
                    headers=displayed_headers
                )
                
                self.logger.info(f"[数据流追踪] 优化渲染完成: {render_result['data_size']}行 x {len(displayed_headers)}列, "
                               f"策略={render_result['strategy']}, 耗时={render_result['elapsed_ms']:.1f}ms, "
                               f"性能评级={render_result['performance_rating']}")
                
                # 记录渲染性能到监控系统
                if not hasattr(self, '_performance_monitor'):
                    from src.gui.prototype.widgets.optimized_table_renderer import RenderingPerformanceMonitor
                    self._performance_monitor = RenderingPerformanceMonitor()
                
                self._performance_monitor.record_performance(
                    data_size=render_result['data_size'],
                    elapsed_ms=render_result['elapsed_ms'],
                    strategy=render_result['strategy']
                )
                
            except ImportError as e:
                self.logger.warning(f"无法导入优化渲染器，使用原有渲染逻辑: {e}")
                # 降级到原有的渲染逻辑
                data_set_count = 0
                for row_idx, row_data in enumerate(data):
                    for col_idx, header in enumerate(displayed_headers):
                        value = row_data.get(header, "")
                        if value == "" and header in ['工号', 'employee_id', '人员代码']:
                            value = self._get_employee_id_from_row(row_data, 0) if row_data else ""
                        
                        formatted_value = self._format_cell_value(value, header)
                        item = QTableWidgetItem(str(formatted_value) if formatted_value is not None else "")
                        
                        # 🔧 [P1-2修复] 增强的数字类型判断和右对齐
                        if self._is_numeric_value(value, formatted_value):
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        
                        self.setItem(row_idx, col_idx, item)
                        data_set_count += 1
                
                self.logger.info(f"降级渲染完成: {len(data)}行 x {len(displayed_headers)}列")
            
            except Exception as e:
                self.logger.error(f"🔧 [P0-CRITICAL修复] 优化渲染器执行失败: {e}")
                # 🔧 [P0-CRITICAL修复] 使用备用渲染方案，确保数据能够显示
                try:
                    self.logger.info("🔧 [P0-CRITICAL修复] 启用备用渲染方案")
                    self._render_data_with_fallback_method(data, displayed_headers)
                except Exception as fallback_error:
                    self.logger.error(f"🔧 [P0-CRITICAL修复] 备用渲染方案也失败: {fallback_error}")
                    # 最后的兜底方案：确保基本表格结构存在
                    if self.rowCount() == 0:
                        self.setRowCount(len(data))
                        self.setColumnCount(len(displayed_headers))
                        # 🔧 [P0-修复] 使用HeaderUpdateManager设置表头，避免重影
                        if hasattr(self, 'header_update_manager') and self.header_update_manager:
                            self.header_update_manager.update_headers_safe(displayed_headers)
                        # 显示错误信息给用户
                        if len(data) > 0:
                            error_item = QTableWidgetItem("数据渲染失败，请刷新重试")
                            self.setItem(0, 0, error_item)
            
            step_times['直接数据设置'] = (time.time() - step_start) * 1000
            
            # 🚨 [架构核心修复] 完全跳过自定义模型更新，避免数据不一致
            # 原代码会更新自定义模型，但QTableWidget显示的是内置模型数据，导致排序无效
            # 现在完全使用QTableWidget的内置模型，排序功能依赖QTableWidget本身
            if getattr(self, '_ensure_model_data_sync', False):
                try:
                    # 仅用于兼容性保存，不影响显示
                    if hasattr(self, '_custom_model') and self._custom_model:
                        self._custom_model.set_data(data, displayed_headers, headers)
                        self.logger.debug("🚨 [架构修复] 自定义模型数据已同步（仅用于兼容性）")
                except Exception as e:
                    self.logger.debug(f"🚨 [架构修复] 自定义模型同步跳过: {e}")
            else:
                self.logger.info("🚨 [架构修复] 完全跳过自定义模型，使用QTableWidget内置排序")
            
            # 步骤7：延迟调整列宽 - 🔧 [P0-3修复] 根据模式选择恢复策略
            step_start = time.time()
            
            # 🔧 [P2级导入修复] 正确的PyQt5导入
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QThread
            
            # 检查是否在主线程中
            is_main_thread = QThread.currentThread() == QApplication.instance().thread()
            
            # 🔧 [P0-修复] 统一列宽管理：所有场景都使用相同的列宽恢复逻辑
            if is_main_thread:
                # 在主线程中直接执行，统一使用带恢复的列宽调整
                self._adjust_column_widths_delayed()
                self.logger.debug("🔧 [P0-修复] 主线程：统一使用列宽恢复逻辑")
            else:
                # 在非主线程中使用安全延迟调用，统一使用带恢复的列宽调整
                from src.utils.thread_safe_timer import safe_single_shot
                safe_single_shot(50, self._adjust_column_widths_delayed)
                self.logger.debug("🔧 [P0-修复] 非主线程：统一使用安全延迟列宽恢复")
                
            step_times['调整列宽'] = (time.time() - step_start) * 1000
            
            # 步骤8：设置行号
            step_start = time.time()
            self._setup_row_numbers()
            step_times['设置行号'] = (time.time() - step_start) * 1000

            elapsed_time = (time.time() - start_time) * 1000
            
            # 更新排序管理器的表格上下文
            if current_table_name and hasattr(self, 'sort_manager') and self.sort_manager:
                self.sort_manager.update_table_context(current_table_name)
                self.logger.debug(f"已更新排序管理器表格上下文: {current_table_name}")
            
            # 性能报告（简化）
            self.logger.info(f"表格数据设置完成: {len(data)} 行, 耗时: {elapsed_time:.1f}ms")
            # 🔧 [分页行号修复] 数据设置完成后的行号修复
            try:
                if hasattr(self, 'pagination_state') and self.pagination_state:
                    self.logger.info("🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理")
                    # 🔧 [重影修复] 移除定时器机制，HeaderUpdateManager已确保行号正确设置
                    
            except Exception as e:
                self.logger.error(f"🔧 [分页行号修复] 数据设置后处理失败: {e}")

            # 🔧 [P2-优化] 只在DEBUG模式记录详细性能分析
            if elapsed_time > 1000:  # 只有超过1秒的操作才记录详细信息
                for step_name, step_time in step_times.items():
                    if step_time > 100:  # 只记录超过100ms的步骤
                        percentage = (step_time / elapsed_time) * 100
                        self.logger.debug(f"耗时步骤: {step_name} {step_time:.1f}ms ({percentage:.1f}%)")
            
            if step_times:
                slowest_step = max(step_times.items(), key=lambda x: x[1])
                self.logger.debug(f"最耗时步骤: {slowest_step[0]} ({slowest_step[1]:.2f}ms)")

        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
        finally:
            # 【防重影】释放数据设置锁
            if hasattr(self, '_data_setting_lock'):
                self._data_setting_lock = False
                self.logger.debug("🔧 [防重影] 数据设置锁已释放")
                
            # 确保数据加载完成后清除重载标志，允许排序功能
            if hasattr(self, '_is_data_reloading'):
                self._is_data_reloading = False
                self.logger.debug("数据加载完成，清除重载标志，排序功能已恢复")
                
            # 🔧 [修复] 清除绘制锁，允许正常重绘
            if hasattr(self, '_is_painting'):
                self._is_painting = False
                self.logger.debug("🔧 [修复] 数据设置完成，清除绘制锁，允许正常重绘")

            # 🔧 [修复] 清除数据设置进行标志
            if hasattr(self, '_setting_data_in_progress'):
                self._setting_data_in_progress = False
                self.logger.debug("🔧 [修复] 数据设置完成，清除数据设置进行标志")
            
            # 🔧 [P0-CRITICAL] 清除分页进行标志
            if hasattr(self, '_is_paging_in_progress'):
                self._is_paging_in_progress = False
                self.logger.debug("🔧 [P0-CRITICAL] 数据设置完成，清除分页进行标志")
            
            # 🔧 [关键修复] 注释掉信号重连，避免Qt事件循环冲突
            # self._fix_header_signal_connections()  # 移除：导致重复点击的根本原因
            
            # 🔧 [P0-排序修复] 恢复排序指示器显示
            self._restore_sort_indicators_after_data_reload()
            
            # 🔧 [P0-CRITICAL修复] 智能列宽恢复，避免重复调用
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')

                # 🔧 [P0-CRITICAL修复] 检查是否在分页过程中，避免与分页列宽恢复冲突
                is_in_pagination = getattr(self, '_is_paging_in_progress', False)
                if is_in_pagination:
                    self.logger.debug(f"🔧 [重复操作防护] 分页进行中，跳过set_data中的列宽恢复: {table_name}")
                else:
                    # 🔧 [排序修复] 使用force=True强制恢复，确保排序时列宽不重置
                    self.column_width_manager.restore_column_widths(table_name, force=True)
                    self.logger.info(f"🔧 [排序修复] 数据设置后已强制恢复列宽: {table_name}")
            
            # 🔧 [P1级线程安全修复] 恢复表格UI状态（列宽、排序等）
            if hasattr(self, 'current_table_name') and self.current_table_name:
                # 🔧 [P2级导入修复] 检查线程状态，优化UI恢复调用
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import QThread
                is_main_thread = QThread.currentThread() == QApplication.instance().thread()
                
                if is_main_thread:
                    # 主线程中使用线程安全定时器
                    from src.utils.thread_safe_timer import safe_single_shot
                    safe_single_shot(100, lambda: self._restore_ui_state_after_data_set())
                    self.logger.debug("🔧 [P0-修复] 主线程使用线程安全定时器恢复UI状态")
                else:
                    # 非主线程使用线程安全调用
                    from src.utils.thread_safe_timer import safe_single_shot
                    safe_single_shot(100, lambda: self._restore_ui_state_after_data_set())
                    self.logger.debug("🔧 [P1级修复] 非主线程使用安全延迟恢复UI状态")
                
            # 🔧 [分页行号修复] 数据设置完成后，检查并修复分页行号
            if hasattr(self, '_pagination_mode') and self._pagination_mode:
                if hasattr(self, 'pagination_state') and self.pagination_state:
                    # 🔧 [重影修复] 移除定时器检查，HeaderUpdateManager已确保行号正确
                    self.logger.info("🔧 [重影修复] 行号已由HeaderUpdateManager统一管理")

            # 🔧 [闪动修复] 重新启用重绘，完成数据设置
            self.setUpdatesEnabled(True)
            self.logger.debug("🔧 [闪动修复] 数据设置完成，已重新启用重绘")

    def _fix_header_signal_connections(self):
        """🔧 [关键修复] 强制重新设置表头信号连接为QueuedConnection，确保线程安全"""
        try:
            header = self.horizontalHeader()
            
            # 断开所有现有的信号连接，防止重复连接
            try:
                header.sortIndicatorChanged.disconnect()
                header.sectionDoubleClicked.disconnect()
                header.sectionClicked.disconnect()  # 🔧 [关键修复] 断开sectionClicked信号
                self.logger.debug("成功断开所有表头信号")
            except Exception as disconnect_error:
                self.logger.debug(f"断开信号时的警告（正常）: {disconnect_error}")
                pass  # 忽略断开失败的错误
            
            # 🔧 [修复] 使用新架构的表头点击处理器
            # 注意：不再连接sortIndicatorChanged，因为我们使用自定义排序循环
            # 🔧 [P2-信号时序修复] 改用QueuedConnection避免并发更新冲突
            header.sectionClicked.connect(
                self._on_header_clicked,
                Qt.QueuedConnection
            )

            if hasattr(self, '_on_header_double_clicked'):
                header.sectionDoubleClicked.connect(
                    self._on_header_double_clicked,
                    Qt.QueuedConnection
                )
            
            self.logger.debug("🔧 [关键修复] 表头信号连接修复成功 - 使用新架构排序")
            
        except Exception as e:
            self.logger.warning(f"🔧 [关键修复] 表头信号连接修复失败: {e}")

    def _extract_table_type_from_name(self, table_name: str) -> str:
        """
        从表名中提取表格类型
        
        Args:
            table_name: 表名
            
        Returns:
            表格类型标识
        """
        try:
            # 🔧 [P1-修复] 防止None类型错误
            if not table_name:
                self.logger.debug("表名为空，使用默认格式")
                return 'active_employees'
            
            # 🎯 [新架构] 配置化表类型映射，支持default_table
            table_type_mappings = {
                # 精确匹配
                'default_table': 'active_employees',  # 解决default_table问题
                'active_employees': 'active_employees',
                'retired_employees': 'retired_employees', 
                'pension_employees': 'pension_employees',  # 🔧 [P0修复] 退休人员使用独立配置
                'part_time_employees': 'part_time_employees',
                'contract_employees': 'contract_employees',
                'a_grade_employees': 'a_grade_employees',
                # 中文匹配
                '全部在职人员': 'active_employees',
                '离休人员': 'retired_employees',
                '退休人员': 'pension_employees',  # 🔧 [P0修复] 退休人员使用独立配置
                '临时工': 'part_time_employees',
                '合同工': 'contract_employees',
                'a岗': 'a_grade_employees',
                # 🆕 [离休人员表] 增强离休人员表识别
                '离休人员工资表': 'retired_employees',
                '离休': 'retired_employees',
                'retired_staff': 'retired_employees'
            }
            
            # 精确匹配
            if table_name in table_type_mappings:
                return table_type_mappings[table_name]
            
            # 模糊匹配
            table_name_lower = table_name.lower()
            for pattern, table_type in table_type_mappings.items():
                if pattern.lower() in table_name_lower:
                    return table_type
            
            # 默认类型
            self.logger.debug(f"未识别的表类型: {table_name}, 使用默认格式")
            return 'active_employees'
                
        except Exception as e:
            self.logger.error(f"表类型提取失败: {e}")
            return 'active_employees'

    def _apply_field_mapping(self, headers: List[str]) -> List[str]:
        """🔧 [修复标识] 智能字段映射，支持分页场景下的表头翻译
        
        这个函数会智能地处理字段映射，确保在排序和分页场景下表头能正确显示中文。
        优先使用主窗口的字段映射，如果获取失败则保持原有表头。

        Args:
            headers: 原始表头列表（可能是英文数据库字段名）

        Returns:
            List[str]: 映射后的表头列表（中文显示名）
        """
        try:
            # 获取主窗口的字段映射
            main_window = self._get_main_window()
            if not main_window or not hasattr(main_window, 'dynamic_table_manager'):
                self.logger.debug("🔧 [修复标识] 无法获取主窗口或动态表管理器，保持原有表头")
                return headers
            
            table_manager = main_window.dynamic_table_manager
            table_name = getattr(main_window, 'current_table_name', '')
            
            if not table_name:
                self.logger.debug("🔧 [修复标识] 无法获取当前表名，保持原有表头")
                return headers
            
            # 获取当前表的字段映射
            field_mapping = table_manager._get_dynamic_field_mappings(table_name, headers)
            if not field_mapping:
                self.logger.debug(f"🔧 [修复标识] 表 {table_name} 没有字段映射，保持原有表头")
                return headers
                
            # 应用映射：英文字段名 -> 中文显示名
            mapped_headers = []
            for header in headers:
                mapped_name = field_mapping.get(header, header)
                mapped_headers.append(mapped_name)
                
            self.logger.debug(f"🔧 [修复标识] 字段映射完成: {len(mapped_headers)} 个表头")
            if mapped_headers != headers:
                self.logger.info(f"🔧 [修复标识] 字段映射生效，样例: {headers[:3]} -> {mapped_headers[:3]}")
            
            return mapped_headers
            
        except Exception as e:
            self.logger.error(f"🔧 [修复标识] 字段映射失败: {e}")
            return headers

    def _excel_column_to_db_field(self, excel_column: str) -> str:
        """将Excel列名转换为数据库字段名（与multi_sheet_importer保持一致）

        Args:
            excel_column: Excel列名

        Returns:
            str: 数据库字段名
        """
        # 预定义的映射规则（与multi_sheet_importer保持一致）
        field_mapping = {
            "序号": "sequence_number",
            "工号": "employee_id",
            "人员代码": "employee_id",  # 支持退休/离休表的人员代码字段
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "基本工资": "basic_salary",
            "岗位工资": "position_salary",
            "薪级工资": "grade_salary",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "应发工资": "total_salary",
            "基本离休费": "basic_retirement_salary",
            "基本退休费": "basic_retirement_salary",
            "生活补贴": "living_allowance",
            "住房补贴": "housing_allowance",
            "物业补贴": "property_allowance",
            "护理费": "nursing_fee",
            "补发": "supplement",
            "借支": "advance",
            "备注": "remarks"
        }

        # 1. 直接映射
        if excel_column in field_mapping:
            return field_mapping[excel_column]

        # 2. 处理带年份的字段
        for pattern, db_field in field_mapping.items():
            if pattern in excel_column:
                return db_field

        # 3. 标准化字段名
        return self._normalize_field_name(excel_column)

    def _normalize_field_name(self, field_name: str) -> str:
        """标准化字段名

        Args:
            field_name: 原始字段名

        Returns:
            str: 标准化后的字段名
        """
        import re

        # 移除特殊字符和年份
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '_', field_name)
        normalized = re.sub(r'\d{4}年?', '', normalized)
        normalized = normalized.strip('_').lower()

        # 转换为英文字段名
        chinese_to_english = {
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门": "department",
            "工资": "salary",
            "津贴": "allowance",
            "补贴": "allowance",
            "奖金": "bonus",
            "绩效": "performance"
        }

        for chinese, english in chinese_to_english.items():
            if chinese in normalized:
                normalized = normalized.replace(chinese, english)

        return normalized or "unknown_field"

    def _populate_visible_data(self):
        """填充可见区域数据 - 高性能优化版本"""
        try:
            # 🔧 [递归重绘修复] 防止在绘制过程中重复调用
            if hasattr(self, '_is_populating') and self._is_populating:
                self.logger.warning("🔧 [递归重绘修复] 检测到正在填充数据，跳过重复调用")
                return
                
            self._is_populating = True  # 设置填充锁
            
            start_time = time.time()
            
            row_count = self.model.rowCount()
            col_count = self.model.columnCount()
            visible_rows = min(row_count, self.max_visible_rows)
            
            # 🔧 [递归重绘修复] 完全阻塞所有信号和更新
            self.setUpdatesEnabled(False)  # 禁用窗口更新
            self.blockSignals(True)        # 阻塞信号
            
            try:
                # 🔧 [极致性能优化] 使用批量操作清空表格内容，避免逐个删除
                self.clearContents()
                
                # 🔧 [极致性能优化] 预先批量创建所有 items，减少对象创建开销
                items_matrix = []
                for row in range(visible_rows):
                    row_items = []
                    for col in range(col_count):
                        index = self.model.index(row, col)
                        data = self.model.data(index, Qt.DisplayRole)

                        # 获取列名用于智能格式化
                        column_name = self.model.headers[col] if col < len(self.model.headers) else ''
                        
                        # 使用优化后的格式化方法，传递列名信息
                        formatted_data = self._format_cell_value(data, column_name)
                        
                        # 🔧 [P2-优化] 只在DEBUG模式记录详细格式化信息
                        if row == 0 and column_name in ['工号', '人员代码', '人员类别代码']:
                            self.logger.debug(f"ID字段格式化: {column_name} = {formatted_data}")
                        
                        # 只创建一个 QTableWidgetItem
                        item = QTableWidgetItem(formatted_data)
                        
                        # 🔧 [P1-2修复] 根据数据类型设置对齐方式（增强版）
                        if self._is_numeric_value(data, formatted_data):
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        
                        row_items.append(item)
                    items_matrix.append(row_items)
                
                # 🔧 [递归重绘修复] 批量设置所有items，避免触发重绘
                for row in range(visible_rows):
                    for col in range(col_count):
                        self.setItem(row, col, items_matrix[row][col])
                        
            finally:
                # 🔧 [递归重绘修复] 恢复所有信号和更新
                self.blockSignals(False)       # 恢复信号
                self.setUpdatesEnabled(True)   # 重新启用窗口更新
                self._is_populating = False    # 清除填充锁
            
            elapsed_time = (time.time() - start_time) * 1000
            self.logger.debug(f"🔧 [性能优化] 填充可见数据完成: {visible_rows}x{col_count} 单元格, 耗时: {elapsed_time:.2f}ms")
            
        except Exception as e:
            self.logger.error(f"填充可见数据失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            # 🔧 [递归重绘修复] 确保在异常情况下清除锁
            if hasattr(self, '_is_populating'):
                self._is_populating = False
    
    def _format_cell_value(self, value, column_name: str = '') -> str:
        """
        基于表类型的智能格式化单元格值
        如果数据已预格式化，直接返回
        
        Args:
            value: 单元格原始值
            column_name: 列名，用于判断是否需要特殊格式化
            
        Returns:
            str: 格式化后的字符串
        """
        
        # 🔧 [P0-修复] 增强格式化完整性验证，而不是简单跳过
        if hasattr(self, '_data_pre_formatted') and self._data_pre_formatted:
            # 验证关键字段是否真正格式化完整
            if self._verify_field_formatting_completeness(value, column_name):
                return str(value) if value is not None else ""
            else:
                # 如果验证不通过，清除预格式化标记，重新格式化
                self.logger.warning(f"🔧 [P0-修复] 字段 {column_name} 格式化不完整，重新格式化")
                self._data_pre_formatted = False
        
        # 🆕 [离休人员表专用格式化] 优先处理离休人员表的格式化需求
        if self._is_retired_staff_table():
            return self._format_retired_staff_cell(value, column_name)
        
        # 🔧 [P0-CRITICAL修复] 安全检查格式化管理器属性
        if not hasattr(self, '_format_manager'):
            self._format_manager = None
            self._use_unified_format = False
            self.logger.warning("🔧 [P0-CRITICAL修复] _format_manager属性不存在，已初始化为None")

        if not hasattr(self, '_use_unified_format'):
            self._use_unified_format = False
            self.logger.warning("🔧 [P0-CRITICAL修复] _use_unified_format属性不存在，已初始化为False")

        # 🔧 [P0-性能修复] 使用缓存的格式化管理器并传递正确的table_type
        if self._format_manager is None and not self._use_unified_format:
            try:
                from src.modules.format_management.unified_format_manager import get_unified_format_manager
                self._format_manager = get_unified_format_manager()
                self._use_unified_format = True
                self.logger.info("🔧 [P0-CRITICAL修复] 延迟初始化统一格式管理器成功")
            except ImportError as e:
                self.logger.warning(f"🔧 [P0-CRITICAL修复] 统一格式管理器导入失败: {e}")
                self._format_manager = None
                self._use_unified_format = False
            except Exception as e:
                self.logger.error(f"🔧 [P0-CRITICAL修复] 统一格式管理器初始化失败: {e}")
                self._format_manager = None
                self._use_unified_format = False

        # 🔧 [P0-根本修复] 获取正确的表类型并使用统一格式管理系统
        if self._use_unified_format and self._format_manager:
            try:
                table_type = self._extract_table_type_for_formatting()
                return self._format_manager.format_display_value(value, column_name, table_type)
            except Exception as e:
                self.logger.warning(f"🔧 [P0-CRITICAL修复] 统一格式化管理器调用失败，回退到内置格式化: {e}")
                # 回退到内置格式化
        # 薪级工资字段直接格式化，避免复杂逻辑
        if '薪级工资' in column_name:
            try:
                if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                    return "0.00"
                numeric_value = float(value)
                return f"{numeric_value:.2f}"
            except (ValueError, TypeError):
                return "0.00"
        
        # 🔧 [P0-重复代码清理] 格式化管理器已在上方初始化，移除重复代码
        
        # 🔧 [P0-代码简化] 移除重复逻辑，统一格式化已在上方处理
        # 如果上方的统一格式化失败，回退到内置格式化
        return self._format_cell_value_builtin(value, column_name)
    
    def _verify_field_formatting_completeness(self, value, column_name: str) -> bool:
        """
        🔧 [P0-修复] 验证字段格式化完整性
        
        检查关键字段是否按要求正确格式化：
        - 浮点数字段：是否为两位小数格式
        - 月份字段：是否提取了后两位
        
        Args:
            value: 字段值
            column_name: 字段名
            
        Returns:
            bool: True表示格式化完整，False表示需要重新格式化
        """
        try:
            # 🔧 [用户需求] 检查问题字段的格式化完整性
            problem_float_fields = [
                '2025年基础性绩效', '卫生费', '车补', '2025年奖励性绩效预发',
                '补发', '借支', '2025公积金', '代扣代存养老保险'
            ]
            
            # 检查浮点数字段格式化
            if column_name in problem_float_fields:
                if value is None or value == '':
                    return True  # 空值接受
                
                # 检查是否为两位小数格式
                value_str = str(value)
                if not self._is_two_decimal_format(value_str):
                    self.logger.debug(f"🔧 [格式化验证] 字段 {column_name} 值 '{value_str}' 不符合两位小数格式")
                    return False
            
            # 检查月份字段格式化
            if column_name in ['月份', 'month']:
                if value is None or value == '':
                    return True  # 空值接受
                
                value_str = str(value)
                # 检查是否为2位数字格式
                if not (len(value_str) == 2 and value_str.isdigit()):
                    self.logger.debug(f"🔧 [格式化验证] 月份字段 '{value_str}' 不符合两位数字格式")
                    return False
            
            return True  # 其他字段默认通过验证
            
        except Exception as e:
            self.logger.warning(f"🔧 [格式化验证] 验证失败: {e}")
            return False  # 验证异常时重新格式化
    
    def _is_two_decimal_format(self, value_str: str) -> bool:
        """检查字符串是否为两位小数格式"""
        try:
            # 检查是否为数字格式
            float_val = float(value_str)
            
            # 检查是否有两位小数
            if '.' in value_str:
                decimal_part = value_str.split('.')[-1]
                return len(decimal_part) == 2
            else:
                # 没有小数点的整数不符合两位小数格式
                return False
                
        except (ValueError, TypeError):
            return False
    
    def _format_by_table_config(self, value, column_name: str, config: dict) -> str:
        """
        基于表配置的格式化
        
        Args:
            value: 要格式化的值
            column_name: 列名
            config: 表配置字典
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 🔧 [架构修复] 恢复GUI层独立的格式化逻辑
            # ID字段处理（如果有）
            if column_name in config.get('id_fields', []):
                return self._format_id_display(value)
            
            # 字符串字段：空值显示空白
            elif column_name in config.get('string_fields', []):
                return self._format_string_display(value)
            
            # 浮点数字段：空值显示"0.00"
            elif column_name in config.get('float_fields', []):
                return self._format_float_display(value)
            
            # 特殊字段（月份、年份）
            elif self._is_special_field(column_name, config):
                return self._format_special_display(value, column_name, config)
            
            # 其他字段使用原有逻辑
            return str(value) if value is not None else ''
            
        except Exception as e:
            self.logger.error(f"基于表配置格式化失败: {e}")
            return str(value) if value is not None else ''
    
    def _format_string_display(self, value) -> str:
        """
        字符串字段显示格式化：空值显示为空白
        
        Args:
            value: 要处理的值
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 检查是否为空值（包括None、nan、0、0.0、空字符串、空格等）
            if value in [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL']:
                return ''
            
            # 检查是否为pandas的NaN
            import pandas as pd
            if pd.isna(value):
                return ''
            
            # 检查字符串形式的空值
            if isinstance(value, str):
                cleaned_value = value.strip()
                if cleaned_value.lower() in ['none', 'nan', 'null', '']:
                    return ''
            
            return str(value).strip()
            
        except Exception as e:
            self.logger.warning(f"字符串字段格式化失败: {e}")
            return str(value) if value is not None else ''
    
    def _format_float_display(self, value) -> str:
        """
        浮点数字段显示格式化：空值显示为"0.00"
        
        Args:
            value: 要处理的值
            
        Returns:
            str: 格式化后的字符串，保留两位小数
        """
        try:
            import pandas as pd
            
            # 转换为数值并格式化
            numeric_value = pd.to_numeric(value, errors='coerce')
            
            # 如果无法转换为数值（包括None、nan、空字符串等），显示为"0.00"
            if pd.isna(numeric_value):
                return "0.00"
            
            # 格式化为两位小数
            return f"{numeric_value:.2f}"
            
        except Exception as e:
            self.logger.warning(f"浮点数字段格式化失败: {e}")
            return "0.00"
    
    def _format_id_display(self, value) -> str:
        """
        ID字段显示格式化
        
        Args:
            value: 要处理的值
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 空值处理
            if value in [None, '', 'None', 'nan', 'NaN', 'null', 'NULL']:
                return ''
            
            # 转换为数值处理小数点
            import pandas as pd
            numeric_value = pd.to_numeric(value, errors='coerce')
            if pd.isna(numeric_value):
                return str(value)  # 如果无法转换为数值，返回原值
            
            # 转换为整数去除小数点
            int_value = int(numeric_value)
            
            # 0值显示为空
            if int_value == 0:
                return ''
            
            return str(int_value)
            
        except Exception as e:
            self.logger.warning(f"ID字段格式化失败: {e}")
            return str(value) if value is not None else ''
    
    def _is_special_field(self, column_name: str, config: dict) -> bool:
        """
        检查是否为特殊字段（月份、年份）
        
        Args:
            column_name: 列名
            config: 表配置
            
        Returns:
            bool: 是否为特殊字段
        """
        try:
            special_fields = config.get('special_fields', {})
            month_fields = special_fields.get('month_fields', [])
            year_fields = special_fields.get('year_fields', [])
            
            return column_name in month_fields or column_name in year_fields
            
        except Exception as e:
            self.logger.warning(f"检查特殊字段失败: {e}")
            return False
    
    def _format_special_display(self, value, column_name: str, config: dict) -> str:
        """
        特殊字段格式化（月份、年份）
        
        Args:
            value: 要处理的值
            column_name: 列名
            config: 表配置
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            special_fields = config.get('special_fields', {})
            
            # 月份：提取后两位转字符串
            if column_name in special_fields.get('month_fields', []):
                return self._extract_month_part(value)
            
            # 年份：转字符串
            elif column_name in special_fields.get('year_fields', []):
                return self._format_year_value(value)
            
            return str(value) if value is not None else ''
            
        except Exception as e:
            self.logger.warning(f"特殊字段格式化失败: {e}")
            return str(value) if value is not None else ''
    
    def _extract_month_part(self, value) -> str:
        """
        提取月份部分（后两位）并转换为字符串
        
        Args:
            value: 月份值
            
        Returns:
            str: 格式化后的月份字符串（如："01", "12"）
        """
        try:
            import pandas as pd
            
            if pd.isna(value) or value == '' or value is None:
                return ''
            
            # 转换为字符串并清理
            val_str = str(value).strip()
            
            # 处理各种月份格式
            if val_str.isdigit():
                # 纯数字格式
                if len(val_str) == 1:
                    # 单位月份，前补0
                    return f"0{val_str}"
                elif len(val_str) == 2:
                    # 双位月份，直接返回
                    return val_str
                elif len(val_str) >= 6:
                    # 类似 202501 格式，提取后两位
                    return val_str[-2:]
                else:
                    # 其他数字格式，取后两位
                    return val_str[-2:].zfill(2)
            else:
                # 非纯数字，尝试提取数字部分
                import re
                # 尝试提取最后的1-2位数字
                match = re.search(r'(\d{1,2})(?!.*\d)', val_str)
                if match:
                    month_num = match.group(1)
                    return month_num.zfill(2)  # 确保两位数
                else:
                    return ''
                    
        except Exception as e:
            self.logger.warning(f"月份提取失败: {e}")
            return ''
    
    def _format_year_value(self, value) -> str:
        """
        格式化年份值为字符串
        
        Args:
            value: 年份值
            
        Returns:
            str: 格式化后的年份字符串（如："2025"）
        """
        try:
            import pandas as pd
            
            if pd.isna(value) or value == '' or value is None:
                return ''
            
            # 转换为字符串并清理
            val_str = str(value).strip()
            
            # 处理各种年份格式
            if val_str.replace('.', '').isdigit():
                # 数字格式（可能包含小数点）
                try:
                    year_int = int(float(val_str))
                    # 验证年份范围的合理性（1900-2100）
                    if 1900 <= year_int <= 2100:
                        return str(year_int)
                    else:
                        return str(year_int)  # 仍然返回，但记录警告
                except (ValueError, OverflowError):
                    return ''
            else:
                # 非数字格式，尝试提取年份
                import re
                year_match = re.search(r'(20\d{2}|19\d{2})', val_str)
                if year_match:
                    return year_match.group(1)
                else:
                    return ''
                    
        except Exception as e:
            self.logger.warning(f"年份格式化失败: {e}")
            return ''
    
    def _format_id_field_value(self, value, column_name: str) -> str:
        """
        专门格式化ID字段值（工号、人员代码、人员类别代码）
        
        Args:
            value: 字段值
            column_name: 列名
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 空值处理
            if value in [None, '', 'None', 'nan', 'NaN', 'null', 'NULL']:
                return ''
            
            # 转换为数值处理小数点
            import pandas as pd
            numeric_value = pd.to_numeric(value, errors='coerce')
            if pd.isna(numeric_value):
                return str(value)  # 如果无法转换为数值，返回原值
            
            # 转换为整数去除小数点
            int_value = int(numeric_value)
            
            # 人员类别代码需要两位数格式
            if column_name == '人员类别代码':
                if int_value == 0:
                    return ''  # 0值显示为空
                return str(int_value).zfill(2)
            else:
                # 工号、人员代码
                if int_value == 0:
                    return ''  # 0值显示为空
                return str(int_value)
                
        except Exception as e:
            # 出错时返回原值的字符串形式
            return str(value) if value is not None else ''
    
    def _format_float_field_value(self, value, column_name: str) -> str:
        """
        专门格式化浮点数字段值（住房补贴、车补、补发、借支、应发工资、代扣代存养老保险）
        
        Args:
            value: 字段值
            column_name: 列名
            
        Returns:
            str: 格式化后的字符串，保留两位小数
        """
        try:
            # 按用户新要求：所有浮点数字段的空值和零值都统一显示为"0.00"
            
            # 转换为数值并格式化
            import pandas as pd
            numeric_value = pd.to_numeric(value, errors='coerce')
            
            # 如果无法转换为数值（包括None、nan、空字符串等），显示为"0.00"
            if pd.isna(numeric_value):
                return "0.00"
            
            # 格式化为两位小数
            return f"{numeric_value:.2f}"
                
        except Exception as e:
            # 出错时显示为"0.00"
            return "0.00"
    
    def _render_data_with_fallback_method(self, data: List[Dict[str, Any]], headers: List[str]):
        """
        🔧 [P0-CRITICAL修复] 备用数据渲染方法
        当主渲染器失败时使用的简化渲染方案
        """
        try:
            self.logger.info(f"🔧 [P0-CRITICAL修复] 开始备用渲染: {len(data)}行 x {len(headers)}列")

            # 设置表格基本结构
            self.setRowCount(len(data))
            self.setColumnCount(len(headers))
            # 🔧 [P0-修复] 使用HeaderUpdateManager设置表头，避免重影
            if hasattr(self, 'header_update_manager') and self.header_update_manager:
                self.header_update_manager.update_headers_safe(headers)

            # 简化的数据填充，不使用复杂的格式化
            for row_idx, row_data in enumerate(data):
                for col_idx, header in enumerate(headers):
                    try:
                        # 获取原始值
                        value = row_data.get(header, "")

                        # 简单的值处理
                        if value is None:
                            display_value = ""
                        elif isinstance(value, (int, float)):
                            display_value = str(value)
                        else:
                            display_value = str(value)

                        # 创建表格项
                        item = QTableWidgetItem(display_value)

                        # 简单的对齐设置
                        if isinstance(value, (int, float)):
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

                        self.setItem(row_idx, col_idx, item)

                    except Exception as cell_error:
                        # 单个单元格失败不影响整体渲染
                        self.logger.warning(f"🔧 [P0-CRITICAL修复] 单元格渲染失败 [{row_idx},{col_idx}]: {cell_error}")
                        error_item = QTableWidgetItem("渲染错误")
                        self.setItem(row_idx, col_idx, error_item)

            self.logger.info(f"🔧 [P0-CRITICAL修复] 备用渲染完成: {len(data)}行数据")

        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL修复] 备用渲染方法失败: {e}")
            raise

    def _format_cell_value_builtin(self, value, column_name: str = '') -> str:
        """
        内置的格式化逻辑（备用方案）- 支持表类型感知格式化
        
        Args:
            value: 要处理的值
            column_name: 列名，用于判断是否需要特殊格式化
            
        Returns:
            str: 格式化后的字符串
        """
        # 获取表类型配置
        config = self._get_table_specific_format_config()
        
        if config:
            # 如果有表类型配置，使用基于配置的格式化
            return self._format_by_table_config(value, column_name, config)
        else:
            # 回退到原有的硬编码逻辑
            return self._format_cell_value_builtin_legacy(value, column_name)
    
    def _format_cell_value_builtin_legacy(self, value, column_name: str = '') -> str:
        """🔧 [新架构] 简化的格式化逻辑，移除旧架构兼容代码"""
        
        # 🚨 [关键修复] 检查数据是否已经被格式化
        if isinstance(value, str):
            # 检查是否包含货币符号和格式化标记
            if ('¥' in value and ',' in value) or value.startswith('¥'):
                # 已经格式化过，直接返回
                return value
            
            # 检查是否是纯数字字符串但已经格式化为小数
            if '.' in value and value.replace('.', '').replace(',', '').isdigit():
                try:
                    # 如果是有效的数字格式，且看起来是工资字段，认为已格式化
                    float_val = float(value.replace(',', ''))
                    if ('工资' in column_name or '津贴' in column_name or '补贴' in column_name) and '.' in value:
                        return value
                except ValueError:
                    pass
        
        # 🔧 [修复标识] 内置版本也支持ID字段格式化
        if column_name in ['工号', '人员代码', '人员类别代码']:
            return self._format_id_field_value(value, column_name)
        # 🔧 [关键性能修复] 缓存导入的模块，避免重复导入造成性能问题
        if not hasattr(self, '_pd_module'):
            import pandas as pd
            import numpy as np
            self._pd_module = pd
            self._np_module = np
            # 预定义空值列表，避免每次创建
            self._empty_values = [None, 'None', 'nan', 'NaN', np.nan, 0, 0.0, '', ' ', 'null', 'NULL']
            # 定义需要保留两位小数的字段（与SalaryDataFormatter保持一致）
            self._float_fields = [
                '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', 
                '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴',
                '2025年奖励性绩效预发', '2025公积金',
                '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险',
                # 对应的英文字段名
                'position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance',
                'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 
                'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025',
                'housing_allowance', 'car_allowance', 'supplement', 'advance', 
                'total_salary', 'pension_insurance'
            ]
        
        # 检查是否为空值
        if value in self._empty_values:
            return ''
        
        # 检查是否为pandas的NaN
        if self._pd_module.isna(value):
            return ''
        
        # 检查字符串形式的空值
        if isinstance(value, str):
            cleaned_value = value.strip()
            if cleaned_value.lower() in ['none', 'nan', 'null', '']:
                return ''
        
        # 🔧 [新增] 如果是浮点数字段，格式化为两位小数
        if column_name and column_name in self._float_fields:
            try:
                # 按用户新要求：所有浮点数字段的空值和零值都统一显示为"0.00"
                numeric_value = self._pd_module.to_numeric(value, errors='coerce')
                
                # 如果无法转换为数值（包括None、nan、空字符串等），显示为"0.00"
                if self._pd_module.isna(numeric_value):
                    return "0.00"
                
                # 格式化为两位小数
                return f"{numeric_value:.2f}"
            except Exception:
                # 如果格式化失败，显示为"0.00"
                return "0.00"
        
        # 返回字符串形式的值
        return str(value)
    
    def _populate_visible_data_with_row_numbers(self):
        """填充可见区域数据，使用垂直表头显示行号（不添加序号列）"""
        try:
            # 直接使用标准的数据填充方法，不添加序号列
            self._populate_visible_data()
            
            # 设置垂直表头显示行号
            self._setup_row_numbers()
            
        except Exception as e:
            self.logger.error(f"填充可见数据并设置行号失败: {e}")
    
    def _adjust_column_widths_with_row_number(self):
        """调整列宽，现在不再需要特殊处理序号列（使用垂直表头）"""
        try:
            # 现在直接使用标准的列宽调整，不需要特殊处理序号列
            self._adjust_column_widths()
            
        except Exception as e:
            self.logger.error(f"调整列宽失败: {e}")
    
    def _setup_row_numbers(self):
        """设置行号显示（使用垂直表头）- 高性能优化版本"""
        try:
            start_time = time.time()
            # 显示垂直表头（行号）
            vertical_header = self.verticalHeader()
            vertical_header.setVisible(True)
            vertical_header.setDefaultSectionSize(30)  # 设置默认行高
            
            # 🔧 [性能优化] 设置垂直表头的宽度，防止行号挤在一起
            max_row_number = self.rowCount()
            if hasattr(self, 'pagination_state') and self.pagination_state:
                # 分页模式下，计算实际的行号范围
                max_row_number = self.pagination_state.get('end_record', self.rowCount())
            
            # 根据最大行号计算所需宽度
            digits = len(str(max_row_number)) if max_row_number > 0 else 2
            header_width = max(40, digits * 12 + 20)  # 每位数字12px + 20px边距
            vertical_header.setFixedWidth(header_width)
            
            # 🔧 [性能优化] 使用批量模式设置垂直表头样式，避免逐行设置
            # 设置默认的调整模式而不是逐行设置
            vertical_header.setSectionResizeMode(QHeaderView.Fixed)
            
            # 🔧 [性能优化] 生成行号标签并一次性设置
            row_count = self.rowCount()
            if hasattr(self, 'pagination_state') and self.pagination_state:
                start_record = self.pagination_state.get('start_record', 1)
                # 批量生成行号标签
                row_labels = [str(start_record + i) for i in range(row_count)]
                self.logger.debug(f"分页行号设置完成，显示第{start_record}-{start_record + row_count - 1}行")
            else:
                # 非分页模式，使用默认行号（从1开始）
                row_labels = [str(i + 1) for i in range(row_count)]
                self.logger.debug(f"普通行号设置完成，共{row_count}行")
            
            # 🔧 [重影修复] 使用统一管理器设置行号，防止重影
            if hasattr(self, 'pagination_state') and self.pagination_state:
                start_record = self.pagination_state.get('start_record', 1)
                self.header_update_manager.update_row_numbers_safe(start_record, row_count)
            else:
                # 非分页模式从1开始
                self.header_update_manager.update_row_numbers_safe(1, row_count)
            
            # 🔧 [性能优化] 只在需要时设置样式（避免重复设置）
            if not hasattr(self, '_row_header_styled') or not self._row_header_styled:
                vertical_header.setStyleSheet("""
                    QHeaderView::section {
                        background-color: #f5f5f5;
                        border: 1px solid #e0e0e0;
                        border-right: 2px solid #d0d0d0;
                        padding: 4px 8px;
                        font-size: 12px;
                        font-weight: normal;
                        color: #666;
                        text-align: center;
                    }
                    QHeaderView::section:hover {
                        background-color: #e8e8e8;
                    }
                """)
                self._row_header_styled = True
            
            elapsed_time = (time.time() - start_time) * 1000
            self.logger.debug(f"🔧 [性能优化] 行号设置完成: {row_count} 行, 耗时: {elapsed_time:.2f}ms")
            
        except Exception as e:
            self.logger.error(f"设置行号失败: {e}")
    
    def set_pagination_state(self, pagination_state: dict):
        """设置分页状态信息，用于正确显示分页行号

        Args:
            pagination_state: 包含分页信息的字典，应包含：
                - current_page: 当前页码
                - page_size: 每页大小
                - total_records: 总记录数
                - start_record: 当前页起始记录号
                - end_record: 当前页结束记录号
        """
        # 🔧 [闪动修复] 防止重复设置相同的分页状态
        if hasattr(self, '_last_pagination_state') and self._last_pagination_state == pagination_state:
            self.logger.debug(f"🔧 [闪动修复] 跳过重复的分页状态设置: 第{pagination_state.get('current_page', '?')}页")
            return

        self.logger.info("🔍 [表格调试] ================== set_pagination_state 开始 ==================")
        self.logger.info(f"🔍 [表格调试] 收到分页状态参数: {pagination_state}")
        self.logger.info(f"🔍 [表格调试] 当前表格行数: {self.rowCount()}")
        self.logger.info(f"🔍 [表格调试] 当前表格列数: {self.columnCount()}")
        try:
            self.logger.info(f"🔧 [分页行号修复] 接收分页状态: 第{pagination_state.get('current_page', 1)}页, "
                           f"记录{pagination_state.get('start_record', 1)}-{pagination_state.get('end_record', 0)}")
            
            # 验证分页状态参数
            required_keys = ['current_page', 'page_size', 'total_records', 'start_record', 'end_record']
            missing_keys = [key for key in required_keys if key not in pagination_state]
            if missing_keys:
                self.logger.error(f"🔧 [分页行号修复] 缺少必要参数: {missing_keys}")
                return
                
            # 验证参数合理性
            current_page = pagination_state.get('current_page', 1)
            start_record = pagination_state.get('start_record', 1)
            end_record = pagination_state.get('end_record', 0)
            
            if current_page < 1 or start_record < 1 or end_record < start_record:
                self.logger.error(f"🔧 [分页行号修复] 参数无效: 页码={current_page}, 记录范围={start_record}-{end_record}")
                return
            
            self.pagination_state = pagination_state
            self._pagination_mode = True
            
            # 🔧 [P0-修复] 分页时延迟恢复用户列宽设置
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')
                try:
                    # 🔧 [闪动修复] 防止重复恢复列宽，添加恢复标记
                    restore_key = f"{table_name}_pagination_restore"
                    current_time = time.time()

                    # 检查是否在短时间内已经恢复过
                    if hasattr(self, '_last_pagination_restore') and restore_key in self._last_pagination_restore:
                        if current_time - self._last_pagination_restore[restore_key] < 1.0:  # 1秒内不重复恢复
                            self.logger.debug(f"🔧 [闪动修复] 跳过重复的分页列宽恢复: {table_name}")
                            return

                    # 🔧 [闪动修复] 延迟恢复列宽，使用强制恢复忽略缓存
                    def delayed_restore_column_widths():
                        try:
                            self.column_width_manager.restore_column_widths(table_name, force=True)
                            self.logger.info(f"🔧 [闪动修复] 分页时已强制恢复列宽设置: {table_name}")

                            # 记录恢复时间，防止重复
                            if not hasattr(self, '_last_pagination_restore'):
                                self._last_pagination_restore = {}
                            self._last_pagination_restore[restore_key] = current_time

                        except Exception as e:
                            self.logger.warning(f"🔧 [闪动修复] 延迟恢复列宽失败: {e}")

                    # 🔧 [闪动修复] 使用线程安全的延迟恢复，减少延迟时间
                    from src.utils.thread_safe_timer import safe_single_shot
                    success = safe_single_shot(100, delayed_restore_column_widths)  # 减少到100ms
                    if not success:
                        self.logger.warning(f"🔧 [闪动修复] 线程安全定时器失败，立即恢复列宽")
                        delayed_restore_column_widths()

                except Exception as e:
                    self.logger.warning(f"🔧 [P0-修复] 分页列宽恢复失败: {e}")
            
            # 获取当前表格状态
            current_row_count = self.rowCount()
            expected_row_count = end_record - start_record + 1
            
            self.logger.info(f"🔧 [分页行号修复] 表格状态: 当前行数={current_row_count}, 期望行数={expected_row_count}")
            
            # 🔧 [P1级线程安全修复] 如果表格还没有数据，延迟设置行号
            if current_row_count == 0:
                self.logger.info(f"🔧 [分页行号修复] 表格无数据，延迟设置行号")
                
                # 🔧 [P2级导入修复] 线程安全检查和优化延迟调用
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import QThread, QTimer
                is_main_thread = QThread.currentThread() == QApplication.instance().thread()
                
                # 🔧 [P0-CRITICAL修复] 统一使用线程安全的定时器，解决QTimer线程警告
                from src.utils.thread_safe_timer import safe_single_shot
                try:
                    success = safe_single_shot(50, lambda: self._delayed_set_pagination_row_numbers(pagination_state))
                    if success:
                        self.logger.debug("🔧 [P0-修复] 使用线程安全定时器延迟设置分页行号")
                    else:
                        self.logger.warning("🔧 [P0-CRITICAL] 线程安全定时器启动失败，直接设置分页行号")
                        self._delayed_set_pagination_row_numbers(pagination_state)
                except Exception as e:
                    self.logger.error(f"🔧 [P0-CRITICAL] 线程安全定时器异常: {e}，直接设置分页行号")
                    self._delayed_set_pagination_row_numbers(pagination_state)
                return
            
            # 🔧 [关键修复] 立即更新行号
            self._force_update_pagination_row_numbers()

            # 验证行号设置结果
            # 🔧 [重影修复] 移除验证机制，HeaderUpdateManager已确保行号正确

            # 🔧 [闪动修复] 保存当前分页状态，防止重复设置
            self._last_pagination_state = pagination_state.copy()

        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 设置分页状态失败: {e}")
            import traceback
            traceback.print_exc()



    def _update_pagination_row_labels_only(self):
        """🔧 [性能优化] 仅更新分页行号标签，避免重复设置表头样式"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                return
            
            row_count = self.rowCount()
            if row_count <= 0:
                return
            
            start_record = self.pagination_state.get('start_record', 1)
            # 🔧 [P1-统一更新] 统一使用HeaderUpdateManager更新行号
            if hasattr(self, 'header_update_manager'):
                self.header_update_manager.update_row_numbers_safe(start_record, row_count)
                self.logger.debug(f"🔧 [统一更新] 行号已通过管理器更新: 第{start_record}-{start_record + row_count - 1}行")
            else:
                # 降级处理：直接设置行号标签
                row_labels = [str(start_record + i) for i in range(row_count)]
                self.setVerticalHeaderLabels(row_labels)
                self.logger.debug(f"🔧 [降级更新] 分页行号标签已更新: 第{start_record}-{start_record + row_count - 1}行")
            
        except Exception as e:
            self.logger.error(f"更新分页行号标签失败: {e}")

    def _delayed_set_pagination_row_numbers(self, pagination_state: dict):
        """🔧 [分页行号修复] 延迟设置分页行号"""
        try:
            current_row_count = self.rowCount()
            if current_row_count > 0:
                self.logger.info(f"🔧 [分页行号修复] 延迟设置成功，表格行数: {current_row_count}")
                self.pagination_state = pagination_state
                self._pagination_mode = True
                self._force_update_pagination_row_numbers()
                # 🔧 [重影修复] 移除验证机制，HeaderUpdateManager已确保行号正确
            else:
                self.logger.warning(f"🔧 [分页行号修复] 延迟设置失败，表格仍无数据")
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 延迟设置失败: {e}")

    def _force_update_pagination_row_numbers(self):
        """🔧 [分页行号修复] 强制更新分页行号"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                return
            
            row_count = self.rowCount()
            if row_count <= 0:
                return
            
            start_record = self.pagination_state.get('start_record', 1)
            
            # 🔧 [P0-关键修复] 修复空表头问题：只更新行号，不触发表头更新
            if hasattr(self, 'current_headers') and self.current_headers:
                # 如果有有效表头，传递当前表头
                self.header_update_manager.force_update_all(self.current_headers, start_record, row_count)
            else:
                # 否则只更新行号，避免空表头强制更新
                self.header_update_manager.update_row_numbers_safe(start_record, row_count)
            
            self.logger.info(f"🔧 [分页行号修复] 强制更新完成: 起始记录{start_record}, 共{row_count}行")
            
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 强制更新失败: {e}")

    def _verify_pagination_row_numbers(self):
        """🔧 [分页行号修复] 验证分页行号设置结果"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                return
            
            row_count = self.rowCount()
            if row_count <= 0:
                return
            
            start_record = self.pagination_state.get('start_record', 1)
            
            # 检查前5行的行号
            actual_labels = []
            expected_labels = []
            
            for i in range(min(5, row_count)):
                # 获取实际标签
                header_item = self.verticalHeaderItem(i)
                actual_label = header_item.text() if header_item else str(i + 1)
                actual_labels.append(actual_label)
                
                # 期望标签
                expected_label = str(start_record + i)
                expected_labels.append(expected_label)
            
            # 验证结果
            if actual_labels == expected_labels:
                self.logger.info(f"🔧 [分页行号修复] ✅ 验证成功: {actual_labels}")
            else:
                self.logger.error(f"🔧 [分页行号修复] ❌ 验证失败: 期望{expected_labels}, 实际{actual_labels}")
                
                # 🔧 [紧急修复] 如果验证失败，再次尝试设置
                self.logger.info(f"🔧 [分页行号修复] 尝试紧急修复...")
                self._emergency_fix_row_numbers()
                
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 验证失败: {e}")

    def _emergency_fix_row_numbers(self):
        """🔧 [分页行号修复] 紧急修复行号显示"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                self.logger.warning("🔧 [紧急修复] 无分页状态，使用默认行号")
                # 使用默认行号
                row_count = self.rowCount()
                for i in range(row_count):
                    self.setVerticalHeaderItem(i, QTableWidgetItem(str(i + 1)))
                return
            
            row_count = self.rowCount()
            if row_count <= 0:
                self.logger.warning("🔧 [紧急修复] 表格无数据")
                return
            
            start_record = self.pagination_state.get('start_record', 1)
            
            self.logger.info(f"🔧 [紧急修复] 开始修复行号: 起始记录{start_record}, 行数{row_count}")
            
            # 方法1: 使用 setVerticalHeaderItem
            for i in range(row_count):
                row_number = start_record + i
                header_item = QTableWidgetItem(str(row_number))
                self.setVerticalHeaderItem(i, header_item)
            
            # 🔧 [P1-统一更新] 方法2: 统一管理器备用方案
            try:
                if hasattr(self, 'header_update_manager'):
                    self.header_update_manager.update_row_numbers_safe(start_record, row_count)
                    self.logger.debug(f"🔧 [统一更新-备用] 通过管理器更新行号")
                else:
                    # 最后降级：直接设置
                    row_labels = [str(start_record + i) for i in range(row_count)]
                    self.setVerticalHeaderLabels(row_labels)
                    self.logger.debug(f"🔧 [最终降级] 直接设置行号标签")
            except Exception as backup_e:
                self.logger.warning(f"🔧 [紧急修复] 统一管理器备用方案失败: {backup_e}")
            
            # 确保垂直表头可见
            self.verticalHeader().setVisible(True)
            
            # 🔧 [P2-简化事件处理] 移除processEvents，让Qt自然控制事件循环
            self.viewport().update()
            
            # 再次验证
            first_item = self.verticalHeaderItem(0)
            if first_item:
                actual_first = first_item.text()
                expected_first = str(start_record)
                
                if actual_first == expected_first:
                    self.logger.info(f"🔧 [紧急修复] ✅ 成功: 首行号 {actual_first}")
                else:
                    self.logger.error(f"🔧 [紧急修复] ❌ 失败: 期望{expected_first}, 实际{actual_first}")
                    
                    # 🔧 [P2级导入修复] 最后的尝试：直接操作UI
                    from PyQt5.QtWidgets import QApplication
                    from PyQt5.QtCore import QThread, QTimer
                    is_main_thread = QThread.currentThread() == QApplication.instance().thread()
                    
                    # 统一使用线程安全定时器，无需区分主线程和非主线程
                    from src.utils.thread_safe_timer import safe_single_shot
                    safe_single_shot(100, lambda: self._final_row_number_fix(start_record, row_count))
                    self.logger.debug("🔧 [P0-修复] 使用线程安全定时器进行最终行号修复")
            
        except Exception as e:
            self.logger.error(f"🔧 [紧急修复] 修复失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _final_row_number_fix(self, start_record: int, row_count: int):
        """🔧 [最后尝试] 最终行号修复"""
        try:
            self.logger.info(f"🔧 [最后尝试] 最终修复: 起始{start_record}, 行数{row_count}")
            
            # 🔧 [P2-简化事件处理] 确保在主线程中执行，移除processEvents
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [最后尝试] 非主线程，跳过处理")
                return
            
            # 逐行设置
            for i in range(min(row_count, self.rowCount())):
                row_number = start_record + i
                
                # 直接创建并设置表头项
                item = QTableWidgetItem()
                item.setText(str(row_number))
                self.setVerticalHeaderItem(i, item)
            
            self.logger.info(f"🔧 [最后尝试] 完成设置 {min(row_count, self.rowCount())} 行")
            
        except Exception as e:
            self.logger.error(f"🔧 [最后尝试] 失败: {e}")
    def _create_header_item(self, text: str):
        """创建表头项"""
        from PyQt5.QtWidgets import QTableWidgetItem
        item = QTableWidgetItem(text)
        item.setTextAlignment(Qt.AlignCenter)
        return item

    def _post_data_set_row_number_check(self):
        """🔧 [分页行号修复] 数据设置后的行号检查和修复"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                self.logger.debug("🔧 [分页行号修复] 无分页状态，跳过检查")
                return
                
            row_count = self.rowCount()
            if row_count <= 0:
                self.logger.debug("🔧 [分页行号修复] 表格无数据，跳过检查")
                return
                
            start_record = self.pagination_state.get('start_record', 1)
            
            # 检查当前行号是否正确
            first_header = self.verticalHeaderItem(0)
            first_label = first_header.text() if first_header else "1"
            
            expected_first_label = str(start_record)
            
            if first_label != expected_first_label:
                self.logger.warning(f"🔧 [分页行号修复] 检测到行号错误: 期望{expected_first_label}, 实际{first_label}")
                self.logger.info(f"🔧 [分页行号修复] 执行后置修复...")
                
                # 执行修复
                self._force_update_pagination_row_numbers()
                # 🔧 [重影修复] 移除验证机制，HeaderUpdateManager已确保行号正确
            else:
                self.logger.info(f"🔧 [分页行号修复] 数据设置后行号检查通过: {first_label}")
                
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 数据设置后检查失败: {e}")

    def clear_pagination_mode(self):
        """🔧 [修复标识] 清除分页模式，在导航切换时调用"""
        self._pagination_mode = False
        self.pagination_state = None
        self.logger.debug("🔧 [修复标识] 分页模式已清除")
    
    def update_row_numbers(self):
        """更新行号（在排序后调用）"""
        try:
            # 使用垂直表头时，行号会自动更新，但我们可以强制刷新
            self._setup_row_numbers()
            self.logger.debug("行号更新完成")
        except Exception as e:
            self.logger.error(f"更新行号失败: {e}")
    
    def _validate_and_fix_data_consistency(self, data: List[Dict[str, Any]], headers: List[str], current_table_name: str = None):
        """
        🔧 [P0-CRITICAL修复] 验证和修复数据流一致性 - DataFrame安全处理

        Args:
            data: 原始数据
            headers: 原始表头
            current_table_name: 当前表名

        Returns:
            tuple: (修复后的数据, 修复后的表头)
        """
        try:
            # 🔧 [P0-CRITICAL修复] DataFrame类型安全处理
            import pandas as pd

            # 首先处理DataFrame类型，避免"truth value is ambiguous"错误
            if isinstance(data, pd.DataFrame):
                if data.empty:
                    self.logger.info("🔧 [P0-CRITICAL修复] DataFrame为空，跳过一致性验证")
                    return [], headers or []
                # 转换DataFrame为字典列表
                data = data.to_dict('records')
                self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame已转换为列表: {len(data)}行")

            # 安全检查数据和表头
            if not data or not headers:
                self.logger.info("🔧 [P0-CRITICAL修复] 数据或表头为空，跳过一致性验证")
                return data or [], headers or []

            # 使用专业数据流验证器
            if self.data_flow_validator:
                # 提取表类型
                table_type = self._extract_table_type(current_table_name) if current_table_name else None

                # 执行验证
                validation_result = self.data_flow_validator.validate_data_consistency(
                    data=data,
                    headers=headers,
                    table_type=table_type,
                    context=f"set_data_{current_table_name or 'unknown'}"
                )

                # 记录验证结果
                if validation_result.issues:
                    self.logger.warning(f"🔧 [数据验证] 发现{len(validation_result.issues)}个问题")
                    for issue in validation_result.issues:
                        self.logger.warning(f"🔧 [数据验证] - {issue}")

                if validation_result.fixes_applied:
                    self.logger.info(f"🔧 [数据验证] 应用{len(validation_result.fixes_applied)}个修复")
                    for fix in validation_result.fixes_applied:
                        self.logger.info(f"🔧 [数据验证] + {fix}")

                # 使用验证器修复后的数据
                if validation_result.data is not None and validation_result.headers is not None:
                    return validation_result.data, validation_result.headers

            # 降级处理：使用原始验证逻辑
            return self._fallback_data_consistency_check(data, headers)

        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL修复] 数据一致性修复失败: {e}")
            # 确保返回安全的默认值
            safe_data = data if isinstance(data, list) else []
            safe_headers = headers if isinstance(headers, list) else []
            return safe_data, safe_headers

    def _fallback_data_consistency_check(self, data: List[Dict[str, Any]], headers: List[str]):
        """降级数据一致性检查"""
        try:
            data_columns = len(data[0]) if isinstance(data[0], dict) else 0
            header_columns = len(headers)

            self.logger.info(f"🔧 [降级修复] 数据列数: {data_columns}, 表头列数: {header_columns}")

            if data_columns != header_columns:
                self.logger.warning(f"🔧 [降级修复] 检测到列数不匹配，开始修复")

                if data_columns > header_columns:
                    # 数据列多，截断数据
                    fixed_data = []
                    for row in data:
                        if isinstance(row, dict):
                            fixed_row = {key: row.get(key, '') for key in headers if key in row}
                            for header in headers:
                                if header not in fixed_row:
                                    fixed_row[header] = ''
                            fixed_data.append(fixed_row)
                        else:
                            fixed_data.append(row)
                    data = fixed_data

                elif header_columns > data_columns:
                    # 表头多，调整表头
                    if data_columns > 0 and isinstance(data[0], dict):
                        actual_headers = list(data[0].keys())
                        headers = actual_headers[:header_columns]
                        while len(headers) < header_columns:
                            headers.append(f"列{len(headers)+1}")

            return data, headers

        except Exception as e:
            self.logger.error(f"🔧 [降级修复] 降级修复失败: {e}")
            return data, headers

    def _extract_table_type(self, table_name: str) -> str:
        """从表名提取表类型"""
        if not table_name:
            return "unknown"

        table_name_lower = table_name.lower()

        if "active_employees" in table_name_lower or "在职人员" in table_name:
            return "active_employees"
        elif "a_grade_employees" in table_name_lower or "a岗职工" in table_name:
            return "a_grade_employees"
        elif "retired_employees" in table_name_lower or "离休人员" in table_name:
            return "retired_employees"
        elif "pension_employees" in table_name_lower or "退休人员" in table_name:
            return "pension_employees"
        else:
            return "unknown"

    def _adjust_column_widths(self):
        """🔧 [根本修复] 自动调整列宽 - 简化逻辑"""
        try:
            import time
            import json

            # 🔧 [根本修复] 检查是否有用户保存的列宽设置
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')
                config_file = self.column_width_manager.config_file
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            widths_config = json.load(f)
                        if table_name in widths_config:
                            self.logger.debug(f"🔧 [根本修复] 检测到用户保存的列宽设置，跳过默认调整: {table_name}")
                            return
                    except Exception as e:
                        self.logger.debug(f"🔧 [根本修复] 检查保存列宽失败: {e}")

            start_time = time.time()

            col_count = self.columnCount()
            if col_count <= 0:
                self.logger.warning("🔧 [P0-1修复] 表格列数为0，跳过列宽调整")
                return

            header = self.horizontalHeader()

            # 🔧 [P0-1修复] 强化的列数一致性验证和修复
            expected_columns = self._get_expected_column_count()
            if expected_columns > 0 and expected_columns != col_count:
                self.logger.warning(f"🔧 [P0-1修复] 列数不匹配: 期望{expected_columns}列, 实际{col_count}列")
                
                # 立即修复列数不匹配
                if expected_columns < col_count:
                    # 表格列数过多，强制调整
                    self.logger.info(f"🔧 [P0-1修复] 删除多余空白列: {col_count} -> {expected_columns}")
                    self.setColumnCount(expected_columns)
                    col_count = expected_columns
                    self.logger.info(f"🔧 [P0-1修复] 列数修复完成: {col_count}列")
                elif expected_columns > col_count:
                    # 表格列数不足，补齐
                    self.logger.info(f"🔧 [P0-1修复] 补齐缺失列: {col_count} -> {expected_columns}")
                    self.setColumnCount(expected_columns)
                    col_count = expected_columns

            # 🔧 [P0-CRITICAL修复] 检查用户是否有保存的列宽设置
            has_saved_widths = False
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')
                config_file = self.column_width_manager.config_file
                if config_file.exists():
                    try:
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            widths_config = json.load(f)
                        has_saved_widths = table_name in widths_config
                        if has_saved_widths:
                            self.logger.info(f"🔧 [P0-CRITICAL修复] 检测到用户保存的列宽设置: {table_name}，跳过默认列宽设置")
                    except Exception as e:
                        self.logger.debug(f"🔧 [P0-CRITICAL修复] 检查保存列宽失败: {e}")

            # 🔧 [P0-CRITICAL修复] 只有在没有用户保存列宽时才应用默认设置
            if not has_saved_widths:
                # 🔧 [极致性能优化] 检查是否需要重新设置列宽（避免重复设置）
                if not hasattr(self, '_column_widths_set') or not self._column_widths_set:
                    # 🔧 [排序修复] 安全设置列宽模式，防止索引越界
                    try:
                        # 先重置所有列为交互模式
                        for col in range(col_count):
                            header.setSectionResizeMode(col, QHeaderView.Interactive)

                        # 为前几列设置固定宽度，最后一列自动拉伸
                        for col in range(col_count - 1):
                            header.setSectionResizeMode(col, QHeaderView.ResizeToContents)

                        # 最后一列拉伸填充
                        if col_count > 0:
                            header.setSectionResizeMode(col_count - 1, QHeaderView.Stretch)

                        # 标记列宽已设置
                        self._column_widths_set = True

                        elapsed_time = (time.time() - start_time) * 1000
                        self.logger.debug(f"🔧 [性能优化] 列宽设置完成: {col_count} 列, 耗时: {elapsed_time:.2f}ms")

                    except Exception as width_error:
                        self.logger.error(f"🔧 [排序修复] 列宽设置失败: {width_error}")
                        # 降级处理：使用最简单的列宽设置
                        try:
                            header.setStretchLastSection(True)
                            self.logger.info("🔧 [排序修复] 使用降级列宽设置")
                        except:
                            pass
                else:
                    self.logger.debug("🔧 [性能优化] 跳过列宽设置（已设置过）")
            else:
                self.logger.info("🔧 [P0-CRITICAL修复] 用户有保存的列宽设置，跳过默认列宽调整")

        except Exception as e:
            self.logger.error(f"🔧 [P0-1修复] 调整列宽失败: {e}")
            
    def _get_expected_column_count(self) -> int:
        """
        🔧 [P0-1修复] 获取期望的列数
        多重验证确保准确性
        """
        try:
            expected_count = 0
            
            # 方法1: 从当前数据获取
            if hasattr(self, '_current_data') and self._current_data:
                if isinstance(self._current_data[0], dict):
                    expected_count = len(self._current_data[0])
                elif hasattr(self._current_data[0], '__len__'):
                    expected_count = len(self._current_data[0])
                    
            # 方法2: 从表头缓存获取
            if expected_count == 0 and hasattr(self, 'current_headers') and self.current_headers:
                expected_count = len(self.current_headers)
                
            # 方法3: 从实际表头获取
            if expected_count == 0:
                for i in range(self.columnCount()):
                    if self.horizontalHeaderItem(i) and self.horizontalHeaderItem(i).text():
                        expected_count = i + 1
                    else:
                        break
                        
            self.logger.debug(f"🔧 [P0-1修复] 期望列数: {expected_count}")
            return expected_count
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-1修复] 获取期望列数失败: {e}")
            return self.columnCount()  # fallback

    def _adjust_column_widths_delayed(self):
        """
        🔧 [P0-1修复] 延迟执行的列宽调整
        最后的防线，确保列数完全正确
        """
        try:
            self.logger.debug("🔧 [P0-1修复] 开始延迟列宽调整")

            # 最终验证列数一致性
            expected_columns = self._get_expected_column_count()
            col_count = self.columnCount()
            
            if expected_columns > 0 and expected_columns != col_count:
                self.logger.warning(f"🔧 [P0-1修复] 延迟调整时发现列数不匹配: 期望{expected_columns}列, 实际{col_count}列")
                # 最后一次修复机会
                self.setColumnCount(expected_columns)
                col_count = expected_columns
                self.logger.info(f"🔧 [P0-1修复] 最终列数修复: {col_count}列")

            # 🔧 [P0-修复] 统一列宽管理：先恢复用户设置，再进行必要调整
            # 步骤1：恢复用户自定义的列宽
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')
                self.column_width_manager.restore_column_widths(table_name)
                self.logger.info(f"🔧 [P0-修复] 用户列宽设置已恢复: {table_name}")

            # 步骤2：检查是否有保存的列宽设置
            has_saved_widths = False
            if hasattr(self, 'column_width_manager') and self.column_width_manager:
                table_name = getattr(self, 'current_table_name', 'default_table')
                config_file = self.column_width_manager.config_file
                if config_file.exists():
                    try:
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            widths_config = json.load(f)
                        has_saved_widths = table_name in widths_config
                    except:
                        pass

            # 步骤3：只有在没有用户保存的列宽时才应用默认调整
            if not has_saved_widths:
                self._adjust_column_widths()
                self.logger.debug("🔧 [P0-修复] 无用户设置，应用默认列宽")
            else:
                self.logger.info("🔧 [P0-修复] 使用用户保存的列宽，跳过默认调整")

            self.logger.debug("🔧 [P0-修复] 统一延迟列宽调整完成")

        except Exception as e:
            self.logger.error(f"🔧 [排序修复] 延迟列宽调整失败: {e}")
    
    def _adjust_column_widths_delayed_with_restore(self):
        """
        🔧 [P0-修复] 延迟执行的列宽调整 - 向后兼容方法，现在统一调用标准方法
        """
        # 🔧 [P0-修复] 统一调用标准的列宽调整方法，确保行为一致
        self._adjust_column_widths_delayed()
        self.logger.debug("🔧 [P0-修复] 向后兼容：调用统一的列宽调整方法")

    def _on_cell_clicked(self, row: int, column: int):
        """处理单击事件 - 支持多选"""
        try:
            current_time = time.time() * 1000  # 转换为毫秒
            
            # 记录点击信息用于双击判断
            self.last_click_time = current_time
            self.last_click_pos = (row, column)
            
            # 获取修饰键状态
            modifiers = QApplication.keyboardModifiers()
            ctrl_pressed = modifiers & Qt.ControlModifier
            shift_pressed = modifiers & Qt.ShiftModifier
            
            # 根据修饰键状态决定选择行为
            if self.multi_select_enabled and ctrl_pressed:
                self._handle_ctrl_click(row)
            elif self.multi_select_enabled and shift_pressed:
                self._handle_shift_click(row)
            else:
                self._handle_normal_click(row, column)
            
            # 更新选择状态
            self._update_selection_display()
            
            self.logger.debug(f"单击单元格: 行{row}, 列{column}, 修饰键: Ctrl={ctrl_pressed}, Shift={shift_pressed}")
            
        except Exception as e:
            self.logger.error(f"处理单击事件失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_normal_click(self, row: int, column: int):
        """处理普通单击 - 单选"""
        try:
            # 清除之前的选择
            self.selected_rows_set.clear()
            self.clearSelection()
            
            # 选择当前行
            self.selected_rows_set.add(row)
            self.selection_anchor = row
            self.last_selected_row = row
            
            # 如果点击第0列，处理展开/折叠
            if column == 0:
                self.toggle_row_expansion(row)
            
        except Exception as e:
            self.logger.error(f"处理普通单击失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_ctrl_click(self, row: int):
        """处理Ctrl+单击 - 非连续多选"""
        try:
            if row in self.selected_rows_set:
                # 如果已选中，则取消选择
                self.selected_rows_set.remove(row)
            else:
                # 如果未选中，则添加选择
                self.selected_rows_set.add(row)
                self.selection_anchor = row
            
            self.last_selected_row = row
            
            self.logger.debug(f"Ctrl+单击: 行{row}, 当前选中: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"处理Ctrl+单击失败: 行{row}, 错误: {e}")
    
    def _handle_shift_click(self, row: int):
        """处理Shift+单击 - 连续范围选择"""
        try:
            if self.selection_anchor == -1:
                # 没有锚点，当作普通单击
                self._handle_normal_click(row, 0)
                return
            
            # 确定选择范围
            start_row = min(self.selection_anchor, row)
            end_row = max(self.selection_anchor, row)
            
            # 清除之前的选择
            self.selected_rows_set.clear()
            
            # 选择范围内的所有行
            for r in range(start_row, end_row + 1):
                if r < self.rowCount():
                    self.selected_rows_set.add(r)
            
            self.last_selected_row = row
            
            self.logger.debug(f"Shift+单击: 范围 {start_row}-{end_row}, 选中: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"处理Shift+单击失败: 行{row}, 错误: {e}")
    
    def _update_selection_display(self):
        """更新选择显示状态"""
        try:
            # 清除当前选择显示
            self.clearSelection()
            
            # 设置选中行的视觉状态
            for row in self.selected_rows_set:
                if row < self.rowCount():
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        if item:
                            item.setSelected(True)
            
            # 发出选择变化信号
            selected_list = list(self.selected_rows_set)
            self.selection_changed.emit(selected_list)
            
        except Exception as e:
            self.logger.error(f"更新选择显示失败: {e}")
    
    def select_all_rows(self):
        """全选所有行"""
        try:
            self.selected_rows_set.clear()
            for row in range(self.rowCount()):
                self.selected_rows_set.add(row)
            
            self._update_selection_display()
            self.logger.debug(f"全选: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"全选失败: {e}")
    
    def clear_selection(self):
        """清除所有选择"""
        try:
            self.selected_rows_set.clear()
            self.clearSelection()
            self.selection_anchor = -1
            self.last_selected_row = -1
            
            self.selection_changed.emit([])
            self.logger.debug("清除所有选择")
            
        except Exception as e:
            self.logger.error(f"清除选择失败: {e}")
    
    def get_selected_rows_count(self) -> int:
        """获取选中行数量"""
        return len(self.selected_rows_set)
    
    def get_selected_rows_list(self) -> List[int]:
        """获取选中行列表"""
        return sorted(list(self.selected_rows_set))
    
    def is_row_selected(self, row: int) -> bool:
        """检查行是否被选中"""
        return row in self.selected_rows_set
    
    def set_multi_select_enabled(self, enabled: bool):
        """设置多选模式是否启用"""
        try:
            self.multi_select_enabled = enabled
            if not enabled:
                # 禁用多选时，清除多选状态
                if len(self.selected_rows_set) > 1:
                    # 保留最后选择的行
                    if self.last_selected_row != -1:
                        self.selected_rows_set = {self.last_selected_row}
                    else:
                        self.clear_selection()
                    self._update_selection_display()
            
            self.logger.debug(f"多选模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置多选模式失败: {e}")
    
    def get_batch_operation_mode(self) -> bool:
        """获取批量操作模式状态"""
        return self.batch_operation_mode
    
    def set_batch_operation_mode(self, enabled: bool):
        """设置批量操作模式"""
        try:
            self.batch_operation_mode = enabled
            
            # 批量操作模式下的视觉提示
            if enabled:
                self.setStyleSheet("""
                    QTableWidget::item:selected {
                        background-color: #ffeb3b;
                        color: #1976d2;
                        border: 2px solid #ff9800;
                    }
                """)
            else:
                # 恢复正常选择样式
                self.setStyleSheet("")
            
            self.logger.debug(f"批量操作模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置批量操作模式失败: {e}")
    
    def _on_cell_double_clicked(self, row: int, column: int):
        """处理双击事件 - 智能编辑/展开切换"""
        try:
            current_time = time.time() * 1000
            
            # 检查是否是有效的双击
            if (self.last_click_pos == (row, column) and 
                current_time - self.last_click_time < self.edit_trigger_delay):
                
                # 决定是编辑还是展开
                if self._should_edit_cell(row, column):
                    self._start_cell_editing(row, column)
                else:
                    self._handle_row_expansion(row, column)
            else:
                # 单独的双击，默认行为
                self._handle_row_expansion(row, column)
            
        except Exception as e:
            self.logger.error(f"处理双击事件失败: 行{row}, 列{column}, 错误: {e}")
    
    def _should_edit_cell(self, row: int, column: int) -> bool:
        """判断是否应该编辑单元格"""
        try:
            # 检查编辑模式是否启用
            if not self.edit_mode_enabled:
                return False
            
            # 检查是否有其他单元格正在编辑
            if self.current_edit_cell is not None:
                return False
            
            # 检查列是否可编辑 (第0列通常是展开/折叠控制列)
            if column == 0:
                return False
            
            # 检查单元格内容是否可编辑
            item = self.item(row, column)
            if item and item.flags() & Qt.ItemIsEditable:
                return True
            
            return True  # 默认可编辑
            
        except Exception as e:
            self.logger.error(f"判断单元格是否可编辑失败: 行{row}, 列{column}, 错误: {e}")
            return False
    
    def _start_cell_editing(self, row: int, column: int):
        """开始单元格编辑"""
        try:
            # 设置当前编辑单元格
            self.current_edit_cell = (row, column)
            
            # 获取原始值
            item = self.item(row, column)
            original_value = item.text() if item else ""
            
            # 开始编辑
            self.editItem(item)
            
            # 发出编辑模式变化信号
            self.edit_mode_changed.emit(True)
            
            self.logger.debug(f"开始编辑单元格: 行{row}, 列{column}, 原值: {original_value}")
            
        except Exception as e:
            self.logger.error(f"开始单元格编辑失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_row_expansion(self, row: int, column: int):
        """处理行展开/折叠"""
        try:
            # 如果在第0列或者不可编辑列，直接切换展开状态
            self.toggle_row_expansion(row)
            
        except Exception as e:
            self.logger.error(f"处理行展开失败: 行{row}, 列{column}, 错误: {e}")
    
    def _on_selection_changed(self):
        """处理选择变化"""
        try:
            selected_rows = []
            for item in self.selectedItems():
                if item.row() not in selected_rows:
                    selected_rows.append(item.row())
            
            self.selected_rows = set(selected_rows)
            self.selection_changed.emit(selected_rows)
            
        except Exception as e:
            self.logger.error(f"处理选择变化失败: {e}")
    
    def toggle_row_expansion(self, row: int) -> bool:
        """
        切换行展开状态
        
        Args:
            row: 行索引
            
        Returns:
            bool: 操作是否成功
        """
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 检查动画状态
            if self.animation_manager.is_animating(row_data.id):
                self.logger.debug(f"行 {row} 正在动画中，跳过操作")
                return False
            
            # 根据当前状态决定操作
            if row_data.expand_state == ExpandState.COLLAPSED:
                return self._expand_row(row)
            elif row_data.expand_state == ExpandState.EXPANDED:
                return self._collapse_row(row)
            
            return False
            
        except Exception as e:
            self.logger.error(f"切换行展开状态失败: 行{row}, 错误: {e}")
            return False
    
    def _expand_row(self, row: int) -> bool:
        """展开指定行"""
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 更新状态
            self.model.update_expand_state(
                row, ExpandState.EXPANDING, 0, 0.0
            )
            self.expanded_rows.add(row)
            
            # 启动展开动画
            success = self.animation_manager.start_expand_animation(
                row_data.id,
                self.expanded_row_height,
                lambda row_id, progress: self._update_expand_progress(row, progress)
            )
            
            if success:
                self.logger.debug(f"开始展开行: {row}")
                return True
            else:
                # 动画启动失败，回退状态
                self.model.update_expand_state(
                    row, ExpandState.COLLAPSED, 0, 0.0
                )
                self.expanded_rows.discard(row)
                return False
                
        except Exception as e:
            self.logger.error(f"展开行失败: 行{row}, 错误: {e}")
            return False
    
    def _collapse_row(self, row: int) -> bool:
        """折叠指定行"""
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 更新状态
            self.model.update_expand_state(
                row, ExpandState.COLLAPSING, row_data.expand_height, 1.0
            )
            
            # 启动折叠动画
            success = self.animation_manager.start_collapse_animation(
                row_data.id,
                row_data.expand_height,
                lambda row_id, progress: self._update_collapse_progress(row, progress)
            )
            
            if success:
                self.logger.debug(f"开始折叠行: {row}")
                return True
            else:
                # 动画启动失败，保持展开状态
                self.model.update_expand_state(
                    row, ExpandState.EXPANDED, self.expanded_row_height, 1.0
                )
                return False
                
        except Exception as e:
            self.logger.error(f"折叠行失败: 行{row}, 错误: {e}")
            return False
    
    def _update_expand_progress(self, row: int, progress: float):
        """更新展开进度"""
        try:
            current_height = int(self.expanded_row_height * progress)
            self.model.update_expand_state(
                row, ExpandState.EXPANDING, current_height, progress
            )
            
            # 更新行高
            self.setRowHeight(row, self.default_row_height + current_height)
            
        except Exception as e:
            self.logger.error(f"更新展开进度失败: 行{row}, 进度{progress}, 错误: {e}")
    
    def _update_collapse_progress(self, row: int, progress: float):
        """更新折叠进度"""
        try:
            current_height = int(self.expanded_row_height * (1.0 - progress))
            self.model.update_expand_state(
                row, ExpandState.COLLAPSING, current_height, progress
            )
            
            # 更新行高
            self.setRowHeight(row, self.default_row_height + current_height)
            
        except Exception as e:
            self.logger.error(f"更新折叠进度失败: 行{row}, 进度{progress}, 错误: {e}")
    
    def _on_animation_finished(self, row_id: str, final_state: ExpandState):
        """动画完成处理"""
        try:
            # 查找对应的行索引
            row = -1
            for i, row_data in enumerate(self.model.row_data):
                if row_data.id == row_id:
                    row = i
                    break
            
            if row < 0:
                return
            
            # 更新最终状态
            if final_state == ExpandState.EXPANDED:
                self.model.update_expand_state(
                    row, ExpandState.EXPANDED, self.expanded_row_height, 1.0
                )
                self.setRowHeight(row, self.default_row_height + self.expanded_row_height)
                self.row_expanded.emit(row, self.model.get_row_data(row).detail_data)
                
            elif final_state == ExpandState.COLLAPSED:
                self.model.update_expand_state(
                    row, ExpandState.COLLAPSED, 0, 0.0
                )
                self.setRowHeight(row, self.default_row_height)
                self.expanded_rows.discard(row)
                self.row_collapsed.emit(row, self.model.get_row_data(row).main_data)
            
            self.logger.debug(f"动画完成处理: {row_id}, 最终状态: {final_state.value}")
            
        except Exception as e:
            self.logger.error(f"动画完成处理失败: {row_id}, 错误: {e}")
    
    def get_expanded_rows(self) -> List[int]:
        """获取所有展开的行"""
        return list(self.expanded_rows)
    
    def get_selected_rows(self) -> List[int]:
        """获取所有选中的行"""
        return list(self.selected_rows)
    
    def expand_all_rows(self):
        """展开所有行"""
        for row in range(self.rowCount()):
            if row not in self.expanded_rows:
                self.toggle_row_expansion(row)
    
    def collapse_all_rows(self):
        """折叠所有行"""
        for row in list(self.expanded_rows):
            self.toggle_row_expansion(row)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'expanded_rows_count': len(self.expanded_rows),
            'total_rows': self.rowCount(),
            'max_visible_rows': self.max_visible_rows,
            'animation_stats': self.animation_manager.performance_stats,
            'memory_usage': {
                'data_cache_size': len(self.model.data_cache),
                'active_animations': len(self.animation_manager.active_animations)
            }
        }

    def set_max_visible_rows(self, max_rows: int) -> bool:
        """
        设置最大可见行数
        
        Args:
            max_rows: 最大可见行数，必须大于0
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if max_rows <= 0:
                self.logger.warning(f"最大可见行数必须大于0，当前值: {max_rows}")
                return False
            
            # 🔧 [P1修复] 强制最小值为10，防止UI异常
            if max_rows < 10:
                self.logger.warning(f"🔧 [P1修复] 最大可见行数过小({max_rows})，自动调整为最小安全值10")
                max_rows = 10
            
            old_value = self.max_visible_rows
            self.max_visible_rows = max_rows
            
            # 如果新值小于旧值，需要重新填充可见数据
            if max_rows < old_value:
                self._populate_visible_data()
            
            self.logger.info(f"最大可见行数已更新: {old_value} -> {max_rows}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置最大可见行数失败: {e}")
            return False
    
    def get_max_visible_rows(self) -> int:
        """
        获取当前最大可见行数
        
        Returns:
            int: 当前最大可见行数
        """
        return self.max_visible_rows
    
    def auto_adjust_max_visible_rows(self, data_count: int) -> bool:
        """
        根据数据量自动调整最大可见行数
        
        Args:
            data_count: 数据总行数
            
        Returns:
            bool: 调整是否成功
        """
        try:
            # 如果数据量为0，不进行调整，保持当前设置
            if data_count <= 0:
                self.logger.info(f"数据量为{data_count}，不调整最大可见行数，保持当前值: {self.max_visible_rows}")
                return True
            
            # 🔧 [P1修复] 改进自动调整算法，确保最小值安全
            # - 小于100行：显示全部，但不少于10行
            # - 100-1000行：显示50%，但不少于50行
            # - 1000-10000行：显示10%，但不少于100行
            # - 大于10000行：最多显示1000行
            
            if data_count <= 100:
                new_max = max(10, data_count)  # 🔧 [P1修复] 确保最小10行
            elif data_count <= 1000:
                new_max = max(50, data_count // 2)  # 🔧 [P1修复] 确保最小50行
            elif data_count <= 10000:
                new_max = max(100, data_count // 10)  # 🔧 [P1修复] 确保最小100行
            else:
                new_max = 1000
            
            # 确保不超过原设定的上限
            original_max = 1000  # 原始默认值
            new_max = min(new_max, original_max)
            
            success = self.set_max_visible_rows(new_max)
            if success:
                self.logger.info(f"根据数据量({data_count})自动调整最大可见行数为: {new_max}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"自动调整最大可见行数失败: {e}")
            return False
    
    def _on_edit_finished(self, row: int, column: int, value: Any, is_valid: bool):
        """处理编辑完成"""
        try:
            # 获取原始值
            item = self.item(row, column)
            old_value = item.text() if item else ""
            
            if is_valid and value is not None:
                # 更新单元格值
                if item:
                    item.setText(str(value))
                else:
                    new_item = QTableWidgetItem(str(value))
                    self.setItem(row, column, new_item)
                
                # 发出编辑完成信号
                self.cell_edited.emit(row, column, old_value, value)
                
                self.logger.debug(f"单元格编辑完成: 行{row}, 列{column}, 旧值: {old_value}, 新值: {value}")
            else:
                self.logger.debug(f"单元格编辑取消: 行{row}, 列{column}")
            
            # 清除编辑状态
            self.current_edit_cell = None
            self.edit_mode_changed.emit(False)
            
        except Exception as e:
            self.logger.error(f"处理编辑完成失败: 行{row}, 列{column}, 错误: {e}")

    def set_edit_mode_enabled(self, enabled: bool):
        """设置编辑模式是否启用"""
        try:
            self.edit_mode_enabled = enabled
            self.logger.debug(f"编辑模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置编辑模式失败: {e}")
    
    def is_editing(self) -> bool:
        """检查是否正在编辑"""
        return self.current_edit_cell is not None
    
    def cancel_current_edit(self):
        """取消当前编辑"""
        try:
            if self.current_edit_cell:
                row, column = self.current_edit_cell
                self.closePersistentEditor(self.item(row, column))
                self.current_edit_cell = None
                self.edit_mode_changed.emit(False)
                self.logger.debug("取消当前编辑")
                
        except Exception as e:
            self.logger.error(f"取消编辑失败: {e}")
    
    # ==================== 批量操作功能 ====================
    
    def batch_edit_cells(self, column: int, new_value: Any) -> bool:
        """
        批量编辑选中行的指定列
        
        Args:
            column: 要编辑的列索引
            new_value: 新值
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量编辑")
                return False
            
            affected_rows = []
            
            # 对每个选中的行进行编辑
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount() and column < self.columnCount():
                    # 获取原值
                    item = self.item(row, column)
                    old_value = item.text() if item else ""
                    
                    # 设置新值
                    if item:
                        item.setText(str(new_value))
                    else:
                        new_item = QTableWidgetItem(str(new_value))
                        self.setItem(row, column, new_item)
                    
                    # 发出编辑信号
                    self.cell_edited.emit(row, column, old_value, new_value)
                    affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_edit", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量编辑完成: 列{column}, 影响{len(affected_rows)}行, 新值: {new_value}")
            return True
            
        except Exception as e:
            self.logger.error(f"批量编辑失败: 列{column}, 新值{new_value}, 错误: {e}")
            return False
    
    def batch_delete_rows(self) -> bool:
        """
        批量删除选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量删除")
                return False
            
            # 获取要删除的行列表，按降序排列（从后往前删除）
            rows_to_delete = sorted(self.selected_rows_set, reverse=True)
            affected_rows = rows_to_delete.copy()
            
            # 删除行
            for row in rows_to_delete:
                if row < self.rowCount():
                    self.removeRow(row)
            
            # 清除选择状态
            self.clear_selection()
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_delete", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量删除完成: 删除{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量删除失败: {e}")
            return False
    
    def batch_expand_rows(self) -> bool:
        """
        批量展开选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量展开")
                return False
            
            affected_rows = []
            
            # 展开所有选中的行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = self.model.get_row_data(row)
                    if row_data and row_data.expand_state == ExpandState.COLLAPSED:
                        if self._expand_row(row):
                            affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_expand", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量展开完成: 展开{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量展开失败: {e}")
            return False
    
    def batch_collapse_rows(self) -> bool:
        """
        批量折叠选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量折叠")
                return False
            
            affected_rows = []
            
            # 折叠所有选中的行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = self.model.get_row_data(row)
                    if row_data and row_data.expand_state == ExpandState.EXPANDED:
                        if self._collapse_row(row):
                            affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_collapse", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量折叠完成: 折叠{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量折叠失败: {e}")
            return False
    
    def batch_copy_data(self) -> List[List[str]]:
        """
        批量复制选中行的数据
        
        Returns:
            List[List[str]]: 复制的数据矩阵
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量复制")
                return []
            
            copied_data = []
            
            # 复制所有选中行的数据
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = []
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        cell_value = item.text() if item else ""
                        row_data.append(cell_value)
                    copied_data.append(row_data)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_copy", len(copied_data), list(self.selected_rows_set))
            
            self.logger.info(f"批量复制完成: 复制{len(copied_data)}行数据")
            return copied_data
            
        except Exception as e:
            self.logger.error(f"批量复制失败: {e}")
            return []
    
    def batch_export_data(self, include_headers: bool = True) -> Dict[str, Any]:
        """
        批量导出选中行的数据
        
        Args:
            include_headers: 是否包含表头
            
        Returns:
            Dict: 导出的数据结构
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量导出")
                return {}
            
            export_data = {
                'headers': [],
                'rows': [],
                'metadata': {
                    'export_time': time.time(),
                    'total_rows': len(self.selected_rows_set),
                    'selected_rows': sorted(list(self.selected_rows_set))
                }
            }
            
            # 添加表头
            if include_headers:
                for column in range(self.columnCount()):
                    header_item = self.horizontalHeaderItem(column)
                    header_text = header_item.text() if header_item else f"列{column}"
                    export_data['headers'].append(header_text)
            
            # 添加数据行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = []
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        cell_value = item.text() if item else ""
                        row_data.append(cell_value)
                    export_data['rows'].append(row_data)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_export", len(export_data['rows']), list(self.selected_rows_set))
            
            self.logger.info(f"批量导出完成: 导出{len(export_data['rows'])}行数据")
            return export_data
            
        except Exception as e:
            self.logger.error(f"批量导出失败: {e}")
            return {}
    
    def get_batch_operation_stats(self) -> Dict[str, Any]:
        """获取批量操作统计信息"""
        return {
            'enabled_operations': [
                'edit', 'delete', 'copy', 'export', 'expand', 'collapse'
            ],
            'multi_select_enabled': self.multi_select_enabled,
            'batch_mode_enabled': self.batch_operation_mode,
            'selected_count': len(self.selected_rows_set),
            'max_batch_size': 1000,  # 最大批量处理数量
        }
    
    # ==================== 右键菜单系统方法 ====================
    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """处理右键菜单事件"""
        try:
            if not self.context_menu_enabled:
                super().contextMenuEvent(event)
                return
            
            # 记录右键点击信息
            self.last_right_click_pos = event.pos()
            self.last_right_click_time = time.time() * 1000
            
            # 确定点击的位置和上下文
            context = self._determine_context_menu_context(event.pos())
            
            if context:
                # 发出右键菜单请求信号
                self.context_menu_requested.emit(context, event.globalPos())
                
                # 显示右键菜单
                self.context_menu.show_context_menu(context, event.globalPos())
                
                self.logger.debug(f"显示右键菜单: {context.menu_type.value}")
            else:
                # 如果无法确定上下文，显示默认菜单
                super().contextMenuEvent(event)
                
        except Exception as e:
            self.logger.error(f"处理右键菜单事件失败: {e}")
            super().contextMenuEvent(event)
    
    def _determine_context_menu_context(self, position: QPoint) -> Optional[ContextMenuContext]:
        """确定右键菜单上下文"""
        try:
            # 获取点击的项目
            item = self.itemAt(position)
            
            if item is None:
                # 点击空白区域
                return ContextMenuContext(
                    menu_type=ContextMenuType.EMPTY_AREA,
                    clicked_row=-1,
                    clicked_column=-1,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=self.is_editing()
                )
            
            # 获取点击的行和列
            clicked_row = item.row()
            clicked_column = item.column()
            
            # 检查是否在表头区域
            header_height = self.horizontalHeader().height()
            if position.y() <= header_height:
                return ContextMenuContext(
                    menu_type=ContextMenuType.HEADER,
                    clicked_row=-1,
                    clicked_column=clicked_column,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=self.is_editing()
                )
            
            # 检查当前编辑状态
            if self.is_editing() and self.current_edit_cell == (clicked_row, clicked_column):
                return ContextMenuContext(
                    menu_type=ContextMenuType.CELL_EDIT,
                    clicked_row=clicked_row,
                    clicked_column=clicked_column,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=True
                )
            
            # 检查行的展开状态
            row_expanded = self._is_row_expanded(clicked_row)
            
            # 确定菜单类型
            selected_rows = self.get_selected_rows_list()
            if len(selected_rows) > 1:
                menu_type = ContextMenuType.MULTI_ROW
            elif row_expanded:
                menu_type = ContextMenuType.EXPANDED_ROW
            else:
                menu_type = ContextMenuType.SINGLE_ROW
            
            return ContextMenuContext(
                menu_type=menu_type,
                clicked_row=clicked_row,
                clicked_column=clicked_column,
                selected_rows=selected_rows,
                click_position=position,
                is_editing=self.is_editing(),
                row_expanded=row_expanded
            )
            
        except Exception as e:
            self.logger.error(f"确定右键菜单上下文失败: {e}")
            return None
    
    def _is_row_expanded(self, row: int) -> bool:
        """检查行是否已展开"""
        try:
            if hasattr(self, 'model') and self.model:
                row_data = self.model.get_row_data(row)
                if row_data:
                    return row_data.expand_state == ExpandState.EXPANDED
            return row in self.expanded_rows
        except Exception as e:
            self.logger.error(f"检查行展开状态失败: 行{row}, 错误: {e}")
            return False
    
    def _on_context_action_triggered(self, action_name: str, context: ContextMenuContext):
        """处理右键菜单动作触发"""
        try:
            # 发出信号通知外部
            self.context_action_triggered.emit(action_name, context)
            
            # 内部处理具体动作
            self._handle_context_action(action_name, context)
            
            self.logger.debug(f"处理右键菜单动作: {action_name}")
            
        except Exception as e:
            self.logger.error(f"处理右键菜单动作失败: {action_name}, 错误: {e}")
    
    def _handle_context_action(self, action_name: str, context: ContextMenuContext):
        """处理具体的右键菜单动作"""
        try:
            # 基础操作
            if action_name == "copy":
                self._handle_copy_action(context)
            elif action_name == "paste":
                self._handle_paste_action(context)
            elif action_name == "cut":
                self._handle_cut_action(context)
            elif action_name == "delete":
                self._handle_delete_action(context)
            elif action_name == "select_all":
                self.select_all_rows()
            elif action_name == "clear_selection":
                self.clear_selection()
            
            # 编辑操作
            elif action_name == "edit_cell":
                if context.clicked_row >= 0 and context.clicked_column >= 0:
                    self._start_cell_editing(context.clicked_row, context.clicked_column)
            elif action_name == "batch_edit":
                self._handle_batch_edit_action(context)
            elif action_name == "cancel_edit":
                self.cancel_current_edit()
            
            # 展开操作
            elif action_name == "expand_row":
                if context.clicked_row >= 0:
                    self.toggle_row_expansion(context.clicked_row)
            elif action_name == "collapse_row":
                if context.clicked_row >= 0:
                    self.toggle_row_expansion(context.clicked_row)
            elif action_name == "expand_all":
                self.expand_all_rows()
            elif action_name == "collapse_all":
                self.collapse_all_rows()
            
            # 批量操作
            elif action_name == "batch_delete":
                self.batch_delete_rows()
            elif action_name == "batch_copy":
                self.batch_copy_data()
            elif action_name == "batch_export":
                self.batch_export_data()
            elif action_name == "batch_expand":
                self.batch_expand_rows()
            elif action_name == "batch_collapse":
                self.batch_collapse_rows()
            
            # 表格操作
            elif action_name == "insert_row":
                self._handle_insert_row_action(context)
            elif action_name == "sort_column":
                self._handle_sort_column_action(context)
            elif action_name == "hide_column":
                self._handle_hide_column_action(context)
            elif action_name == "resize_columns":
                self._handle_resize_columns_action(context)
            
            # 高级操作
            elif action_name == "filter_rows":
                self._handle_filter_rows_action(context)
            elif action_name == "refresh_data":
                self._handle_refresh_data_action(context)
            elif action_name == "export_data":
                self.batch_export_data(include_headers=True)
            
        except Exception as e:
            self.logger.error(f"处理右键菜单动作失败: {action_name}, 错误: {e}")
    
    def _handle_copy_action(self, context: ContextMenuContext):
        """处理复制动作"""
        try:
            if len(context.selected_rows) > 1:
                # 多行复制
                data = self.batch_copy_data()
                self.logger.debug(f"复制 {len(data)} 行数据")
            else:
                # 单行复制
                if context.clicked_row >= 0:
                    # 这里可以实现单行复制逻辑
                    self.logger.debug(f"复制行 {context.clicked_row}")
        except Exception as e:
            self.logger.error(f"复制动作处理失败: {e}")
    
    def _handle_paste_action(self, context: ContextMenuContext):
        """处理粘贴动作"""
        try:
            # 这里可以实现粘贴逻辑
            self.logger.debug("执行粘贴操作")
        except Exception as e:
            self.logger.error(f"粘贴动作处理失败: {e}")
    
    def _handle_cut_action(self, context: ContextMenuContext):
        """处理剪切动作"""
        try:
            # 先复制，再删除
            self._handle_copy_action(context)
            self._handle_delete_action(context)
            self.logger.debug("执行剪切操作")
        except Exception as e:
            self.logger.error(f"剪切动作处理失败: {e}")
    
    def _handle_delete_action(self, context: ContextMenuContext):
        """处理删除动作"""
        try:
            if len(context.selected_rows) > 1:
                # 批量删除
                self.batch_delete_rows()
            elif context.clicked_row >= 0:
                # 单行删除
                self.removeRow(context.clicked_row)
                self.logger.debug(f"删除行 {context.clicked_row}")
        except Exception as e:
            self.logger.error(f"删除动作处理失败: {e}")
    
    def _handle_batch_edit_action(self, context: ContextMenuContext):
        """处理批量编辑动作"""
        try:
            if context.clicked_column > 0:  # 跳过第0列（展开/折叠控制列）
                # 这里可以弹出批量编辑对话框
                self.logger.debug(f"批量编辑列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"批量编辑动作处理失败: {e}")
    
    def _handle_insert_row_action(self, context: ContextMenuContext):
        """处理插入行动作"""
        try:
            insert_position = context.clicked_row if context.clicked_row >= 0 else self.rowCount()
            self.insertRow(insert_position)
            self.logger.debug(f"在位置 {insert_position} 插入新行")
        except Exception as e:
            self.logger.error(f"插入行动作处理失败: {e}")
    
    def _handle_sort_column_action(self, context: ContextMenuContext):
        """处理排序列动作"""
        try:
            if context.clicked_column >= 0:
                self.sortItems(context.clicked_column)
                self.logger.debug(f"排序列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"排序列动作处理失败: {e}")
    
    def _handle_hide_column_action(self, context: ContextMenuContext):
        """处理隐藏列动作"""
        try:
            if context.clicked_column >= 0:
                self.setColumnHidden(context.clicked_column, True)
                self.logger.debug(f"隐藏列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"隐藏列动作处理失败: {e}")
    
    def _handle_resize_columns_action(self, context: ContextMenuContext):
        """处理调整列宽动作"""
        try:
            self.resizeColumnsToContents()
            self.logger.debug("调整所有列宽")
        except Exception as e:
            self.logger.error(f"调整列宽动作处理失败: {e}")
    
    def _handle_filter_rows_action(self, context: ContextMenuContext):
        """处理筛选行动作"""
        try:
            # 这里可以实现筛选逻辑
            self.logger.debug("执行行筛选")
        except Exception as e:
            self.logger.error(f"筛选行动作处理失败: {e}")
    
    def _handle_refresh_data_action(self, context: ContextMenuContext):
        """处理刷新数据动作"""
        try:
            # 这里可以实现数据刷新逻辑
            self.logger.debug("刷新表格数据")
        except Exception as e:
            self.logger.error(f"刷新数据动作处理失败: {e}")
    
    def set_context_menu_enabled(self, enabled: bool):
        """设置右键菜单是否启用"""
        try:
            self.context_menu_enabled = enabled
            self.logger.debug(f"右键菜单设置为: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置右键菜单状态失败: {e}")
    
    def get_context_menu_stats(self) -> Dict[str, Any]:
        """获取右键菜单统计信息"""
        try:
            if hasattr(self.context_menu, 'get_menu_stats'):
                return self.context_menu.get_menu_stats()
            return {}
        except Exception as e:
            self.logger.error(f"获取右键菜单统计失败: {e}")
            return {}
    
    # ==================== 快捷键系统方法 ====================
    
    def _on_shortcut_triggered(self, shortcut_name: str, context_data: Dict[str, Any]):
        """处理快捷键触发"""
        try:
            if not self.shortcuts_enabled:
                self.logger.debug(f"快捷键已禁用，忽略: {shortcut_name}")
                return
            
            # 发出信号通知外部
            self.shortcut_triggered.emit(shortcut_name, context_data)
            
            # 内部处理具体动作
            action = context_data.get('action', shortcut_name)
            self._handle_shortcut_action(action, context_data)
            
            self.logger.debug(f"处理快捷键: {shortcut_name} ({context_data.get('key_sequence')})")
            
        except Exception as e:
            self.logger.error(f"处理快捷键触发失败: {shortcut_name}, 错误: {e}")
    
    def _handle_shortcut_action(self, action: str, context_data: Dict[str, Any]):
        """处理具体的快捷键动作"""
        try:
            # 编辑相关快捷键
            if action == 'edit_cell':
                self._handle_edit_cell_shortcut()
            elif action == 'save_edit':
                self._handle_save_edit_shortcut()
            elif action == 'cancel_edit':
                self.cancel_current_edit()
            
            # 选择相关快捷键
            elif action == 'select_all':
                self.select_all_rows()
            elif action == 'clear_selection':
                self.clear_selection()
            elif action == 'invert_selection':
                self._handle_invert_selection_shortcut()
            
            # 剪贴板相关快捷键
            elif action == 'copy_data':
                self._handle_copy_data_shortcut()
            elif action == 'paste_data':
                self._handle_paste_data_shortcut()
            elif action == 'cut_data':
                self._handle_cut_data_shortcut()
            
            # 展开折叠相关快捷键
            elif action == 'expand_all':
                self.expand_all_rows()
            elif action == 'collapse_all':
                self.collapse_all_rows()
            elif action == 'toggle_current_row':
                self._handle_toggle_current_row_shortcut()
            
            # 批量操作相关快捷键
            elif action == 'batch_delete':
                self.batch_delete_rows()
            elif action == 'batch_export':
                self.batch_export_data()
            
            # 导航相关快捷键
            elif action == 'goto_first_row':
                self._handle_goto_first_row_shortcut()
            elif action == 'goto_last_row':
                self._handle_goto_last_row_shortcut()
            
            # 系统相关快捷键
            elif action == 'refresh_data':
                self._handle_refresh_data_shortcut()
            elif action == 'show_help':
                self._handle_show_help_shortcut()
            
            else:
                self.logger.warning(f"未处理的快捷键动作: {action}")
            
        except Exception as e:
            self.logger.error(f"处理快捷键动作失败: {action}, 错误: {e}")
    
    def _handle_edit_cell_shortcut(self):
        """处理编辑单元格快捷键"""
        try:
            current_row = self.currentRow()
            current_col = self.currentColumn()
            
            if current_row >= 0 and current_col >= 0:
                self._start_cell_editing(current_row, current_col)
            else:
                self.logger.debug("没有选中的单元格，无法开始编辑")
        except Exception as e:
            self.logger.error(f"处理编辑单元格快捷键失败: {e}")
    
    def _handle_save_edit_shortcut(self):
        """处理保存编辑快捷键"""
        try:
            if self.is_editing():
                # 提交当前编辑
                current_row, current_col = self.current_edit_cell
                item = self.item(current_row, current_col)
                if item:
                    self.commitData(item)
                
                # 移动到下一行
                next_row = current_row + 1
                if next_row < self.rowCount():
                    self.setCurrentCell(next_row, current_col)
                    # 如果下一行也可编辑，自动开始编辑
                    if self.edit_mode_enabled:
                        self._start_cell_editing(next_row, current_col)
            else:
                self.logger.debug("当前没有正在编辑的单元格")
        except Exception as e:
            self.logger.error(f"处理保存编辑快捷键失败: {e}")
    
    def _handle_invert_selection_shortcut(self):
        """处理反选快捷键"""
        try:
            all_rows = set(range(self.rowCount()))
            selected_rows = self.selected_rows_set.copy()
            
            # 反选：选中未选中的，取消选中已选中的
            new_selection = all_rows - selected_rows
            
            self.selected_rows_set = new_selection
            self._update_selection_display()
            
            self.logger.debug(f"反选完成: 选中 {len(new_selection)} 行")
        except Exception as e:
            self.logger.error(f"处理反选快捷键失败: {e}")
    
    def _handle_copy_data_shortcut(self):
        """处理复制数据快捷键"""
        try:
            if self.selected_rows_set:
                copied_data = self.batch_copy_data()
                # 这里可以将数据复制到剪贴板
                self.logger.debug(f"复制数据: {len(copied_data)} 行")
            else:
                self.logger.debug("没有选中的数据可复制")
        except Exception as e:
            self.logger.error(f"处理复制数据快捷键失败: {e}")
    
    def _handle_paste_data_shortcut(self):
        """处理粘贴数据快捷键"""
        try:
            # 这里可以实现从剪贴板粘贴数据的逻辑
            self.logger.debug("粘贴数据功能待实现")
        except Exception as e:
            self.logger.error(f"处理粘贴数据快捷键失败: {e}")
    
    def _handle_cut_data_shortcut(self):
        """处理剪切数据快捷键"""
        try:
            if self.selected_rows_set:
                # 先复制
                self._handle_copy_data_shortcut()
                # 再删除
                self.batch_delete_rows()
                self.logger.debug("剪切数据完成")
            else:
                self.logger.debug("没有选中的数据可剪切")
        except Exception as e:
            self.logger.error(f"处理剪切数据快捷键失败: {e}")
    
    def _handle_toggle_current_row_shortcut(self):
        """处理切换当前行展开状态快捷键"""
        try:
            current_row = self.currentRow()
            if current_row >= 0:
                self.toggle_row_expansion(current_row)
            else:
                self.logger.debug("没有当前行可切换展开状态")
        except Exception as e:
            self.logger.error(f"处理切换行展开状态快捷键失败: {e}")
    
    def _handle_goto_first_row_shortcut(self):
        """处理跳转到第一行快捷键"""
        try:
            if self.rowCount() > 0:
                self.setCurrentCell(0, 0)
                self.scrollToTop()
                self.logger.debug("跳转到第一行")
            else:
                self.logger.debug("表格为空，无法跳转")
        except Exception as e:
            self.logger.error(f"处理跳转到第一行快捷键失败: {e}")
    
    def _handle_goto_last_row_shortcut(self):
        """处理跳转到最后一行快捷键"""
        try:
            if self.rowCount() > 0:
                last_row = self.rowCount() - 1
                self.setCurrentCell(last_row, 0)
                self.scrollToBottom()
                self.logger.debug(f"跳转到最后一行: {last_row}")
            else:
                self.logger.debug("表格为空，无法跳转")
        except Exception as e:
            self.logger.error(f"处理跳转到最后一行快捷键失败: {e}")
    
    def _handle_refresh_data_shortcut(self):
        """处理刷新数据快捷键"""
        try:
            # 这里可以实现数据刷新逻辑
            # 比如重新从数据源加载数据
            self.logger.debug("刷新数据功能触发")
            
            # 可以发出信号通知外部组件刷新数据
            # self.refresh_requested.emit()
        except Exception as e:
            self.logger.error(f"处理刷新数据快捷键失败: {e}")
    
    # ==================== 工具方法 ====================
    
    def _get_employee_id_from_row(self, row_data: dict, index: int = 0) -> str:
        """
        从行数据中获取员工ID，支持多种字段名
        
        Args:
            row_data: 行数据字典
            index: 行索引，用于生成默认值
            
        Returns:
            员工ID字符串
        """
        try:
            # 支持的员工ID字段名（按优先级排序）
            id_fields = ['工号', 'employee_id', '人员代码']
            
            for field in id_fields:
                if field in row_data and row_data[field]:
                    return str(row_data[field])
            
            # 如果都没找到，返回默认值
            return f"未知{index}"
            
        except Exception as e:
            self.logger.error(f"获取员工ID失败: {e}")
            return f"未知{index}"
    
    def _handle_show_help_shortcut(self):
        """处理显示帮助快捷键"""
        try:
            help_text = self.shortcut_manager.get_shortcut_help_text()
            
            # 发出帮助请求信号
            self.shortcut_help_requested.emit()
            
            # 也可以直接显示帮助对话框
            self._show_shortcut_help_dialog(help_text)
            
        except Exception as e:
            self.logger.error(f"处理显示帮助快捷键失败: {e}")
    
    def _show_shortcut_help_dialog(self, help_text: str):
        """显示快捷键帮助对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
            
            dialog = QDialog(self)
            dialog.setWindowTitle("快捷键帮助")
            dialog.resize(500, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 帮助文本
            text_edit = QTextEdit()
            text_edit.setPlainText(help_text)
            text_edit.setReadOnly(True)
            text_edit.setFont(QFont("Consolas", 10))
            layout.addWidget(text_edit)
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)
            
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"显示快捷键帮助对话框失败: {e}")
    
    def _on_shortcut_help_requested(self, shortcuts_list: List[Dict[str, Any]]):
        """处理快捷键帮助请求"""
        try:
            self.shortcut_help_requested.emit()
            self.logger.debug("快捷键帮助请求已发出")
        except Exception as e:
            self.logger.error(f"处理快捷键帮助请求失败: {e}")
    
    def set_shortcuts_enabled(self, enabled: bool):
        """设置快捷键是否启用"""
        try:
            self.shortcuts_enabled = enabled
            
            # 也可以批量启用/禁用所有快捷键
            for shortcut_name in self.shortcut_manager.shortcuts:
                self.shortcut_manager.enable_shortcut(shortcut_name, enabled)
            
            self.logger.debug(f"快捷键系统设置为: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置快捷键状态失败: {e}")
    
    def enable_shortcut(self, shortcut_name: str, enabled: bool = True):
        """启用或禁用指定快捷键"""
        try:
            return self.shortcut_manager.enable_shortcut(shortcut_name, enabled)
        except Exception as e:
            self.logger.error(f"设置指定快捷键状态失败: {shortcut_name}, 错误: {e}")
            return False
    
    def get_shortcuts_help_text(self) -> str:
        """获取快捷键帮助文本"""
        try:
            return self.shortcut_manager.get_shortcut_help_text()
        except Exception as e:
            self.logger.error(f"获取快捷键帮助文本失败: {e}")
            return "快捷键帮助获取失败"
    
    def get_shortcuts_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """按分类获取快捷键列表"""
        try:
            return self.shortcut_manager.get_shortcuts_by_category()
        except Exception as e:
            self.logger.error(f"获取快捷键分类失败: {e}")
            return {}
    
    def get_shortcut_usage_statistics(self) -> Dict[str, Any]:
        """获取快捷键使用统计"""
        try:
            return self.shortcut_manager.get_usage_statistics()
        except Exception as e:
            self.logger.error(f"获取快捷键使用统计失败: {e}")
            return {}
    
    def detect_shortcut_conflicts(self) -> List[Dict[str, Any]]:
        """检测快捷键冲突"""
        return self.shortcut_manager.detect_conflicts()
    
    def show_shortcut_help(self):
        """显示快捷键帮助"""
        help_text = self.get_shortcuts_help_text()
        self._show_shortcut_help_dialog(help_text)
    
    # ==================== 拖拽排序功能 (P4-005) ====================
    
    def set_drag_sort_enabled(self, enabled: bool):
        """启用/禁用拖拽排序功能"""
        self.drag_sort_enabled = enabled
        self.drag_sort_manager.enable_drag_sort(enabled)
        
        if enabled:
            # 启用拖拽功能
            self.setDragEnabled(True)
            self.setAcceptDrops(True)
            self.setDropIndicatorShown(True)
            self.setDragDropMode(QAbstractItemView.InternalMove)
            self.setDefaultDropAction(Qt.MoveAction)
        else:
            # 禁用拖拽功能
            self.setDragEnabled(False)
            self.setAcceptDrops(False)
            self.setDropIndicatorShown(False)
            self.setDragDropMode(QAbstractItemView.NoDragDrop)
        
        self.logger.info(f"拖拽排序功能{'启用' if enabled else '禁用'}")
    
    def set_drag_constraints(self, 
                           cross_group: bool = True,
                           expanded_rows: bool = True,
                           allowed_rows: Optional[List[int]] = None):
        """设置拖拽约束条件"""
        self.drag_sort_manager.set_drag_constraints(cross_group, expanded_rows, allowed_rows)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 检测拖拽开始"""
        try:
            if event.button() == Qt.LeftButton and self.drag_sort_enabled:
                # 记录按下位置用于拖拽检测
                self.drag_start_position = event.pos()
                self.is_potential_drag = True
            
            # 调用父类处理
            super().mousePressEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标按下事件处理失败: {e}")
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 处理拖拽"""
        try:
            if (self.is_potential_drag and 
                event.buttons() & Qt.LeftButton and 
                self.drag_sort_enabled):
                
                # 检查是否达到拖拽阈值
                distance = (event.pos() - self.drag_start_position).manhattanLength()
                if distance >= self.drag_sort_manager.drag_threshold:
                    self._start_drag_operation(event.pos())
            
            # 如果正在拖拽，更新拖拽状态
            if self.drag_sort_manager.is_dragging:
                current_row = self.rowAt(event.pos().y())
                self.drag_sort_manager.update_drag(current_row)
            
            # 调用父类处理
            super().mouseMoveEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标移动事件处理失败: {e}")
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 完成拖拽"""
        try:
            if event.button() == Qt.LeftButton:
                self.is_potential_drag = False
                
                # 如果正在拖拽，完成拖拽操作
                if self.drag_sort_manager.is_dragging:
                    self.drag_sort_manager.complete_drag()
                
                # 列宽调整完成时保存（真正的事件驱动）
                if hasattr(self, 'column_width_manager'):
                    self.column_width_manager.save_on_resize_end()
            
            # 调用父类处理
            super().mouseReleaseEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标释放事件处理失败: {e}")
    
    def startDrag(self, supportedActions):
        """开始拖拽操作 - PyQt5拖拽系统集成"""
        try:
            current_row = self.currentRow()
            if current_row < 0 or not self.drag_sort_enabled:
                return
            
            # 获取行数据
            row_data = self.model.get_row_data(current_row)
            if not row_data:
                return
            
            # 开始拖拽
            if self.drag_sort_manager.start_drag(current_row, self.drag_start_position, row_data.__dict__):
                # 创建拖拽对象
                drag = QDrag(self)
                mime_data = QMimeData()
                mime_data.setText(f"row:{current_row}")
                drag.setMimeData(mime_data)
                
                # 创建拖拽预览图
                pixmap = self.drag_sort_manager.create_drag_preview(self, current_row)
                drag.setPixmap(pixmap)
                drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
                
                # 执行拖拽
                result = drag.exec_(Qt.MoveAction)
                
                # 处理拖拽结果
                if result != Qt.MoveAction:
                    self.drag_sort_manager.cancel_drag()
        
        except Exception as e:
            self.logger.error(f"开始拖拽操作失败: {e}")
            self.drag_sort_manager.cancel_drag()
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if (event.mimeData().hasText() and 
            event.mimeData().text().startswith("row:") and
            self.drag_sort_enabled):
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if (event.mimeData().hasText() and 
            event.mimeData().text().startswith("row:") and
            self.drag_sort_enabled):
            
            # 更新拖拽状态
            target_row = self.rowAt(event.pos().y())
            if self.drag_sort_manager.update_drag(target_row):
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """拖拽放置事件"""
        try:
            if not (event.mimeData().hasText() and 
                   event.mimeData().text().startswith("row:") and
                   self.drag_sort_enabled):
                event.ignore()
                return
            
            # 获取目标行
            target_row = self.rowAt(event.pos().y())
            
            # 完成拖拽操作
            if self.drag_sort_manager.is_dragging:
                self.drag_sort_manager.drag_target_row = target_row
                success = self.drag_sort_manager.complete_drag()
                
                if success:
                    event.acceptProposedAction()
                else:
                    event.ignore()
            else:
                event.ignore()
                
        except Exception as e:
            self.logger.error(f"拖拽放置事件处理失败: {e}")
            event.ignore()
    
    def _start_drag_operation(self, position: QPoint):
        """启动拖拽操作"""
        try:
            current_row = self.rowAt(position.y())
            if current_row < 0:
                return
            
            # 获取行数据
            row_data = self.model.get_row_data(current_row)
            if not row_data:
                return
            
            # 开始拖拽
            self.drag_sort_manager.start_drag(current_row, position, row_data.__dict__)
            self.is_potential_drag = False
            
        except Exception as e:
            self.logger.error(f"启动拖拽操作失败: {e}")
    
    def _move_row_data(self, from_row: int, to_row: int) -> bool:
        """移动行数据"""
        try:
            if (from_row < 0 or to_row < 0 or 
                from_row >= self.rowCount() or 
                to_row >= self.rowCount() or
                from_row == to_row):
                return False
            
            # 获取源行数据
            source_data = self.model.get_row_data(from_row)
            if not source_data:
                return False
            
            # 保存表格项数据
            row_items = []
            for col in range(self.columnCount()):
                item = self.item(from_row, col)
                if item:
                    row_items.append(item.clone())
                else:
                    row_items.append(None)
            
            # 调整目标行索引（如果目标行在源行之后）
            actual_target = to_row if to_row < from_row else to_row - 1
            
            # 删除源行
            self.removeRow(from_row)
            
            # 在目标位置插入新行
            self.insertRow(actual_target)
            
            # 恢复表格项数据
            for col, item in enumerate(row_items):
                if item:
                    self.setItem(actual_target, col, item)
            
            # 更新数据模型
            self.model.move_row_data(from_row, actual_target)
            
            # 选中移动后的行
            self.selectRow(actual_target)
            
            self.logger.info(f"行数据移动成功: {from_row} -> {actual_target}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动行数据失败: {e}")
            return False
    
    # 拖拽事件处理回调
    def _on_drag_started(self, source_row: int):
        """拖拽开始回调"""
        self.logger.info(f"拖拽开始: 行 {source_row}")
        
        # 发射拖拽开始信号
        self.row_drag_started.emit(source_row)
        
        # 可以在这里添加视觉反馈
        self.setProperty("dragging", True)
        self.style().unpolish(self)
        self.style().polish(self)
    
    def _on_drag_moved(self, source_row: int, target_row: int):
        """拖拽移动回调"""
        self.logger.debug(f"拖拽移动: {source_row} -> {target_row}")
        
        # 可以在这里添加拖拽预览效果
        pass
    
    def _on_drag_completed(self, from_row: int, to_row: int, success: bool):
        """拖拽完成回调"""
        try:
            if success:
                # 执行实际的行移动
                move_success = self._move_row_data(from_row, to_row)
                
                if move_success:
                    self.logger.info(f"拖拽排序完成: {from_row} -> {to_row}")
                    
                    # 发射行重排序信号
                    self.row_reordered.emit(from_row, to_row)
                    
                    # 发射拖拽操作完成信号
                    self.drag_operation_completed.emit("row_reorder", True)
                    
                    # 更新选择状态
                    self._update_selection_after_move(from_row, to_row)
                else:
                    self.logger.warning(f"拖拽排序失败: 行移动操作失败")
                    self.drag_operation_completed.emit("row_reorder", False)
            else:
                self.logger.info(f"拖拽操作未完成")
                self.drag_operation_completed.emit("drag_cancelled", False)
                
        except Exception as e:
            self.logger.error(f"拖拽完成回调处理失败: {e}")
            self.drag_operation_completed.emit("error", False)
        finally:
            # 清理拖拽状态
            self.setProperty("dragging", False)
            self.style().unpolish(self)
            self.style().polish(self)
    
    def _on_drag_cancelled(self, source_row: int):
        """拖拽取消回调"""
        self.logger.info(f"拖拽取消: 行 {source_row}")
        
        # 清理拖拽状态
        self.setProperty("dragging", False)
        self.style().unpolish(self)
        self.style().polish(self)
    
    def _update_selection_after_move(self, from_row: int, to_row: int):
        """移动后更新选择状态"""
        try:
            # 计算实际目标位置
            actual_target = to_row if to_row < from_row else to_row - 1
            
            # 更新选择状态
            if from_row in self.selected_rows:
                self.selected_rows.remove(from_row)
                self.selected_rows.add(actual_target)
            
            # 选中移动后的行
            self.selectRow(actual_target)
            
            # 更新显示
            self._update_selection_display()
            
        except Exception as e:
            self.logger.error(f"更新选择状态失败: {e}")
    
    def get_drag_sort_statistics(self) -> Dict[str, Any]:
        """获取拖拽排序统计信息"""
        return self.drag_sort_manager.get_drag_statistics()
    
    def cancel_current_drag(self):
        """取消当前拖拽操作"""
        if self.drag_sort_manager.is_dragging:
            self.drag_sort_manager.cancel_drag()
    
    # ==================== 字段映射编辑功能 ====================
    
    def _on_header_double_clicked(self, logical_index: int):
        """处理表头双击事件 - 使用HeaderEditManager（按照解决方案设计）"""
        try:
            if not self.header_edit_enabled:
                return

            # 获取当前显示的表头名称
            current_display_name = self.horizontalHeaderItem(logical_index).text()

            # 获取对应的数据库字段名
            db_field_name = self._get_db_field_name_by_column_index(logical_index)

            if not db_field_name:
                self.logger.error(f"无法获取列{logical_index}对应的数据库字段名")
                return

            # 使用HeaderEditManager显示编辑对话框
            success = self.header_edit_manager.show_edit_dialog(
                self,
                current_display_name,
                self.current_table_name,
                logical_index
            )

            if success:
                self.logger.info(f"表头编辑成功: 列{logical_index} {current_display_name}")

        except Exception as e:
            self.logger.error(f"处理表头双击编辑失败: {e}")

    def _on_mapping_updated(self, table_name: str, field_name: str, new_display_name: str):
        """处理字段映射更新信号"""
        try:
            if table_name != self.current_table_name:
                return

            # 找到对应的列并更新表头
            for col in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(col)
                if header_item:
                    # 检查是否是要更新的字段
                    db_field = self._get_db_field_name_by_column_index(col)
                    if db_field == field_name:
                        header_item.setText(new_display_name)
                        self.logger.info(f"表头已更新: 列{col} -> {new_display_name}")
                        break

            # 发出字段映射更新信号
            mapping = self.config_sync_manager.load_mapping(table_name)
            if mapping:
                self.field_mapping_updated.emit(table_name, mapping)

        except Exception as e:
            self.logger.error(f"处理映射更新信号失败: {e}")

    def _on_edit_completed(self, table_name: str, old_name: str, new_name: str):
        """处理编辑完成信号"""
        try:
            if table_name != self.current_table_name:
                return

            # 找到对应的列并发出表头编辑信号
            for col in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(col)
                if header_item and header_item.text() == new_name:
                    self.header_edited.emit(col, old_name, new_name)
                    break

            self.logger.info(f"编辑完成: {old_name} -> {new_name}")

        except Exception as e:
            self.logger.error(f"处理编辑完成信号失败: {e}")
    
    def set_table_name(self, table_name: str):
        """设置当前表名，用于字段映射"""
        try:
            self.current_table_name = table_name
            self.logger.debug(f"设置表名: {table_name}")
        except Exception as e:
            self.logger.error(f"设置表名失败: {e}")
    
    def set_table_type(self, table_type: str):
        """
        设置当前表类型，用于动态格式化
        
        Args:
            table_type (str): 表类型，如'a_grade_employees'等
        """
        try:
            if self.current_table_type != table_type:
                self.current_table_type = table_type
                self._clear_format_cache()  # 清除格式化缓存
                self.logger.debug(f"设置表类型: {table_type}")
        except Exception as e:
            self.logger.error(f"设置表类型失败: {e}")
    
    def _get_unified_table_type(self, table_name: str) -> str:
        """
        🔧 [P1修复] 统一的表类型识别入口
        
        优先使用table_data_service中的标准方法，确保系统一致性
        
        Args:
            table_name: 表名
            
        Returns:
            标准化的表类型
        """
        # 🔧 [P1修复] 直接使用本地表类型识别方法，避免依赖注入问题
        return self._extract_table_type_from_name(table_name)
    
    def _clear_format_cache(self):
        """清除格式化缓存"""
        try:
            self._table_format_config = None
            self._format_cache_dirty = True
            
            # 🚀 [性能优化] 清除新的缓存项
            if hasattr(self, '_cached_table_config'):
                delattr(self, '_cached_table_config')
                
            # 清除其他相关缓存
            if hasattr(self, '_float_fields'):
                delattr(self, '_float_fields')
            if hasattr(self, '_string_fields'):
                delattr(self, '_string_fields')
            self.logger.debug("格式化缓存已清除")
        except Exception as e:
            self.logger.error(f"清除格式化缓存失败: {e}")
    
    def _get_format_config(self):
        """
        获取FormatConfig实例（使用单例缓存）
        
        🔧 [P0性能修复] 解决12000次重复初始化的严重性能问题
        原问题：每次单元格格式化都创建新的FormatConfig实例
        修复方案：类级别单例缓存，大幅提升性能
        """
        if self._format_config_instance is None:
            try:
                from src.modules.format_management.format_config import FormatConfig
                config_path = "state/format_config.json"
                self._format_config_instance = FormatConfig(config_path)
                self.logger.debug("🔧 [P0性能修复] FormatConfig实例已缓存创建")
            except Exception as e:
                self.logger.error(f"🔧 [P0性能修复] FormatConfig实例创建失败: {e}")
                return None
        return self._format_config_instance
    
    def _is_retired_staff_table(self) -> bool:
        """
        判断当前是否为离休人员表
        
        🔧 [P0性能修复] 使用缓存机制，避免重复初始化FormatConfig
        
        Returns:
            bool: 是否为离休人员表
        """
        try:
            # 🔧 [P0性能修复] 检查结果缓存，避免重复计算
            current_table = self.current_table_name or ""
            if (self._cache_table_name == current_table and 
                current_table in self._is_retired_staff_cache):
                return self._is_retired_staff_cache[current_table]
            
            # 方法1：基于表类型判断（最快）
            if self.current_table_type == 'retired_employees':
                result = True
                self._is_retired_staff_cache[current_table] = result
                self._cache_table_name = current_table
                return result
            
            # 方法2：基于表名关键词判断（快速）
            if current_table:
                table_name = current_table.lower()
                retired_keywords = ['离休人员', 'retired', '离休']
                for keyword in retired_keywords:
                    if keyword in table_name:
                        result = True
                        self._is_retired_staff_cache[current_table] = result
                        self._cache_table_name = current_table
                        return result
            
            # 方法3：使用格式化配置判断（使用缓存的实例）
            try:
                format_config = self._get_format_config()
                if format_config:
                    result = format_config.is_retired_staff_table(current_table)
                    self._is_retired_staff_cache[current_table] = result
                    self._cache_table_name = current_table
                    return result
            except Exception as e:
                self.logger.debug(f"🔧 [P0性能修复] 格式化配置判断失败: {e}")
            
            # 默认返回False并缓存结果
            result = False
            self._is_retired_staff_cache[current_table] = result
            self._cache_table_name = current_table
            return result
            
        except Exception as e:
            self.logger.error(f"判断离休人员表失败: {e}")
            return False
    
    def _format_retired_staff_cell(self, value, column_name: str) -> str:
        """
        离休人员表专用单元格格式化
        
        Args:
            value: 单元格原始值
            column_name: 列名
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 浮点数字段（保留两位小数）
            float_fields = [
                "基本离休费", "结余津贴", "生活补贴", "住房补贴", 
                "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", 
                "补发", "合计", "借支"
            ]
            
            # 字符串字段
            string_fields = ["姓名", "部门名称", "备注"]
            
            if column_name in float_fields:
                return self._format_as_currency(value)
            elif column_name in string_fields:
                return self._format_as_string(value)
            else:
                # 其他字段使用默认格式化
                return self._format_cell_value_builtin(value, column_name)
                
        except Exception as e:
            self.logger.error(f"离休人员表格式化失败: {e}")
            # 降级处理，使用默认格式化
            return self._format_cell_value_builtin(value, column_name)
    
    def _format_as_currency(self, value) -> str:
        """
        格式化为两位小数的货币格式
        
        Args:
            value: 要格式化的值
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return "-"
            
            # 处理字符串形式的数字
            if isinstance(value, str):
                value = value.strip()
                if value == '' or value.lower() in ['nan', 'none', 'null']:
                    return "-"
            
            # 转换为浮点数并格式化
            float_val = float(value)
            return f"{float_val:.2f}"
            
        except (ValueError, TypeError):
            # 如果无法转换为数字，返回原始值
            return str(value) if value is not None else "-"
    
    def _format_as_string(self, value) -> str:
        """
        格式化为字符串
        
        Args:
            value: 要格式化的值
            
        Returns:
            str: 格式化后的字符串
        """
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return ""
            
            # 转换为字符串并去除首尾空白
            result = str(value).strip()
            
            # 处理特殊值
            if result.lower() in ['nan', 'none', 'null']:
                return ""
            
            return result
            
        except Exception:
            # 如果转换失败，返回空字符串
            return ""
    
    def _extract_table_type_for_formatting(self) -> str:
        """
        🔧 [根本修复] 提取表类型用于统一格式化
        
        Returns:
            str: 表类型标识，用于UnifiedFormatManager
        """
        try:
            # 优先使用当前表类型
            if hasattr(self, 'current_table_type') and self.current_table_type:
                return self._normalize_table_type_to_standard(self.current_table_type)
            
            # 从表名提取类型
            if hasattr(self, 'table_name') and self.table_name:
                return self._extract_table_type_from_name(self.table_name)
            
            # 默认使用在职人员类型
            return 'active_employees'
            
        except Exception as e:
            self.logger.warning(f"提取表类型失败: {e}")
            return 'active_employees'
    
    def _normalize_table_type_to_standard(self, table_type: str) -> str:
        """将表类型标准化为UnifiedFormatManager识别的格式"""
        type_mapping = {
            'salary_data_2025_07_active_employees': 'active_employees',
            'salary_data_2025_07_retired_employees': 'retired_employees',
            'salary_data_2025_07_pension_employees': 'retired_employees',
            'salary_data_2025_07_a_grade_employees': 'a_grade_employees',  # 🔧 [保险扣款修复] A岗职工使用专用配置
            'active_employees': 'active_employees',
            'retired_employees': 'retired_employees',
            'pension_employees': 'retired_employees',
            'a_grade_employees': 'a_grade_employees',  # 🔧 [保险扣款修复] A岗职工使用专用配置
            'A岗职工': 'a_grade_employees'  # 🔧 [保险扣款修复] 支持中文表类型
        }
        return type_mapping.get(table_type, 'active_employees')
    
    def _extract_table_type_from_name(self, table_name: str) -> str:
        """从表名提取标准化的表类型"""
        table_name_lower = table_name.lower()
        # 🔧 [保险扣款修复] 优先检查A岗职工表
        if 'a_grade_employees' in table_name_lower or 'A岗职工' in table_name or 'a岗职工' in table_name:
            return 'a_grade_employees'
        elif 'active_employees' in table_name_lower or '全部在职人员' in table_name:
            return 'active_employees'
        elif 'retired_employees' in table_name_lower or '离休人员' in table_name:
            return 'retired_employees'
        elif 'pension_employees' in table_name_lower or '退休人员' in table_name:
            return 'pension_employees'  # 🔧 [P0修复] 退休人员使用独立配置
        elif 'a_grade' in table_name_lower or 'a岗' in table_name:
            return 'a_grade_employees'
        else:
            return 'active_employees'

    def _get_table_specific_format_config(self):
        """
        获取当前表的格式化配置
        
        Returns:
            dict: 表格式化配置，如果无法获取则返回None
        """
        if not self.current_table_type:
            return None
        
        # 如果缓存有效，直接返回
        if self._table_format_config and not self._format_cache_dirty:
            return self._table_format_config
        
        try:
            # 🔧 [P1优化] 使用已缓存的格式管理器，避免重复获取
            if not hasattr(self, '_format_manager') or self._format_manager is None:
                from src.modules.format_management.unified_format_manager import get_unified_format_manager
                self._format_manager = get_unified_format_manager()
            
            if hasattr(self._format_manager, 'field_registry') and self._format_manager.field_registry:
                field_types = self._format_manager.field_registry.get_table_field_types(self.current_table_type)
                
                # 转换为组件需要的格式
                config = {
                    'field_types': field_types,
                    'float_fields': [name for name, type_name in field_types.items() if type_name == 'float'],
                    'string_fields': [name for name, type_name in field_types.items() if type_name == 'string'],
                    'special_fields': {
                        'month_fields': [name for name, type_name in field_types.items() if type_name == 'month_string'],
                        'year_fields': [name for name, type_name in field_types.items() if type_name == 'year_string']
                    }
                }
            else:
                # 默认配置
                config = {
                    'field_types': {},
                    'float_fields': ['2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '车补', '2025年奖励性绩效预发', '补发', '借支', '2025公积金', '代扣代存养老保险'],
                    'string_fields': ['姓名', '部门名称', '人员类别'],
                    'special_fields': {
                        'month_fields': ['月份', 'month'],
                        'year_fields': ['年份', 'year']
                    }
                }
            
            # 更新缓存
            self._table_format_config = config
            self._format_cache_dirty = False
            
            self.logger.debug(f"🔧 [新系统] 获取表类型 {self.current_table_type} 的格式化配置")
            return config
            
        except ImportError as e:
            self.logger.warning(f"🔧 [新系统] 无法导入统一格式管理系统: {e}")
            return None
        except Exception as e:
            self.logger.error(f"🔧 [新系统] 获取表格式化配置失败: {e}")
            return None
    
    def set_header_edit_enabled(self, enabled: bool):
        """启用/禁用表头编辑功能"""
        try:
            self.header_edit_enabled = enabled
            self.logger.debug(f"表头编辑功能: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置表头编辑状态失败: {e}")
    
    def _refresh_header_display(self):
        """刷新表头显示，应用最新的字段映射"""
        try:
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                self.logger.debug("没有设置当前表名，跳过表头刷新")
                return

            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if not mapping:
                self.logger.debug(f"表 {self.current_table_name} 没有字段映射配置")
                return

            # 如果有原始表头，使用原始表头进行映射
            if hasattr(self, 'original_headers') and self.original_headers:
                self.logger.debug(f"使用原始表头应用字段映射: {len(self.original_headers)} 个表头")

                # 重新应用映射
                mapped_headers = self._apply_field_mapping(self.original_headers)

                # 更新表头标签
                updated_count = 0
                for column in range(min(self.columnCount(), len(mapped_headers))):
                    header_item = self.horizontalHeaderItem(column)
                    if header_item:
                        new_header = mapped_headers[column]
                        if header_item.text() != new_header:
                            header_item.setText(new_header)
                            updated_count += 1
                            self.logger.debug(f"表头更新: {self.original_headers[column]} -> {new_header}")

                self.logger.info(f"表头显示刷新完成，更新了 {updated_count} 个表头")
            else:
                # 兼容旧逻辑：直接更新当前表头
                self.logger.debug(f"使用当前表头应用字段映射")

                updated_count = 0
                for column in range(self.columnCount()):
                    header_item = self.horizontalHeaderItem(column)
                    if header_item:
                        current_name = header_item.text()
                        # 查找反向映射（从显示名到原始名）
                        original_name = None
                        for orig, disp in mapping.items():
                            if disp == current_name:
                                original_name = orig
                                break

                        # 如果找到原始名，重新应用映射
                        if original_name and original_name in mapping:
                            display_name = mapping[original_name]
                            if display_name != current_name:
                                header_item.setText(display_name)
                                updated_count += 1
                                self.logger.debug(f"表头更新: {current_name} -> {display_name}")

                self.logger.info(f"表头显示刷新完成，更新了 {updated_count} 个表头")

        except Exception as e:
            self.logger.error(f"刷新表头显示失败: {e}", exc_info=True)

    def _force_clear_header_state(self):
        """
        【第二层防护】强制清理表头状态，防止重影

        这个方法会彻底清理表格的表头状态，包括：
        1. 清空现有表格内容
        2. 重置列数为0
        3. 清理表头缓存
        4. 重置选择状态
        """
        try:
            self.logger.debug("开始强制清理表头状态")

            # 1. 清空现有表格内容
            self.clearContents()

            # 2. 重置列数为0（这会清除所有表头）
            self.setColumnCount(0)

            # 3. 重置行数为0
            self.setRowCount(0)

            # 4. 清理表头缓存
            h_header = self.horizontalHeader()
            if h_header:
                # 🔧 [重影修复] 简化重绘，只使用update()
                h_header.update()
                # 移除updateGeometry()调用，避免重复重绘

                # 清除选择状态
                if hasattr(h_header, 'clearSelection'):
                    h_header.clearSelection()

            # 🔧 [P1-简化重绘] 安全清理垂直表头 - 只使用update()
            v_header = self.verticalHeader()
            if v_header:
                v_header.update()  # 只使用update()，让Qt控制重绘时机

            # 6. 🔧 [P0-CRITICAL] 安全清除表格视口缓存
            viewport = self.viewport()
            if viewport:
                viewport.update()  # 【修复闪动】只使用update()，避免强制重绘

            # 7. 清除选择状态
            self.clearSelection()

            # 8. 重置内部状态
            self.selected_rows_set.clear()
            self.expanded_rows.clear()

            self.logger.debug("表头状态强制清理完成")

        except Exception as e:
            self.logger.error(f"强制清理表头状态失败: {e}", exc_info=True)

    def _safe_set_headers(self, headers: List[str]):
        """
        【修复版】原子性表头设置流程 - 避免重复设置导致的重影问题

        Args:
            headers: 要设置的表头列表
        """
        try:
            self.logger.debug(f"开始原子性设置表头: {len(headers)} 个表头")

            # 1. 确保列数正确
            if self.columnCount() != len(headers):
                self.setColumnCount(len(headers))

            # 2. 【关键修复】检查是否需要更新表头，避免不必要的重复设置
            current_headers = [self.horizontalHeaderItem(i).text() if self.horizontalHeaderItem(i) else "" 
                              for i in range(self.columnCount())]
            
            # 🔧 [P0-修复] 统一使用HeaderUpdateManager更新表头，移除双重设置
            if current_headers != headers:
                # 统一通过管理器更新，移除降级处理避免双重设置
                if hasattr(self, 'header_update_manager') and self.header_update_manager:
                    self.header_update_manager.update_headers_safe(headers)
                    self.logger.debug(f"🔧 [P0-修复] 表头已通过管理器更新: {len(headers)} 个表头")
                else:
                    # 🔧 [P0-修复] 如果管理器不可用，说明初始化有问题，记录错误但不降级
                    self.logger.error(f"🔧 [P0-修复] HeaderUpdateManager未正确初始化，无法更新表头")
                    # 不再使用降级处理，避免双重设置导致的重影问题
            else:
                self.logger.debug("表头无变化，跳过重复设置")

            # 🔧 [P1-简化重绘] 只使用update()，完全移除updateGeometry()
            h_header = self.horizontalHeader()
            if h_header:
                h_header.update()  # 只使用update()，让Qt控制重绘时机

            self.logger.debug(f"原子性表头设置完成: {len(headers)} 个表头")

        except Exception as e:
            self.logger.error(f"原子性表头设置失败: {e}", exc_info=True)
            # 简化的错误处理，避免在回退中再次造成重复设置
            raise

    def _safe_update_headers_with_sort_state(self, headers: List[str]):
        """🔧 [修复标识] 安全地更新表头并保持排序状态
        
        这个函数用于分页模式下更新表头，同时保持排序状态不丢失。
        
        Args:
            headers: 要设置的表头列表
        """
        try:
            # 保存当前排序状态
            header = self.horizontalHeader()
            current_sort_column = header.sortIndicatorSection()
            current_sort_order = header.sortIndicatorOrder()
            
            self.logger.debug(f"🔧 [修复标识] 保存排序状态: 列{current_sort_column}, 顺序{current_sort_order}")
            
            # 🔧 [重影修复] 使用统一管理器更新表头
            self.header_update_manager.update_headers_safe(headers)
            
            # 恢复排序状态
            if current_sort_column >= 0 and current_sort_column < len(headers):
                header.setSortIndicator(current_sort_column, current_sort_order)
                self.logger.debug(f"🔧 [修复标识] 恢复排序状态: 列{current_sort_column}, 顺序{current_sort_order}")
            else:
                self.logger.debug(f"🔧 [修复标识] 排序列索引无效，清除排序状态")
            
        except Exception as e:
            self.logger.error(f"🔧 [修复标识] 更新表头并保持排序状态失败: {e}")
            # 降级处理：至少更新表头
            try:
                self._safe_set_headers(headers)
            except Exception as e:
                self.logger.error(f"🔧 [P0-修复] 表头设置失败: {e}", exc_info=True)

    def _get_mapped_field_name(self, original_name: str) -> str:
        """获取字段的映射显示名称"""
        try:
            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if mapping:
                return mapping.get(original_name, original_name)
            return original_name
        except Exception as e:
            self.logger.error(f"获取字段映射名称失败: {e}")
            return original_name
    
    def apply_field_mapping(self, mapping: Dict[str, str]):
        """应用字段映射到表头显示"""
        try:
            # 保存映射到配置管理器
            self.config_sync_manager.save_mapping(self.current_table_name, mapping)
            
            # 刷新表头显示
            self._refresh_header_display()
            
            # 发出信号
            self.field_mapping_updated.emit(self.current_table_name, mapping)
            
            self.logger.info(f"字段映射应用成功: {len(mapping)} 个字段")
            
        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}")
    
    def get_field_mapping_stats(self) -> Dict[str, Any]:
        """获取字段映射统计信息"""
        try:
            # 先获取基本的字段映射
            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if not mapping:
                return {"error": "无映射配置"}
            
            # 尝试获取详细配置信息
            try:
                config = self.config_sync_manager._load_config_file()
                table_config = config.get("table_mappings", {}).get(self.current_table_name, {})
                metadata = table_config.get('metadata', {})
                edit_history = table_config.get('edit_history', [])
            except Exception as e:
                self.logger.error(f"🔧 [P0-修复] 字段映射配置加载失败: {e}", exc_info=True)
                metadata = {}
                edit_history = []
            
            return {
                "table_name": self.current_table_name,
                "total_fields": len(mapping),
                "mapped_fields": len([v for k, v in mapping.items() if k != v]),
                "auto_generated": metadata.get('auto_generated', False),
                "user_modified": metadata.get('user_modified', False),
                "last_modified": metadata.get('last_modified', ''),
                "edit_history_count": len(edit_history)
            }
            
        except Exception as e:
            self.logger.error(f"获取字段映射统计失败: {e}")
            return {"error": str(e)}

    def apply_table_field_preference(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """根据表级偏好设置过滤显示字段
        
        Args:
            df: 原始数据（包含所有字段）
            table_name: 表名
            
        Returns:
            过滤后的数据（仅包含用户选择的字段）
        """
        try:
            # 使用已有的配置同步管理器实例
            config_sync = self.config_sync_manager
            
            # 获取表级偏好字段
            preferred_fields = config_sync.get_table_field_preference(table_name)
            
            if preferred_fields and len(preferred_fields) > 0:
                # 过滤出存在的偏好字段
                available_fields = [field for field in preferred_fields if field in df.columns]
                
                if available_fields:
                    self.logger.info(f"应用表 {table_name} 的字段偏好: {len(available_fields)}个字段")
                    filtered_df = df[available_fields].copy()
                    return filtered_df
                else:
                    self.logger.warning(f"表 {table_name} 的偏好字段在数据中不存在，显示所有字段")
            else:
                self.logger.info(f"表 {table_name} 无字段偏好设置，显示所有字段")
            
            return df
            
        except Exception as e:
            self.logger.error(f"应用表级字段偏好失败: {e}")
            return df

    def _get_db_field_name_by_column_index(self, logical_index: int) -> Optional[str]:
        """支持从列索引获取数据库字段名（按照解决方案设计）

        Args:
            logical_index: 列索引

        Returns:
            Optional[str]: 数据库字段名，如果找不到返回None
        """
        try:
            if not self.current_table_name:
                return None

            # 获取字段映射配置
            field_mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if not field_mapping:
                return None

            # 获取当前显示的表头名称
            current_display_name = self.horizontalHeaderItem(logical_index).text()

            # 反向查找：从显示名找到数据库字段名
            for db_field, display_name in field_mapping.items():
                if display_name == current_display_name:
                    return db_field

            # 如果没找到映射，可能是原始字段名
            return current_display_name

        except Exception as e:
            self.logger.error(f"获取数据库字段名失败: {e}")
            return None

    def _update_field_mapping(self, db_field_name: str, new_display_name: str) -> bool:
        """更新字段映射中的显示名称（按照解决方案设计）

        Args:
            db_field_name: 数据库字段名
            new_display_name: 新的显示名称

        Returns:
            bool: 是否更新成功
        """
        try:
            # 使用ConfigSyncManager的新方法更新单个字段映射
            return self.config_sync_manager.update_single_field_mapping(
                self.current_table_name, db_field_name, new_display_name
            )

        except Exception as e:
            self.logger.error(f"更新字段映射失败: {e}")
            return False

    def _record_header_edit_history(self, db_field_name: str, old_display_name: str, new_display_name: str):
        """记录表头编辑历史（按照解决方案设计）

        Args:
            db_field_name: 数据库字段名
            old_display_name: 旧显示名
            new_display_name: 新显示名
        """
        try:
            # 这里可以添加编辑历史记录逻辑
            # 目前ConfigSyncManager已经在update_single_field_mapping中记录了历史
            self.logger.info(f"表头编辑历史记录: {db_field_name} {old_display_name} -> {new_display_name}")

        except Exception as e:
            self.logger.error(f"记录表头编辑历史失败: {e}")
    
    # 🆕 [新架构多列排序] 新的信号处理方法
    def _on_sort_state_changed(self, sort_columns):
        """
        响应多列排序状态变化事件
        
        Args:
            sort_columns: 排序列列表
        """
        try:
            self.logger.info(f"🆕 [新架构多列排序] 排序状态变化: {len(sort_columns)} 列")
            
            # 更新排序指示器显示
            if self.sort_manager:
                self.sort_manager.update_sort_indicators()
                
            # 获取排序描述信息
            sort_description = self.sort_manager.get_sort_description() if self.sort_manager else "无排序"
            self.logger.info(f"🆕 [新架构多列排序] 当前排序: {sort_description}")
            
        except Exception as e:
            self.logger.error(f"🆕 [新架构多列排序] 处理排序状态变化失败: {e}")
    
    def _on_sort_request(self, table_name: str, column_name: str, sort_state: str):
        """
        响应排序请求事件
        
        Args:
            table_name: 表名
            column_name: 列名
            sort_state: 排序状态
        """
        try:
            self.logger.info(f"🆕 [新架构多列排序] 排序请求: {table_name}.{column_name} -> {sort_state}")
            
            # 调用现有的排序通知方法
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, '_handle_sort_applied'):
                # 获取列索引
                logical_index = self._get_column_index_by_name(column_name)
                if logical_index >= 0:
                    main_window._handle_sort_applied(logical_index, column_name, sort_state)
                else:
                    self.logger.warning(f"无法找到列{column_name}的索引")
            else:
                self.logger.warning("无法通知主窗口排序请求")
                
        except Exception as e:
            self.logger.error(f"🆕 [新架构多列排序] 处理排序请求失败: {e}")
    
    def _on_sort_cleared(self):
        """
        响应排序清除事件
        """
        try:
            self.logger.info("🆕 [新架构多列排序] 排序已清除")
            
            # 清除排序指示器
            header = self.horizontalHeader()
            header.setSortIndicator(-1, Qt.AscendingOrder)
            
            # 通知主窗口排序已清除
            self._notify_sort_cleared()
            
        except Exception as e:
            self.logger.error(f"🆕 [新架构多列排序] 处理排序清除失败: {e}")
    
    def _get_column_index_by_name(self, column_name: str) -> int:
        """
        根据列名获取列索引
        
        支持：
        1. 直接匹配中文列名（如"2025年薪级工资"）
        2. 通过数据库字段名反向映射查找（如"grade_salary_2025"）
        
        Args:
            column_name: 列名（支持中文列名或数据库字段名）
            
        Returns:
            int: 列索引，未找到返回-1
        """
        # 🔧 [P0-排序修复] 第一步：直接匹配列名
        for i in range(self.columnCount()):
            header_item = self.horizontalHeaderItem(i)
            if header_item and header_item.text() == column_name:
                return i
        
        # 🔧 [新架构] 动态获取反向字段映射
        reverse_mapping = self._get_reverse_field_mapping()
        
        # 如果输入的是数据库字段名，转换为中文列名再查找
        chinese_column_name = reverse_mapping.get(column_name)
        if chinese_column_name:
            for i in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(i)
                if header_item and header_item.text() == chinese_column_name:
                    self.logger.debug(f"🔧 [P0-排序修复] 通过反向映射找到列: {column_name} -> {chinese_column_name} (索引: {i})")
                    return i
        
        self.logger.warning(f"🔧 [P0-排序修复] 无法找到列: {column_name}")
        return -1
    
    def _on_sort_changed(self, sort_columns):
        """🔧 [新架构] 已移除旧架构排序逻辑"""
        pass  # 新架构使用自定义排序循环
    
    def _save_and_apply_sort_state(self, sort_columns):
        """
        保存排序状态并触发全表排序
        
        Args:
            sort_columns: 排序列列表
        """
        try:
            self.logger.info(f"🔧 [调试] 开始保存排序状态: {len(sort_columns)} 列")
            
            # 🔧 [循环防护] 防止排序触发时的重复调用
            if hasattr(self, '_is_applying_sort') and self._is_applying_sort:
                self.logger.info("🔧 [调试] 正在应用排序，跳过重复调用")
                return
            
            # 设置排序应用标志
            self._is_applying_sort = True
            
            # 尝试获取主窗口和相关管理器
            main_window = self._get_main_window()
            if not main_window:
                self.logger.warning("🔧 [调试] 无法获取主窗口，跳过全表排序")
                return
            
            self.logger.info(f"🔧 [调试] 已获取主窗口: {type(main_window).__name__}")
            
            # 🔧 [P0修复] 从多个来源获取当前表名，确保不为空
            table_name = getattr(main_window, 'current_table_name', '')
            if not table_name:
                table_name = getattr(self, 'current_table_name', '')
            if not table_name and hasattr(main_window, 'current_nav_path') and len(main_window.current_nav_path) == 4:
                table_name = main_window._generate_table_name_from_path(main_window.current_nav_path)
            
            table_type = getattr(main_window, 'current_table_type', '')
            
            # 🔧 [P1修复] 确保table_type有合理的默认值
            if not table_type:
                table_type = 'salary_data'  # 默认表类型
                self.logger.info(f"🔧 [P1修复] table_type为空，使用默认值: {table_type}")
            
            self.logger.info(f"🔧 [调试] 表名: {table_name}, 表类型: {table_type}")
            
            if not table_name:
                self.logger.warning("🔧 [P0修复] 无法获取当前表名，使用新架构排序")
                # 直接使用新架构的排序机制
                self._trigger_new_architecture_sort(sort_columns)
                return
            
            # 🔧 [P0修复] 确保表名已设置到主窗口
            if table_name and not main_window.current_table_name:
                main_window.current_table_name = table_name
                self.logger.info(f"🔧 [P0修复] 已设置主窗口表名: {table_name}")
            
            # 简化映射逻辑，直接使用列名
            self.logger.info(f"🔧 [调试] 使用简化排序逻辑，直接使用列名")
            
            # 转换排序列格式为排序状态管理器需要的格式
            sort_state_columns = []
            for sort_col in sort_columns:
                self.logger.info(f"🔧 [调试] 处理排序列: {sort_col}")
                if hasattr(sort_col, 'column_name') and hasattr(sort_col, 'order'):
                    column_name = sort_col.column_name
                    
                    # 使用固定的映射关系
                    column_mapping = {
                        "2025年薪级工资": "grade_salary_2025",
                        "2025年岗位工资": "position_salary_2025",
                        "工号": "employee_id",
                        "姓名": "employee_name",
                        "部门名称": "department",
                        "人员类别": "employee_type",
                        "人员类别代码": "employee_type_code",
                        "津贴": "allowance",
                        "结余津贴": "balance_allowance",
                        "2025年基础性绩效": "basic_performance_2025",
                        "卫生费": "health_fee",
                        "交通补贴": "transport_allowance",
                        "物业补贴": "property_allowance",
                        "通讯补贴": "communication_allowance",
                        "2025年奖励性绩效预发": "performance_bonus_2025",
                        "2025公积金": "provident_fund_2025",
                        "住房补贴": "housing_allowance",
                        "车补": "car_allowance",
                        "补发": "supplement",
                        "借支": "advance",
                        "应发工资": "total_salary",
                        "代扣代存养老保险": "pension_insurance"
                    }
                    
                    # 转换为数据库字段名
                    db_column_name = column_mapping.get(column_name, column_name)
                    
                    sort_state_columns.append({
                        'column_name': db_column_name,  # 使用数据库字段名
                        'order': sort_col.order.value if hasattr(sort_col.order, 'value') else str(sort_col.order),
                        'priority': getattr(sort_col, 'priority', 0),
                        'column_index': getattr(sort_col, 'column_index', -1)
                    })
            
            self.logger.info(f"🔧 [调试] 转换后的排序列: {sort_state_columns}")
            
            # 保存排序状态
            if hasattr(main_window, 'sort_state_manager') and main_window.sort_state_manager:
                self.logger.info(f"🔧 [调试] 排序状态管理器可用")
                try:
                    
                    # 使用空的字段映射，简化排序逻辑
                    success = main_window.sort_state_manager.save_sort_state(
                        table_name=table_name,
                        table_type=table_type,
                        sort_columns=sort_state_columns,
                        field_mapping={}  # 传递空映射，直接使用列名
                    )
                    self.logger.info(f"🔧 [调试] 排序状态保存结果: {success}")
                    
                    if success:
                        self.logger.info(f"🆕 排序状态已保存: {table_name}, {len(sort_state_columns)} 列")

                        # 清理缓存以确保重新加载数据
                        if hasattr(main_window, 'pagination_cache') and main_window.pagination_cache:
                            main_window.pagination_cache.clear_table_cache_for_sort(table_name)

                        # 触发数据重新加载以应用排序
                        self._trigger_data_reload(main_window, table_name)
                    else:
                        self.logger.error("🔧 [调试] 保存排序状态失败")
                except Exception as e:
                    self.logger.error(f"🔧 [调试] 保存排序状态异常: {e}")
            else:
                self.logger.warning("🔧 [调试] 排序状态管理器不可用")
            
        except Exception as e:
            self.logger.error(f"保存并应用排序状态失败: {e}")
            # 🔧 [错误处理] 排序失败时恢复界面状态
            try:
                self._handle_sort_failure(e)
            except Exception as recovery_error:
                self.logger.error(f"🔧 [错误处理] 排序失败恢复处理也出错: {recovery_error}")
        finally:
            # 🔧 [循环防护] 重置排序应用标志
            if hasattr(self, '_is_applying_sort'):
                self._is_applying_sort = False
    
    def _handle_sort_failure(self, error):
        """
        🔧 [错误处理] 处理排序失败的情况
        """
        try:
            self.logger.warning(f"🔧 [错误处理] 排序失败，开始恢复界面状态: {error}")
            
            # 清除排序指示器
            try:
                header = self.horizontalHeader()
                if header:
                    header.blockSignals(True)
                    header.setSortIndicator(-1, Qt.AscendingOrder)
                    header.blockSignals(False)
                    self.logger.info("🔧 [错误处理] 已清除排序指示器")
            except Exception as header_error:
                self.logger.error(f"🔧 [错误处理] 清除排序指示器失败: {header_error}")
            
            # 🔧 [架构修复] 新架构不使用sort_manager，无需重置
            self.logger.info("🆕 [新架构排序] 使用自定义排序，无需重置老架构sort_manager")
            
            # 🔧 [修复] 防止Qt绘制冲突，直接更新而不使用定时器
            try:
                # 直接调用安全更新方法，避免定时器导致的线程安全问题
                self._safe_update_display()
                self.logger.info("🔧 [错误处理] 已执行安全的界面更新")
            except Exception as display_error:
                self.logger.error(f"🔧 [错误处理] 界面更新失败: {display_error}")
                
        except Exception as e:
            self.logger.error(f"🔧 [错误处理] 排序失败处理函数本身出错: {e}")
    
    def _safe_update_display(self):
        """
        🔧 [P0-CRITICAL] 绝对安全的更新显示，避免Qt绘制冲突和递归重绘
        """
        try:
            # 🔧 [P0-CRITICAL] 检查递归重绘深度
            current_depth = getattr(self, '_repaint_depth', 0)
            max_depth = getattr(self, '_max_repaint_depth', 3)
            
            if current_depth >= max_depth:
                self.logger.error(f"🔧 [P0-CRITICAL] 重绘深度达到限制 {current_depth}/{max_depth}，强制停止防止崩溃")
                return
            
            # 检查是否正在绘制
            if getattr(self, '_is_painting', False) or getattr(self, '_is_repainting', False):
                self.logger.warning("🔧 [P0-CRITICAL] 正在绘制或重绘中，延迟更新")
                # 🔧 [Timer修复] 使用线程安全的延迟更新机制
                self._setup_delayed_update_timer_safe()
                return
                
            # 🔧 [P0-CRITICAL] 标记重绘状态
            self._is_repainting = True
            self._repaint_depth = current_depth + 1
            
            # 安全地更新显示（只用update，不用repaint）
            self.viewport().update()
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL] 安全更新显示失败: {e}")
        finally:
            # 🔧 [P0-CRITICAL] 重置绘制标志
            self._is_repainting = False
            if hasattr(self, '_repaint_depth') and self._repaint_depth > 0:
                self._repaint_depth -= 1
    
    def _safe_delayed_update(self):
        """🔧 [P0-CRITICAL] 安全的延迟更新方法"""
        try:
            # 再次检查是否可以安全更新
            if not getattr(self, '_is_painting', False) and not getattr(self, '_is_repainting', False):
                if getattr(self, '_repaint_depth', 0) < getattr(self, '_max_repaint_depth', 3):
                    self.viewport().update()
                    self.logger.debug("🔧 [P0-CRITICAL] 延迟更新已执行")
        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL] 延迟更新失败: {e}")
    
    def _trigger_new_architecture_sort(self, sort_columns):
        """
        🔧 [P0修复] 使用新架构的排序机制，绕过表名问题
        """
        try:
            self.logger.info("🔧 [P0修复] 启用新架构排序机制")
            
            # 获取主窗口
            main_window = self._get_main_window()
            if not main_window:
                self.logger.error("🔧 [P0修复] 无法获取主窗口，排序失败")
                return
            
            # 🔧 [架构修复] 新架构不使用sort_manager，直接使用数据库排序
            self.logger.info("🆕 [新架构排序] 使用自定义排序循环，直接调用数据层排序")
            # TODO: 这里应该调用数据层的排序接口
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [新架构排序] 排序失败: {e}")
            return False
    
    def _get_mapped_column_name(self, column_name):
        """
        🔧 [P0修复] 获取映射后的列名
        """
        column_mapping = {
            "2025年薪级工资": "grade_salary_2025",
            "2025年岗位工资": "position_salary_2025",
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "交通补贴": "transport_allowance",
            "物业补贴": "property_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "2025公积金": "provident_fund_2025",
            "住房补贴": "housing_allowance",
            "车补": "car_allowance",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "代扣代存养老保险": "pension_insurance"
        }
        
        return column_mapping.get(column_name, column_name)

    def _trigger_data_reload(self, main_window, table_name):
        """
        触发数据重新加载以应用排序
        
        Args:
            main_window: 主窗口实例
            table_name: 表名
        """
        try:
            self.logger.info(f"🔧 [调试] 开始触发数据重新加载: {table_name}")
            
            # 仅设置数据重载标志，防止在数据重新加载期间触发排序
            self._is_data_reloading = True
            
            # 检查是否为分页模式
            has_pagination = (hasattr(main_window, 'main_workspace') and 
                            hasattr(main_window.main_workspace, 'pagination_widget') and 
                            main_window.main_workspace.pagination_widget and 
                            main_window.main_workspace.pagination_widget.isVisible())
            
            self.logger.info(f"🔧 [调试] 分页模式检查: {has_pagination}")
            
            if has_pagination:
                
                # 分页模式：排序时保持当前页码，显示排序后当前页的内容
                # 🔧 修复：保持用户当前页码，显示排序后该页的内容
                current_page = 1
                page_size = 50
                if hasattr(main_window.main_workspace.pagination_widget, 'get_current_page'):
                    current_page = main_window.main_workspace.pagination_widget.get_current_page()
                    self.logger.info(f"🔧 获取分页组件当前页码: {current_page}")
                if hasattr(main_window.main_workspace.pagination_widget, 'get_page_size'):
                    page_size = main_window.main_workspace.pagination_widget.get_page_size()
                
                # 🔧 确保页码有效性
                if current_page < 1:
                    current_page = 1
                    self.logger.warning(f"页码无效，重置为1")
                
                self.logger.info(f"🔧 排序时保持页码: {current_page}")
                
                if hasattr(main_window, '_load_data_with_pagination'):
                    self.logger.info(f"🆕 触发分页数据重新加载（应用排序）: {table_name}, 第{current_page}页")
                    main_window._load_data_with_pagination(table_name, current_page, page_size)
                else:
                    self.logger.warning("主窗口不支持分页数据加载方法")
            else:
                # 普通模式：重新加载全部数据
                if hasattr(main_window, '_reload_current_table_data'):
                    self.logger.info(f"🆕 触发普通模式数据重新加载（应用排序）: {table_name}")
                    main_window._reload_current_table_data()
                else:
                    self.logger.warning("主窗口不支持普通模式数据重新加载方法")
                    
        except Exception as e:
            self.logger.error(f"触发数据重新加载失败: {e}")
            # 出错时立即清除数据重载标志
            if hasattr(self, '_is_data_reloading'):
                self._is_data_reloading = False
    
    def _get_main_window(self):
        """
        获取主窗口实例
        
        Returns:
            主窗口实例或None
        """
        try:
            # 向上遍历父组件找到主窗口
            widget = self
            while widget:
                if hasattr(widget, 'dynamic_table_manager') and hasattr(widget, 'sort_state_manager'):
                    return widget
                widget = widget.parent()
            
            # 如果找不到，尝试从QApplication获取
            app = QApplication.instance()
            if app:
                for window in app.topLevelWidgets():
                    if hasattr(window, 'dynamic_table_manager') and hasattr(window, 'sort_state_manager'):
                        return window
            
            return None
        except Exception as e:
            self.logger.error(f"获取主窗口失败: {e}")
            return None
    
    def _reset_data_reloading_flag(self):
        """
        重置数据重新加载标志
        """
        try:
            self._is_data_reloading = False
            self.logger.debug("数据重新加载标志已重置")
        except Exception as e:
            self.logger.error(f"重置数据重新加载标志失败: {e}")
    
    def _reset_sorting_triggered_reload_flag(self):
        """🔧 [新架构] 旧架构方法，已移除"""
        pass
    
    def _reset_restoring_sort_state_flag(self):
        """🔧 [新架构] 旧架构方法，已移除"""
        pass
    
    def _restore_sort_indicators_after_reload(self):
        """
        数据重新加载完成后恢复排序指示器显示
        """
        try:
            self.logger.info("🔧 [排序指示器修复] 开始恢复排序指示器显示")
            
            # 🔧 [P1级线程安全修复] 在数据重载期间不恢复排序指示器，避免触发新的排序事件
            if hasattr(self, '_is_data_reloading') and self._is_data_reloading:
                self.logger.info("数据正在重载，延迟恢复排序指示器")
                
                # 🔧 [P2级导入修复] 线程安全检查和优化延迟调用
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import QThread, QTimer
                is_main_thread = QThread.currentThread() == QApplication.instance().thread()
                
                # 统一使用线程安全定时器，无需区分主线程和非主线程
                from src.utils.thread_safe_timer import safe_single_shot
                safe_single_shot(300, self._restore_sort_indicators_after_reload)
                self.logger.debug("🔧 [P0-修复] 使用线程安全定时器延迟恢复排序指示器")
                return
            
            # 🔧 [架构修复] 新架构不使用sort_manager，从排序状态管理器获取
            self.logger.info("🆕 [新架构排序] 使用TableSortStateManager获取排序状态")
            # TODO: 从 TableSortStateManager 获取排序状态
        except Exception as e:
            self.logger.error(f"恢复排序指示器失败: {e}")
    
    def _restore_sort_indicators_after_data_reload(self):
        """
        数据重新加载完成后恢复排序指示器显示 - 新架构版本
        """
        try:
            self.logger.info("🔧 [P0-排序修复] 开始恢复排序指示器显示")
            
            # 🔧 [P0-排序修复] 检查是否有新架构排序管理器
            if hasattr(self, 'sort_manager') and self.sort_manager:
                # 更新排序指示器显示
                self.sort_manager.update_sort_indicators()
                
                # 获取当前排序状态信息
                sort_info = self.sort_manager.get_sort_info()
                if sort_info['total_columns'] > 0:
                    self.logger.info(f"🔧 [P0-排序修复] 恢复排序指示器成功: {sort_info['total_columns']} 列排序")
                    self.logger.info(f"🔧 [P0-排序修复] 排序描述: {self.sort_manager.get_sort_description()}")
                else:
                    self.logger.info("🔧 [P0-排序修复] 无排序状态需要恢复")
            else:
                self.logger.warning("🔧 [P0-排序修复] 未找到新架构排序管理器，无法恢复排序指示器")
                
        except Exception as e:
            self.logger.error(f"🔧 [P0-排序修复] 恢复排序指示器失败: {e}")
    
    def _restore_ui_state_after_data_set(self):
        """🔧 [P0-2修复] 数据设置完成后恢复UI状态（列宽、排序等）"""
        try:
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                return
            
            # 获取主窗口
            main_window = self._get_main_window()
            if not main_window or not hasattr(main_window, '_restore_table_ui_state'):
                self.logger.debug("没有找到主窗口或状态恢复方法")
                return
            
            # 调用主窗口的状态恢复方法
            main_window._restore_table_ui_state(self.current_table_name)
            
            self.logger.info(f"🔧 [P0-2修复] 数据设置后UI状态恢复完成: {self.current_table_name}")
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-2修复] 数据设置后UI状态恢复失败: {e}")
    
    def _on_sort_applied(self, sorted_data):
        """🔧 [新架构] 已移除旧架构排序逻辑"""
        pass  # 新架构通过事件总线处理排序
    
    def _update_sort_indicators(self, sort_columns):
        """
        🔧 [修复标识] 更新排序指示器显示 - 简化版
        
        Args:
            sort_columns: 排序列列表
        """
        try:
            header = self.horizontalHeader()
            
            # 🔧 [关键修复] 简化信号处理，只在设置指示器时临时阻塞
            header.blockSignals(True)
            
            # 设置当前排序的指示器
            if sort_columns:
                # 显示优先级最高的排序列的指示器
                primary_sort = min(sort_columns, key=lambda x: x.priority)
                
                # 确保列索引有效
                if primary_sort.column_index >= 0 and primary_sort.column_index < self.columnCount():
                    qt_order = Qt.AscendingOrder if primary_sort.order.value == "ascending" else Qt.DescendingOrder
                    header.setSortIndicator(primary_sort.column_index, qt_order)
                    self.logger.info(f"🔧 [排序指示器修复] 设置排序指示器: 列{primary_sort.column_index}, 顺序{primary_sort.order.value}")
                else:
                    # 无效的列索引，清除排序指示器
                    header.setSortIndicator(-1, Qt.AscendingOrder)
                    self.logger.warning(f"🔧 [排序指示器修复] 清除排序指示器: 无效列索引{primary_sort.column_index}")
            else:
                # 没有排序列，清除排序指示器
                header.setSortIndicator(-1, Qt.AscendingOrder)
                self.logger.info("🔧 [排序指示器修复] 清除排序指示器: 无排序列")
            
            # 恢复信号处理
            header.blockSignals(False)
            
        except Exception as e:
            self.logger.error(f"🔧 [修复标识] 更新排序指示器失败: {e}")
            # 确保信号状态恢复
            try:
                header = self.horizontalHeader()
                header.blockSignals(False)
            except Exception as e:
                self.logger.error(f"🔧 [P0-修复] 排序后信号恢复失败: {e}", exc_info=True)
    
    def _refresh_table_after_sort(self):
        """排序后刷新表格显示"""
        try:
            # 重新填充可见数据
            if hasattr(self, '_populate_visible_data_with_row_numbers'):
                self._populate_visible_data_with_row_numbers()
            else:
                self._populate_visible_data()
            
            # 保持选择状态（如果需要）
            self._restore_selection_after_sort()
            
        except Exception as e:
            self.logger.error(f"排序后刷新表格失败: {e}")
    
    def _restore_selection_after_sort(self):
        """排序后恢复选择状态"""
        try:
            # 这里可以根据需要恢复选择状态
            # 由于排序改变了行的位置，需要特殊处理选择状态的恢复
            pass
            
        except Exception as e:
            self.logger.error(f"恢复选择状态失败: {e}")
    
    def get_sort_info(self) -> str:
        """
        获取当前排序信息描述
        
        Returns:
            str: 排序信息描述
        """
        try:
            # 🆕 [新架构多列排序] 使用新的多列排序管理器
            if self.sort_manager:
                return self.sort_manager.get_sort_description()
            return "🆕 [新架构排序] 使用自定义排序循环"
            
        except Exception as e:
            self.logger.error(f"获取排序信息失败: {e}")
            return "获取排序信息失败"
    
    def clear_all_sorts(self):
        """清除所有排序"""
        try:
            # 🆕 [新架构多列排序] 使用新的多列排序管理器
            if self.sort_manager:
                self.sort_manager.clear_all_sorts()
                self.logger.info("🆕 [新架构多列排序] 已清除所有排序")
            else:
                self.logger.info("🆕 [新架构排序] 多列排序管理器不可用")
                
        except Exception as e:
            self.logger.error(f"清除排序失败: {e}")
    
    # 🗑️ [新架构清理] 移除旧的排序信号处理器，使用自定义排序循环
    # def _on_header_sort_changed(self, logical_index: int, order):
    #     """旧的排序信号处理器 - 已移除，使用自定义排序循环"""
    #     pass

    def _on_header_clicked(self, logical_index: int):
        """
        处理表头点击事件 - 实现完整排序循环

        Args:
            logical_index: 被点击的列索引
        """
        # 🚨 [简化修复] 简化状态检查
        if getattr(self, '_is_sorting_in_progress', False):
            self.logger.info("排序操作正在进行，跳过")
            return
        
        # 🚨 [简化修复] 减少防护机制复杂度
        current_time = time.time()
        time_threshold = 0.2  # 减少到200毫秒，提高响应速度
        
        # 检查时间间隔防护
        if hasattr(self, '_last_header_click_time'):
            time_diff = current_time - self._last_header_click_time
            if time_diff < time_threshold:
                self.logger.info(f"🚨 [简化修复] 快速重复点击，间隔: {time_diff:.3f}s，跳过")
                return
        
        # 🔧 [P1-状态管理] 用于状态回滚的备份变量
        state_backup = {}
        
        # 🔧 [P0-紧急修复] 多重防护机制 - 在try块外进行检查
        if getattr(self, '_processing_header_click', False):
            self.logger.warning(f"🔧 [P0-紧急修复] 正在处理表头点击，跳过重复请求: 列{logical_index}")
            return

        # 🔧 [P1-状态管理] 验证列索引有效性 - 在try块外进行检查
        if logical_index < 0 or logical_index >= self.columnCount():
            self.logger.warning(f"🔧 [P1-状态管理] 列索引无效: {logical_index}, 总列数: {self.columnCount()}")
            return

        # 检查是否为序号列，只有序号列才阻止排序
        header_item = self.horizontalHeaderItem(logical_index)
        if header_item:
            column_name = header_item.text()
            # 只有列名为"序号"的列才阻止排序，"工号"等其他列都允许排序
            if column_name in ["序号"]:
                self.logger.debug(f"点击序号列 '{column_name}'，阻止排序操作")
                return

        try:
            # 🔧 [P0-紧急修复] 立即设置处理标志和时间戳
            self._processing_header_click = True
            self._last_header_click_time = current_time
            
            # 🔧 [P0-CRITICAL] 设置排序进行标志，防止并发操作
            self._is_sorting_in_progress = True

            # 🆕 [新架构多列排序] 使用新架构多列排序管理器
            if self.sort_manager:
                try:
                    # 获取列名
                    header_item = self.horizontalHeaderItem(logical_index)
                    column_name = header_item.text() if header_item else f"列{logical_index}"
                    
                    # 交给多列排序管理器处理
                    success = self.sort_manager.handle_header_click(logical_index, column_name)
                    if success:
                        self.logger.info(f"🆕 [新架构多列排序] 成功处理列{logical_index}({column_name})点击")
                        return  # 成功处理，直接返回
                    else:
                        self.logger.error(f"🆕 [新架构多列排序] 处理列{logical_index}({column_name})点击失败")
                        # 🔧 [逻辑修复] 不要无条件return，应该继续执行单列排序降级逻辑
                except Exception as sort_error:
                    self.logger.error(f"🆕 [新架构多列排序] 排序管理器异常: {sort_error}", exc_info=True)
                    # 🔧 [容错处理] 继续执行单列排序降级逻辑
            
            # 🔧 [逻辑修复] 如果多列排序管理器不可用或失败，降级到单列排序

            self.logger.info(f"🔧 [新架构排序] 表头点击: 列{logical_index}")

            # 🔧 [P1-状态管理] 备份当前状态
            try:
                header = self.horizontalHeader()
                state_backup = {
                    'current_sort_column': header.sortIndicatorSection(),
                    'current_sort_order': header.sortIndicatorOrder(),
                    'signals_blocked': header.signalsBlocked()
                }
            except Exception as backup_error:
                self.logger.error(f"🔧 [P1-状态管理] 状态备份失败: {backup_error}")
                state_backup = {}

            # 🆕 [单列排序降级] 实现完整的排序循环：无排序 → 升序 → 降序 → 无排序
            current_sort_state = self._get_column_sort_state(logical_index)
            next_sort_state = self._get_next_sort_state(current_sort_state)

            self.logger.info(f"🔧 [单列排序降级] 列{logical_index}: {current_sort_state} → {next_sort_state}")

            # 应用新的排序状态
            self._apply_column_sort_state(logical_index, next_sort_state)

        except Exception as e:
            self.logger.error(f"🔧 [P1-状态管理] 处理表头点击失败: {e}", exc_info=True)
            
            # 🔧 [P1-状态管理] 尝试状态回滚
            if state_backup:
                try:
                    self._restore_sort_state(state_backup)
                    self.logger.info("🔧 [P1-状态管理] 排序状态已回滚")
                except Exception as rollback_error:
                    self.logger.error(f"🔧 [P1-状态管理] 状态回滚失败: {rollback_error}")
            
        finally:
            # 🔧 [P1-状态管理] 确保处理标志被清除
            self._processing_header_click = False
            # 🔧 [P0-CRITICAL] 清除排序进行标志
            self._is_sorting_in_progress = False

    def _restore_sort_state(self, state_backup: dict):
        """
        🔧 [P1-状态管理] 恢复排序状态
        
        Args:
            state_backup: 之前备份的状态字典
        """
        try:
            if not state_backup:
                return
                
            header = self.horizontalHeader()
            
            # 恢复信号阻塞状态
            header.blockSignals(state_backup.get('signals_blocked', False))
            
            # 恢复排序指示器
            sort_column = state_backup.get('current_sort_column', -1)
            sort_order = state_backup.get('current_sort_order', 0)
            
            if sort_column >= 0:
                header.setSortIndicator(sort_column, sort_order)
            else:
                header.setSortIndicator(-1, 0)
                
            self.logger.debug(f"🔧 [P1-状态管理] 排序状态恢复: 列{sort_column}, 顺序{sort_order}")
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-状态管理] 恢复排序状态失败: {e}", exc_info=True)

    def _get_column_sort_state(self, logical_index: int) -> str:
        """
        获取列的当前排序状态

        Args:
            logical_index: 列索引

        Returns:
            str: 'none', 'ascending', 'descending'
        """
        try:
            header = self.horizontalHeader()
            current_sort_column = header.sortIndicatorSection()
            current_sort_order = header.sortIndicatorOrder()

            if current_sort_column == logical_index:
                if current_sort_order == Qt.AscendingOrder:
                    return 'ascending'
                elif current_sort_order == Qt.DescendingOrder:
                    return 'descending'

            return 'none'

        except Exception as e:
            self.logger.error(f"获取列排序状态失败: {e}")
            return 'none'

    def _get_next_sort_state(self, current_state: str) -> str:
        """
        获取下一个排序状态

        Args:
            current_state: 当前排序状态

        Returns:
            str: 下一个排序状态
        """
        # 完整的排序循环：无排序 → 升序 → 降序 → 无排序
        sort_cycle = {
            'none': 'ascending',
            'ascending': 'descending',
            'descending': 'none'
        }

        return sort_cycle.get(current_state, 'ascending')

    def _apply_column_sort_state(self, logical_index: int, sort_state: str):
        """
        应用列的排序状态

        Args:
            logical_index: 列索引
            sort_state: 排序状态 ('none', 'ascending', 'descending')
        """
        header = None
        original_signals_blocked = False
        sort_manager_disconnected = False
        
        try:
            header = self.horizontalHeader()
            
            # 🔧 [新架构] 使用自定义排序循环，无信号冲突
            self.logger.debug("🆕 [新架构排序] 使用自定义排序循环")
            
            # 🔧 [P0-紧急修复] 记录原始信号状态并强制阻塞
            original_signals_blocked = header.signalsBlocked()
            header.blockSignals(True)
            
            # 🔧 [P0-紧急修复] 额外阻塞表格本身的信号
            self.blockSignals(True)

            if sort_state == 'none':
                # 清除排序
                header.setSortIndicator(-1, Qt.AscendingOrder)
                self.logger.info(f"🔧 [排序循环] 清除列{logical_index}的排序")

                # 通知系统清除排序
                self._notify_sort_cleared()

            else:
                # 设置排序
                qt_order = Qt.AscendingOrder if sort_state == 'ascending' else Qt.DescendingOrder
                header.setSortIndicator(logical_index, qt_order)
                self.logger.info(f"🔧 [排序循环] 设置列{logical_index}排序: {sort_state}")

                # 通知系统应用排序
                self._notify_sort_applied(logical_index, sort_state)

        except Exception as e:
            self.logger.error(f"🔧 [P0-紧急修复] 应用列排序状态失败: {e}", exc_info=True)
            
        finally:
            # 🔧 [架构修复] 新架构不使用sort_manager，无需恢复信号连接
            self.logger.debug("🆕 [新架构排序] 无sort_manager，无需恢复信号连接")
            
            # 🔧 [P0-紧急修复] 确保信号恢复，即使出现异常
            try:
                if header:
                    header.blockSignals(original_signals_blocked)
                self.blockSignals(False)
            except Exception as restore_error:
                self.logger.error(f"🔧 [P0-紧急修复] 信号恢复失败: {restore_error}", exc_info=True)

    def _notify_sort_cleared(self):
        """通知系统清除排序"""
        try:
            # 通过主窗口处理排序清除
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, '_handle_sort_cleared'):
                main_window._handle_sort_cleared()
            elif main_window and hasattr(main_window, 'current_table_name'):
                # 发布排序清除事件
                self._publish_sort_clear_event(main_window.current_table_name)
            else:
                self.logger.warning("无法通知系统清除排序：主窗口不可用")

        except Exception as e:
            self.logger.error(f"通知排序清除失败: {e}")

    def _check_data_consistency_after_sort(self, sorted_data, original_headers):
        """排序后检查数据一致性"""
        try:
            issues = []

            # 检查1: 数据行数
            if not sorted_data:
                issues.append("排序后数据为空")
                return False, issues

            # 检查2: 数据列数
            if isinstance(sorted_data[0], dict):
                data_columns = len(sorted_data[0].keys())
                if data_columns != len(original_headers):
                    issues.append(f"数据列数不匹配: {data_columns} vs {len(original_headers)}")

            # 检查3: 必要字段存在性
            required_fields = ['工号', 'employee_id']
            if isinstance(sorted_data[0], dict):
                data_keys = sorted_data[0].keys()
                has_required = any(field in data_keys for field in required_fields)
                if not has_required:
                    issues.append("缺少必要的标识字段")

            # 检查4: 数据完整性
            for i, row in enumerate(sorted_data[:5]):  # 检查前5行
                if isinstance(row, dict):
                    if not any(str(v).strip() for v in row.values() if v is not None):
                        issues.append(f"第{i+1}行数据全为空")

            if issues:
                return False, issues
            else:
                return True, ["数据一致性检查通过"]

        except Exception as e:
            return False, [f"一致性检查异常: {e}"]

    def _validate_column_consistency_before_sort(self):
        """排序前验证列数一致性"""
        try:
            current_column_count = self.columnCount()

            # 检查数据列数
            if hasattr(self, '_current_data') and self._current_data:
                if isinstance(self._current_data[0], dict):
                    data_column_count = len(self._current_data[0].keys())
                else:
                    data_column_count = len(self._current_data[0]) if hasattr(self._current_data[0], '__len__') else 0

                if data_column_count != current_column_count:
                    self.logger.warning(f"🔧 [排序修复] 排序前发现列数不匹配: 数据{data_column_count}列, 表头{current_column_count}列")
                    return False, f"列数不匹配: 数据{data_column_count}列, 表头{current_column_count}列"

            return True, "列数一致性验证通过"

        except Exception as e:
            self.logger.error(f"🔧 [排序修复] 列数一致性验证失败: {e}")
            return False, f"验证异常: {e}"
            
    def _is_numeric_value(self, original_value, formatted_value) -> bool:
        """
        🔧 [P1-2修复] 增强的数字类型判断
        判断一个值是否为数字类型，应该右对齐显示
        """
        try:
            # 1. 原始值是数字类型
            if isinstance(original_value, (int, float)):
                return True
                
            # 2. 格式化后的值是数字字符串
            if isinstance(formatted_value, str) and formatted_value.strip():
                # 去除千分位分隔符和货币符号
                clean_value = formatted_value.replace(',', '').replace('￥', '').replace('$', '').strip()
                try:
                    float(clean_value)
                    return True
                except ValueError:
                    pass
                    
            # 3. 原始值是数字字符串
            if isinstance(original_value, str) and original_value.strip():
                try:
                    float(original_value.replace(',', ''))
                    return True
                except ValueError:
                    pass
                    
            return False
            
        except Exception as e:
            self.logger.debug(f"🔧 [P1-2修复] 数字类型判断异常: {e}")
            return False

    def _notify_sort_applied(self, logical_index: int, sort_state: str):
        """通知系统应用排序"""
        try:
            self.logger.info(f"🔧 [排序调试] 开始处理排序通知: 列{logical_index}, 状态{sort_state}")

            # 🔧 [排序修复] 排序前验证列数一致性
            is_valid, validation_msg = self._validate_column_consistency_before_sort()
            if not is_valid:
                self.logger.error(f"🔧 [排序修复] 排序前验证失败: {validation_msg}")
                # 不阻止排序，但记录警告
            else:
                self.logger.info(f"🔧 [排序修复] 排序前验证通过: {validation_msg}")

            # 获取列名
            if logical_index >= self.columnCount():
                self.logger.error(f"🔧 [排序调试] 列索引超出范围: {logical_index} >= {self.columnCount()}")
                return

            header_item = self.horizontalHeaderItem(logical_index)
            if not header_item:
                self.logger.error(f"🔧 [排序调试] 无法获取列{logical_index}的表头信息")
                return

            column_name = header_item.text()
            self.logger.info(f"🔧 [排序调试] 列名: {column_name}")

            # 🔧 [保持功能] 强化直接数据库排序逻辑，保持所有自定义排序功能
            main_window = self._get_main_window()
            if main_window:
                try:
                    # 获取当前表名
                    table_name = getattr(main_window, 'current_table_name', '')
                    if not table_name and hasattr(main_window, 'main_workspace'):
                        table_name = getattr(main_window.main_workspace, 'current_table_name', '')
                    
                    if table_name:
                        self.logger.info(f"🔧 [保持功能] 开始自定义排序: {table_name}")
                        
                        # 获取数据库管理器
                        if hasattr(main_window, 'dynamic_table_manager'):
                            # 🔧 [强化] 改进字段名映射，支持更多字段
                            db_field_name = self._convert_column_name_to_db_field(column_name, table_name)
                            if not db_field_name:
                                # 备用映射逻辑
                                field_mapping = {
                                    "2025年薪级工资": "grade_salary_2025",
                                    "2025年岗位工资": "position_salary_2025", 
                                    "工号": "employee_id",
                                    "姓名": "employee_name",
                                    "部门名称": "department",
                                    "人员类别": "employee_type"
                                }
                                db_field_name = field_mapping.get(column_name, column_name)
                            
                            self.logger.info(f"🔧 [保持功能] 字段名映射: {column_name} -> {db_field_name}")
                            
                            # 构建排序列
                            sort_columns = [{
                                'column_name': db_field_name,
                                'order': sort_state
                            }]
                            
                            # 🔧 [保持功能] 保持当前页码，不强制跳转到第1页
                            current_page = 1
                            page_size = 50
                            if hasattr(main_window, 'main_workspace') and hasattr(main_window.main_workspace, 'pagination_widget'):
                                try:
                                    pagination_widget = main_window.main_workspace.pagination_widget
                                    current_page = getattr(pagination_widget, 'current_page', 1)
                                    page_size = getattr(pagination_widget, 'page_size', 50)
                                    self.logger.info(f"🔧 [保持功能] 保持当前页码: {current_page}")
                                except Exception:
                                    pass
                            
                            # 直接调用数据库排序查询
                            df, total = main_window.dynamic_table_manager.get_dataframe_paginated_with_sort(
                                table_name, current_page, page_size, sort_columns
                            )
                            
                            if df is not None and not df.empty:
                                self.logger.info(f"🔧 [保持功能] 数据库排序成功: {len(df)}行, 排序列: {db_field_name} {sort_state}")
                                
                                # 应用字段映射（英文->中文表头）
                                if hasattr(main_window.main_workspace, '_apply_field_mapping_to_dataframe'):
                                    df = main_window.main_workspace._apply_field_mapping_to_dataframe(df, table_name)
                                    self.logger.info(f"🔧 [保持功能] 字段映射完成")
                                
                                # 🔧 [架构修复] 确保数据正确显示，使用改进的set_data方法
                                headers = df.columns.tolist()
                                data = df.to_dict('records')
                                
                                # 🔧 [P1修复] 在排序前缓存当前表类型，确保一致性
                                cached_table_type = self.current_table_type
                                if not cached_table_type:
                                    # 如果没有缓存的表类型，使用统一识别方法
                                    from src.services.table_data_service import TableDataService
                                    service = TableDataService()
                                    cached_table_type = service._extract_table_type_from_name(table_name)
                                
                                self.logger.info(f"🔧 [P1修复] 缓存表类型: {cached_table_type}，用于排序后数据设置")
                                
                                # 🔧 [保持功能] 设置分页模式标志，保持排序状态
                                self._pagination_mode = True
                                
                                # 直接调用表格的数据设置方法，并强制使用缓存的表类型
                                self.set_data(data, headers, current_table_name=table_name, force_table_type=cached_table_type)
                                self.logger.info(f"🔧 [保持功能] UI数据更新完成: {len(data)}行")
                                
                                # 🔧 [保持功能] 更新分页信息，保持分页状态
                                if hasattr(main_window, 'main_workspace') and hasattr(main_window.main_workspace, 'pagination_widget'):
                                    try:
                                        pagination_widget = main_window.main_workspace.pagination_widget
                                        pagination_widget.set_total_records(total)
                                        # 静默更新当前页，不触发额外的数据加载
                                        if hasattr(pagination_widget, 'set_current_page_silent'):
                                            pagination_widget.set_current_page_silent(current_page)
                                        elif hasattr(pagination_widget, 'silent_set_current_page'):
                                            pagination_widget.silent_set_current_page(current_page)
                                        self.logger.info(f"🔧 [保持功能] 分页信息更新: 第{current_page}页，总计{total}条")
                                    except Exception as page_error:
                                        self.logger.warning(f"🔧 [保持功能] 分页信息更新失败: {page_error}")
                                
                                # 🔧 [保持功能] 重置分页模式标志
                                self._pagination_mode = False
                                
                                self.logger.info(f"🔧 [保持功能] 自定义排序完成！保持所有高级功能")
                                return  # 自定义排序成功，不需要继续备用流程
                            else:
                                self.logger.error(f"🚨 [紧急修复] 数据库排序返回空数据")
                        else:
                            self.logger.error(f"🚨 [紧急修复] 无法获取数据库管理器")
                    else:
                        self.logger.error(f"🚨 [紧急修复] 无法获取当前表名")
                        
                except Exception as emergency_error:
                    self.logger.error(f"🚨 [紧急修复] 直接修复失败: {emergency_error}")
                    import traceback
                    traceback.print_exc()

            # 继续原有的事件流程（作为备用）
            # 通过主窗口处理排序
            if main_window and hasattr(main_window, '_handle_sort_applied'):
                self.logger.info(f"🔧 [排序调试] 调用主窗口排序处理: {column_name} {sort_state}")
                main_window._handle_sort_applied(logical_index, column_name, sort_state)
            elif main_window and hasattr(main_window, 'current_table_name'):
                # 发布排序应用事件
                self.logger.info(f"🔧 [排序调试] 发布排序事件: {main_window.current_table_name}")
                self._publish_sort_apply_event(main_window.current_table_name, column_name, sort_state)
            else:
                self.logger.error("🔧 [排序调试] 无法通知系统应用排序：主窗口不可用")

        except Exception as e:
            self.logger.error(f"🔧 [排序调试] 通知排序应用失败: {e}")
            import traceback
            traceback.print_exc()

    def _convert_column_name_to_db_field(self, column_name: str, table_name: str) -> str:
        """将中文列名转换为英文数据库字段名"""
        try:
            # 尝试获取字段映射
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'main_workspace'):
                if hasattr(main_window.main_workspace, '_get_reverse_field_mapping'):
                    field_mapping = main_window.main_workspace._get_reverse_field_mapping(table_name)
                    if field_mapping and column_name in field_mapping:
                        return field_mapping[column_name]
            
            # 如果没有映射，返回原名
            return column_name
            
        except Exception as e:
            self.logger.warning(f"字段名转换失败: {e}")
            return column_name

    def _get_main_window(self):
        """获取主窗口引用"""
        try:
            # 向上查找主窗口
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_table_name') and hasattr(parent, 'event_bus'):
                    return parent
                parent = parent.parent()
            return None
        except Exception as e:
            self.logger.error(f"获取主窗口失败: {e}")
            return None

    def _publish_sort_clear_event(self, table_name: str):
        """发布排序清除事件"""
        try:
            from src.core.event_bus import SortRequestEvent

            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'event_bus'):
                # 获取当前页码
                current_page = 1
                if hasattr(main_window, 'main_workspace') and main_window.main_workspace:
                    if hasattr(main_window.main_workspace, 'pagination_widget'):
                        try:
                            current_page = getattr(main_window.main_workspace.pagination_widget, 'current_page', 1)
                        except Exception as e:
                            self.logger.error(f"🔧 [P0-修复] 获取当前页码失败: {e}", exc_info=True)

                # 创建清除排序事件（空的排序列表）
                sort_event = SortRequestEvent(
                    event_type="sort_request",
                    table_name=table_name,
                    sort_columns=[],  # 空列表表示清除排序
                    current_page=current_page
                )

                main_window.event_bus.publish(sort_event)
                self.logger.info(f"🔧 [排序清除] 已发布排序清除事件: {table_name}")
            else:
                self.logger.warning("无法发布排序清除事件：事件总线不可用")

        except Exception as e:
            self.logger.error(f"发布排序清除事件失败: {e}")

    def _publish_sort_apply_event(self, table_name: str, column_name: str, sort_state: str):
        """发布排序应用事件"""
        try:
            from src.core.event_bus import SortRequestEvent

            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'event_bus'):
                # 获取当前页码
                current_page = 1
                if hasattr(main_window, 'main_workspace') and main_window.main_workspace:
                    if hasattr(main_window.main_workspace, 'pagination_widget'):
                        try:
                            current_page = getattr(main_window.main_workspace.pagination_widget, 'current_page', 1)
                        except Exception as e:
                            self.logger.error(f"🔧 [P0-修复] 获取当前页码失败: {e}", exc_info=True)

                # 创建排序应用事件
                sort_columns = [{
                    'column_name': column_name,
                    'order': sort_state,
                    'priority': 0
                }]

                sort_event = SortRequestEvent(
                    event_type="sort_request",
                    table_name=table_name,
                    sort_columns=sort_columns,
                    current_page=current_page
                )

                main_window.event_bus.publish(sort_event)
                self.logger.info(f"🔧 [排序应用] 已发布排序应用事件: {table_name}, {column_name} {sort_state}")
            else:
                self.logger.warning("无法发布排序应用事件：事件总线不可用")

        except Exception as e:
            self.logger.error(f"发布排序应用事件失败: {e}")
    
    def _get_reverse_field_mapping(self) -> dict:
        """🔧 [新架构] 动态获取反向字段映射（数据库字段名 -> 中文列名）"""
        try:
            # 方法1：从主窗口获取字段映射
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'current_table_name'):
                current_table_name = getattr(main_window, 'current_table_name', '')
                if current_table_name:
                    # 获取字段映射（数据库字段名 -> 中文列名）
                    field_mapping = main_window._get_current_field_mapping(current_table_name)
                    if field_mapping:
                        # 🔧 [关键修复] field_mapping 本身就是 数据库字段名 -> 中文列名 的映射
                        self.logger.debug(f"🔧 [新架构] 从主窗口获取字段映射 {len(field_mapping)} 个")
                        return field_mapping
            
            # 方法2：从统一配置文件读取
            import json
            from pathlib import Path
            
            config_path = Path(__file__).parent.parent.parent.parent.parent / "state" / "data" / "field_mappings.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 🔧 [关键修复] 直接从当前表的配置获取映射
                table_mappings = config_data.get('table_mappings', {})
                
                # 方法2.1：尝试通过主窗口的当前表名获取
                main_window = self._get_main_window()
                if main_window and hasattr(main_window, 'current_table_name'):
                    current_table_name = getattr(main_window, 'current_table_name', '')
                    if current_table_name and current_table_name in table_mappings:
                        field_mappings = table_mappings[current_table_name].get('field_mappings', {})
                        if field_mappings:
                            self.logger.debug(f"🔧 [新架构] 从配置文件获取当前表映射 {len(field_mappings)} 个")
                            return field_mappings
                
                # 方法2.2：从所有表格模板中构建通用映射
                all_mappings = {}
                if 'field_templates' in config_data:
                    for template_name, mappings in config_data['field_templates'].items():
                        # 数据库字段名 -> 中文列名
                        for db_field, chinese_name in mappings.items():
                            all_mappings[db_field] = chinese_name
                
                self.logger.debug(f"🔧 [新架构] 从配置文件获取通用映射 {len(all_mappings)} 个")
                return all_mappings
            
            # 方法3：使用后备映射
            return self._get_fallback_reverse_mapping()
            
        except Exception as e:
            self.logger.error(f"🔧 [新架构] 获取反向字段映射失败: {e}")
            return self._get_fallback_reverse_mapping()
    
    def _get_fallback_reverse_mapping(self) -> dict:
        """🔧 [新架构] 获取后备反向字段映射"""
        return {
            # 通用字段
            "sequence_number": "序号",
            "employee_id": "工号",
            "employee_name": "姓名", 
            "department": "部门名称",
            "employee_type": "人员类别",
            "employee_type_code": "人员类别代码",
            
            # 在职人员字段
            "position_salary_2025": "2025年岗位工资",
            "grade_salary_2025": "2025年薪级工资",
            "seniority_salary_2025": "2025年校龄工资",  # A岗职工特有
            "allowance": "津贴",
            "balance_allowance": "结余津贴",
            "basic_performance_2025": "2025年基础性绩效",
            "health_fee": "卫生费",
            "living_allowance_2025": "2025年生活补贴",  # A岗职工特有
            "transport_allowance": "交通补贴",
            "property_allowance": "物业补贴",
            "housing_allowance": "住房补贴",
            "car_allowance": "车补",
            "communication_allowance": "通讯补贴",
            "performance_bonus_2025": "2025年奖励性绩效预发",
            "supplement": "补发",
            "advance": "借支",
            "total_salary": "应发工资",
            "provident_fund_2025": "2025公积金",
            "insurance_deduction": "保险扣款",
            "pension_insurance": "代扣代存养老保险",
            
            # 离退休人员字段
            "basic_retirement_salary": "基本离休费",  # 也可能是基本退休费
            "living_allowance": "生活补贴",
            "retirement_allowance": "离休补贴",
            "nursing_fee": "护理费",
            "one_time_living_allowance": "增发一次性生活补贴",
            "total": "合计",
            "retirement_living_allowance": "离退休生活补贴",
            "salary_advance": "增资预付",
            "adjustment_2016": "2016待遇调整",
            "adjustment_2017": "2017待遇调整",
            "adjustment_2018": "2018待遇调整",
            "adjustment_2019": "2019待遇调整",
            "adjustment_2020": "2020待遇调整",
            "adjustment_2021": "2021待遇调整",
            "adjustment_2022": "2022待遇调整",
            "adjustment_2023": "2023待遇调整",
            "provident_fund": "公积",
            "remarks": "备注"
        }
    def debug_pagination_state(self):
        """🔧 [分页行号修复] 调试分页状态"""
        try:
            if hasattr(self, 'pagination_state') and self.pagination_state:
                state = self.pagination_state
                self.logger.info(f"🔧 [调试] 分页状态: 第{state.get('current_page', 'N/A')}页")
                self.logger.info(f"🔧 [调试] 记录范围: {state.get('start_record', 'N/A')}-{state.get('end_record', 'N/A')}")
                self.logger.info(f"🔧 [调试] 总记录数: {state.get('total_records', 'N/A')}")
                
                # 检查当前表格状态
                row_count = self.rowCount()
                self.logger.info(f"🔧 [调试] 表格行数: {row_count}")
                
                if row_count > 0:
                    # 检查前3行的行号
                    for i in range(min(3, row_count)):
                        header_item = self.verticalHeaderItem(i)
                        actual_label = header_item.text() if header_item else "N/A"
                        expected_label = str(state.get('start_record', 1) + i)
                        status = "✅" if actual_label == expected_label else "❌"
                        self.logger.info(f"🔧 [调试] 第{i}行: 期望{expected_label}, 实际{actual_label} {status}")
            else:
                self.logger.info("🔧 [调试] 无分页状态")
                
        except Exception as e:
            self.logger.error(f"🔧 [调试] 分页状态调试失败: {e}")
    
    def force_fix_row_numbers_now(self):
        """🔧 [分页行号修复] 立即强制修复行号"""
        try:
            self.logger.info("🔧 [立即修复] 开始强制修复行号...")
            
            # 调试当前状态
            self.debug_pagination_state()
            
            # 强制修复
            self._force_update_pagination_row_numbers()
            
            # 验证结果
            # 🔧 [重影修复] 移除验证机制，HeaderUpdateManager已确保行号正确
            
            self.logger.info("🔧 [立即修复] 强制修复完成")
            
        except Exception as e:
            self.logger.error(f"🔧 [立即修复] 强制修复失败: {e}")

    def _cleanup_timers(self):
        """🔧 [P0-CRITICAL修复] 全面清理所有定时器和资源"""
        try:
            self._timer_destroyed = True
            self._cleanup_save_timer()
            self._cleanup_delayed_update_timer()

            # 🔧 [P0-CRITICAL修复] 清理重复操作防护的时间戳记录
            if hasattr(self, '_last_restore_time'):
                self._last_restore_time.clear()
            if hasattr(self, '_last_pagination_restore'):
                self._last_pagination_restore.clear()

            # 🔧 [P0-CRITICAL修复] 清理缓存数据
            if hasattr(self, '_restored_tables'):
                self._restored_tables.clear()
            if hasattr(self, '_no_config_tables'):
                self._no_config_tables.clear()

            self.logger.debug("🔧 [P0-CRITICAL修复] 所有定时器和缓存资源已安全清理")
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"🔧 [P0-CRITICAL修复] 清理资源时出错: {e}")

    # 🔧 [Timer修复] closeEvent已在上方定义，移除重复

# 示例和测试代码
def create_sample_data(count: int = 100) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    创建示例数据
    
    Args:
        count: 数据行数
        
    Returns:
        Tuple[List[Dict[str, Any]], List[str]]: 数据和表头
    """
    headers = ["ID", "姓名", "部门", "职位", "薪资", "入职日期", "状态"]
    
    departments = ["技术部", "市场部", "财务部", "人事部", "运营部"]
    positions = ["工程师", "经理", "主管", "专员", "总监"]
    
    data = []
    for i in range(count):
        row = {
            "ID": f"EMP{i+1:04d}",
            "姓名": f"员工{i+1}",
            "部门": departments[i % len(departments)],
            "职位": positions[i % len(positions)],
            "薪资": 5000 + (i % 20) * 1000,
            "入职日期": f"2023-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            "状态": "在职" if i % 10 != 0 else "离职"
        }
        data.append(row)
    
    return data, headers

def demo_max_visible_rows():
    """
    演示max_visible_rows功能的示例
    """
    import sys
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("VirtualizedExpandableTable - max_visible_rows 演示")
    window.resize(1200, 800)
    
    # 创建表格组件，指定max_visible_rows
    table = VirtualizedExpandableTable(parent=window, max_visible_rows=500)
    window.setCentralWidget(table)
    
    # 创建大量测试数据
    large_data, headers = create_sample_data(2000)
    
    # 设置数据，启用自动调整
    table.set_data(large_data, headers, auto_adjust_visible_rows=True)
    
    # 输出性能统计
    stats = table.get_performance_stats()
    print(f"性能统计: {stats}")
    
    # 手动调整max_visible_rows
    print(f"当前最大可见行数: {table.get_max_visible_rows()}")
    
    # 尝试设置不同的值
    table.set_max_visible_rows(200)
    print(f"调整后最大可见行数: {table.get_max_visible_rows()}")
    
    # 尝试设置无效值
    success = table.set_max_visible_rows(-1)
    print(f"设置无效值(-1)是否成功: {success}")
    
    # 再次输出性能统计
    stats = table.get_performance_stats()
    print(f"调整后性能统计: {stats}")
    
    window.show()
    return app.exec_()

if __name__ == "__main__":
    # 运行演示
    demo_max_visible_rows() 