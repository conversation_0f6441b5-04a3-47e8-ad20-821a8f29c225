# 统一格式管理系统实现方案

## 📝 需求背景

用户需求：**"应该采用统一的格式管理（表头及其数据格式化），不管主界面列表展示区域的数据来自那条路径，展示出来的表头及其数据格式都是统一的，日后如果有新的格式变动，更改一次就能够统一所有路径来源数据的显示格式。"**

## 🎯 解决方案概述

构建了一个完整的统一格式管理系统，确保不管数据来自哪个路径，表头和数据格式都完全统一。系统采用**配置驱动架构**，实现了**一次配置，全局生效**的格式管理目标。

## 🏗️ 系统架构

### 核心组件

```
统一格式管理系统
├── UnifiedFormatManager (核心管理器)
├── FormatConfig (格式配置管理)
├── FieldRegistry (字段注册系统)
└── FormatRenderer (数据渲染器)
```

### 架构特点

- **单一数据源**: 所有格式配置集中管理
- **统一接口**: 对外提供简洁统一的API
- **配置驱动**: 通过配置文件控制格式规则
- **可扩展性**: 支持新表格类型的快速接入

## 🔧 实现细节

### 1. 核心管理器 (UnifiedFormatManager)

**文件**: `src/modules/format_management/unified_format_manager.py`

**主要功能**:
- 统一数据格式化入口
- 协调各子组件工作
- 提供缓存机制
- 错误处理和降级方案

**关键API**:
```python
# 完整表格格式化
formatted_headers, formatted_data = format_manager.format_table_complete(
    data, table_type, data_source
)

# 单独格式化表头
formatted_headers = format_manager.format_headers(
    raw_headers, table_type, data_source
)

# 单独格式化数据
formatted_data = format_manager.format_data(
    raw_data, table_type, headers, data_source
)
```

### 2. 格式配置管理 (FormatConfig)

**文件**: `src/modules/format_management/format_config.py`

**配置文件**: `state/data/format_config.json`

**配置结构**:
```json
{
  "table_types": {
    "active_employees": {
      "display_name": "全部在职人员工资表",
      "priority": 1,
      "enabled": true
    }
  },
  "default_formats": {
    "currency": {
      "decimal_places": 2,
      "thousand_separator": ",",
      "symbol": "¥",
      "symbol_position": "prefix"
    },
    "date": {
      "display_format": "%Y年%m月%d日"
    }
  }
}
```

### 3. 字段注册系统 (FieldRegistry)

**文件**: `src/modules/format_management/field_registry.py`

**映射文件**: `state/data/field_mappings.json`

**功能特性**:
- 数据库字段名 ↔ 显示名称映射
- 字段类型定义和检测
- 字段验证规则管理
- 动态字段注册和更新

**映射示例**:
```json
{
  "table_mappings": {
    "active_employees": {
      "field_mappings": {
        "employee_id": "工号",
        "employee_name": "姓名",
        "position_salary_2025": "2025年岗位工资"
      },
      "field_types": {
        "employee_id": "string",
        "position_salary_2025": "currency"
      }
    }
  }
}
```

### 4. 数据渲染器 (FormatRenderer)

**文件**: `src/modules/format_management/format_renderer.py`

**支持的数据类型**:
- **currency**: 货币格式 (¥1,234.56)
- **integer**: 整型格式 (1,234)
- **float**: 浮点型格式 (1,234.56)
- **percentage**: 百分比格式 (12.3%)
- **date**: 日期格式 (2025年07月18日)
- **string**: 字符串格式

## 🔗 集成点

### 1. 数据服务层集成

**文件**: `src/services/table_data_service.py`

**集成位置**: `load_table_data()` 方法

**实现**:
```python
# 🎯 [统一格式管理] 格式化数据
if response.data is not None and not response.data.empty:
    table_type = self._extract_table_type_from_name(table_name)
    
    formatted_headers, formatted_data = self.format_manager.format_table_complete(
        response.data, 
        table_type, 
        data_source=f"table_data_service:{table_name}"
    )
    
    response.data = formatted_data
```

### 2. 表格组件集成

**文件**: `src/gui/prototype/widgets/virtualized_expandable_table.py`

**集成位置**: `set_data()` 方法

**实现**:
```python
# 🎯 [统一格式管理] 统一格式化处理
if hasattr(self, 'format_manager') and self.format_manager:
    table_type = self._extract_table_type_from_name(self.current_table_name)
    
    formatted_headers, formatted_data = self.format_manager.format_table_complete(
        df, table_type, 
        data_source=f"virtualized_table:{self.current_table_name}"
    )
    
    data = formatted_data.to_dict('records')
    displayed_headers = formatted_headers
```

## 📊 支持的表格类型

### 当前支持的表格类型

| 类型ID | 显示名称 | 描述 |
|--------|----------|------|
| `active_employees` | 全部在职人员工资表 | 包含所有在职员工的工资数据 |
| `retired_employees` | 离休人员工资表 | 离休员工的工资数据 |
| `part_time_employees` | 临时工工资表 | 临时工和兼职员工的工资数据 |
| `contract_employees` | 合同工工资表 | 合同制员工的工资数据 |

### 表格类型自动识别

系统会根据表名自动识别表格类型：

```python
def _extract_table_type_from_name(self, table_name: str) -> str:
    table_name_lower = table_name.lower()
    
    if 'active_employees' in table_name_lower or '全部在职人员' in table_name:
        return 'active_employees'
    elif 'retired_employees' in table_name_lower or '离休人员' in table_name:
        return 'retired_employees'
    # ... 其他类型匹配
```

## 🎨 格式化效果

### 货币格式
- **输入**: `12345.67`
- **输出**: `¥12,345.67`

### 日期格式
- **输入**: `2025-07-18`
- **输出**: `2025年07月18日`

### 整数格式
- **输入**: `1234567`
- **输出**: `1,234,567`

### 字符串格式
- **输入**: `  张三  `
- **输出**: `张三`

## 📁 文件结构

```
src/modules/format_management/
├── __init__.py                 # 模块初始化
├── unified_format_manager.py   # 统一格式管理器
├── format_config.py           # 格式配置管理
├── field_registry.py          # 字段注册系统
└── format_renderer.py         # 数据渲染器

state/data/
├── format_config.json         # 格式配置文件
├── field_mappings.json        # 字段映射文件
└── backups/                   # 配置备份目录
```

## 🔄 数据流向

```mermaid
graph TB
    A[原始数据] --> B[TableDataService]
    B --> C[UnifiedFormatManager]
    C --> D[FormatConfig]
    C --> E[FieldRegistry]
    C --> F[FormatRenderer]
    D --> G[格式规则]
    E --> H[字段映射]
    F --> I[数据渲染]
    G --> I
    H --> I
    I --> J[格式化数据]
    J --> K[VirtualizedExpandableTable]
    K --> L[统一显示]
```

## ⚙️ 配置管理

### 格式配置更新

```python
# 更新格式配置
format_manager.update_format_config(
    'default_formats.currency.decimal_places', 
    3
)

# 保存配置
format_manager.format_config.save_config()
```

### 字段映射更新

```python
# 更新字段映射
format_manager.update_field_mapping(
    'active_employees',
    {'new_field': '新字段显示名'}
)

# 保存映射
format_manager.field_registry.save_mappings()
```

## 🚀 使用效果

### ✅ 实现的目标

1. **格式一致性**: 所有数据源的显示格式完全统一
2. **集中管理**: 格式变更只需在配置文件中修改一次
3. **兼容性**: 与现有系统完全兼容，无需重构
4. **可扩展性**: 支持新表格类型的快速接入

### 🎯 核心优势

- **🔄 统一入口**: 所有数据路径都经过统一格式化
- **🎨 一致显示**: 货币、日期、数字等格式完全统一
- **📝 配置驱动**: 修改配置文件即可调整全系统格式
- **🔧 易于维护**: 新增格式规则只需一次配置

## 📈 性能特性

### 缓存机制
- 格式化结果缓存，避免重复计算
- 配置文件缓存，减少I/O操作
- 智能缓存失效策略

### 错误处理
- 格式化失败时自动降级到原始数据
- 详细的错误日志记录
- 不影响主要业务流程

### 性能优化
- 批量数据处理优化
- 懒加载配置文件
- 异步处理支持

## 🔮 扩展计划

### 即将支持的功能

1. **条件格式化**: 根据数据值应用不同格式
2. **多语言支持**: 支持中英文切换
3. **自定义格式**: 用户自定义格式规则
4. **导出格式**: 统一的导出格式管理

### 配置文件扩展

```json
{
  "conditional_formatting": {
    "salary_ranges": {
      "low": {"max": 5000, "color": "#ffcdd2"},
      "high": {"min": 15000, "color": "#e8f5e8"}
    }
  },
  "localization": {
    "zh_CN": {"currency_symbol": "¥"},
    "en_US": {"currency_symbol": "$"}
  }
}
```

## 🎉 总结

统一格式管理系统完全满足了用户的需求：

> **"日后如果有新的格式变动，更改一次就能够统一所有路径来源数据的显示格式"**

通过配置驱动的架构设计，实现了：
- **一次配置，全局生效**
- **统一的数据显示格式**
- **易于维护和扩展**
- **与现有系统完全兼容**

这个系统为工资数据管理提供了强大而灵活的格式化支持，确保了数据展示的专业性和一致性。