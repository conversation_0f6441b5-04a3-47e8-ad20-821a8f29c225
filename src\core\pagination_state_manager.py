#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页状态管理器
🔧 [立即修复] 强化分页防循环机制，使用原子操作和时间戳管理
"""

import time
import threading
from typing import Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger


class PaginationState(Enum):
    """分页状态枚举"""
    IDLE = "idle"           # 空闲状态
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败


@dataclass
class PaginationRequest:
    """分页请求记录"""
    table_name: str
    page: int
    timestamp: float
    request_id: str
    state: PaginationState
    thread_id: int
    sort_columns: Optional[list] = None


class PaginationStateManager:
    """
    🔧 [立即修复] 分页状态管理器
    
    使用原子操作和时间戳管理分页处理标志，防止竞态条件
    """
    
    def __init__(self):
        """初始化分页状态管理器"""
        self.logger = setup_logger(self.__class__.__name__)
        
        # 使用可重入锁确保线程安全
        self._lock = threading.RLock()
        
        # 分页请求状态记录
        self._active_requests: Dict[str, PaginationRequest] = {}
        
        # 全局分页处理状态
        self._global_processing = False
        self._global_processing_start_time = 0.0
        
        # 🔧 [P0-CRITICAL修复] 优化超时配置参数，减少"未找到分页请求"警告
        self._max_processing_time = 15.0  # 全局最大处理时间（秒） - 增加到15秒
        self._request_timeout = 10.0      # 请求超时时间（秒） - 增加到10秒 
        self._quick_timeout = 3.0         # 快速超时（秒） - 增加到3秒，用于重复请求检测
        self._cleanup_interval = 2.0      # 清理检查间隔（秒） - 增加到2秒
        
        self.logger.info("🔧 [立即修复] 分页状态管理器初始化完成")
    
    def can_start_pagination(self, table_name: str, page: int, 
                           sort_columns: Optional[list] = None) -> bool:
        """
        🔧 [立即修复] 检查是否可以开始分页操作
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            
        Returns:
            True: 可以开始分页, False: 不能开始（有冲突）
        """
        current_time = time.time()
        thread_id = threading.get_ident()
        request_key = self._generate_request_key(table_name, page, sort_columns)
        
        with self._lock:
            # 🔧 [P0级修复] 优化全局处理状态检查，只拒绝相同请求而非所有请求
            if self._global_processing:
                processing_duration = current_time - self._global_processing_start_time
                if processing_duration > self._max_processing_time:
                    # 处理时间过长，强制重置
                    self.logger.warning(f"🔧 [强制重置] 分页处理超时，强制重置: {processing_duration:.2f}s")
                    self._global_processing = False
                    self._global_processing_start_time = 0.0
                    self._active_requests.clear()
                else:
                    # 🔧 [P0级修复] 不再全局拒绝，只在后续检查具体的相同请求
                    self.logger.debug(f"🔍 [状态检查] 全局分页处理中，但允许检查具体请求: {table_name}, 页{page}")
                    # 继续执行，不直接返回False
            
            # 🔧 [P0级修复] 优化相同请求检查，更加智能的去重策略
            if request_key in self._active_requests:
                existing_request = self._active_requests[request_key]
                request_age = current_time - existing_request.timestamp
                
                # 🔧 [P1级超时优化] 使用快速超时进行重复请求检测
                quick_timeout = self._quick_timeout  # 使用配置的快速超时时间
                
                if request_age < quick_timeout:
                    if existing_request.state == PaginationState.PROCESSING:
                        self.logger.info(f"🚫 [防循环] 相同分页请求正在处理: {table_name}, 页{page}, 年龄{request_age:.1f}s")
                        return False
                    elif existing_request.state in [PaginationState.COMPLETED, PaginationState.FAILED]:
                        # 🔧 [P0级修复] 如果请求已完成但未清理，直接清理并允许新请求
                        self.logger.info(f"🔧 [智能清理] 清理已完成的旧请求，允许新请求: {table_name}, 页{page}")
                        del self._active_requests[request_key]
                else:
                    # 请求超时，清理
                    self.logger.warning(f"🔧 [超时清理] 清理超时分页请求: {table_name}, 页{page}, 年龄{request_age:.1f}s")
                    del self._active_requests[request_key]
            
            # 清理过期请求
            self._cleanup_expired_requests(current_time)
            
            self.logger.info(f"✅ [允许] 分页请求可以开始: {table_name}, 页{page}")
            return True
    
    def start_pagination(self, table_name: str, page: int, 
                        sort_columns: Optional[list] = None) -> str:
        """
        🔧 [立即修复] 开始分页操作
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            
        Returns:
            请求ID
        """
        current_time = time.time()
        thread_id = threading.get_ident()
        request_key = self._generate_request_key(table_name, page, sort_columns)
        request_id = f"page_{int(current_time * 1000000)}_{thread_id}"
        
        with self._lock:
            # 创建分页请求记录
            request = PaginationRequest(
                table_name=table_name,
                page=page,
                timestamp=current_time,
                request_id=request_id,
                state=PaginationState.PROCESSING,
                thread_id=thread_id,
                sort_columns=sort_columns
            )
            
            self._active_requests[request_key] = request
            
            # 设置全局处理状态
            self._global_processing = True
            self._global_processing_start_time = current_time
            
            self.logger.info(f"🔧 [开始] 分页操作已开始: {table_name}, 页{page}, ID={request_id}")
            return request_id
    
    def complete_pagination(self, table_name: str, page: int, 
                          sort_columns: Optional[list] = None, 
                          success: bool = True) -> bool:
        """
        🔧 [立即修复] 完成分页操作
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
            success: 是否成功
            
        Returns:
            True: 成功完成, False: 未找到对应请求
        """
        request_key = self._generate_request_key(table_name, page, sort_columns)
        
        with self._lock:
            if request_key in self._active_requests:
                request = self._active_requests[request_key]
                request.state = PaginationState.COMPLETED if success else PaginationState.FAILED
                
                # 清理请求记录
                del self._active_requests[request_key]
                
                # 如果没有其他活跃请求，清除全局处理状态
                if not self._active_requests:
                    self._global_processing = False
                    self._global_processing_start_time = 0.0
                
                self.logger.info(f"🔧 [完成] 分页操作已完成: {table_name}, 页{page}, 成功={success}")
                return True
            else:
                # 🔧 [P0-CRITICAL修复] 降低日志级别，减少不必要的警告噪音
                self.logger.debug(f"🔧 [调试] 未找到对应的分页请求: {table_name}, 页{page} (可能已过期或已完成)")
                return False
    
    def force_reset(self):
        """🔧 [立即修复] 强制重置所有分页状态"""
        with self._lock:
            active_count = len(self._active_requests)
            self._active_requests.clear()
            self._global_processing = False
            self._global_processing_start_time = 0.0
            
            self.logger.warning(f"🔧 [强制重置] 已清理所有分页状态，清理请求数: {active_count}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态信息"""
        with self._lock:
            return {
                'global_processing': self._global_processing,
                'active_requests_count': len(self._active_requests),
                'processing_duration': time.time() - self._global_processing_start_time if self._global_processing else 0,
                'active_requests': [
                    {
                        'table_name': req.table_name,
                        'page': req.page,
                        'state': req.state.value,
                        'age': time.time() - req.timestamp
                    }
                    for req in self._active_requests.values()
                ]
            }
    
    def _generate_request_key(self, table_name: str, page: int, 
                            sort_columns: Optional[list] = None) -> str:
        """🔧 [P0级修复] 生成请求键（增强调试）"""
        sort_key = ""
        if sort_columns:
            sort_parts = []
            for col in sort_columns:
                if isinstance(col, dict):
                    col_name = col.get('column_name', '')
                    order = col.get('order', 'asc')
                    sort_parts.append(f"{col_name}:{order}")
            sort_key = "|".join(sort_parts)
        
        request_key = f"{table_name}:p{page}:s{sort_key}"
        
        # 🔧 [P0级修复] 调试日志，帮助排查状态不匹配问题
        self.logger.debug(f"🔧 [请求键生成] 表={table_name}, 页={page}, 排序={sort_columns} -> 键={request_key}")
        
        return request_key
    
    def _cleanup_expired_requests(self, current_time: float):
        """🔧 [P1级超时优化] 智能清理过期请求"""
        expired_keys = []
        stuck_keys = []
        completed_keys = []
        
        for key, request in self._active_requests.items():
            age = current_time - request.timestamp
            
            # 🔧 [P1级优化] 分类清理不同状态的请求
            if request.state in [PaginationState.COMPLETED, PaginationState.FAILED]:
                # 已完成或失败的请求，超过清理间隔就清理
                if age > self._cleanup_interval:
                    completed_keys.append(key)
            elif request.state == PaginationState.PROCESSING:
                # 处理中的请求，超过请求超时时间就认为卡住了
                if age > self._request_timeout:
                    stuck_keys.append(key)
            else:
                # 其他状态的请求，超过快速超时就清理
                if age > self._quick_timeout:
                    expired_keys.append(key)
        
        # 执行清理
        all_cleanup_keys = expired_keys + stuck_keys + completed_keys
        for key in all_cleanup_keys:
            del self._active_requests[key]
        
        # 🔧 [P1级优化] 详细的清理日志
        if expired_keys:
            self.logger.debug(f"🔧 [清理] 清理过期请求: {len(expired_keys)}个")
        if stuck_keys:
            self.logger.warning(f"🔧 [清理] 清理卡住的处理请求: {len(stuck_keys)}个")
        if completed_keys:
            self.logger.debug(f"🔧 [清理] 清理已完成请求: {len(completed_keys)}个")

    def set_table_total_records(self, table_name: str, total_records: int):
        """🔧 [P2-统一状态] 设置表的总记录数"""
        with self._lock:
            if not hasattr(self, '_table_total_records'):
                self._table_total_records = {}

            self._table_total_records[table_name] = {
                'total_records': total_records,
                'timestamp': time.time()
            }

            self.logger.debug(f"🔧 [P2-统一状态] 设置表总记录数: {table_name} = {total_records}")

    def get_table_total_records(self, table_name: str) -> int:
        """🔧 [P2-统一状态] 获取表的总记录数"""
        with self._lock:
            if hasattr(self, '_table_total_records') and table_name in self._table_total_records:
                record_info = self._table_total_records[table_name]
                # 检查记录是否过期（5分钟）
                if time.time() - record_info['timestamp'] < 300:
                    return record_info['total_records']
                else:
                    # 记录过期，删除
                    del self._table_total_records[table_name]
                    self.logger.debug(f"🔧 [P2-统一状态] 表总记录数已过期: {table_name}")

            return 0


# 全局单例实例
_pagination_state_manager = None


def get_pagination_state_manager() -> PaginationStateManager:
    """获取分页状态管理器单例"""
    global _pagination_state_manager
    if _pagination_state_manager is None:
        _pagination_state_manager = PaginationStateManager()
    return _pagination_state_manager
