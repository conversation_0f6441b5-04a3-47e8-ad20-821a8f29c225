#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段注册系统

负责管理字段映射关系，包括数据库字段名与显示名称的映射、
字段类型定义、字段验证规则等。

主要功能:
- 字段映射关系管理（数据库字段 <-> 显示名称）
- 字段类型定义和检测
- 字段验证规则管理
- 动态字段注册和更新
- 字段别名和多语言支持

映射关系:
- 数据库字段名 -> 显示名称（如 'position_salary_2025' -> '2025年岗位工资'）
- 显示名称 -> 数据库字段名（反向映射）
- 字段类型映射（如 'position_salary_2025' -> 'currency'）
- 字段验证规则映射

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import json
import shutil
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from pathlib import Path
from datetime import datetime
import logging
import re

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class FieldRegistry:
    """
    字段注册系统
    
    负责管理系统中所有字段的映射关系、类型定义和验证规则。
    确保字段名称和显示的一致性。
    """
    
    def __init__(self, mapping_path: str):
        """
        初始化字段注册系统
        
        Args:
            mapping_path: 字段映射配置文件路径
        """
        self.logger = setup_logger("format_management.field_registry")
        
        self.mapping_path = Path(mapping_path)
        self.backup_dir = self.mapping_path.parent / "backups"
        
        # 映射数据
        self._field_mappings = {}  # 存储所有字段映射
        self._reverse_mappings = {}  # 反向映射（显示名称 -> 字段名）
        self._field_types = {}  # 字段类型映射
        self._field_aliases = {}  # 字段别名
        self._loaded = False
        self._last_modified = None
        
        # 🚀 [P1-性能优化] 添加字段类型缓存，避免重复查找
        self._table_field_types_cache = {}  # 表字段类型缓存
        self._table_config_cache = {}  # 表配置缓存
        self._cache_hits = 0  # 缓存命中统计
        self._cache_misses = 0  # 缓存未命中统计
        
        # 默认字段映射
        self._default_mappings = self._get_default_mappings()
        
        # 🔧 [方案B-P1] 架构级配置管理状态
        self._config_changes = 0  # 配置变更计数器
        self._validation_cache = {}  # 验证结果缓存
        self._repair_history = []  # 修复历史记录
        
        # 确保目录存在
        self._ensure_directories()
        
        self.logger.info(f"🏷️ [字段注册] 字段注册系统初始化: {mapping_path}")
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        try:
            self.mapping_path.parent.mkdir(parents=True, exist_ok=True)
            self.backup_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 创建目录失败: {e}")
    
    def _get_default_mappings(self) -> Dict[str, Any]:
        """获取默认字段映射"""
        return {
            "metadata": {
                "version": "1.0.0",
                "created_at": datetime.now().isoformat(),
                "description": "字段映射配置文件"
            },
            "table_mappings": {
                "active_employees": {
                    "display_name": "全部在职人员工资表",
                    "field_mappings": {
                        # 基础信息字段
                        "employee_id": "工号",
                        "employee_name": "姓名",
                        "department": "部门名称",
                        "employee_type_code": "人员类别代码",
                        "employee_type": "人员类别",
                        
                        # 工资字段
                        "position_salary_2025": "2025年岗位工资",
                        "grade_salary_2025": "2025年薪级工资",
                        "allowance": "津贴",
                        "balance_allowance": "结余津贴",
                        "basic_performance_2025": "2025年基础性绩效",
                        "health_fee": "卫生费",
                        "transport_allowance": "交通补贴",
                        "property_allowance": "物业补贴",
                        "communication_allowance": "通讯补贴",
                        "performance_bonus_2025": "2025年奖励性绩效预发",
                        "provident_fund_2025": "2025公积金",
                        "housing_allowance": "住房补贴",
                        "car_allowance": "车补",
                        "supplement": "补发",
                        "advance": "借支",
                        "total_salary": "应发工资",
                        "pension_insurance": "代扣代存养老保险",
                        
                        # 时间字段
                        "year": "年份",
                        "month": "月份",
                        
                        # 系统字段（需要隐藏）
                        "created_at": "创建时间",
                        "updated_at": "更新时间", 
                        "id": "自增主键",
                        "sequence": "序号",
                        "row_number": "序号"
                    },
                    "field_types": {
                        # 字符串类型
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "employee_type_code": "string",
                        "employee_type": "string",
                        
                        # 浮点型（小数点后保留两位）- 用户需求的工资字段
                        "position_salary_2025": "float",           # 2025年岗位工资
                        "grade_salary_2025": "float",              # 2025年薪级工资  
                        "allowance": "float",                      # 津贴
                        "balance_allowance": "float",              # 结余津贴
                        "basic_performance_2025": "float",         # 2025年基础性绩效
                        "health_fee": "float",                     # 卫生费
                        "transport_allowance": "float",            # 交通补贴
                        "property_allowance": "float",             # 物业补贴
                        "housing_allowance": "float",              # 住房补贴
                        "car_allowance": "float",                  # 车补
                        "communication_allowance": "float",        # 通讯补贴
                        "performance_bonus_2025": "float",         # 2025年奖励性绩效预发
                        "supplement": "float",                     # 补发
                        "advance": "float",                        # 借支
                        "total_salary": "float",                   # 应发工资
                        "provident_fund_2025": "float",            # 2025公积金
                        "pension_insurance": "float",              # 代扣代存养老保险
                        
                        # 特殊字符串类型 - 年份和月份的特殊处理
                        "year": "year_string",                     # 年份（转字符串）
                        "month": "month_string",                   # 月份（提取后两位）
                        
                        # 系统字段类型
                        "created_at": "date",
                        "updated_at": "date",
                        "id": "integer", 
                        "sequence": "integer",
                        "row_number": "integer"
                    },
                    # 🎯 [用户需求] 隐藏字段列表 - 不在主界面右侧列表展示区域显示
                    "hidden_fields": [
                        "created_at",      # 创建时间
                        "updated_at",      # 更新时间
                        "id",              # 自增主键
                        "sequence",        # 序号
                        "row_number"       # 序号
                    ],
                    # 🎯 [用户需求] 字段显示顺序 - 确保重要字段优先显示
                    "display_order": [
                        # 基础信息（优先显示）
                        "employee_id", "employee_name", "department", "employee_type", "employee_type_code",
                        # 主要工资项目
                        "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", 
                        "basic_performance_2025", "performance_bonus_2025", "total_salary",
                        # 补贴和其他
                        "health_fee", "transport_allowance", "property_allowance", "housing_allowance", 
                        "car_allowance", "communication_allowance",
                        # 扣除项
                        "provident_fund_2025", "pension_insurance",
                        # 调整项
                        "supplement", "advance",
                        # 时间信息
                        "year", "month"
                    ],
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                },
                "retired_employees": {
                    "display_name": "离休人员工资表",
                    "field_mappings": {
                        # 基础信息字段
                        "employee_id": "人员代码",
                        "employee_name": "姓名", 
                        "department": "部门名称",
                        
                        # 🎯 [用户需求] 离休人员专用工资字段
                        "basic_retirement_salary": "基本离休费",
                        "balance_allowance": "结余津贴",
                        "living_allowance": "生活补贴",
                        "housing_allowance": "住房补贴", 
                        "property_allowance": "物业补贴",
                        "retirement_allowance": "离休补贴",
                        "nursing_fee": "护理费",
                        "one_time_living_allowance": "增发一次性生活补贴",  # 🎯 [用户需求1] 新增字段
                        "supplement": "补发",                                 # 🎯 [用户需求1] 新增字段
                        "total": "合计",
                        "advance": "借支",                                    # 🎯 [用户需求1] 新增字段
                        "remarks": "备注",                                   # 🎯 [用户需求2] 新增字段
                        
                        # 时间字段
                        "year": "年份",
                        "month": "月份",
                        
                        # 系统字段（需要隐藏）
                        "created_at": "创建时间",
                        "updated_at": "更新时间", 
                        "id": "自增主键",
                        "sequence_number": "序号"
                    },
                    "field_types": {
                        # 字符串类型
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "remarks": "string",                     # 🎯 [用户需求2] 备注字段转字符串
                        
                        # 🎯 [用户需求1] 浮点型字段（小数点后保留两位）
                        "basic_retirement_salary": "float",      # 基本离休费
                        "balance_allowance": "float",           # 结余津贴
                        "living_allowance": "float",            # 生活补贴
                        "housing_allowance": "float",           # 住房补贴
                        "property_allowance": "float",          # 物业补贴
                        "retirement_allowance": "float",        # 离休补贴
                        "nursing_fee": "float",                 # 护理费
                        "one_time_living_allowance": "float",   # 🎯 [用户需求1] 增发一次性生活补贴
                        "supplement": "float",                  # 🎯 [用户需求1] 补发
                        "total": "float",                       # 合计
                        "advance": "float",                     # 🎯 [用户需求1] 借支
                        
                        # 🎯 [用户需求3] 特殊字符串类型 - 年份和月份的特殊处理
                        "year": "year_string",                  # 年份（转字符串）
                        "month": "month_string_extract_last_two", # 🎯 [用户需求3] 月份（提取后两位）
                        
                        # 系统字段类型
                        "created_at": "date",
                        "updated_at": "date",
                        "id": "integer", 
                        "sequence_number": "integer"
                    },
                    # 🎯 [用户需求] 隐藏字段列表 - 不在主界面右侧列表展示区域显示
                    "hidden_fields": [
                        "created_at",      # 创建时间
                        "updated_at",      # 更新时间
                        "id",              # 自增主键
                        "sequence_number"  # 序号
                    ],
                    # 🎯 [用户需求] 字段显示顺序 - 确保重要字段优先显示
                    "display_order": [
                        # 基础信息（优先显示）
                        "employee_id", "employee_name", "department",
                        # 主要离休费项目
                        "basic_retirement_salary", "balance_allowance", "living_allowance",
                        "housing_allowance", "property_allowance", "retirement_allowance", 
                        "nursing_fee", "one_time_living_allowance",
                        # 调整项
                        "supplement", "advance", "total",
                        # 备注
                        "remarks",
                        # 时间信息
                        "year", "month"
                    ],
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                },
                "pension_employees": {
                    "display_name": "退休人员工资表",
                    "field_mappings": {
                        # 基础信息字段
                        "employee_id": "人员代码",
                        "employee_name": "姓名", 
                        "department": "部门名称",
                        "employee_type_code": "人员类别代码",
                        
                        # 🎯 [用户需求] 退休人员专用工资字段
                        "basic_retirement_salary": "基本退休费",
                        "allowance": "津贴",
                        "balance_allowance": "结余津贴",
                        "retirement_living_allowance": "离退休生活补贴",
                        "nursing_fee": "护理费",
                        "property_allowance": "物业补贴",
                        "housing_allowance": "住房补贴",
                        "salary_advance": "增资预付",
                        "adjustment_2016": "2016待遇调整",
                        "adjustment_2017": "2017待遇调整",
                        "adjustment_2018": "2018待遇调整",
                        "adjustment_2019": "2019待遇调整",
                        "adjustment_2020": "2020待遇调整",
                        "adjustment_2021": "2021待遇调整",
                        "adjustment_2022": "2022待遇调整",
                        "adjustment_2023": "2023待遇调整",
                        "supplement": "补发",
                        "advance": "借支",
                        "total_salary": "应发工资",
                        "provident_fund": "公积",
                        "insurance_deduction": "保险扣款",
                        "remarks": "备注",
                        
                        # 时间字段
                        "year": "年份",
                        "month": "月份",
                        
                        # 系统字段（需要隐藏）
                        "created_at": "创建时间",
                        "updated_at": "更新时间", 
                        "id": "自增主键",
                        "sequence_number": "序号"
                    },
                    "field_types": {
                        # 字符串类型
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "employee_type_code": "string",
                        "remarks": "string",
                        
                        # 🎯 [用户需求] 浮点型字段（小数点后保留两位，null值显示为0.00）
                        "basic_retirement_salary": "float",
                        "allowance": "float",
                        "balance_allowance": "float",
                        "retirement_living_allowance": "float",
                        "nursing_fee": "float",
                        "property_allowance": "float",
                        "housing_allowance": "float",
                        "salary_advance": "float",
                        "adjustment_2016": "float",
                        "adjustment_2017": "float",
                        "adjustment_2018": "float",
                        "adjustment_2019": "float",
                        "adjustment_2020": "float",
                        "adjustment_2021": "float",
                        "adjustment_2022": "float",
                        "adjustment_2023": "float",
                        "supplement": "float",
                        "advance": "float",
                        "total_salary": "float",
                        "provident_fund": "float",
                        "insurance_deduction": "float",
                        
                        # 🎯 [用户需求] 特殊字符串类型
                        "year": "year_string",
                        "month": "month_string_extract_last_two",
                        
                        # 系统字段类型
                        "created_at": "date",
                        "updated_at": "date",
                        "id": "integer", 
                        "sequence_number": "integer"
                    },
                    # 🎯 [用户需求] 隐藏字段列表
                    "hidden_fields": [
                        "created_at",
                        "updated_at",
                        "id",
                        "sequence_number"
                    ],
                    # 🎯 [用户需求] 字段显示顺序
                    "display_order": [
                        "employee_id", "employee_name", "department", "employee_type_code",
                        "basic_retirement_salary", "allowance", "balance_allowance",
                        "retirement_living_allowance", "nursing_fee", "property_allowance",
                        "housing_allowance", "salary_advance",
                        "adjustment_2016", "adjustment_2017", "adjustment_2018", "adjustment_2019",
                        "adjustment_2020", "adjustment_2021", "adjustment_2022", "adjustment_2023",
                        "supplement", "advance", "total_salary",
                        "provident_fund", "insurance_deduction",
                        "remarks", "year", "month"
                    ],
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                },
                "part_time_employees": {
                    "display_name": "临时工工资表",
                    "field_mappings": {
                        "employee_id": "工号",
                        "employee_name": "姓名",
                        "department": "部门名称",
                        "hourly_rate": "小时工资",
                        "hours_worked": "工作小时",
                        "total_salary": "应发工资",
                        "year": "年份",
                        "month": "月份"
                    },
                    "field_types": {
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "hourly_rate": "currency",
                        "hours_worked": "float",
                        "total_salary": "currency",
                        "year": "integer",
                        "month": "integer"
                    },
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                },
                "contract_employees": {
                    "display_name": "合同工工资表",
                    "field_mappings": {
                        "employee_id": "工号",
                        "employee_name": "姓名",
                        "department": "部门名称",
                        "base_salary": "基本工资",
                        "performance_bonus": "绩效奖金",
                        "allowance": "津贴",
                        "total_salary": "应发工资",
                        "social_insurance": "社会保险",
                        "year": "年份",
                        "month": "月份"
                    },
                    "field_types": {
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "base_salary": "currency",
                        "performance_bonus": "currency",
                        "allowance": "currency",
                        "total_salary": "currency",
                        "social_insurance": "currency",
                        "year": "integer",
                        "month": "integer"
                    },
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                },
                "a_grade_employees": {
                    "display_name": "A岗职工工资表",
                    "field_mappings": {
                        # 基础信息字段
                        "employee_id": "工号",
                        "employee_name": "姓名",
                        "department": "部门名称",
                        "employee_type": "人员类别",
                        "employee_type_code": "人员类别代码",
                        
                        # A岗职工专有工资字段
                        "position_salary_2025": "2025年岗位工资",
                        "seniority_salary_2025": "2025年校龄工资",
                        "allowance": "津贴",
                        "balance_allowance": "结余津贴",
                        "basic_performance_2025": "2025年基础性绩效",
                        "health_fee": "卫生费",
                        "living_allowance_2025": "2025年生活补贴",
                        "car_allowance": "车补",
                        "performance_bonus_2025": "2025年奖励性绩效预发",
                        "supplement": "补发",
                        "advance": "借支",
                        "total_salary": "应发工资",
                        "provident_fund_2025": "2025公积金",
                        "insurance_deduction": "保险扣款",
                        "pension_insurance": "代扣代存养老保险",
                        
                        # 时间字段
                        "year": "年份",
                        "month": "月份",
                        
                        # 系统字段（需要隐藏）
                        "created_at": "创建时间",
                        "updated_at": "更新时间",
                        "id": "自增主键",
                        "sequence_number": "序号"
                    },
                    "field_types": {
                        # 字符串类型
                        "employee_id": "string",
                        "employee_name": "string",
                        "department": "string",
                        "employee_type": "string",
                        "employee_type_code": "string",
                        
                        # 浮点型字段（用户需求：保留两位小数）
                        "position_salary_2025": "float",
                        "seniority_salary_2025": "float",
                        "allowance": "float",
                        "balance_allowance": "float",
                        "basic_performance_2025": "float",
                        "health_fee": "float",
                        "living_allowance_2025": "float",
                        "car_allowance": "float",
                        "performance_bonus_2025": "float",
                        "supplement": "float",
                        "advance": "float",
                        "total_salary": "float",
                        "provident_fund_2025": "float",
                        "insurance_deduction": "float",
                        "pension_insurance": "float",
                        
                        # 特殊字符串类型
                        "year": "year_string",
                        "month": "month_string",
                        
                        # 系统字段类型
                        "created_at": "date",
                        "updated_at": "date",
                        "id": "integer",
                        "sequence_number": "integer"
                    },
                    "hidden_fields": [
                        "created_at",
                        "updated_at", 
                        "id",
                        "sequence_number"
                    ],
                    "display_order": [
                        # 基础信息（优先显示，修复表头顺序）
                        "employee_id", "employee_name", "department",
                        "employee_type",  # 人员类别
                        "employee_type_code",  # 🔧 [用户需求] 人员类别代码放在人员类别后面
                        # A岗职工主要工资项目
                        "position_salary_2025", "seniority_salary_2025", 
                        "allowance", "balance_allowance", "basic_performance_2025",
                        "health_fee", "living_allowance_2025", "car_allowance",
                        "performance_bonus_2025", "supplement", "advance",
                        "total_salary", "provident_fund_2025", 
                        "insurance_deduction", "pension_insurance",
                        # 时间字段
                        "year", "month"
                    ],
                    "required_fields": ["employee_id", "employee_name"],
                    "primary_key": "employee_id"
                }
            },
            "global_aliases": {
                # 通用别名
                "id": "employee_id",
                "name": "employee_name",
                "dept": "department",
                "salary": "total_salary",
                "工资": "total_salary",
                "工号": "employee_id",
                "姓名": "employee_name",
                "部门": "department"
            },
            "field_patterns": {
                # 字段名称模式匹配
                "salary_patterns": [
                    r".*salary.*",
                    r".*工资.*",
                    r".*薪.*"
                ],
                "allowance_patterns": [
                    r".*allowance.*",
                    r".*津贴.*",
                    r".*补贴.*"
                ],
                "bonus_patterns": [
                    r".*bonus.*",
                    r".*奖金.*",
                    r".*绩效.*"
                ]
            }
        }
    
    # ================== 映射加载和保存 ==================
    
    def load_mappings(self) -> bool:
        """
        加载字段映射
        
        Returns:
            是否加载成功
        """
        try:
            if not self.mapping_path.exists():
                self.logger.info("🏷️ [字段注册] 映射文件不存在，创建默认映射")
                self._field_mappings = self._default_mappings.copy()
                self.save_mappings()
                self._build_reverse_mappings()
                self._loaded = True
                return True
            
            with open(self.mapping_path, 'r', encoding='utf-8') as f:
                self._field_mappings = json.load(f)
            
            # 验证映射完整性
            self._validate_mappings()
            
            # 合并缺失的默认映射
            self._merge_default_mappings()
            
            # 构建反向映射
            self._build_reverse_mappings()
            
            self._loaded = True
            self._last_modified = datetime.fromtimestamp(self.mapping_path.stat().st_mtime)
            
            self.logger.info("🏷️ [字段注册] 字段映射加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 字段映射加载失败: {e}")
            # 使用默认映射
            self._field_mappings = self._default_mappings.copy()
            self._build_reverse_mappings()
            self._loaded = True
            return False
    
    def save_mappings(self) -> bool:
        """
        保存字段映射
        
        Returns:
            是否保存成功
        """
        try:
            # 备份现有映射
            if self.mapping_path.exists():
                self._backup_mappings()
            
            # 更新元数据
            if "metadata" in self._field_mappings:
                self._field_mappings["metadata"]["last_modified"] = datetime.now().isoformat()
            
            # 保存映射
            with open(self.mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self._field_mappings, f, indent=2, ensure_ascii=False)
            
            self._last_modified = datetime.now()
            
            self.logger.info("🏷️ [字段注册] 字段映射保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 字段映射保存失败: {e}")
            return False
    
    def _backup_mappings(self):
        """备份映射文件"""
        try:
            if not self.mapping_path.exists():
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"field_mappings_{timestamp}.json"
            
            shutil.copy2(self.mapping_path, backup_file)
            
            # 保留最近10个备份
            self._cleanup_old_backups()
            
            self.logger.debug(f"🏷️ [字段注册] 映射备份完成: {backup_file}")
            
        except Exception as e:
            self.logger.warning(f"🏷️ [字段注册] 映射备份失败: {e}")
    
    def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧备份"""
        try:
            if not self.backup_dir.exists():
                return
            
            backup_files = list(self.backup_dir.glob("field_mappings_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for backup_file in backup_files[keep_count:]:
                backup_file.unlink()
                self.logger.debug(f"🏷️ [字段注册] 删除旧备份: {backup_file}")
                
        except Exception as e:
            self.logger.warning(f"🏷️ [字段注册] 清理旧备份失败: {e}")
    
    def _validate_mappings(self):
        """验证映射完整性"""
        try:
            required_sections = ["table_mappings"]
            
            for section in required_sections:
                if section not in self._field_mappings:
                    self.logger.warning(f"🏷️ [字段注册] 缺少必要映射段: {section}")
                    
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 映射验证失败: {e}")
    
    def _merge_default_mappings(self):
        """合并缺失的默认映射 - 增强版本，确保字段类型配置完整"""
        try:
            def merge_dict(target: dict, source: dict, deep_merge=True):
                for key, value in source.items():
                    if key not in target:
                        target[key] = value
                    elif isinstance(value, dict) and isinstance(target[key], dict) and deep_merge:
                        merge_dict(target[key], value, deep_merge)
            
            # 🔧 [P1-修复] 首先合并基础结构
            merge_dict(self._field_mappings, self._default_mappings)
            
            # 🔧 [P1-修复] 确保所有表配置都有完整的字段类型定义
            table_mappings = self._field_mappings.get("table_mappings", {})
            default_table_mappings = self._default_mappings.get("table_mappings", {})
            config_updated = False  # 标记是否有配置更新需要保存
            
            for table_key, table_config in table_mappings.items():
                # 检查是否缺少field_types配置
                if "field_types" not in table_config or not table_config["field_types"]:
                    self.logger.info(f"🔧 [自动修复] 为表 {table_key} 自动生成字段类型配置")
                    
                    # 尝试从默认配置中找到匹配的表类型
                    matched_default = self._find_matching_default_config(table_key, default_table_mappings)
                    
                    if matched_default:
                        # 使用匹配的默认配置
                        table_config["field_types"] = matched_default.get("field_types", {}).copy()
                        if "hidden_fields" not in table_config:
                            table_config["hidden_fields"] = matched_default.get("hidden_fields", []).copy()
                        if "display_order" not in table_config:
                            table_config["display_order"] = matched_default.get("display_order", []).copy()
                        # 更新metadata标记已修改
                        if "metadata" in table_config:
                            table_config["metadata"]["user_modified"] = True
                            table_config["metadata"]["auto_repaired"] = True
                        config_updated = True
                        self._config_changes += 1  # 🔧 [方案B-P1] 增加变更计数
                        self.logger.info(f"🔧 [自动修复] 使用匹配的默认配置: {table_key}")
                    else:
                        # 根据字段映射自动推断字段类型
                        auto_types = self._auto_infer_field_types(table_config.get("field_mappings", {}))
                        table_config["field_types"] = auto_types
                        # 设置默认隐藏字段
                        table_config.setdefault("hidden_fields", ["created_at", "updated_at", "id", "sequence_number"])
                        # 更新metadata标记已修改
                        if "metadata" in table_config:
                            table_config["metadata"]["user_modified"] = True
                            table_config["metadata"]["auto_repaired"] = True
                        config_updated = True
                        self._config_changes += 1  # 🔧 [方案B-P1] 增加变更计数
                        self.logger.info(f"🔧 [自动修复] 自动推断字段类型: {table_key}, 生成{len(auto_types)}个字段类型")
            
            # 🔧 [方案B-P1] 架构级配置同步机制
            if config_updated:
                try:
                    # 执行配置一致性验证
                    validation_result = self._validate_configuration_consistency()
                    if validation_result['is_valid']:
                        self.logger.info(f"🔧 [架构优化] 配置一致性验证通过: {validation_result['summary']}")
                    else:
                        self.logger.warning(f"🔧 [架构优化] 配置一致性警告: {validation_result['warnings']}")
                    
                    # 应用智能配置修复策略
                    self._apply_smart_config_repair_strategy()
                    
                    # 持久化保存配置
                    save_result = self._save_mappings_to_file()
                    if save_result:
                        self.logger.info(f"🔧 [架构优化] 配置修复已持久化保存，变更数量: {self._get_config_change_count()}")
                        # 清除变更计数器
                        self._config_changes = 0
                    else:
                        self.logger.error(f"🔧 [架构优化] 配置保存失败，可能需要手动干预")
                        
                except Exception as save_error:
                    self.logger.error(f"🔧 [架构优化] 配置同步失败: {save_error}")
                    # 错误恢复机制
                    self._handle_config_sync_error(save_error)
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 合并默认映射失败: {e}")
    
    def _save_mappings_to_file(self):
        """内部方法：将修复后的配置保存到文件"""
        try:
            # 更新全局元数据
            self._field_mappings.setdefault("metadata", {})
            self._field_mappings["metadata"]["last_updated"] = datetime.now().isoformat()
            
            # 调用现有的保存方法
            return self.save_mappings()
        except Exception as e:
            self.logger.error(f"🔧 [自动修复] 保存配置文件失败: {e}")
            return False
    
    def _find_matching_default_config(self, table_key: str, default_table_mappings: Dict) -> Optional[Dict]:
        """
        🔧 [方案B-P2] 增强的表名匹配算法
        支持更多表名变体和智能匹配策略
        """
        try:
            table_key_lower = table_key.lower()
            
            # 🔧 [方案B-P2] 扩展的匹配规则集
            enhanced_match_patterns = {
                'active_employees': {
                    'keywords': ['active', 'in_service', 'current', 'working', 'employed'],
                    'chinese_keywords': ['在职', '全部在职', '在职人员', '现职', '工作人员'],
                    'patterns': [r'.*active.*employee.*', r'.*在职.*人员.*', r'.*current.*staff.*'],
                    'table_suffixes': ['active_employees', 'current_employees', 'working_staff']
                },
                'retired_employees': {
                    'keywords': ['retired', 'former', 'ex_employee'],
                    'chinese_keywords': ['离休', '离休人员', '退职'],
                    'patterns': [r'.*retired.*employee.*', r'.*离休.*人员.*'],
                    'table_suffixes': ['retired_employees', 'former_staff']
                },
                'pension_employees': {
                    'keywords': ['pension', 'pension_employees', 'retiree', 'pensioner'],
                    'chinese_keywords': ['退休', '退休人员', '退休人员工资表', '养老', '退休工资'],
                    'patterns': [r'.*pension.*employee.*', r'.*退休.*人员.*', r'.*pension.*'],
                    'table_suffixes': ['pension_employees', 'retiree_staff', 'pensioner_employees']
                },
                'a_grade_employees': {
                    'keywords': ['a_grade', 'grade_a', 'level_a', 'class_a', 'a_grade_employees'],
                    'chinese_keywords': ['a岗', 'A岗', 'a岗职工', 'A级', 'a级', 'A岗职工'],
                    'patterns': [r'.*a.*grade.*employee.*', r'.*a岗.*职工.*', r'.*grade.*a.*'],
                    'table_suffixes': ['a_grade_employees', 'grade_a_staff', 'level_a_workers'],
                    'priority_score': 100  # 提高A岗职工匹配优先级
                },
                'part_time_employees': {
                    'keywords': ['part_time', 'temporary', 'temp', 'hourly', 'casual'],
                    'chinese_keywords': ['临时', '小时工', '兼职', '临时工', '计时工'],
                    'patterns': [r'.*part.*time.*', r'.*临时.*工.*', r'.*temp.*'],
                    'table_suffixes': ['part_time_employees', 'temporary_staff', 'hourly_workers']
                },
                'contract_employees': {
                    'keywords': ['contract', 'contractor', 'outsourced'],
                    'chinese_keywords': ['合同', '合同工', '外包', '承包'],
                    'patterns': [r'.*contract.*', r'.*合同.*工.*'],
                    'table_suffixes': ['contract_employees', 'contractor_staff']
                }
            }
            
            # 🔧 [方案B-P2] 多层次匹配策略
            best_match = None
            match_score = 0
            match_reason = ""
            
            for default_key, config in default_table_mappings.items():
                if default_key in enhanced_match_patterns:
                    pattern_config = enhanced_match_patterns[default_key]
                    current_score = 0
                    current_reasons = []
                    
                    # 层次1：精确后缀匹配（最高优先级）
                    for suffix in pattern_config['table_suffixes']:
                        if table_key_lower.endswith(suffix):
                            current_score += 100
                            current_reasons.append(f"精确后缀匹配:{suffix}")
                            break
                    
                    # 层次2：关键词完全匹配
                    for keyword in pattern_config['keywords']:
                        if keyword in table_key_lower:
                            current_score += 50
                            current_reasons.append(f"英文关键词:{keyword}")
                    
                    # 层次3：中文关键词匹配
                    for chinese_keyword in pattern_config['chinese_keywords']:
                        if chinese_keyword in table_key:  # 注意这里用原始table_key保持中文
                            current_score += 60
                            current_reasons.append(f"中文关键词:{chinese_keyword}")
                    
                    # 层次4：正则表达式模式匹配
                    import re
                    for pattern in pattern_config['patterns']:
                        if re.search(pattern, table_key_lower, re.IGNORECASE):
                            current_score += 30
                            current_reasons.append(f"模式匹配:{pattern}")
                    
                    # 层次5：智能分词匹配
                    table_words = re.split(r'[_\-\s]+', table_key_lower)
                    for keyword in pattern_config['keywords']:
                        if keyword in table_words:
                            current_score += 40
                            current_reasons.append(f"分词匹配:{keyword}")
                    
                    # 层次6：应用优先级加分
                    if 'priority_score' in pattern_config:
                        current_score += pattern_config['priority_score']
                        current_reasons.append(f"优先级加分:{pattern_config['priority_score']}")
                    
                    # 更新最佳匹配
                    if current_score > match_score:
                        match_score = current_score
                        best_match = config
                        match_reason = f"{default_key} (得分:{current_score}, 原因:{','.join(current_reasons)})"
            
            # 🔧 [方案B-P2] 匹配结果日志和验证
            if best_match:
                # 验证匹配结果的合理性
                if self._validate_match_result(table_key, best_match):
                    self.logger.info(f"🔧 [智能匹配] 表 {table_key} 匹配成功: {match_reason}")
                    return best_match
                else:
                    self.logger.warning(f"🔧 [匹配验证失败] 表 {table_key} 匹配结果不合理，拒绝使用")
                    best_match = None
            else:
                # 🔧 [方案B-P2] 回退到模糊匹配
                fallback_match = self._fallback_fuzzy_matching(table_key, default_table_mappings)
                if fallback_match and self._validate_match_result(table_key, fallback_match):
                    self.logger.info(f"🔧 [模糊匹配] 表 {table_key} 通过模糊匹配找到配置")
                    return fallback_match
                elif fallback_match:
                    self.logger.warning(f"🔧 [模糊匹配验证失败] 表 {table_key} 模糊匹配结果不合理，拒绝使用")
                else:
                    self.logger.warning(f"🔧 [匹配失败] 表 {table_key} 未找到匹配的默认配置")
                    return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 增强表名匹配失败: {e}")
            return None
    
    def _fallback_fuzzy_matching(self, table_key: str, default_table_mappings: Dict) -> Optional[Dict]:
        """
        🔧 [方案B-P2] 回退模糊匹配策略
        当精确匹配失败时使用的模糊匹配算法
        """
        try:
            # 简单的编辑距离模糊匹配
            def calculate_similarity(str1: str, str2: str) -> float:
                """计算字符串相似度（简化版编辑距离）"""
                if not str1 or not str2:
                    return 0.0
                
                # 转换为小写进行比较
                s1, s2 = str1.lower(), str2.lower()
                
                # 计算公共子串长度
                common_chars = set(s1) & set(s2)
                common_score = len(common_chars) / max(len(set(s1)), len(set(s2)))
                
                # 计算长度相似度
                length_score = 1 - abs(len(s1) - len(s2)) / max(len(s1), len(s2))
                
                # 综合相似度
                return (common_score * 0.7 + length_score * 0.3)
            
            best_similarity = 0.0
            best_match = None
            similarity_threshold = 0.8  # 相似度阈值 - 提高到0.8避免错误匹配
            
            table_key_clean = table_key.lower().replace('_', '').replace('-', '')
            
            for default_key, config in default_table_mappings.items():
                default_key_clean = default_key.lower().replace('_', '').replace('-', '')
                similarity = calculate_similarity(table_key_clean, default_key_clean)
                
                if similarity > best_similarity and similarity >= similarity_threshold:
                    best_similarity = similarity
                    best_match = config
            
            if best_match:  
                self.logger.info(f"🔧 [模糊匹配] 表 {table_key} 模糊匹配成功，相似度: {best_similarity:.2f}")
                
            return best_match
            
        except Exception as e:
            self.logger.error(f"🔧 [模糊匹配] 模糊匹配算法失败: {e}")
            return None
    
    def _validate_match_result(self, table_key: str, matched_config: Dict) -> bool:
        """验证匹配结果的合理性"""
        try:
            # 检查A岗职工表是否被错误匹配为临时工
            if 'a_grade' in table_key.lower() or 'a岗' in table_key:
                invalid_fields = {'hourly_rate', 'hours_worked'}
                matched_field_types = set(matched_config.get('field_types', {}).keys())
                if invalid_fields & matched_field_types:
                    self.logger.warning(f"🔧 [验证失败] A岗职工表包含临时工字段: {invalid_fields & matched_field_types}")
                    return False
            
            # 检查退休/离休表是否被错误匹配
            if ('retired' in table_key.lower() or '退休' in table_key or 
                'pension' in table_key.lower() or '离休' in table_key):
                required_retirement_fields = {'employee_id', 'employee_name', 'department'}
                matched_field_types = set(matched_config.get('field_types', {}).keys())
                if not (required_retirement_fields & matched_field_types):
                    self.logger.warning(f"🔧 [验证失败] 退休表缺少必要字段")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [验证异常] 匹配验证失败: {e}")
            return False
    
    def _auto_infer_field_types(self, field_mappings: Dict[str, str]) -> Dict[str, str]:
        """根据字段映射自动推断字段类型"""
        try:
            auto_types = {}
            
            # 🎯 [用户需求] 增强的字段类型推断规则
            # 浮点型关键词 - 包含用户要求的新字段
            float_keywords = [
                '工资', '津贴', '补贴', '奖金', '绩效', '公积金', '保险', '费', 
                '补发', '借支', '扣', '基本', '岗位', '薪级', '合计', '总计',
                # 🎯 [用户需求1] 离休人员专用浮点型字段关键词
                '离休费', '结余', '生活补贴', '住房', '物业', '离休补贴', '护理费',
                '增发', '一次性', '生活补贴', '护理', '离休'
            ]
            # 字符串关键词 - 包含备注字段  
            string_keywords = [
                '姓名', '部门', '代码', '类别', '备注', '工号', '人员', '名称',
                # 🎯 [用户需求2] 备注字段专门设为字符串
                '备注', '说明', '注释', '描述'
            ]
            integer_keywords = ['序号', '主键']
            
            for field_name, display_name in field_mappings.items():
                # 基于字段名和显示名推断类型
                if field_name in ['created_at', 'updated_at']:
                    auto_types[field_name] = 'date'
                elif field_name in ['id', 'sequence_number'] or any(kw in display_name for kw in integer_keywords):
                    auto_types[field_name] = 'integer'
                elif field_name in ['year']:
                    auto_types[field_name] = 'year_string'  # 🎯 [用户需求3] 年份转字符串
                elif field_name in ['month']:
                    # 🎯 [用户需求3] 月份提取后两位转字符串
                    auto_types[field_name] = 'month_string_extract_last_two'
                elif any(kw in display_name for kw in float_keywords):
                    auto_types[field_name] = 'float'  # 🎯 [用户需求1] 浮点型字段（包含新增字段）
                elif any(kw in display_name for kw in string_keywords):
                    auto_types[field_name] = 'string'  # 🎯 [用户需求2] 字符串字段（包含备注）
                else:
                    # 默认为字符串类型
                    auto_types[field_name] = 'string'
            
            return auto_types
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 自动推断字段类型失败: {e}")
            return {}
    
    def _build_reverse_mappings(self):
        """构建反向映射"""
        try:
            self._reverse_mappings = {}
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            for table_type, table_config in table_mappings.items():
                if table_type not in self._reverse_mappings:
                    self._reverse_mappings[table_type] = {}
                
                field_mappings = table_config.get("field_mappings", {})
                for db_field, display_name in field_mappings.items():
                    self._reverse_mappings[table_type][display_name] = db_field
            
            self.logger.debug("🏷️ [字段注册] 反向映射构建完成")
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 反向映射构建失败: {e}")
    
    def _find_table_config(self, table_type: str, table_mappings: Dict) -> Optional[Dict]:
        """
        🚀 [P1-性能优化] 智能查找表配置（带缓存），支持多种表名格式匹配
        
        Args:
            table_type: 表格类型（如 'active_employees' 或具体表名）
            table_mappings: 表映射配置
            
        Returns:
            找到的表配置，如果未找到则返回None
        """
        # 🚀 [P1-性能优化] 检查表配置缓存
        cache_key = f"config_{table_type}"
        if cache_key in self._table_config_cache:
            return self._table_config_cache[cache_key]
        
        try:
            # 直接匹配
            if table_type in table_mappings:
                result = table_mappings[table_type]
                # 🚀 [P1-性能优化] 缓存结果
                self._table_config_cache[cache_key] = result
                self.logger.debug(f"🏷️ [智能匹配] 直接匹配成功并缓存: {table_type}")
                return result
            
            # 🔧 [关键修复] 智能匹配：查找包含table_type关键词的具体表名
            table_type_lower = table_type.lower()
            
            # 先尝试通过后缀匹配查找具体表名（如 retired_employees -> salary_data_*_retired_employees）
            for config_key in table_mappings.keys():
                if config_key.endswith(f"_{table_type}"):
                    result = table_mappings[config_key]
                    self._table_config_cache[cache_key] = result
                    self.logger.info(f"🔧 [智能匹配] 后缀匹配成功: {table_type} -> {config_key}")
                    return result
            
            # 智能匹配：通过关键词匹配找到对应的表配置
            # 匹配规则映射
            match_rules = {
                'active_employees': ['active_employees', '全部在职人员', 'active'],
                'retired_employees': ['retired_employees', '离休人员', 'retired'],
                'pension_employees': ['pension_employees', '退休人员工资表', '退休人员', 'pension'],  # 🎯 [新增] 独立的退休人员匹配规则
                'a_grade_employees': ['a_grade_employees', 'a岗职工', 'a_grade', 'a岗'],
                'part_time_employees': ['part_time', '临时工', 'temp'],
                'contract_employees': ['contract', '合同工']
            }
            
            # 首先尝试通过通用类型匹配
            for generic_type, keywords in match_rules.items():
                if table_type_lower == generic_type or any(keyword in table_type_lower for keyword in keywords):
                    # 在table_mappings中查找包含这些关键词的表
                    for table_key, config in table_mappings.items():
                        table_key_lower = table_key.lower()
                        if any(keyword in table_key_lower for keyword in keywords):
                            # 🚀 [P1-性能优化] 缓存匹配结果
                            self._table_config_cache[cache_key] = config
                            self.logger.debug(f"🏷️ [智能匹配] 通过关键词匹配成功并缓存: {table_type} -> {table_key}")
                            return config
            
            # 如果上述匹配失败，尝试部分匹配
            for table_key, config in table_mappings.items():
                if table_type_lower in table_key.lower() or table_key.lower() in table_type_lower:
                    # 🚀 [P1-性能优化] 缓存匹配结果
                    self._table_config_cache[cache_key] = config
                    self.logger.debug(f"🏷️ [智能匹配] 部分匹配成功并缓存: {table_type} -> {table_key}")
                    return config
            
            # 🚀 [P1-性能优化] 也缓存未找到的结果，避免重复查找
            self._table_config_cache[cache_key] = None
            if table_type != 'unknown':  # 不为unknown类型记录警告，避免日志泛滥
                self.logger.warning(f"🏷️ [智能匹配] 未找到匹配的表配置: {table_type}")
            return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [智能匹配] 查找表配置失败: {e}")
            return None
    
    # ================== 字段映射查询接口 ==================
    
    def get_display_name(self, field_name: str, table_type: str) -> Optional[str]:
        """
        获取字段的显示名称
        
        Args:
            field_name: 数据库字段名
            table_type: 表格类型
            
        Returns:
            显示名称，如果未找到则返回None
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            table_config = table_mappings.get(table_type, {})
            field_mappings = table_config.get("field_mappings", {})
            
            # 直接查找
            if field_name in field_mappings:
                return field_mappings[field_name]
            
            # 查找别名
            global_aliases = self._field_mappings.get("global_aliases", {})
            if field_name in global_aliases:
                alias_field = global_aliases[field_name]
                if alias_field in field_mappings:
                    return field_mappings[alias_field]
            
            # 模式匹配
            display_name = self._find_by_pattern(field_name, table_type)
            if display_name:
                return display_name
            
            return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取显示名称失败 {field_name}: {e}")
            return None
    
    def get_field_name(self, display_name: str, table_type: str) -> Optional[str]:
        """
        获取显示名称对应的字段名
        
        Args:
            display_name: 显示名称
            table_type: 表格类型
            
        Returns:
            数据库字段名，如果未找到则返回None
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            if table_type in self._reverse_mappings:
                return self._reverse_mappings[table_type].get(display_name)
            
            return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取字段名失败 {display_name}: {e}")
            return None
    
    def get_field_type(self, field_name: str, table_type: str) -> Optional[str]:
        """
        获取字段类型
        
        Args:
            field_name: 字段名
            table_type: 表格类型
            
        Returns:
            字段类型，如果未找到则返回None
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            # 🔧 [P1-修复] 智能匹配表类型，支持多种表名格式
            table_config = None
            if table_type in table_mappings:
                table_config = table_mappings[table_type]
            else:
                # 尝试通过关键词匹配找到对应的表配置
                for table_key, config in table_mappings.items():
                    if table_type == 'active_employees' and 'active_employees' in table_key:
                        table_config = config
                        break
                    elif table_type == 'pension_employees' and 'pension' in table_key:
                        table_config = config
                        break
                    elif table_type == 'retired_employees' and 'retired' in table_key:
                        table_config = config
                        break
                    elif table_type in table_key:
                        table_config = config
                        break
            
            if table_config:
                field_types = table_config.get("field_types", {})
                field_type = field_types.get(field_name)
                if field_type:
                    self.logger.debug(f"🏷️ [字段注册] 找到字段类型: {field_name} -> {field_type}")
                    return field_type
            
            self.logger.debug(f"🏷️ [字段注册] 未找到字段类型: {field_name}, table_type: {table_type}")
            return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取字段类型失败 {field_name}: {e}")
            return None
    
    def _find_by_pattern(self, field_name: str, table_type: str) -> Optional[str]:
        """通过模式匹配查找字段显示名称"""
        try:
            field_patterns = self._field_mappings.get("field_patterns", {})
            
            for pattern_type, patterns in field_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, field_name, re.IGNORECASE):
                        # 🔧 [表头显示修复] 检查是否已经包含类型后缀，避免重复添加
                        if "salary" in pattern_type or "工资" in pattern_type:
                            # 如果字段名已经包含"工资"，直接返回，不添加后缀
                            if "工资" in field_name:
                                return field_name
                            else:
                                return f"{field_name}(工资)"
                        elif "allowance" in pattern_type or "津贴" in pattern_type:
                            # 如果字段名已经包含"津贴"，直接返回，不添加后缀
                            if "津贴" in field_name:
                                return field_name
                            else:
                                return f"{field_name}(津贴)"
                        elif "bonus" in pattern_type or "奖金" in pattern_type:
                            # 如果字段名已经包含"奖金"，直接返回，不添加后缀
                            if "奖金" in field_name:
                                return field_name
                            else:
                                return f"{field_name}(奖金)"
            
            return None
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 模式匹配失败 {field_name}: {e}")
            return None
    
    # ================== 表映射管理 ==================
    
    def get_table_mapping(self, table_type: str) -> Dict[str, str]:
        """
        获取表的字段映射
        
        Args:
            table_type: 表格类型
            
        Returns:
            字段映射字典
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            table_config = table_mappings.get(table_type, {})
            
            return table_config.get("field_mappings", {})
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取表映射失败 {table_type}: {e}")
            return {}
    
    def update_table_mapping(self, table_type: str, field_mappings: Dict[str, str]):
        """
        更新表的字段映射
        
        Args:
            table_type: 表格类型
            field_mappings: 字段映射字典
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.setdefault("table_mappings", {})
            table_config = table_mappings.setdefault(table_type, {})
            
            # 更新字段映射
            current_mappings = table_config.setdefault("field_mappings", {})
            current_mappings.update(field_mappings)
            
            # 重新构建反向映射
            self._build_reverse_mappings()
            
            self.logger.info(f"🏷️ [字段注册] 表映射更新完成: {table_type}")
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 表映射更新失败: {e}")
    
    def has_table_mapping(self, table_type: str) -> bool:
        """
        检查是否存在表映射
        
        Args:
            table_type: 表格类型
            
        Returns:
            是否存在映射
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            return table_type in table_mappings
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 检查表映射失败: {e}")
            return False
    
    def get_table_field_types(self, table_type: str) -> Dict[str, str]:
        """
        🚀 [P1-性能优化] 获取表的字段类型映射（带缓存）
        
        Args:
            table_type: 表格类型
            
        Returns:
            字段类型映射字典
        """
        # 🚀 [P1-性能优化] 检查缓存
        if table_type in self._table_field_types_cache:
            self._cache_hits += 1
            return self._table_field_types_cache[table_type]
        
        self._cache_misses += 1
        
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            # 🔧 [P1-修复] 智能匹配表类型，支持多种表名格式
            table_config = self._find_table_config(table_type, table_mappings)
            
            if table_config:
                field_types = table_config.get("field_types", {})
                # 🚀 [P1-性能优化] 缓存结果
                self._table_field_types_cache[table_type] = field_types
                self.logger.debug(f"🏷️ [字段注册] 找到字段类型配置并缓存: {table_type}, 字段数: {len(field_types)}")
                return field_types
            else:
                # 🚀 [P1-性能优化] 也缓存空结果，避免重复查找
                empty_result = {}
                self._table_field_types_cache[table_type] = empty_result
                if table_type != 'unknown':  # 不为unknown类型记录警告，避免日志泛滥
                    self.logger.warning(f"🏷️ [字段注册] 未找到表类型配置: {table_type}")
                return empty_result
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取表字段类型失败 {table_type}: {e}")
            # 🚀 [P1-性能优化] 缓存错误结果，避免重复尝试
            error_result = {}
            self._table_field_types_cache[table_type] = error_result
            return error_result
    
    def get_required_fields(self, table_type: str) -> List[str]:
        """
        获取表的必需字段
        
        Args:
            table_type: 表格类型
            
        Returns:
            必需字段列表
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            table_config = table_mappings.get(table_type, {})
            
            return table_config.get("required_fields", [])
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取必需字段失败 {table_type}: {e}")
            return []
    
    def get_hidden_fields(self, table_type: str) -> List[str]:
        """
        获取表的隐藏字段列表
        
        Args:
            table_type: 表格类型
            
        Returns:
            隐藏字段列表
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            # 🔧 [P1-修复] 使用智能匹配查找表配置
            table_config = self._find_table_config(table_type, table_mappings)
            
            if table_config:
                hidden_fields = table_config.get("hidden_fields", [])
                self.logger.debug(f"🏷️ [字段注册] 找到隐藏字段配置: {table_type}, 隐藏字段数: {len(hidden_fields)}")
                return hidden_fields
            else:
                self.logger.warning(f"🏷️ [字段注册] 未找到表类型配置: {table_type}")
                return []
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取隐藏字段失败 {table_type}: {e}")
            return []
    
    def get_display_fields(self, table_type: str) -> List[str]:
        """
        获取表的显示字段列表（排除隐藏字段）
        
        Args:
            table_type: 表格类型
            
        Returns:
            显示字段列表
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            # 🔧 [P1-修复] 使用智能匹配查找表配置
            table_config = self._find_table_config(table_type, table_mappings)
            
            if not table_config:
                self.logger.warning(f"🏷️ [字段注册] 未找到表类型配置: {table_type}，尝试降级处理")
                # 🔧 [格式修复] 降级处理：返回默认字段配置
                return self._get_default_display_fields(table_type)
                
            # 获取所有字段映射
            all_fields = list(table_config.get("field_mappings", {}).keys())
            
            # 获取隐藏字段
            hidden_fields = table_config.get("hidden_fields", [])
            
            # 排除隐藏字段
            display_fields = [field for field in all_fields if field not in hidden_fields]
            
            # 如果有display_order配置，按顺序排列
            display_order = table_config.get("display_order", [])
            if display_order:
                # 🔧 [P1修复] 将英文字段名转换为中文显示名
                field_mappings = table_config.get("field_mappings", {})
                ordered_fields = []
                
                for field_key in display_order:
                    # 🔧 [P0-2修复] 改进字段转换逻辑，确保所有有效字段都能被正确转换
                    if field_key in field_mappings:
                        display_name = field_mappings[field_key]
                        # 使用英文字段名进行匹配，但输出中文显示名
                        if field_key in display_fields:
                            ordered_fields.append(display_name)
                            self.logger.debug(f"🔧 [P0-2修复] 字段映射: {field_key} -> {display_name}")
                    elif field_key in display_fields:
                        # 如果字段名本身就是显示名（向后兼容）
                        ordered_fields.append(field_key)
                        self.logger.debug(f"🔧 [P0-2修复] 直接字段: {field_key}")
                    else:
                        self.logger.debug(f"🔧 [P0-2修复] 跳过字段: {field_key} (不在显示字段中)")
                
                # 🔧 [P0-2修复] 添加未在display_order中配置的字段（映射后的显示名）
                field_mappings = table_config.get("field_mappings", {})
                for field_key in display_fields:
                    # 将英文字段名转换为中文显示名再检查
                    if field_key in field_mappings:
                        display_name = field_mappings[field_key]
                        if display_name not in ordered_fields:
                            ordered_fields.append(display_name)
                            self.logger.debug(f"🔧 [P0-2修复] 补充字段映射: {field_key} -> {display_name}")
                    elif field_key not in ordered_fields:
                        ordered_fields.append(field_key)
                        self.logger.debug(f"🔧 [P0-2修复] 补充直接字段: {field_key}")
                
                self.logger.info(f"🔧 [P0-2修复] display_order转换完成: {len(ordered_fields)}个字段，原始字段数: {len(display_fields)}")
                return ordered_fields
            
            return display_fields
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取显示字段失败 {table_type}: {e}")
            # 🔧 [格式修复] 异常时的降级处理
            self.logger.info(f"🔧 [格式修复] 尝试异常降级处理: {table_type}")
            return self._get_default_display_fields(table_type)
    
    def _get_default_display_fields(self, table_type: str) -> List[str]:
        """
        🔧 [格式修复] 获取默认显示字段配置

        Args:
            table_type: 表类型

        Returns:
            默认字段列表
        """
        try:
            # 定义各表类型的默认字段顺序
            default_configs = {
                'active_employees': [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别",
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴",
                    "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发",
                    "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
                ],
                'a_grade_employees': [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别",
                    "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补",
                    "2025年奖励性绩效预发", "补发", "借支", "应发工资",
                    "2025公积金", "保险扣款", "代扣代存养老保险"
                ],
                'retired_employees': [
                    "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴",
                    "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费",
                    "增发一次性生活补贴", "补发", "合计", "借支", "备注"
                ],
                'pension_employees': [
                    "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费",
                    "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴",
                    "住房补贴", "增资预付", "补发", "借支", "应发工资",
                    "公积", "保险扣款", "备注"
                ]
            }

            # 获取对应的默认配置
            default_fields = default_configs.get(table_type, [])

            if default_fields:
                self.logger.info(f"🔧 [格式修复] 使用默认字段配置: {table_type}, {len(default_fields)}个字段")
                return default_fields
            else:
                self.logger.warning(f"🔧 [格式修复] 无默认配置: {table_type}")
                return []

        except Exception as e:
            self.logger.error(f"🔧 [格式修复] 获取默认字段配置失败: {e}")
            return []

    # ================== 字段别名管理 ==================

    def add_alias(self, alias: str, field_name: str):
        """
        添加字段别名
        
        Args:
            alias: 别名
            field_name: 实际字段名
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            global_aliases = self._field_mappings.setdefault("global_aliases", {})
            global_aliases[alias] = field_name
            
            self.logger.info(f"🏷️ [字段注册] 添加字段别名: {alias} -> {field_name}")
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 添加字段别名失败: {e}")
    
    def remove_alias(self, alias: str) -> bool:
        """
        删除字段别名
        
        Args:
            alias: 别名
            
        Returns:
            是否删除成功
        """
        try:
            if not self._loaded:
                self.load_mappings()
            
            global_aliases = self._field_mappings.get("global_aliases", {})
            if alias in global_aliases:
                del global_aliases[alias]
                self.logger.info(f"🏷️ [字段注册] 删除字段别名: {alias}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 删除字段别名失败: {e}")
            return False
    
    def get_all_aliases(self) -> Dict[str, str]:
        """获取所有字段别名"""
        try:
            if not self._loaded:
                self.load_mappings()
            
            return self._field_mappings.get("global_aliases", {})
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取所有别名失败: {e}")
            return {}
    
    # ================== 状态和工具方法 ==================
    
    def is_loaded(self) -> bool:
        """检查映射是否已加载"""
        return self._loaded
    
    def get_last_modified(self) -> Optional[datetime]:
        """获取最后修改时间"""
        return self._last_modified
    
    def reload_mappings(self) -> bool:
        """重新加载映射"""
        try:
            self._loaded = False
            return self.load_mappings()
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 重新加载映射失败: {e}")
            return False
    
    def get_supported_table_types(self) -> List[str]:
        """获取支持的表格类型"""
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            return list(table_mappings.keys())
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取支持的表格类型失败: {e}")
            return []
    
    def get_registry_statistics(self) -> Dict[str, Any]:
        """获取注册系统统计信息"""
        try:
            if not self._loaded:
                self.load_mappings()
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            global_aliases = self._field_mappings.get("global_aliases", {})
            
            stats = {
                "loaded": self._loaded,
                "last_modified": self._last_modified.isoformat() if self._last_modified else None,
                "table_types_count": len(table_mappings),
                "global_aliases_count": len(global_aliases),
                "total_field_mappings": 0,
                "table_details": {}
            }
            
            for table_type, table_config in table_mappings.items():
                field_mappings = table_config.get("field_mappings", {})
                field_types = table_config.get("field_types", {})
                required_fields = table_config.get("required_fields", [])
                
                stats["total_field_mappings"] += len(field_mappings)
                stats["table_details"][table_type] = {
                    "field_count": len(field_mappings),
                    "typed_fields": len(field_types),
                    "required_fields": len(required_fields),
                    "display_name": table_config.get("display_name", table_type)
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"🏷️ [字段注册] 获取统计信息失败: {e}")
            return {"error": str(e)}
    
    # ================== 🔧 [方案B-P1] 架构级配置同步机制 ==================
    
    def _get_config_change_count(self) -> int:
        """获取配置变更数量"""
        return getattr(self, '_config_changes', 0)
    
    def _validate_configuration_consistency(self) -> Dict[str, Any]:
        """
        🔧 [方案B-P3] 配置一致性验证
        检查字段映射、字段类型、隐藏字段等配置的一致性
        """
        try:
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'errors': [],
                'summary': ''
            }
            
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            for table_key, table_config in table_mappings.items():
                # 检查必需配置项
                required_keys = ['field_mappings', 'field_types', 'metadata']
                missing_keys = [key for key in required_keys if key not in table_config]
                
                if missing_keys:
                    validation_result['errors'].append(f"表 {table_key} 缺少配置项: {missing_keys}")
                    validation_result['is_valid'] = False
                
                # 检查字段映射与字段类型的一致性
                if 'field_mappings' in table_config and 'field_types' in table_config:
                    field_mappings = table_config['field_mappings']
                    field_types = table_config['field_types']
                    
                    # 检查字段类型是否覆盖所有映射字段
                    unmapped_fields = set(field_mappings.keys()) - set(field_types.keys())
                    if unmapped_fields:
                        validation_result['warnings'].append(f"表 {table_key} 有字段缺少类型定义: {unmapped_fields}")
                    
                    # 检查多余的字段类型定义
                    extra_types = set(field_types.keys()) - set(field_mappings.keys())
                    if extra_types:
                        validation_result['warnings'].append(f"表 {table_key} 有多余的字段类型定义: {extra_types}")
            
            # 🔧 [P3增强] 添加更严格的验证规则
            self._validate_critical_fields_coverage(table_mappings, validation_result)
            self._validate_field_type_consistency(table_mappings, validation_result)
            self._validate_existing_display_fields(table_mappings, validation_result)
            
            # 生成验证摘要
            error_count = len(validation_result['errors'])
            warning_count = len(validation_result['warnings'])
            table_count = len(table_mappings)
            
            validation_result['summary'] = f"{table_count}个表配置，{error_count}个错误，{warning_count}个警告"
            
            # 缓存验证结果
            self._validation_cache['last_validation'] = validation_result
            self._validation_cache['validation_time'] = datetime.now().isoformat()
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"🔧 [架构优化] 配置一致性验证失败: {e}")
            return {
                'is_valid': False,
                'errors': [str(e)],
                'warnings': [],
                'summary': '验证失败'
            }
    
    def _validate_critical_fields_coverage(self, table_mappings: Dict, validation_result: Dict):
        """🔧 [P3增强] 验证关键字段覆盖率"""
        try:
            # 定义关键字段（每种表类型必须有的字段）
            critical_fields = {
                'active_employees': ['employee_id', 'employee_name', 'department'],
                'pension_employees': ['employee_id', 'employee_name', 'allowance', 'total_salary'],
                'retired_employees': ['employee_id', 'employee_name', 'basic_retirement_salary'],
                'a_grade_employees': ['employee_id', 'employee_name', 'grade_salary_2025']
            }
            
            for table_key, table_config in table_mappings.items():
                table_type = self._extract_table_type_from_name(table_key)
                if table_type in critical_fields:
                    required_fields = critical_fields[table_type]
                    field_types = table_config.get('field_types', {})
                    
                    missing_critical = [field for field in required_fields if field not in field_types]
                    if missing_critical:
                        validation_result['errors'].append(
                            f"🔧 [P3增强] 表 {table_key} 缺少关键字段类型定义: {missing_critical}"
                        )
                        validation_result['is_valid'] = False
                        
        except Exception as e:
            self.logger.warning(f"🔧 [P3增强] 关键字段验证失败: {e}")
    
    def _extract_table_type_from_name(self, table_name: str) -> str:
        """
        从表名中提取表格类型
        
        Args:
            table_name: 表名
            
        Returns:
            表格类型标识
        """
        try:
            # 根据表名模式匹配表格类型
            table_name_lower = table_name.lower()
            
            if 'active_employees' in table_name_lower or '全部在职人员' in table_name:
                return 'active_employees'
            elif 'retired_employees' in table_name_lower or '离休人员' in table_name:
                return 'retired_employees'
            elif 'pension_employees' in table_name_lower or '退休人员' in table_name:
                return 'pension_employees'
            elif 'part_time_employees' in table_name_lower or '临时工' in table_name:
                return 'part_time_employees'
            elif 'contract_employees' in table_name_lower or '合同工' in table_name:
                return 'contract_employees'
            elif 'a_grade_employees' in table_name_lower or 'A岗职工' in table_name or 'a岗职工' in table_name:
                return 'a_grade_employees'
            else:
                # 默认返回通用类型
                return 'active_employees'
                
        except Exception as e:
            self.logger.warning(f"🔧 [P1修复] 表名类型提取失败 {table_name}: {e}")
            return 'active_employees'
    
    def _validate_field_type_consistency(self, table_mappings: Dict, validation_result: Dict):
        """🔧 [P3增强] 验证字段类型一致性"""
        try:
            # 检查浮点数字段是否正确标记为float
            float_field_patterns = [
                'salary', 'allowance', 'adjustment', 'bonus', 'deduction', 
                'total', 'amount', 'fund', 'insurance', 'tax'
            ]
            
            for table_key, table_config in table_mappings.items():
                field_types = table_config.get('field_types', {})
                type_fixes = []

                for field_name, field_type in field_types.items():
                    # 🔧 [P1-CRITICAL修复] 检查明显应该是float的字段并自动修复
                    should_be_float = any(pattern in field_name.lower() for pattern in float_field_patterns)
                    if should_be_float and field_type != 'float':
                        # 自动修复字段类型
                        field_types[field_name] = 'float'
                        type_fixes.append(f"字段 {field_name}: {field_type} -> float")

                if type_fixes:
                    validation_result['fixes'].append(
                        f"🔧 [P1-CRITICAL修复] 表 {table_key} 字段类型已自动修复: {', '.join(type_fixes)}"
                    )
                    self.logger.info(f"🔧 [P1-CRITICAL修复] 自动修复字段类型: {table_key} -> {len(type_fixes)}个字段")
                        
        except Exception as e:
            self.logger.warning(f"🔧 [P3增强] 字段类型一致性验证失败: {e}")
    
    def _validate_existing_display_fields(self, table_mappings: Dict, validation_result: Dict):
        """🔧 [P3增强] 验证existing_display_fields配置，防止FormatRenderer降级"""
        try:
            for table_key, table_config in table_mappings.items():
                metadata = table_config.get('metadata', {})
                existing_display_fields = metadata.get('existing_display_fields', [])
                field_mappings = table_config.get('field_mappings', {})
                
                # 🔧 [P1-CRITICAL修复] 检查existing_display_fields是否为空（导致降级处理的根因）
                if not existing_display_fields:
                    # 尝试自动生成display_fields
                    auto_generated_fields = self._auto_generate_display_fields(table_key, field_mappings)
                    if auto_generated_fields:
                        # 自动修复空的existing_display_fields
                        table_config['existing_display_fields'] = auto_generated_fields
                        validation_result['fixes'].append(
                            f"🔧 [P1-CRITICAL修复] 表 {table_key} existing_display_fields已自动生成: {auto_generated_fields[:5]}..."
                        )
                        self.logger.info(f"🔧 [P1-CRITICAL修复] 自动生成display_fields: {table_key} -> {len(auto_generated_fields)}个字段")
                    else:
                        validation_result['warnings'].append(
                            f"🔧 [P1-CRITICAL修复] 表 {table_key} existing_display_fields为空且无法自动生成，将导致FormatRenderer降级处理"
                        )
                else:
                    # 检查existing_display_fields中的字段是否都有对应的映射
                    unmapped_display_fields = [field for field in existing_display_fields if field not in field_mappings]
                    if unmapped_display_fields:
                        validation_result['warnings'].append(
                            f"🔧 [P3增强] 表 {table_key} existing_display_fields包含未映射字段: {unmapped_display_fields}"
                        )
                        
        except Exception as e:
            self.logger.warning(f"🔧 [P3增强] existing_display_fields验证失败: {e}")

    def _auto_generate_display_fields(self, table_key: str, field_mappings: Dict[str, str]) -> List[str]:
        """
        🔧 [P1-CRITICAL修复] 自动生成display_fields

        Args:
            table_key: 表名
            field_mappings: 字段映射

        Returns:
            List[str]: 生成的display_fields列表
        """
        try:
            # 优先级字段列表（按重要性排序）
            priority_fields = [
                'employee_id', 'employee_name', 'department',
                'employee_type', 'employee_type_code', 'position',
                'total_salary', 'base_salary', 'allowance',
                'performance_bonus', 'basic_retirement_salary',
                'hourly_rate', 'hours_worked'
            ]

            # 从field_mappings中提取可用字段
            available_fields = list(field_mappings.values()) if field_mappings else []

            # 按优先级选择字段
            display_fields = []
            for field in priority_fields:
                if field in available_fields:
                    display_fields.append(field)
                    if len(display_fields) >= 8:  # 限制显示字段数量
                        break

            # 如果优先级字段不够，补充其他字段
            if len(display_fields) < 5:
                for field in available_fields:
                    if field not in display_fields and not field.startswith(('id', 'created_', 'updated_')):
                        display_fields.append(field)
                        if len(display_fields) >= 8:
                            break

            self.logger.debug(f"🔧 [P1-CRITICAL修复] 为表 {table_key} 生成display_fields: {display_fields}")
            return display_fields

        except Exception as e:
            self.logger.error(f"🔧 [P1-CRITICAL修复] 自动生成display_fields失败: {e}")
            return []

    def _apply_smart_config_repair_strategy(self):
        """
        🔧 [方案B-P1] 智能配置修复策略
        应用更高级的配置修复和优化策略
        """
        try:
            repair_actions = []
            table_mappings = self._field_mappings.get("table_mappings", {})
            
            for table_key, table_config in table_mappings.items():
                # 智能修复策略1：确保关键字段有合适的显示顺序
                if 'display_order' in table_config and 'field_mappings' in table_config:
                    field_mappings = table_config['field_mappings']
                    display_order = table_config['display_order']
                    
                    # 确保关键字段优先显示
                    priority_fields = ['employee_id', 'employee_name', 'department']
                    current_order = list(display_order) if display_order else []
                    
                    # 将优先字段移到前面
                    optimized_order = []
                    for field in priority_fields:
                        if field in field_mappings and field not in optimized_order:
                            optimized_order.append(field)
                    
                    # 添加其他字段
                    for field in current_order:
                        if field not in optimized_order:
                            optimized_order.append(field)
                    
                    # 添加未在显示顺序中的字段
                    for field in field_mappings.keys():
                        if field not in optimized_order and field not in table_config.get('hidden_fields', []):
                            optimized_order.append(field)
                    
                    if optimized_order != current_order:
                        table_config['display_order'] = optimized_order
                        repair_actions.append(f"优化表 {table_key} 字段显示顺序")
                
                # 智能修复策略2：自动设置合理的隐藏字段
                if 'hidden_fields' in table_config and 'field_mappings' in table_config:
                    field_mappings = table_config['field_mappings']
                    hidden_fields = table_config['hidden_fields']
                    
                    # 建议隐藏的系统字段
                    system_fields = ['id', 'created_at', 'updated_at', 'sequence_number', 'row_number']
                    current_hidden = set(hidden_fields) if hidden_fields else set()
                    
                    # 自动添加应该隐藏的系统字段
                    for field in system_fields:
                        if field in field_mappings and field not in current_hidden:
                            current_hidden.add(field)
                            repair_actions.append(f"自动隐藏系统字段 {field} 在表 {table_key}")
                    
                    table_config['hidden_fields'] = list(current_hidden)
                
                # 🔧 [P1-1修复] 智能修复策略3：自动生成existing_display_fields
                metadata = table_config.setdefault('metadata', {})
                existing_display_fields = metadata.get('existing_display_fields', [])
                
                if not existing_display_fields and 'field_mappings' in table_config:
                    # 从field_mappings和display_order生成existing_display_fields
                    field_mappings = table_config['field_mappings']
                    display_order = table_config.get('display_order', [])
                    hidden_fields = set(table_config.get('hidden_fields', []))
                    
                    # 按display_order顺序生成existing_display_fields
                    auto_display_fields = []
                    for field in display_order:
                        if field in field_mappings and field not in hidden_fields:
                            auto_display_fields.append(field)
                    
                    # 添加不在display_order中但存在于field_mappings中的字段
                    for field in field_mappings.keys():
                        if field not in auto_display_fields and field not in hidden_fields:
                            auto_display_fields.append(field)
                    
                    if auto_display_fields:
                        metadata['existing_display_fields'] = auto_display_fields
                        repair_actions.append(f"🔧 [P1-1修复] 自动生成表 {table_key} 的existing_display_fields: {len(auto_display_fields)}个字段")
                        self.logger.info(f"🔧 [P1-1修复] 表 {table_key} existing_display_fields已自动修复: {auto_display_fields[:5]}...")
            
            # 记录修复历史
            if repair_actions:
                repair_record = {
                    'timestamp': datetime.now().isoformat(),
                    'actions': repair_actions,
                    'strategy': 'smart_config_repair'
                }
                self._repair_history.append(repair_record)
                self.logger.info(f"🔧 [架构优化] 应用智能修复策略，执行 {len(repair_actions)} 项修复操作")
            
        except Exception as e:
            self.logger.error(f"🔧 [架构优化] 智能配置修复策略失败: {e}")
    
    def _handle_config_sync_error(self, error: Exception):
        """
        🔧 [方案B-P1] 配置同步错误处理
        """
        try:
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'error_type': type(error).__name__,
                'error_message': str(error),
                'recovery_action': 'logged_for_manual_intervention'
            }
            
            # 记录错误到修复历史
            self._repair_history.append({
                'timestamp': error_info['timestamp'],
                'actions': [f"配置同步错误: {error_info['error_message']}"],
                'strategy': 'error_recovery'
            })
            
            # 尝试基本的错误恢复
            if "permission" in str(error).lower():
                self.logger.error("🔧 [架构优化] 配置文件权限问题，请检查文件访问权限")
            elif "disk" in str(error).lower() or "space" in str(error).lower():
                self.logger.error("🔧 [架构优化] 磁盘空间不足，请清理磁盘空间")
            else:
                self.logger.error(f"🔧 [架构优化] 未知配置同步错误，建议重启系统或联系技术支持")
                
        except Exception as recovery_error:
            self.logger.error(f"🔧 [架构优化] 错误恢复机制本身失败: {recovery_error}")
    
    def get_repair_history(self) -> List[Dict]:
        """获取配置修复历史记录"""
        return getattr(self, '_repair_history', [])
    
    def clear_repair_history(self):
        """清除配置修复历史记录"""
        self._repair_history = []
    
    # ================== 🚀 [P1-性能优化] 缓存管理接口 ==================
    
    def get_cache_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计字典，包含命中率等信息
        """
        total_requests = self._cache_hits + self._cache_misses
        hit_rate = (self._cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'total_requests': total_requests,
            'hit_rate_percent': round(hit_rate, 2),
            'cached_field_types': len(self._table_field_types_cache),
            'cached_configs': len(self._table_config_cache)
        }
    
    def clear_cache(self):
        """
        清空所有缓存，在配置文件更新后调用
        """
        self._table_field_types_cache.clear()
        self._table_config_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
        self.logger.info("🚀 [P1-性能优化] 缓存已清空")
    
    def force_clear_a_grade_cache(self):
        """
        [A岗修复] 强制清理A岗职工相关的所有缓存
        
        用于修复A岗职工被错误识别为active_employees的问题
        """
        # 清理所有与A岗相关的缓存条目
        a_grade_keys_to_remove = []
        for key in self._table_config_cache.keys():
            if 'a_grade' in key.lower() or 'a岗' in key:
                a_grade_keys_to_remove.append(key)
        
        for key in a_grade_keys_to_remove:
            del self._table_config_cache[key]
            
        # 同样清理字段类型缓存
        a_grade_type_keys_to_remove = []
        for key in self._table_field_types_cache.keys():
            if 'a_grade' in key.lower() or 'a岗' in key:
                a_grade_type_keys_to_remove.append(key)
                
        for key in a_grade_type_keys_to_remove:
            del self._table_field_types_cache[key]
            
        self.logger.info(f"[A岗修复] 强制清理了 {len(a_grade_keys_to_remove)} 个配置缓存和 {len(a_grade_type_keys_to_remove)} 个类型缓存")
    
    def log_cache_performance(self):
        """
        记录缓存性能统计
        """
        stats = self.get_cache_stats()
        if stats['total_requests'] > 0:
            self.logger.info(
                f"🚀 [P1-缓存统计] 缓存性能: 命中率{stats['hit_rate_percent']}% "
                f"({stats['cache_hits']}/{stats['total_requests']}), "
                f"缓存项: 字段类型{stats['cached_field_types']}, 配置{stats['cached_configs']}"
            )
        self.logger.info("🔧 [架构优化] 配置修复历史记录已清除")