#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试格式化功能脚本
"""

import sys
import os
import pandas as pd
sys.path.append('.')

def test_format_functionality():
    print("Testing format functionality...")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        # Initialize components
        config_manager = ConfigManager()
        db_manager = DynamicTableManager("data/salary_system.db")
        factory = get_architecture_factory(db_manager, config_manager)
        
        if not factory.initialize_architecture():
            print("FAILED: Architecture initialization")
            return False
        
        # Get unified format manager
        unified_format_manager = factory.get_unified_format_manager()
        print("SUCCESS: Got unified format manager")
        
        # Test data formatting with sample data
        test_data = {
            '人员代码': ['19990089.0', '20010123.0', '19850456.0'],
            '增发一次性生活补贴': [None, 0, ''],
            '补发': ['', None, 0],
            '月份': ['2025年07月', '202507', '25-07'],
            '基本工资': [5000.123, 4500.456, 6000.789]
        }
        
        df = pd.DataFrame(test_data)
        print(f"Test data created: {len(df)} rows, {len(df.columns)} columns")
        
        # Test formatting
        formatted_data = unified_format_manager.format_data(
            df, 'retired_employees', data_source='test'
        )
        
        print("SUCCESS: Data formatting completed")
        print(f"Formatted data: {len(formatted_data)} rows, {len(formatted_data.columns)} columns")
        
        # Check specific formatting results
        if len(formatted_data) > 0:
            print("Formatting results:")
            for col in formatted_data.columns:
                sample_value = formatted_data.iloc[0][col]
                print(f"  {col}: {sample_value} (type: {type(sample_value)})")
        
        return True
        
    except Exception as e:
        print(f"FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_format_functionality()
    if success:
        print("\n*** FORMAT TEST: SUCCESS ***")
        print("The unified format manager is working correctly!")
    else:
        print("\n*** FORMAT TEST: FAILED ***")
    
    sys.exit(0 if success else 1)