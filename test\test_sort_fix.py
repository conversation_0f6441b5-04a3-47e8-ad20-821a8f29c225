#!/usr/bin/env python3
"""
排序功能修复测试脚本

测试新架构排序功能是否正常工作，包括：
1. 字段映射功能
2. 排序请求处理
3. 分页状态保持
4. 事件系统集成
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.services.table_data_service import TableDataService
from src.core.unified_data_request_manager import DataRequest, DataResponse


class TestSortFix(unittest.TestCase):
    """排序功能修复测试"""
    
    def setUp(self):
        """测试前准备"""
        # Mock必要的依赖
        self.mock_db_manager = Mock()
        self.mock_config_manager = Mo<PERSON>()
        self.mock_event_bus = Mock()
        
        # 创建测试用的字段映射
        self.test_field_mapping = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'grade_salary_2025': '2025年薪级工资',
            'department': '部门名称'
        }
        
        # 创建反向映射
        self.test_reverse_mapping = {v: k for k, v in self.test_field_mapping.items()}
    
    def test_field_mapping_conversion(self):
        """测试字段映射转换功能"""
        # 模拟主窗口实例
        main_window = Mock()
        main_window.field_mapping_cache = {}
        main_window.config_sync_manager = Mock()
        main_window.config_sync_manager.load_mapping.return_value = self.test_field_mapping
        
        # 测试正向映射获取
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        result = PrototypeMainWindow._get_table_field_mapping(main_window, "test_table")
        
        # 验证结果
        self.assertEqual(result, self.test_field_mapping)
        main_window.config_sync_manager.load_mapping.assert_called_with("test_table")
    
    def test_sort_columns_conversion(self):
        """测试排序列转换功能"""
        # 创建测试排序列
        sort_columns = [
            {
                'column_name': '2025年薪级工资',
                'order': 'ascending',
                'priority': 0
            }
        ]
        
        # 模拟主窗口
        main_window = Mock()
        main_window.field_mapping_cache = {'reverse_test_table': self.test_reverse_mapping}
        main_window._convert_column_name_to_db_field = Mock(return_value='grade_salary_2025')
        
        # 测试转换
        expected_result = [
            {
                'column_name': 'grade_salary_2025',
                'order': 'ascending',
                'priority': 0
            }
        ]
        
        # 验证转换逻辑
        self.assertEqual(main_window._convert_column_name_to_db_field('2025年薪级工资', 'test_table'), 'grade_salary_2025')
    
    def test_table_data_service_sort_support(self):
        """测试TableDataService是否支持排序参数"""
        # 创建TableDataService实例
        service = TableDataService(self.mock_db_manager, self.mock_config_manager)
        
        # 模拟数据请求管理器
        service.data_request_manager = Mock()
        mock_response = DataResponse(
            success=True,
            data=Mock(),
            total_records=100,
            current_page=1,
            page_size=50,
            total_pages=2,
            table_name="test_table"
        )
        service.data_request_manager.request_table_data.return_value = mock_response
        
        # 测试带排序的数据加载
        sort_columns = [{'column_name': 'grade_salary_2025', 'order': 'ascending'}]
        result = service.load_table_data(
            table_name="test_table",
            page=1,
            page_size=50,
            sort_columns=sort_columns
        )
        
        # 验证调用
        self.assertTrue(result.success)
        service.data_request_manager.request_table_data.assert_called_once()
        
        # 验证请求参数
        call_args = service.data_request_manager.request_table_data.call_args[0][0]
        self.assertEqual(call_args.table_name, "test_table")
        self.assertEqual(call_args.sort_columns, sort_columns)
    
    def test_event_system_integration(self):
        """测试事件系统集成"""
        # 模拟事件总线
        mock_event_bus = Mock()
        
        # 模拟主窗口
        main_window = Mock()
        main_window.event_bus = mock_event_bus
        main_window.field_mapping_cache = {'reverse_test_table': self.test_reverse_mapping}
        main_window._convert_column_name_to_db_field = Mock(return_value='grade_salary_2025')
        
        # 测试事件发布
        sort_columns = [{'column_name': '2025年薪级工资', 'order': 'ascending', 'priority': 0}]
        
        # 调用事件发布方法
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        PrototypeMainWindow._publish_sort_request_event(main_window, "test_table", sort_columns, 1)
        
        # 验证事件发布
        mock_event_bus.publish.assert_called_once()
    
    def test_pagination_state_preservation(self):
        """测试分页状态保持"""
        # 模拟分页组件
        mock_pagination = Mock()
        mock_pagination.current_page = 5
        mock_pagination.page_size = 50
        
        # 模拟主工作区
        mock_workspace = Mock()
        mock_workspace.pagination_widget = mock_pagination
        
        # 模拟主窗口
        main_window = Mock()
        main_window.main_workspace = mock_workspace
        main_window.current_sort_columns = [{'column_name': '2025年薪级工资', 'order': 'ascending'}]
        main_window.current_table_name = "test_table"
        main_window.event_bus = Mock()
        main_window._convert_column_name_to_db_field = Mock(return_value='grade_salary_2025')
        
        # 测试排序时页码保持
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        PrototypeMainWindow._publish_sort_request_event(main_window, "test_table", main_window.current_sort_columns, 5)
        
        # 验证事件发布时使用了正确的页码
        main_window.event_bus.publish.assert_called_once()


if __name__ == '__main__':
    print("开始排序功能修复测试...")
    unittest.main(verbosity=2)
