#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头顺序验证器

专门验证关键字段的相对位置，确保字段映射的正确性。
防止"人员类别代码"出现在"人员类别"之前等问题。

创建时间: 2025-08-03
作者: 系统分析修复团队
"""

from typing import Dict, List, Tuple, Optional
import logging
from src.utils.log_config import setup_logger


class HeaderOrderValidator:
    """表头顺序验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = setup_logger(__name__)
        
        # 定义关键字段的顺序规则
        self.order_rules = {
            # 规则格式：{字段A: 字段B} 表示字段A必须在字段B之前
            'employee_type': 'employee_type_code',  # 人员类别 必须在 人员类别代码 之前
            'employee_id': 'employee_name',          # 工号 通常在 姓名 之前
            'employee_name': 'department',           # 姓名 通常在 部门 之前
        }
        
        # 定义字段的中文显示名映射
        self.field_display_names = {
            'employee_type': '人员类别',
            'employee_type_code': '人员类别代码',
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门名称'
        }
    
    def validate_field_mapping_order(self, field_mapping: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        验证字段映射中的顺序关系
        
        Args:
            field_mapping: 字段映射配置 (数据库字段名 -> 显示名)
            
        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []
        
        # 获取字段顺序
        field_order = list(field_mapping.keys())
        
        # 检查每个顺序规则
        for field_a, field_b in self.order_rules.items():
            if field_a in field_order and field_b in field_order:
                pos_a = field_order.index(field_a)
                pos_b = field_order.index(field_b)
                
                if pos_a > pos_b:
                    display_a = self.field_display_names.get(field_a, field_a)
                    display_b = self.field_display_names.get(field_b, field_b)
                    error_msg = f"字段顺序错误: '{display_a}' (位置{pos_a+1}) 应该在 '{display_b}' (位置{pos_b+1}) 之前"
                    errors.append(error_msg)
                    self.logger.warning(f"🚨 [顺序验证] {error_msg}")
        
        is_valid = len(errors) == 0
        if is_valid:
            self.logger.info("✅ [顺序验证] 字段映射顺序验证通过")
        else:
            self.logger.error(f"❌ [顺序验证] 发现 {len(errors)} 个顺序错误")
        
        return is_valid, errors
    
    def validate_excel_headers(self, excel_headers: List[str]) -> Tuple[bool, List[str]]:
        """
        验证Excel表头的顺序
        
        Args:
            excel_headers: Excel表头列表
            
        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []
        
        # 检查关键字段的相对位置
        if '人员类别' in excel_headers and '人员类别代码' in excel_headers:
            pos_type = excel_headers.index('人员类别')
            pos_code = excel_headers.index('人员类别代码')
            
            if pos_type > pos_code:
                error_msg = f"Excel表头顺序错误: '人员类别' (位置{pos_type+1}) 应该在 '人员类别代码' (位置{pos_code+1}) 之前"
                errors.append(error_msg)
                self.logger.warning(f"🚨 [Excel验证] {error_msg}")
        
        is_valid = len(errors) == 0
        if is_valid:
            self.logger.info("✅ [Excel验证] Excel表头顺序验证通过")
        else:
            self.logger.error(f"❌ [Excel验证] 发现 {len(errors)} 个Excel表头顺序错误")
        
        return is_valid, errors
    
    def auto_fix_field_mapping_order(self, field_mapping: Dict[str, str]) -> Dict[str, str]:
        """
        自动修复字段映射的顺序问题
        
        Args:
            field_mapping: 原始字段映射
            
        Returns:
            Dict[str, str]: 修复后的字段映射
        """
        # 创建副本
        fixed_mapping = field_mapping.copy()
        
        # 获取当前字段顺序
        field_order = list(fixed_mapping.keys())
        
        # 应用顺序规则进行修复
        for field_a, field_b in self.order_rules.items():
            if field_a in field_order and field_b in field_order:
                pos_a = field_order.index(field_a)
                pos_b = field_order.index(field_b)
                
                if pos_a > pos_b:
                    # 需要调整顺序：将field_a移动到field_b之前
                    field_order.remove(field_a)
                    new_pos_b = field_order.index(field_b)
                    field_order.insert(new_pos_b, field_a)
                    
                    display_a = self.field_display_names.get(field_a, field_a)
                    display_b = self.field_display_names.get(field_b, field_b)
                    self.logger.info(f"🔧 [自动修复] 调整字段顺序: '{display_a}' 移动到 '{display_b}' 之前")
        
        # 重建映射字典
        fixed_mapping = {field: field_mapping[field] for field in field_order}
        
        return fixed_mapping
    
    def generate_validation_report(self, table_name: str, field_mapping: Dict[str, str], 
                                 excel_headers: List[str] = None) -> str:
        """
        生成详细的验证报告
        
        Args:
            table_name: 表名
            field_mapping: 字段映射
            excel_headers: Excel表头（可选）
            
        Returns:
            str: 验证报告
        """
        report_lines = [
            f"📋 表头顺序验证报告 - {table_name}",
            "=" * 50
        ]
        
        # 验证字段映射
        mapping_valid, mapping_errors = self.validate_field_mapping_order(field_mapping)
        report_lines.append(f"字段映射验证: {'✅ 通过' if mapping_valid else '❌ 失败'}")
        if mapping_errors:
            for error in mapping_errors:
                report_lines.append(f"  - {error}")
        
        # 验证Excel表头（如果提供）
        if excel_headers:
            headers_valid, headers_errors = self.validate_excel_headers(excel_headers)
            report_lines.append(f"Excel表头验证: {'✅ 通过' if headers_valid else '❌ 失败'}")
            if headers_errors:
                for error in headers_errors:
                    report_lines.append(f"  - {error}")
        
        # 提供修复建议
        if not mapping_valid:
            report_lines.append("\n🔧 修复建议:")
            report_lines.append("  1. 使用 auto_fix_field_mapping_order() 自动修复")
            report_lines.append("  2. 手动调整配置文件中的字段顺序")
            report_lines.append("  3. 重新生成字段映射配置")
        
        return "\n".join(report_lines)


def validate_current_configuration():
    """验证当前系统配置的字段顺序"""
    import json
    from pathlib import Path
    
    validator = HeaderOrderValidator()
    logger = setup_logger("header_order_check")
    
    # 读取当前配置文件
    config_path = Path("state/data/field_mappings.json")
    if not config_path.exists():
        logger.error("配置文件不存在")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    # 验证各个表的配置
    table_mappings = config_data.get("table_mappings", {})
    
    for table_name, table_config in table_mappings.items():
        if "field_mappings" in table_config:
            field_mappings = table_config["field_mappings"]
            report = validator.generate_validation_report(table_name, field_mappings)
            print(report)
            print()


if __name__ == "__main__":
    validate_current_configuration()