"""
架构重构工厂类 - 统一组件管理

提供统一的架构重构组件初始化、配置和管理接口。
解决组件间依赖关系，确保正确的初始化顺序。

主要功能：
1. 统一组件初始化
2. 依赖注入管理
3. 配置统一管理
4. 生命周期管理

使用方式：
```python
factory = ArchitectureFactory(db_manager, config_manager)
table_service = factory.get_table_data_service()
```
"""

from typing import Optional, Dict, Any
import threading
from datetime import datetime

from src.utils.log_config import setup_logger
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config import ConfigManager

# 架构重构组件
from src.core.unified_data_request_manager import UnifiedDataRequestManager
from src.core.unified_state_manager import UnifiedStateManager
from src.core.event_bus import EventBus, get_event_bus
from src.services.table_data_service import TableDataService
from src.modules.data_import.config_sync_manager import ConfigSyncManager


class ArchitectureFactory:
    """架构重构工厂类"""
    
    def __init__(self, 
                 db_manager: DynamicTableManager,
                 config_manager: ConfigManager):
        self.logger = setup_logger("ArchitectureFactory")
        
        # 核心依赖
        self.db_manager = db_manager
        self.config_manager = config_manager
        
        # 组件实例缓存
        self._unified_data_request_manager: Optional[UnifiedDataRequestManager] = None
        self._unified_state_manager: Optional[UnifiedStateManager] = None
        self._event_bus: Optional[EventBus] = None
        self._table_data_service: Optional[TableDataService] = None
        self._config_sync_manager: Optional[ConfigSyncManager] = None
        
        # 线程安全
        self._lock = threading.Lock()
        
        # 初始化状态
        self._initialized = False
        self._initialization_time: Optional[datetime] = None
        
        self.logger.info("架构重构工厂初始化完成")
    
    def initialize_architecture(self) -> bool:
        """初始化整个架构重构系统"""
        with self._lock:
            if self._initialized:
                self.logger.warning("架构已经初始化，跳过重复初始化")
                return True
            
            try:
                self.logger.info("开始初始化架构重构系统...")
                start_time = datetime.now()
                
                # 1. 初始化事件总线（最先初始化，其他组件需要它）
                self._event_bus = self._create_event_bus()
                self.logger.debug("✅ 事件总线初始化完成")
                
                # 2. 初始化配置同步管理器（需要事件总线）
                self._config_sync_manager = self._create_config_sync_manager()
                self.logger.debug("✅ 配置同步管理器初始化完成")
                
                # 3. 初始化统一状态管理器
                self._unified_state_manager = self._create_unified_state_manager()
                self.logger.debug("✅ 统一状态管理器初始化完成")
                
                # 4. 初始化统一数据请求管理器
                self._unified_data_request_manager = self._create_unified_data_request_manager()
                self.logger.debug("✅ 统一数据请求管理器初始化完成")
                
                # 5. 初始化表格数据服务（依赖上述所有组件）
                self._table_data_service = self._create_table_data_service()
                self.logger.debug("✅ 表格数据服务初始化完成")
                
                # 6. 执行组件间连接和配置
                self._configure_components()
                self.logger.debug("✅ 组件配置完成")
                
                self._initialized = True
                self._initialization_time = datetime.now()
                
                init_duration = (self._initialization_time - start_time).total_seconds() * 1000
                self.logger.info(f"🎉 架构重构系统初始化成功！耗时: {init_duration:.1f}ms")
                
                return True
                
            except Exception as e:
                self.logger.error(f"架构重构系统初始化失败: {e}", exc_info=True)
                self._cleanup_partial_initialization()
                return False
    
    def _create_config_sync_manager(self) -> ConfigSyncManager:
        """创建配置同步管理器"""
        try:
            # 🆕 [新架构] 通过依赖注入创建ConfigSyncManager
            config_sync_manager = ConfigSyncManager(
                config_path="state/data/field_mappings.json",
                event_bus=self._event_bus
            )

            self.logger.debug("配置同步管理器创建完成")
            return config_sync_manager

        except Exception as e:
            self.logger.error(f"创建配置同步管理器失败: {e}")
            raise
    
    def _create_event_bus(self) -> EventBus:
        """创建事件总线"""
        try:
            # 使用全局单例事件总线
            event_bus = get_event_bus()
            
            # 可以在这里进行事件总线的特殊配置
            self.logger.debug("事件总线创建完成")
            return event_bus
            
        except Exception as e:
            self.logger.error(f"创建事件总线失败: {e}")
            raise
    
    def _create_unified_state_manager(self) -> UnifiedStateManager:
        """创建统一状态管理器"""
        try:
            # 从配置获取状态文件路径
            state_file_path = self.config_manager.get_config_value(
                "state_manager.state_file_path", 
                "state/unified_state.json"
            )
            
            state_manager = UnifiedStateManager(state_file_path=state_file_path)
            
            self.logger.debug("统一状态管理器创建完成")
            return state_manager
            
        except Exception as e:
            self.logger.error(f"创建统一状态管理器失败: {e}")
            raise
    
    def _create_unified_data_request_manager(self) -> UnifiedDataRequestManager:
        """创建统一数据请求管理器"""
        try:
            data_request_manager = UnifiedDataRequestManager(
                db_manager=self.db_manager,
                config_manager=self.config_manager
            )
            
            self.logger.debug("统一数据请求管理器创建完成")
            return data_request_manager
            
        except Exception as e:
            self.logger.error(f"创建统一数据请求管理器失败: {e}")
            raise
    
    def _create_table_data_service(self) -> TableDataService:
        """创建表格数据服务"""
        try:
            table_data_service = TableDataService(
                db_manager=self.db_manager,
                config_manager=self.config_manager
            )
            
            self.logger.debug("表格数据服务创建完成")
            return table_data_service
            
        except Exception as e:
            self.logger.error(f"创建表格数据服务失败: {e}")
            raise
    
    def _configure_components(self):
        """配置组件间的连接"""
        try:
            # 这里可以进行组件间的额外配置
            # 例如设置特殊的事件监听器、配置缓存策略等
            
            # 配置状态管理器的自动保存
            if self._unified_state_manager:
                auto_save_enabled = self.config_manager.get_config_value(
                    "state_manager.auto_save_enabled", 
                    True
                )
                self._unified_state_manager.auto_save_enabled = auto_save_enabled
            
            # 配置数据请求管理器的缓存策略
            # 这里可以添加更多配置
            
            # 配置ConfigSyncManager的事件总线
            if self._config_sync_manager and self._event_bus:
                self._config_sync_manager.event_bus = self._event_bus
                self.logger.debug("ConfigSyncManager事件总线配置完成")
            
            self.logger.debug("组件配置完成")
            
        except Exception as e:
            self.logger.error(f"配置组件失败: {e}")
            raise
    
    def _cleanup_partial_initialization(self):
        """清理部分初始化的组件"""
        try:
            self.logger.warning("清理部分初始化的组件...")
            
            # 清理已创建的组件
            if self._table_data_service:
                # 这里可以添加清理逻辑
                self._table_data_service = None
            
            if self._unified_data_request_manager:
                self._unified_data_request_manager = None
            
            if self._unified_state_manager:
                self._unified_state_manager = None
            
            if self._event_bus:
                # 注意：全局事件总线不应该被清理
                pass
            
            if self._config_sync_manager:
                self._config_sync_manager = None
            
            self.logger.debug("组件清理完成")
            
        except Exception as e:
            self.logger.error(f"清理组件失败: {e}")
    
    # 公共访问接口
    def get_config_sync_manager(self) -> ConfigSyncManager:
        """获取配置同步管理器"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        return self._config_sync_manager
    
    def get_event_bus(self) -> EventBus:
        """获取事件总线"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        return self._event_bus
    
    def get_unified_state_manager(self) -> UnifiedStateManager:
        """获取统一状态管理器"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        return self._unified_state_manager
    
    def get_unified_data_request_manager(self) -> UnifiedDataRequestManager:
        """获取统一数据请求管理器"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        return self._unified_data_request_manager
    
    def get_table_data_service(self) -> TableDataService:
        """获取表格数据服务"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        return self._table_data_service
    
    def get_unified_format_manager(self) -> 'UnifiedFormatManager':
        """获取统一格式管理器"""
        if not self._initialized:
            raise RuntimeError("架构未初始化，请先调用 initialize_architecture()")
        
        try:
            from src.modules.format_management.unified_format_manager import UnifiedFormatManager
            
            # 创建统一格式管理器实例，注入已初始化的依赖
            unified_format_manager = UnifiedFormatManager(
                config_path="state/format_config.json",
                field_mapping_path="state/data/field_mappings.json",
                event_bus=self._event_bus,
                state_manager=self._unified_state_manager
            )
            
            self.logger.info("🎯 [统一格式管理] 统一格式管理器创建成功")
            return unified_format_manager
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 创建统一格式管理器失败: {e}")
            raise
    
    # 状态查询
    def is_initialized(self) -> bool:
        """检查架构是否已初始化"""
        return self._initialized
    
    def get_initialization_time(self) -> Optional[datetime]:
        """获取初始化时间"""
        return self._initialization_time
    
    def get_component_status(self) -> Dict[str, Any]:
        """获取组件状态"""
        return {
            "initialized": self._initialized,
            "initialization_time": self._initialization_time.isoformat() if self._initialization_time else None,
            "components": {
                "event_bus": self._event_bus is not None,
                "unified_state_manager": self._unified_state_manager is not None,
                "unified_data_request_manager": self._unified_data_request_manager is not None,
                "table_data_service": self._table_data_service is not None
            }
        }
    
    def get_architecture_statistics(self) -> Dict[str, Any]:
        """获取架构统计信息"""
        if not self._initialized:
            return {"error": "Architecture not initialized"}
        
        try:
            return {
                "factory_status": self.get_component_status(),
                "event_bus_stats": self._event_bus.get_statistics() if self._event_bus else {},
                "state_manager_stats": self._unified_state_manager.get_state_statistics() if self._unified_state_manager else {},
                "data_request_stats": self._unified_data_request_manager.get_request_statistics() if self._unified_data_request_manager else {},
                "table_service_stats": self._table_data_service.get_service_statistics() if self._table_data_service else {}
            }
        except Exception as e:
            self.logger.error(f"获取架构统计失败: {e}")
            return {"error": str(e)}
    
    def shutdown(self):
        """关闭架构系统"""
        with self._lock:
            try:
                self.logger.info("开始关闭架构重构系统...")
                
                # 按依赖关系逆序关闭组件
                if self._table_data_service:
                    # 这里可以添加服务关闭逻辑
                    self.logger.debug("表格数据服务已关闭")
                
                if self._unified_data_request_manager:
                    # 这里可以添加数据请求管理器关闭逻辑
                    self.logger.debug("统一数据请求管理器已关闭")
                
                if self._unified_state_manager:
                    # 保存最终状态
                    self._unified_state_manager._save_state()
                    self.logger.debug("统一状态管理器已关闭")
                
                if self._event_bus:
                    self._event_bus.shutdown()
                    self.logger.debug("事件总线已关闭")
                
                self._initialized = False
                self.logger.info("架构重构系统已完全关闭")
                
            except Exception as e:
                self.logger.error(f"关闭架构系统失败: {e}")


# 全局工厂实例
_global_architecture_factory: Optional[ArchitectureFactory] = None
_factory_lock = threading.Lock()


def get_architecture_factory(db_manager: DynamicTableManager, config_manager: ConfigManager) -> ArchitectureFactory:
    """获取全局架构工厂实例（单例模式）"""
    global _global_architecture_factory
    
    if _global_architecture_factory is None:
        with _factory_lock:
            if _global_architecture_factory is None:
                _global_architecture_factory = ArchitectureFactory(db_manager, config_manager)
    
    return _global_architecture_factory


def reset_architecture_factory():
    """重置全局架构工厂（主要用于测试）"""
    global _global_architecture_factory
    
    with _factory_lock:
        if _global_architecture_factory:
            _global_architecture_factory.shutdown()
        _global_architecture_factory = None 