#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt5现代化样式技术验证 - 概念验证

本文件用于验证界面现代化改造的关键技术可行性：
1. Material Design配色方案
2. 渐变背景效果 
3. 现代化按钮样式
4. 表格编辑功能
5. 响应式布局
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QTreeWidget, QTreeWidgetItem, QSplitter
)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QPalette, QLinearGradient, QColor, QPainter, QBrush


class MaterialDesignPalette:
    """Material Design配色方案"""
    PRIMARY = "#4285F4"           # 主色（蓝色）
    SUCCESS = "#34A853"           # 成功色（绿色）
    WARNING = "#FBBC05"           # 警告色（黄色）
    ERROR = "#EA4335"             # 错误色（红色）
    BACKGROUND = "#f5f5f5"        # 背景色
    PANEL = "#fafafa"             # 面板色
    WHITE = "#ffffff"             # 白色
    BORDER = "#e0e0e0"            # 边框色
    TEXT = "#333333"              # 文字色
    TEXT_LIGHT = "#666666"        # 浅色文字


class ModernStyleSheet:
    """现代化样式表定义"""
    
    @staticmethod
    def get_main_style():
        """获取主要样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {MaterialDesignPalette.BACKGROUND};
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }}
        
        /* Header样式 - 渐变背景 */
        QFrame#headerFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {MaterialDesignPalette.PRIMARY}, 
                stop:1 {MaterialDesignPalette.SUCCESS});
            border: none;
            min-height: 60px;
            max-height: 60px;
        }}
        
        QLabel#headerTitle {{
            color: white;
            font-size: 20px;
            font-weight: 400;
            padding: 0px 30px;
        }}
        
        /* 左侧面板样式 */
        QFrame#leftPanel {{
            background-color: {MaterialDesignPalette.PANEL};
            border-right: 1px solid {MaterialDesignPalette.BORDER};
            min-width: 280px;
            max-width: 400px;
        }}
        
        /* 现代化按钮样式 */
        QPushButton.primary {{
            background-color: {MaterialDesignPalette.PRIMARY};
            color: white;
            border: 1px solid {MaterialDesignPalette.PRIMARY};
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
        }}
        
        QPushButton.primary:hover {{
            background-color: #3367D6;
            border-color: #3367D6;
        }}
        
        QPushButton.primary:pressed {{
            background-color: #2E5BD6;
        }}
        
        QPushButton.success {{
            background-color: {MaterialDesignPalette.SUCCESS};
            color: white;
            border: 1px solid {MaterialDesignPalette.SUCCESS};
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
        }}
        
        QPushButton.success:hover {{
            background-color: #2E7D32;
            border-color: #2E7D32;
        }}
        
        /* 现代化输入框样式 */
        QLineEdit {{
            padding: 8px 12px;
            border: 1px solid {MaterialDesignPalette.BORDER};
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }}
        
        QLineEdit:focus {{
            border-color: {MaterialDesignPalette.PRIMARY};
            outline: none;
        }}
        
        /* 现代化表格样式 */
        QTableWidget {{
            gridline-color: #f0f0f0;
            background-color: white;
            border: 1px solid {MaterialDesignPalette.BORDER};
            border-radius: 8px;
            selection-background-color: #e3f2fd;
        }}
        
        QTableWidget::item {{
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }}
        
        QTableWidget::item:selected {{
            background-color: #e3f2fd;
            color: {MaterialDesignPalette.TEXT};
        }}
        
        QHeaderView::section {{
            background-color: #f8f9fa;
            padding: 12px 16px;
            border: none;
            border-bottom: 1px solid {MaterialDesignPalette.BORDER};
            font-weight: 500;
            font-size: 14px;
            color: {MaterialDesignPalette.TEXT};
        }}
        
        /* 树形控件样式 */
        QTreeWidget {{
            background-color: transparent;
            border: none;
            outline: none;
        }}
        
        QTreeWidget::item {{
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }}
        
        QTreeWidget::item:hover {{
            background-color: #e9e9e9;
        }}
        
        QTreeWidget::item:selected {{
            background-color: {MaterialDesignPalette.PRIMARY};
            color: white;
        }}
        
        /* Footer状态栏样式 */
        QFrame#footerFrame {{
            background-color: #f8f9fa;
            border-top: 1px solid {MaterialDesignPalette.BORDER};
            min-height: 60px;
            max-height: 60px;
        }}
        """


class ModernTableWidget(QTableWidget):
    """现代化表格组件 - 支持表格内编辑"""
    
    def __init__(self, rows=0, columns=0):
        super().__init__(rows, columns)
        self.setupTable()
    
    def setupTable(self):
        """设置表格"""
        # 设置表格属性
        self.setAlternatingRowColors(False)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.setShowGrid(True)
        
        # 设置水平表头
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.horizontalHeader().setHighlightSections(False)
        
        # 设置垂直表头
        self.verticalHeader().setVisible(False)
        
        # 添加测试数据
        self.addTestData()
    
    def addTestData(self):
        """添加测试数据"""
        self.setRowCount(5)
        self.setColumnCount(4)
        
        # 设置表头
        headers = ["姓名", "工号", "基本工资", "岗位津贴"]
        self.setHorizontalHeaderLabels(headers)
        
        # 添加数据
        test_data = [
            ["张三", "2024001", "5000.00", "1000.00"],
            ["李四", "2024002", "5500.00", "1200.00"],
            ["王五", "2024003", "6000.00", "1500.00"],
            ["赵六", "2024004", "4800.00", "900.00"],
            ["钱七", "2024005", "5200.00", "1100.00"]
        ]
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col >= 2:  # 工资列可编辑
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                else:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.setItem(row, col, item)


class ModernTreeWidget(QTreeWidget):
    """现代化树形组件"""
    
    def __init__(self):
        super().__init__()
        self.setupTree()
    
    def setupTree(self):
        """设置树形控件"""
        self.setHeaderHidden(True)
        self.setIndentation(20)
        
        # 添加测试数据
        self.addTestData()
    
    def addTestData(self):
        """添加测试数据"""
        # 月度工资表
        salary_root = QTreeWidgetItem(self, ["月度工资表"])
        year_2025 = QTreeWidgetItem(salary_root, ["2025年"])
        month_05 = QTreeWidgetItem(year_2025, ["5月"])
        QTreeWidgetItem(month_05, ["全部在职人员"])
        QTreeWidgetItem(month_05, ["离休人员"])
        QTreeWidgetItem(month_05, ["退休人员"])
        
        # 异动人员表
        change_root = QTreeWidgetItem(self, ["异动人员表"])
        change_2025 = QTreeWidgetItem(change_root, ["2025年"])
        change_05 = QTreeWidgetItem(change_2025, ["5月"])
        QTreeWidgetItem(change_05, ["起薪"])
        QTreeWidgetItem(change_05, ["转正定级"])
        QTreeWidgetItem(change_05, ["停薪-退休"])
        
        # 展开根节点
        self.expandItem(salary_root)
        self.expandItem(year_2025)
        self.expandItem(month_05)


class TechnicalValidationWindow(QMainWindow):
    """技术验证主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setupUI()
        self.applyStyles()
    
    def setupUI(self):
        """设置用户界面"""
        self.setWindowTitle("PyQt5现代化样式技术验证")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建Header
        self.createHeader(main_layout)
        
        # 创建工作区
        self.createWorkspace(main_layout)
        
        # 创建Footer
        self.createFooter(main_layout)
    
    def createHeader(self, parent_layout):
        """创建Header区域"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QHBoxLayout(header_frame)
        
        # 标题
        title_label = QLabel("月度工资异动处理系统 v1.3.1")
        title_label.setObjectName("headerTitle")
        header_layout.addWidget(title_label)
        
        # 菜单按钮（模拟）
        header_layout.addStretch()
        menu_items = ["文件", "报表", "分析", "工具", "设置", "帮助"]
        for item in menu_items:
            btn = QPushButton(item)
            btn.setProperty("class", "primary")
            btn.setMaximumWidth(80)
            header_layout.addWidget(btn)
        
        parent_layout.addWidget(header_frame)
    
    def createWorkspace(self, parent_layout):
        """创建工作区"""
        workspace = QSplitter(Qt.Horizontal)
        
        # 左侧面板
        left_panel = QFrame()
        left_panel.setObjectName("leftPanel")
        left_layout = QVBoxLayout(left_panel)
        
        # 数据导航标题
        nav_title = QLabel("数据导航")
        nav_title.setStyleSheet("font-size: 16px; font-weight: 500; color: #333; margin: 15px 0px;")
        left_layout.addWidget(nav_title)
        
        # 树形控件
        tree_widget = ModernTreeWidget()
        left_layout.addWidget(tree_widget)
        
        workspace.addWidget(left_panel)
        
        # 右侧主工作区
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 控制面板
        control_panel = QFrame()
        control_layout = QHBoxLayout(control_panel)
        control_layout.setContentsMargins(30, 15, 30, 15)
        
        # 查询输入框
        query_input = QLineEdit()
        query_input.setPlaceholderText("输入工号或姓名查询...")
        query_input.setMaximumWidth(300)
        control_layout.addWidget(query_input)
        
        # 查询按钮
        query_btn = QPushButton("查询")
        query_btn.setProperty("class", "primary")
        control_layout.addWidget(query_btn)
        
        control_layout.addStretch()
        
        # 功能按钮组
        function_btns = ["异动处理", "导出审批表", "导出请示(OA)", "导出请示(部务会)"]
        for btn_text in function_btns:
            btn = QPushButton(btn_text)
            if btn_text == "异动处理":
                btn.setProperty("class", "primary")
            else:
                btn.setProperty("class", "success")
            control_layout.addWidget(btn)
        
        right_layout.addWidget(control_panel)
        
        # 数据表格
        table_widget = ModernTableWidget()
        right_layout.addWidget(table_widget)
        
        workspace.addWidget(right_panel)
        
        # 设置分割比例
        workspace.setSizes([320, 880])
        parent_layout.addWidget(workspace)
    
    def createFooter(self, parent_layout):
        """创建Footer区域"""
        footer_frame = QFrame()
        footer_frame.setObjectName("footerFrame")
        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(30, 0, 30, 0)
        
        # 状态信息
        status_label = QLabel("● 系统就绪")
        status_label.setStyleSheet("color: #34A853; font-size: 14px;")
        footer_layout.addWidget(status_label)
        
        file_label = QLabel("已加载: 月度工资表>2025年>5月>全部在职人员")
        file_label.setStyleSheet("color: #666; font-size: 14px;")
        footer_layout.addWidget(file_label)
        
        footer_layout.addStretch()
        
        stats_label = QLabel("共 159 条记录")
        stats_label.setStyleSheet("color: #666; font-size: 14px;")
        footer_layout.addWidget(stats_label)
        
        parent_layout.addWidget(footer_frame)
    
    def applyStyles(self):
        """应用样式表"""
        self.setStyleSheet(ModernStyleSheet.get_main_style())


def run_technical_validation():
    """运行技术验证"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("PyQt5现代化样式技术验证")
    app.setStyle('Fusion')  # 使用Fusion样式作为基础
    
    # 创建主窗口
    window = TechnicalValidationWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(run_technical_validation()) 