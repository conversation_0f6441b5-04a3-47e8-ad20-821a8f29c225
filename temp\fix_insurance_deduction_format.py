#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复保险扣款字段空值显示问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_insurance_deduction_issue():
    """分析保险扣款字段问题"""
    print("=" * 80)
    print("保险扣款字段空值显示问题分析")
    print("=" * 80)
    
    print("\n1. 问题描述:")
    print("   - A岗职工表中保险扣款字段显示空白")
    print("   - 应该显示为'0.00'而不是空白")
    print("   - 排序时问题更加明显")
    
    print("\n2. 预期行为:")
    print("   - 空值(None, nan, 0, 0.0, '', ' ')应显示为'0.00'")
    print("   - 保留两位小数格式")
    print("   - 与其他浮点数字段保持一致")

def test_format_renderer():
    """测试格式渲染器"""
    print("\n" + "=" * 80)
    print("测试格式渲染器")
    print("=" * 80)
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        
        renderer = FormatRenderer()
        
        # 测试不同的空值情况
        test_values = [None, "", "0", 0, 0.0, "nan", "null", "-", "None"]
        
        print("\n保险扣款字段空值格式化测试:")
        for value in test_values:
            # 模拟浮点数格式化配置
            format_config = {
                'decimal_places': 2,
                'zero_display': '0.00'
            }
            
            result = renderer._render_float_value(value, format_config, "保险扣款")
            expected = "0.00"
            status = "✅" if result == expected else "❌"
            print(f"  {status} 输入: {repr(value):>8} -> 输出: '{result}' (期望: '{expected}')")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式渲染器测试失败: {e}")
        return False

def test_format_config():
    """测试格式配置"""
    print("\n" + "=" * 80)
    print("测试格式配置")
    print("=" * 80)
    
    try:
        from src.modules.format_management.format_config import FormatConfig
        
        config = FormatConfig()
        
        # 检查A岗职工配置
        a_grade_config = config.get_a_grade_employees_config()
        
        print("\nA岗职工格式配置:")
        print(f"  启用状态: {a_grade_config.get('enabled', False)}")
        
        float_fields = a_grade_config.get('field_formats', {}).get('float_fields', [])
        print(f"  浮点数字段数量: {len(float_fields)}")
        
        if "保险扣款" in float_fields:
            print("  ✅ 保险扣款字段已配置为浮点数类型")
        else:
            print("  ❌ 保险扣款字段未配置为浮点数类型")
            print(f"  当前浮点数字段: {float_fields}")
        
        format_rules = a_grade_config.get('format_rules', {})
        print(f"  空值显示规则: {format_rules.get('null_display', 'N/A')}")
        print(f"  零值显示规则: {format_rules.get('zero_display', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式配置测试失败: {e}")
        return False

def create_insurance_deduction_fix():
    """创建保险扣款修复方案"""
    print("\n" + "=" * 80)
    print("创建保险扣款修复方案")
    print("=" * 80)
    
    fix_strategies = [
        "1. 确保A岗职工配置中包含保险扣款字段",
        "2. 验证格式渲染器的空值处理逻辑",
        "3. 检查排序时的格式化应用",
        "4. 修复表格显示层的格式化调用",
        "5. 添加特殊字段的格式化验证"
    ]
    
    for strategy in fix_strategies:
        print(f"   {strategy}")
    
    # 生成修复代码
    fix_code = '''
# 修复1: 确保A岗职工配置包含保险扣款字段
def ensure_insurance_deduction_in_config():
    """确保保险扣款字段在A岗职工配置中"""
    config_path = "src/modules/format_management/format_config.json"
    
    # 检查配置文件中是否包含保险扣款字段
    # 如果没有，添加到float_fields列表中
    
# 修复2: 增强格式渲染器的保险扣款处理
def enhance_insurance_deduction_rendering():
    """增强保险扣款字段的格式化处理"""
    # 在_render_float_value方法中添加特殊处理
    # 确保保险扣款字段的空值始终显示为"0.00"
    
# 修复3: 修复排序时的格式化应用
def fix_sorting_format_application():
    """修复排序时的格式化应用"""
    # 确保排序后重新应用格式化
    # 特别关注保险扣款字段的格式化
    
# 修复4: 表格显示层格式化调用
def fix_table_display_formatting():
    """修复表格显示层的格式化调用"""
    # 在virtualized_expandable_table中确保正确调用格式化
    # 特别处理保险扣款字段
'''
    
    print("\n修复代码框架:")
    print(fix_code)
    
    return fix_code

def verify_current_behavior():
    """验证当前行为"""
    print("\n" + "=" * 80)
    print("验证当前行为")
    print("=" * 80)
    
    try:
        # 模拟A岗职工数据
        mock_data = [
            {
                "工号": "20061463",
                "姓名": "张三",
                "保险扣款": None,  # 空值
                "代扣代存养老保险": 0.0,  # 零值
                "车补": "-"  # 特殊字符
            },
            {
                "工号": "20061464", 
                "姓名": "李四",
                "保险扣款": "",  # 空字符串
                "代扣代存养老保险": "nan",  # NaN字符串
                "车补": 150.5  # 正常值
            }
        ]
        
        print("\n模拟数据测试:")
        for i, row in enumerate(mock_data):
            print(f"\n  行 {i+1}:")
            for field, value in row.items():
                if field in ["保险扣款", "代扣代存养老保险", "车补"]:
                    print(f"    {field}: {repr(value)} -> 应显示为: '0.00' 或格式化值")
        
        return True
        
    except Exception as e:
        print(f"❌ 当前行为验证失败: {e}")
        return False

def main():
    """主函数"""
    print("开始分析和修复保险扣款字段显示问题...")
    
    # 1. 分析问题
    analyze_insurance_deduction_issue()
    
    # 2. 测试格式渲染器
    renderer_ok = test_format_renderer()
    
    # 3. 测试格式配置
    config_ok = test_format_config()
    
    # 4. 验证当前行为
    behavior_ok = verify_current_behavior()
    
    # 5. 创建修复方案
    fix_code = create_insurance_deduction_fix()
    
    # 6. 总结
    print("\n" + "=" * 80)
    print("问题分析总结")
    print("=" * 80)
    
    print(f"  格式渲染器测试: {'✅ 通过' if renderer_ok else '❌ 失败'}")
    print(f"  格式配置测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"  当前行为验证: {'✅ 通过' if behavior_ok else '❌ 失败'}")
    
    if all([renderer_ok, config_ok, behavior_ok]):
        print("\n✅ 基础组件正常，问题可能在应用层面")
        print("   建议检查表格显示层的格式化调用逻辑")
    else:
        print("\n❌ 发现基础组件问题，需要修复底层逻辑")
    
    print("\n下一步行动:")
    print("  1. 检查A岗职工表的格式化配置应用")
    print("  2. 验证排序时的格式化保持")
    print("  3. 修复表格显示层的格式化调用")
    
    return True

if __name__ == "__main__":
    main()
