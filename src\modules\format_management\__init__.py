"""
统一格式管理模块

提供全系统的格式化管理功能，确保不同数据源路径的表头和数据格式一致性。

主要组件:
- UnifiedFormatManager: 统一格式管理器（核心）
- FormatConfig: 格式配置管理器  
- FieldRegistry: 字段注册系统
- FormatRenderer: 数据渲染器

功能特性:
- 集中化格式配置管理
- 统一的字段映射和显示规则
- 灵活的数据类型转换和格式化
- 多数据源路径的格式一致性保证
- 易于维护的配置驱动架构

使用方式:
```python
from src.modules.format_management import UnifiedFormatManager

# 初始化格式管理器
format_manager = UnifiedFormatManager()

# 格式化表头
headers = format_manager.format_headers(raw_headers, table_type='active_employees')

# 格式化数据
formatted_data = format_manager.format_data(raw_data, table_type='active_employees')
```

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

from .unified_format_manager import UnifiedFormatManager
from .format_config import FormatConfig
from .field_registry import FieldRegistry
from .format_renderer import FormatRenderer
from .format_events import (
    FormatEventType, FormatEventData, FormatEventBuilder, FormatEventHandler,
    LoggingFormatEventHandler, MetricsFormatEventHandler
)

__all__ = [
    'UnifiedFormatManager',
    'FormatConfig', 
    'FieldRegistry',
    'FormatRenderer',
    'FormatEventType',
    'FormatEventData',
    'FormatEventBuilder',
    'FormatEventHandler',
    'LoggingFormatEventHandler',
    'MetricsFormatEventHandler'
]

__version__ = '1.0.0'
__author__ = '统一格式管理系统重构团队'