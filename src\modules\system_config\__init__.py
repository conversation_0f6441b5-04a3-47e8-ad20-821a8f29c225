"""
月度工资异动处理系统 - 系统配置模块

本模块提供系统配置相关功能：
- 配置文件读写和验证
- 模板文件路径管理
- 用户偏好设置
- 系统升级和维护工具

模块结构：
- config_manager.py: 配置文件管理器
- template_manager.py: 模板文件管理器
- user_preferences.py: 用户偏好设置
- system_maintenance.py: 系统维护工具

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
"""

from .config_manager import ConfigManager, ConfigValidator
from .template_manager import TemplateManager
from .user_preferences import UserPreferences
from .system_maintenance import SystemMaintenance

__all__ = [
    "ConfigManager",
    "ConfigValidator", 
    "TemplateManager",
    "UserPreferences",
    "SystemMaintenance"
]

__version__ = "0.1.0" 