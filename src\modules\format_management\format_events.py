#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式管理事件定义 - 新架构版本

定义了统一格式管理系统中使用的所有事件类型和事件数据结构。
这些事件用于实现格式管理系统的事件驱动架构。

🎯 [新架构特性]:
- 类型化事件定义：使用dataclass定义事件数据结构
- 事件分类：按功能模块分类事件
- 事件文档：详细的事件用途和数据结构说明
- 向后兼容：与现有事件系统兼容

事件分类：
- 格式化事件：表头、数据、表格格式化相关
- 配置事件：配置文件、字段映射变更相关
- 缓存事件：缓存管理和性能相关
- 错误事件：错误处理和故障转移相关
- 状态事件：系统状态变更相关

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum


class FormatEventType(Enum):
    """格式管理事件类型枚举"""
    
    # 格式化完成事件
    FORMAT_HEADERS_COMPLETED = "format_headers_completed"
    FORMAT_DATA_COMPLETED = "format_data_completed"
    FORMAT_TABLE_COMPLETED = "format_table_completed"
    
    # 格式化失败事件
    FORMAT_HEADERS_FAILED = "format_headers_failed"
    FORMAT_DATA_FAILED = "format_data_failed"
    FORMAT_TABLE_FAILED = "format_table_failed"
    
    # 配置更新事件
    FORMAT_CONFIG_UPDATED = "format_config_updated"
    FIELD_MAPPING_UPDATED = "field_mapping_updated"
    
    # 配置更新失败事件
    FORMAT_CONFIG_UPDATE_FAILED = "format_config_update_failed"
    FIELD_MAPPING_UPDATE_FAILED = "field_mapping_update_failed"
    
    # 配置重新加载事件
    FORMAT_CONFIG_RELOADED = "format_config_reloaded"
    FIELD_MAPPING_RELOADED = "field_mapping_reloaded"
    CONFIGURATION_RELOADED = "configuration_reloaded"
    
    # 配置重新加载失败事件
    CONFIGURATION_RELOAD_FAILED = "configuration_reload_failed"
    
    # 缓存事件
    CACHE_CLEARED = "cache_cleared"
    
    # 数据状态事件
    FORMAT_DATA_EMPTY = "format_data_empty"
    
    # 文件变更事件
    CONFIG_FILE_CHANGED = "config_file_changed"
    FIELD_MAPPING_FILE_CHANGED = "field_mapping_file_changed"
    
    # 系统状态事件
    SYSTEM_STATE_CHANGED = "system_state_changed"


@dataclass
class FormatEventData:
    """格式化事件基础数据结构"""
    event_type: FormatEventType
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())
    source: str = "UnifiedFormatManager"
    version: str = "1.0.0"


@dataclass
class FormatHeadersCompletedData(FormatEventData):
    """表头格式化完成事件数据"""
    table_type: str = ""
    data_source: str = ""
    headers_count: int = 0
    raw_headers: Optional[List[str]] = None
    formatted_headers: Optional[List[str]] = None
    cache_hit: bool = False
    processing_time: Optional[float] = None


@dataclass
class FormatDataCompletedData(FormatEventData):
    """数据格式化完成事件数据"""
    table_type: str = ""
    data_source: str = ""
    data_count: int = 0
    columns_count: int = 0
    processing_time: Optional[float] = None


@dataclass
class FormatTableCompletedData(FormatEventData):
    """完整表格格式化完成事件数据"""
    table_type: str = ""
    data_source: str = ""
    headers_count: int = 0
    data_count: int = 0
    processing_time: float = 0.0
    cache_hit: bool = False


@dataclass
class FormatFailedData(FormatEventData):
    """格式化失败事件数据"""
    table_type: str = ""
    data_source: str = ""
    error: str = ""
    error_type: str = "FormatError"
    traceback: Optional[str] = None


@dataclass
class ConfigUpdatedData(FormatEventData):
    """配置更新事件数据"""
    config_key: str = ""
    config_value: Any = None
    old_value: Optional[Any] = None
    update_source: str = "user"


@dataclass
class FieldMappingUpdatedData(FormatEventData):
    """字段映射更新事件数据"""
    table_type: str = ""
    field_mappings: Dict[str, str] = field(default_factory=dict)
    added_mappings: Optional[Dict[str, str]] = None
    updated_mappings: Optional[Dict[str, str]] = None
    removed_mappings: Optional[List[str]] = None


@dataclass
class ConfigUpdateFailedData(FormatEventData):
    """配置更新失败事件数据"""
    config_key: Optional[str] = None
    table_type: Optional[str] = None
    error: str = ""
    error_type: str = "ConfigUpdateError"


@dataclass
class ConfigReloadedData(FormatEventData):
    """配置重新加载事件数据"""
    config_loaded: bool = False
    field_mappings_loaded: bool = False
    reload_source: str = "file_change"
    affected_tables: Optional[List[str]] = None


@dataclass
class CacheClearedData(FormatEventData):
    """缓存清除事件数据"""
    cleared_count: int = 0
    cache_type: str = "all"  # "all", "table_type", "headers", "data"
    table_type: Optional[str] = None
    reason: str = "manual"  # "manual", "config_change", "memory_pressure"


@dataclass
class FormatDataEmptyData(FormatEventData):
    """数据为空事件数据"""
    table_type: str = ""
    data_source: str = ""
    reason: str = "empty_dataset"


@dataclass
class FileChangedData(FormatEventData):
    """文件变更事件数据"""
    file_path: str = ""
    file_type: str = ""  # "config", "field_mapping"
    change_type: str = ""  # "modified", "created", "deleted"
    file_size: Optional[int] = None
    modification_time: Optional[float] = None


@dataclass
class SystemStateChangedData(FormatEventData):
    """系统状态变更事件数据"""
    state_key: str = ""
    state_value: Any = None
    old_value: Optional[Any] = None
    change_source: str = "system"


# 事件类型到数据类的映射
EVENT_DATA_CLASSES = {
    FormatEventType.FORMAT_HEADERS_COMPLETED: FormatHeadersCompletedData,
    FormatEventType.FORMAT_DATA_COMPLETED: FormatDataCompletedData,
    FormatEventType.FORMAT_TABLE_COMPLETED: FormatTableCompletedData,
    FormatEventType.FORMAT_HEADERS_FAILED: FormatFailedData,
    FormatEventType.FORMAT_DATA_FAILED: FormatFailedData,
    FormatEventType.FORMAT_TABLE_FAILED: FormatFailedData,
    FormatEventType.FORMAT_CONFIG_UPDATED: ConfigUpdatedData,
    FormatEventType.FIELD_MAPPING_UPDATED: FieldMappingUpdatedData,
    FormatEventType.FORMAT_CONFIG_UPDATE_FAILED: ConfigUpdateFailedData,
    FormatEventType.FIELD_MAPPING_UPDATE_FAILED: ConfigUpdateFailedData,
    FormatEventType.FORMAT_CONFIG_RELOADED: ConfigReloadedData,
    FormatEventType.FIELD_MAPPING_RELOADED: ConfigReloadedData,
    FormatEventType.CONFIGURATION_RELOADED: ConfigReloadedData,
    FormatEventType.CONFIGURATION_RELOAD_FAILED: ConfigUpdateFailedData,
    FormatEventType.CACHE_CLEARED: CacheClearedData,
    FormatEventType.FORMAT_DATA_EMPTY: FormatDataEmptyData,
    FormatEventType.CONFIG_FILE_CHANGED: FileChangedData,
    FormatEventType.FIELD_MAPPING_FILE_CHANGED: FileChangedData,
    FormatEventType.SYSTEM_STATE_CHANGED: SystemStateChangedData,
}


class FormatEventBuilder:
    """格式管理事件构建器"""
    
    @staticmethod
    def create_event_data(event_type: FormatEventType, **kwargs) -> FormatEventData:
        """
        创建事件数据对象
        
        Args:
            event_type: 事件类型
            **kwargs: 事件数据参数
            
        Returns:
            格式化的事件数据对象
        """
        data_class = EVENT_DATA_CLASSES.get(event_type, FormatEventData)
        
        # 确保包含基础字段
        if 'event_type' not in kwargs:
            kwargs['event_type'] = event_type
        if 'timestamp' not in kwargs:
            kwargs['timestamp'] = datetime.now().timestamp()
        if 'source' not in kwargs:
            kwargs['source'] = "UnifiedFormatManager"
        if 'version' not in kwargs:
            kwargs['version'] = "1.0.0"
        
        try:
            return data_class(**kwargs)
        except TypeError as e:
            # 如果参数不匹配，使用基础数据类
            return FormatEventData(event_type=event_type)
    
    @staticmethod
    def create_headers_completed_event(table_type: str, data_source: str, 
                                     headers_count: int, **kwargs) -> FormatHeadersCompletedData:
        """创建表头格式化完成事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.FORMAT_HEADERS_COMPLETED,
            table_type=table_type,
            data_source=data_source,
            headers_count=headers_count,
            **kwargs
        )
    
    @staticmethod
    def create_data_completed_event(table_type: str, data_source: str, 
                                  data_count: int, columns_count: int, 
                                  **kwargs) -> FormatDataCompletedData:
        """创建数据格式化完成事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.FORMAT_DATA_COMPLETED,
            table_type=table_type,
            data_source=data_source,
            data_count=data_count,
            columns_count=columns_count,
            **kwargs
        )
    
    @staticmethod
    def create_table_completed_event(table_type: str, data_source: str, 
                                   headers_count: int, data_count: int, 
                                   processing_time: float, **kwargs) -> FormatTableCompletedData:
        """创建完整表格格式化完成事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.FORMAT_TABLE_COMPLETED,
            table_type=table_type,
            data_source=data_source,
            headers_count=headers_count,
            data_count=data_count,
            processing_time=processing_time,
            **kwargs
        )
    
    @staticmethod
    def create_format_failed_event(event_type: FormatEventType, table_type: str, 
                                 data_source: str, error: str, **kwargs) -> FormatFailedData:
        """创建格式化失败事件"""
        return FormatEventBuilder.create_event_data(
            event_type,
            table_type=table_type,
            data_source=data_source,
            error=error,
            **kwargs
        )
    
    @staticmethod
    def create_config_updated_event(config_key: str, config_value: Any, 
                                  **kwargs) -> ConfigUpdatedData:
        """创建配置更新事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.FORMAT_CONFIG_UPDATED,
            config_key=config_key,
            config_value=config_value,
            **kwargs
        )
    
    @staticmethod
    def create_field_mapping_updated_event(table_type: str, field_mappings: Dict[str, str], 
                                         **kwargs) -> FieldMappingUpdatedData:
        """创建字段映射更新事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.FIELD_MAPPING_UPDATED,
            table_type=table_type,
            field_mappings=field_mappings,
            **kwargs
        )
    
    @staticmethod
    def create_cache_cleared_event(cleared_count: int, **kwargs) -> CacheClearedData:
        """创建缓存清除事件"""
        return FormatEventBuilder.create_event_data(
            FormatEventType.CACHE_CLEARED,
            cleared_count=cleared_count,
            **kwargs
        )


# 事件处理器接口
class FormatEventHandler:
    """格式管理事件处理器基类"""
    
    def handle_event(self, event_data: FormatEventData) -> None:
        """
        处理格式管理事件
        
        Args:
            event_data: 事件数据
        """
        handler_method = f"handle_{event_data.event_type.value}"
        if hasattr(self, handler_method):
            getattr(self, handler_method)(event_data)
        else:
            self.handle_unknown_event(event_data)
    
    def handle_unknown_event(self, event_data: FormatEventData) -> None:
        """处理未知事件类型"""
        pass


# 常用事件处理器示例
class LoggingFormatEventHandler(FormatEventHandler):
    """日志记录事件处理器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def handle_format_headers_completed(self, event_data: FormatHeadersCompletedData):
        """处理表头格式化完成事件"""
        self.logger.info(f"表头格式化完成: {event_data.table_type}, "
                        f"字段数: {event_data.headers_count}, "
                        f"缓存命中: {event_data.cache_hit}")
    
    def handle_format_table_completed(self, event_data: FormatTableCompletedData):
        """处理完整表格格式化完成事件"""
        self.logger.info(f"表格格式化完成: {event_data.table_type}, "
                        f"数据量: {event_data.data_count}, "
                        f"耗时: {event_data.processing_time:.2f}ms")
    
    def handle_format_config_updated(self, event_data: ConfigUpdatedData):
        """处理配置更新事件"""
        self.logger.info(f"配置更新: {event_data.config_key} = {event_data.config_value}")
    
    def handle_cache_cleared(self, event_data: CacheClearedData):
        """处理缓存清除事件"""
        self.logger.info(f"缓存清除: {event_data.cleared_count} 项, "
                        f"原因: {event_data.reason}")
    
    def handle_unknown_event(self, event_data: FormatEventData):
        """处理未知事件"""
        self.logger.debug(f"未知格式管理事件: {event_data.event_type}")


class MetricsFormatEventHandler(FormatEventHandler):
    """性能指标事件处理器"""
    
    def __init__(self):
        self.metrics = {
            'format_operations': 0,
            'total_processing_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0
        }
    
    def handle_format_table_completed(self, event_data: FormatTableCompletedData):
        """处理完整表格格式化完成事件"""
        self.metrics['format_operations'] += 1
        self.metrics['total_processing_time'] += event_data.processing_time
        
        if event_data.cache_hit:
            self.metrics['cache_hits'] += 1
        else:
            self.metrics['cache_misses'] += 1
    
    def handle_format_headers_failed(self, event_data: FormatFailedData):
        """处理格式化失败事件"""
        self.metrics['errors'] += 1
    
    def handle_format_data_failed(self, event_data: FormatFailedData):
        """处理格式化失败事件"""
        self.metrics['errors'] += 1
    
    def handle_format_table_failed(self, event_data: FormatFailedData):
        """处理格式化失败事件"""
        self.metrics['errors'] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_operations = self.metrics['cache_hits'] + self.metrics['cache_misses']
        cache_hit_rate = (self.metrics['cache_hits'] / max(1, total_operations)) * 100
        
        avg_processing_time = 0
        if self.metrics['format_operations'] > 0:
            avg_processing_time = self.metrics['total_processing_time'] / self.metrics['format_operations']
        
        return {
            **self.metrics,
            'cache_hit_rate': cache_hit_rate,
            'avg_processing_time': avg_processing_time
        }