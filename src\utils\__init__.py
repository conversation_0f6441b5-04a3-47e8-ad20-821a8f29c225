"""
月度工资异动处理系统 - 工具库包

本包包含系统的通用工具模块：
- log_config: 日志配置和管理
- file_utils: 文件操作工具 (待实现)
- data_utils: 数据处理工具 (待实现)
- config_utils: 配置管理工具 (待实现)

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
"""

from .log_config import (
    setup_logger,
    get_module_logger,
    log_function_call,
    log_file_operation,
    log_error_with_context,
    init_default_logger,
    logger
)

__all__ = [
    "setup_logger",
    "get_module_logger",
    "log_function_call", 
    "log_file_operation",
    "log_error_with_context",
    "init_default_logger",
    "logger"
] 