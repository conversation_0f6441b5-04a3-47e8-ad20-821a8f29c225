"""
现代化样式系统 - Modern Style System

基于Material Design规范的PyQt5样式系统，提供现代化的界面样式。

主要功能:
1. Material Design配色方案
2. 现代化QSS样式表
3. 响应式布局支持
4. 动画效果样式
5. 阴影效果样式
"""

from typing import Dict, Any
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QColor, QPalette, QFont
from PyQt5.QtWidgets import QWidget, QGraphicsDropShadowEffect


class MaterialDesignPalette:
    """Material Design配色方案"""
    
    # 主色调 - 蓝色系
    PRIMARY = {
        'main': '#1976D2',      # 主要蓝色
        'light': '#42A5F5',     # 浅蓝色
        'dark': '#0D47A1',      # 深蓝色
        'text': '#FFFFFF'       # 主色调文本
    }
    
    # 次要色调 - 青色系
    SECONDARY = {
        'main': '#00BCD4',      # 青色
        'light': '#4DD0E1',     # 浅青色
        'dark': '#00838F',      # 深青色
        'text': '#FFFFFF'       # 次要色调文本
    }
    
    # 背景色
    BACKGROUND = {
        'default': '#FAFAFA',   # 默认背景
        'paper': '#FFFFFF',     # 纸质背景
        'card': '#FFFFFF',      # 卡片背景
        'header': '#F5F5F5',    # 头部背景
        'sidebar': '#F8F9FA'    # 侧边栏背景
    }
    
    # 文本色
    TEXT = {
        'primary': '#212121',   # 主要文本
        'secondary': '#757575', # 次要文本
        'disabled': '#BDBDBD',  # 禁用文本
        'hint': '#9E9E9E'       # 提示文本
    }
    
    # 状态色
    STATUS = {
        'success': '#4CAF50',   # 成功 - 绿色
        'warning': '#FF9800',   # 警告 - 橙色
        'error': '#F44336',     # 错误 - 红色
        'info': '#2196F3'       # 信息 - 蓝色
    }
    
    # 边框色
    BORDER = {
        'light': '#E0E0E0',     # 浅边框
        'medium': '#BDBDBD',    # 中等边框
        'dark': '#9E9E9E'       # 深边框
    }
    
    # 阴影色
    SHADOW = {
        'light': 'rgba(0, 0, 0, 0.1)',   # 浅阴影
        'medium': 'rgba(0, 0, 0, 0.2)',  # 中等阴影
        'dark': 'rgba(0, 0, 0, 0.3)'     # 深阴影
    }


class ModernStyleSheet:
    """现代化QSS样式表系统"""
    
    @staticmethod
    def get_main_window_style() -> str:
        """获取主窗口样式"""
        return f"""
        QMainWindow {{
            background-color: {MaterialDesignPalette.BACKGROUND['default']};
            color: {MaterialDesignPalette.TEXT['primary']};
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 9pt;
        }}
        
        QMainWindow::separator {{
            background-color: {MaterialDesignPalette.BORDER['light']};
            width: 1px;
            height: 1px;
        }}
        """
    
    @staticmethod
    def get_header_style() -> str:
        """获取头部样式 - 渐变背景"""
        return f"""
        #HeaderWidget {{
            background: qlineargradient(
                x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 {MaterialDesignPalette.PRIMARY['main']},
                stop: 1 {MaterialDesignPalette.SECONDARY['main']}
            );
            border: none;
            min-height: 60px;
            max-height: 60px;
        }}
        
        #HeaderWidget QLabel {{
            color: {MaterialDesignPalette.PRIMARY['text']};
            font-size: 16pt;
            font-weight: bold;
            padding: 10px;
        }}
        """
    
    @staticmethod
    def get_sidebar_style() -> str:
        """获取侧边栏样式"""
        return f"""
        #SidebarWidget {{
            background-color: {MaterialDesignPalette.BACKGROUND['sidebar']};
            border-right: 1px solid {MaterialDesignPalette.BORDER['light']};
            min-width: 250px;
        }}
        
        #SidebarWidget QTreeWidget {{
            background-color: transparent;
            border: none;
            outline: none;
            font-size: 9pt;
        }}
        
        #SidebarWidget QTreeWidget::item {{
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            margin: 2px 8px;
        }}
        
        #SidebarWidget QTreeWidget::item:hover {{
            background-color: {MaterialDesignPalette.PRIMARY['light']};
            color: {MaterialDesignPalette.PRIMARY['text']};
        }}
        
        #SidebarWidget QTreeWidget::item:selected {{
            background-color: {MaterialDesignPalette.PRIMARY['main']};
            color: {MaterialDesignPalette.PRIMARY['text']};
        }}
        """
    
    @staticmethod
    def get_button_style() -> str:
        """获取按钮样式 - 支持悬停效果"""
        return f"""
        QPushButton {{
            background-color: {MaterialDesignPalette.PRIMARY['main']};
            color: {MaterialDesignPalette.PRIMARY['text']};
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 9pt;
            font-weight: 500;
            min-height: 16px;
        }}
        
        QPushButton:hover {{
            background-color: {MaterialDesignPalette.PRIMARY['light']};
        }}
        
        QPushButton:pressed {{
            background-color: {MaterialDesignPalette.PRIMARY['dark']};
        }}
        
        QPushButton:disabled {{
            background-color: {MaterialDesignPalette.TEXT['disabled']};
            color: {MaterialDesignPalette.TEXT['hint']};
        }}
        
        /* 次要按钮样式 */
        QPushButton[buttonStyle="secondary"] {{
            background-color: {MaterialDesignPalette.SECONDARY['main']};
            color: {MaterialDesignPalette.SECONDARY['text']};
        }}
        
        QPushButton[buttonStyle="secondary"]:hover {{
            background-color: {MaterialDesignPalette.SECONDARY['light']};
        }}
        
        QPushButton[buttonStyle="secondary"]:pressed {{
            background-color: {MaterialDesignPalette.SECONDARY['dark']};
        }}
        
        /* 成功按钮样式 */
        QPushButton[buttonStyle="success"] {{
            background-color: {MaterialDesignPalette.STATUS['success']};
            color: white;
        }}
        
        /* 警告按钮样式 */
        QPushButton[buttonStyle="warning"] {{
            background-color: {MaterialDesignPalette.STATUS['warning']};
            color: white;
        }}
        
        /* 错误按钮样式 */
        QPushButton[buttonStyle="error"] {{
            background-color: {MaterialDesignPalette.STATUS['error']};
            color: white;
        }}
        """
    
    @staticmethod
    def get_input_style() -> str:
        """获取输入框样式"""
        return f"""
        QLineEdit, QTextEdit, QComboBox {{
            background-color: {MaterialDesignPalette.BACKGROUND['paper']};
            border: 2px solid {MaterialDesignPalette.BORDER['light']};
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 9pt;
            color: {MaterialDesignPalette.TEXT['primary']};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border-color: {MaterialDesignPalette.PRIMARY['main']};
        }}
        
        QLineEdit:hover, QTextEdit:hover, QComboBox:hover {{
            border-color: {MaterialDesignPalette.PRIMARY['light']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {MaterialDesignPalette.TEXT['secondary']};
        }}
        """
    
    @staticmethod
    def get_input_style() -> str:
        """获取输入框样式"""
        return f"""
        QLineEdit {{
            background-color: {MaterialDesignPalette.BACKGROUND['paper']};
            border: 1px solid {MaterialDesignPalette.BORDER['light']};
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 9pt;
            color: {MaterialDesignPalette.TEXT['primary']};
        }}
        
        QLineEdit:focus {{
            border-color: {MaterialDesignPalette.PRIMARY['main']};
        }}
        
        QLineEdit:disabled {{
            background-color: {MaterialDesignPalette.BACKGROUND['header']};
            color: {MaterialDesignPalette.TEXT['disabled']};
        }}
        
        QComboBox {{
            background-color: {MaterialDesignPalette.BACKGROUND['paper']};
            border: 1px solid {MaterialDesignPalette.BORDER['light']};
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 9pt;
            color: {MaterialDesignPalette.TEXT['primary']};
            min-width: 60px;
        }}
        
        QComboBox:focus {{
            border-color: {MaterialDesignPalette.PRIMARY['main']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-style: solid;
            border-width: 3px;
            border-color: {MaterialDesignPalette.TEXT['secondary']} transparent transparent transparent;
        }}
        """
    
    @staticmethod
    def get_table_style() -> str:
        """获取表格样式"""
        return f"""
        QTableWidget {{
            background-color: {MaterialDesignPalette.BACKGROUND['paper']};
            border: 1px solid {MaterialDesignPalette.BORDER['light']};
            border-radius: 4px;
            gridline-color: {MaterialDesignPalette.BORDER['light']};
            font-size: 9pt;
            selection-background-color: {MaterialDesignPalette.PRIMARY['light']};
        }}
        
        QTableWidget::item {{
            padding: 8px;
            border: none;
        }}
        
        QTableWidget::item:selected {{
            background-color: {MaterialDesignPalette.PRIMARY['light']};
            color: {MaterialDesignPalette.PRIMARY['text']};
        }}
        
        QTableWidget::item:hover {{
            background: qlineargradient(
                x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 rgba(66, 165, 245, 0.08),
                stop: 0.5 rgba(66, 165, 245, 0.12),
                stop: 1 rgba(66, 165, 245, 0.08)
            );
            color: {MaterialDesignPalette.TEXT['primary']};
            border-left: 3px solid {MaterialDesignPalette.PRIMARY['light']};
        }}
        
        QHeaderView::section {{
            background: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #FAFAFA,
                stop: 0.5 {MaterialDesignPalette.BACKGROUND['header']},
                stop: 1 #E8E8E8
            );
            border: none;
            border-bottom: 2px solid {MaterialDesignPalette.BORDER['light']};
            border-right: 1px solid {MaterialDesignPalette.BORDER['light']};
            padding: 10px 14px;
            font-weight: 600;
            color: {MaterialDesignPalette.TEXT['primary']};
            text-align: left;
        }}
        
        QHeaderView::section:hover {{
            background: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 rgba(66, 165, 245, 0.2),
                stop: 0.5 rgba(66, 165, 245, 0.1),
                stop: 1 rgba(66, 165, 245, 0.05)
            );
            color: {MaterialDesignPalette.PRIMARY['main']};
            border-bottom: 2px solid {MaterialDesignPalette.PRIMARY['light']};
        }}
        """
    
    @staticmethod
    def get_card_style() -> str:
        """获取卡片样式 - 模拟阴影效果"""
        return f"""
        .ModernCard {{
            background-color: {MaterialDesignPalette.BACKGROUND['card']};
            border: 1px solid {MaterialDesignPalette.BORDER['light']};
            border-radius: 8px;
            padding: 16px;
        }}
        
        /* 使用渐变模拟阴影效果 */
        .ModernCard[shadowLevel="1"] {{
            background: qradialgradient(
                cx: 0.5, cy: 0.5, radius: 1,
                stop: 0.9 {MaterialDesignPalette.BACKGROUND['card']},
                stop: 1.0 {MaterialDesignPalette.SHADOW['light']}
            );
        }}
        
        .ModernCard[shadowLevel="2"] {{
            background: qradialgradient(
                cx: 0.5, cy: 0.5, radius: 1,
                stop: 0.85 {MaterialDesignPalette.BACKGROUND['card']},
                stop: 1.0 {MaterialDesignPalette.SHADOW['medium']}
            );
        }}
        
        .ModernCard[shadowLevel="3"] {{
            background: qradialgradient(
                cx: 0.5, cy: 0.5, radius: 1,
                stop: 0.8 {MaterialDesignPalette.BACKGROUND['card']},
                stop: 1.0 {MaterialDesignPalette.SHADOW['dark']}
            );
        }}
        """
    
    @staticmethod
    def get_status_bar_style() -> str:
        """获取状态栏样式"""
        return f"""
        QStatusBar {{
            background-color: {MaterialDesignPalette.BACKGROUND['header']};
            border-top: 1px solid {MaterialDesignPalette.BORDER['light']};
            color: {MaterialDesignPalette.TEXT['secondary']};
            font-size: 8pt;
            padding: 4px 8px;
        }}
        
        QStatusBar QLabel {{
            color: {MaterialDesignPalette.TEXT['secondary']};
        }}
        
        QStatusBar QProgressBar {{
            border: 1px solid {MaterialDesignPalette.BORDER['medium']};
            border-radius: 2px;
            background-color: {MaterialDesignPalette.BACKGROUND['paper']};
            text-align: center;
            font-size: 8pt;
            max-height: 16px;
        }}
        
        QStatusBar QProgressBar::chunk {{
            background-color: {MaterialDesignPalette.PRIMARY['main']};
            border-radius: 1px;
        }}
        """
    
    @staticmethod
    def get_complete_style() -> str:
        """获取完整的样式表"""
        return "\n".join([
            ModernStyleSheet.get_main_window_style(),
            ModernStyleSheet.get_header_style(),
            ModernStyleSheet.get_sidebar_style(),
            ModernStyleSheet.get_button_style(),
            ModernStyleSheet.get_input_style(),
            ModernStyleSheet.get_table_style(),
            ModernStyleSheet.get_card_style(),
            ModernStyleSheet.get_status_bar_style()
        ])


class ModernAnimations:
    """现代化动画系统"""
    
    @staticmethod
    def create_fade_animation(widget: QWidget, duration: int = 300) -> QPropertyAnimation:
        """创建淡入淡出动画"""
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_slide_animation(widget: QWidget, start_pos: QRect, end_pos: QRect, 
                             duration: int = 300) -> QPropertyAnimation:
        """创建滑动动画"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_button_press_animation(button: QWidget, duration: int = 150) -> QPropertyAnimation:
        """创建按钮按压动画"""
        animation = QPropertyAnimation(button, b"geometry")
        animation.setDuration(duration)
        
        # 获取当前几何形状
        current_rect = button.geometry()
        pressed_rect = QRect(
            current_rect.x() + 1,
            current_rect.y() + 1,
            current_rect.width() - 2,
            current_rect.height() - 2
        )
        
        animation.setStartValue(current_rect)
        animation.setEndValue(pressed_rect)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation


class ModernShadowEffects:
    """现代化阴影效果系统"""
    
    @staticmethod
    def create_card_shadow(level: int = 1) -> QGraphicsDropShadowEffect:
        """创建卡片阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        
        if level == 1:
            # 轻微阴影
            shadow.setBlurRadius(10)
            shadow.setOffset(0, 2)
            shadow.setColor(QColor(0, 0, 0, 25))
        elif level == 2:
            # 中等阴影
            shadow.setBlurRadius(15)
            shadow.setOffset(0, 4)
            shadow.setColor(QColor(0, 0, 0, 40))
        elif level == 3:
            # 深度阴影
            shadow.setBlurRadius(20)
            shadow.setOffset(0, 6)
            shadow.setColor(QColor(0, 0, 0, 60))
        
        return shadow
    
    @staticmethod
    def create_button_shadow() -> QGraphicsDropShadowEffect:
        """创建按钮阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setOffset(0, 2)
        shadow.setColor(QColor(0, 0, 0, 30))
        return shadow


class ResponsiveLayout:
    """响应式布局系统"""
    
    # 响应式断点
    BREAKPOINTS = {
        'small': 1024,
        'medium': 1440,
        'large': 1920
    }
    
    @staticmethod
    def get_size_category(width: int) -> str:
        """获取尺寸类别"""
        if width < ResponsiveLayout.BREAKPOINTS['small']:
            return 'small'
        elif width < ResponsiveLayout.BREAKPOINTS['medium']:
            return 'medium'
        else:
            return 'large'
    
    @staticmethod
    def get_sidebar_width(size_category: str) -> int:
        """获取侧边栏宽度"""
        widths = {
            'small': 200,
            'medium': 250,
            'large': 300
        }
        return widths.get(size_category, 250)
    
    @staticmethod
    def should_collapse_sidebar(size_category: str) -> bool:
        """是否应该折叠侧边栏"""
        return size_category == 'small'
    
    @staticmethod
    def get_font_size(size_category: str) -> int:
        """获取字体大小"""
        sizes = {
            'small': 8,
            'medium': 9,
            'large': 10
        }
        return sizes.get(size_category, 9) 