#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库表检测问题

验证数据库中是否存在工资数据表
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.utils.log_config import setup_logger
from src.modules.data_storage.database_manager import DatabaseManager
from src.core.config_manager import ConfigManager

logger = setup_logger("DatabaseTableTest")

def test_database_tables():
    """测试数据库表检测"""
    try:
        logger.info("开始测试数据库表检测...")
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 获取数据库路径
        db_path = config_manager.get_database_path()
        logger.info(f"数据库路径: {db_path}")
        
        # 初始化数据库管理器
        db_manager = DatabaseManager(str(db_path))
        
        # 查询所有表
        query = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
        """
        
        all_tables = db_manager.execute_query(query)
        logger.info(f"数据库中找到的所有表: {len(all_tables)}个")
        
        for table in all_tables:
            table_name = table['name']
            logger.info(f"  表名: {table_name}")
            
            # 检查是否是工资数据表
            if table_name.startswith('salary_data_'):
                logger.info(f"    ✅ 工资数据表: {table_name}")
                
                # 检查表中记录数
                count_query = f"SELECT COUNT(*) as count FROM {table_name}"
                count_result = db_manager.execute_query(count_query)
                if count_result:
                    record_count = count_result[0]['count']
                    logger.info(f"    📊 记录数: {record_count}")
                else:
                    logger.warning(f"    ❌ 无法获取记录数")
            else:
                logger.debug(f"    ➖ 其他表: {table_name}")
        
        # 专门查找工资数据表
        salary_tables = [t['name'] for t in all_tables if t['name'].startswith('salary_data_')]
        logger.info(f"工资数据表总计: {len(salary_tables)}个")
        
        if len(salary_tables) == 0:
            logger.warning("⚠️  数据库中没有任何工资数据表！")
            logger.info("这可能说明：")
            logger.info("  1. 数据库是空的，需要导入数据")
            logger.info("  2. 数据表被误删除了")
            logger.info("  3. 数据库文件损坏")
        else:
            logger.info("✅ 数据库检测正常，找到工资数据表")
            
        return len(salary_tables) > 0
        
    except Exception as e:
        logger.error(f"数据库表检测失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔧 开始数据库表检测测试")
    logger.info("=" * 50)
    
    result = test_database_tables()
    
    logger.info("=" * 50)
    logger.info("检测结果:")
    if result:
        logger.info("✅ 数据库表检测正常")
    else:
        logger.warning("❌ 数据库表检测发现问题")
        logger.info("建议:")
        logger.info("  1. 检查数据库文件是否存在")
        logger.info("  2. 尝试重新导入工资数据")
        logger.info("  3. 检查数据库权限")
    
    return result

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)