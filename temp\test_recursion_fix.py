"""
测试递归修复是否有效 - 无emoji版本
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_import():
    """测试是否可以正常导入"""
    try:
        print("测试导入...")
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        print("PrototypeMainWindow导入成功")
        return True
        
    except RecursionError as e:
        print(f"递归错误: {str(e)[:100]}...")
        return False
    except Exception as e:
        print(f"导入测试失败: {str(e)[:100]}...")
        return False

if __name__ == "__main__":
    success = test_import()
    print(f"测试结果: {'通过' if success else '失败'}")