#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工资数据格式化器

本模块负责对不同类型的工资表数据进行统一格式化处理，包括：
- 字段类型转换（字符串、整型、浮点型）
- 特殊字段处理（月份、年份）
- 空值统一处理
- 数据验证和清理

主要功能：
- format_active_employee_data(): 格式化全部在职人员工资表数据
- format_empty_values(): 统一处理空值显示
- 支持扩展其他工资表类型的格式化

创建时间: 2025-07-01
作者: 月度工资异动处理系统开发团队
"""

import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import re
import logging

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

# 初始化日志
logger = setup_logger("data_storage.salary_data_formatter")


class SalaryDataFormatter:
    """
    工资数据格式化器
    
    负责对工资表数据进行统一的格式化处理，确保数据类型正确、
    空值处理统一、显示格式符合要求。
    """
    
    # 定义各表类型的字段分类 - 同时支持中文和英文字段名
    FIELD_TYPE_MAPPING = {
        # 全部在职人员工资表
        'active_employees': {
            'id_fields': [
                # 英文字段名（数据库字段）
                'employee_id', 'employee_type_code',
                # 中文字段名（显示字段）
                '工号', '人员类别代码'
            ],
            'string_fields': [
                # 英文字段名（数据库字段）
                'employee_name', 'department', 'employee_type',
                # 中文字段名（显示字段）
                '姓名', '部门名称', '人员类别'
                # 注意：工号和人员类别代码只在id_fields中定义，不在string_fields中重复定义
            ],
            'integer_fields': [
                # 按用户要求：全部在职人员工资表现在所有数值字段都为浮点型，无整型字段
            ],
            'float_fields': [
                # 英文字段名（数据库字段）
                'position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance',
                'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance',
                'performance_bonus_2025', 'provident_fund_2025',
                'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance',
                # 中文字段名（显示字段）
                '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴',
                '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴',
                '2025年奖励性绩效预发', '2025公积金',
                '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险'
            ],
            'special_fields': {
                # 按用户要求：月份提取后两位转换为字符串，年份转换为字符串
                'month_fields': ['month'],
                'year_fields': ['year']
            }
        },
        
        # 离休人员工资表
        'retired_employees': {
            'id_fields': [
                # 英文字段名（数据库字段）
                'employee_id',
                # 中文字段名（显示字段）
                '人员代码'
            ],
            'string_fields': [
                # 英文字段名（数据库字段）
                'employee_id', 'employee_name', 'department', 'remarks',
                # 中文字段名（显示字段）
                '人员代码', '姓名', '部门名称', '备注'
            ],
            'integer_fields': [
                # 按用户要求：清空整型字段，全部改为浮点型
            ],
            'float_fields': [
                # 英文字段名（数据库字段）
                'basic_retirement_salary', 'balance_allowance', 'living_allowance', 'housing_allowance',
                'property_allowance', 'retirement_allowance', 'nursing_fee', 'one_time_living_allowance',
                'supplement', 'total', 'advance', 'car_allowance', 'total_salary',
                # 中文字段名（显示字段）
                '基本离休费', '结余津贴', '生活补贴', '住房补贴',
                '物业补贴', '离休补贴', '护理费', '增发一次性生活补贴',
                '补发', '合计', '借支', '车补', '应发工资'
            ],
            'special_fields': {
                'month_fields': ['month', '月份'],
                'year_fields': ['year', '年份']
            }
        },
        
        # 退休人员工资表 - 按用户要求重新配置
        'pension_employees': {
            'id_fields': [
                # 英文字段名（数据库字段）
                'employee_id', 'employee_type_code',
                # 中文字段名（显示字段）
                '人员代码', '人员类别代码'
            ],
            'string_fields': [
                # 英文字段名（数据库字段）
                'employee_id', 'employee_name', 'department', 'employee_type_code', 'remarks',
                # 中文字段名（显示字段）
                '人员代码', '姓名', '部门名称', '人员类别代码', '备注'
            ],
            'integer_fields': [
                # 退休人员工资表不再有整型字段，全部改为浮点型
            ],
            'float_fields': [
                # 英文字段名（数据库字段）
                'basic_retirement_salary', 'allowance', 'balance_allowance', 'retirement_living_allowance', 'nursing_fee',
                'property_allowance', 'housing_allowance', 'salary_advance',
                'adjustment_2016', 'adjustment_2017', 'adjustment_2018', 'adjustment_2019',
                'adjustment_2020', 'adjustment_2021', 'adjustment_2022', 'adjustment_2023',
                'supplement', 'advance', 'total_salary', 'provident_fund', 'insurance_deduction',
                # 中文字段名（显示字段）
                '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费',
                '物业补贴', '住房补贴', '增资预付',
                '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整',
                '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整',
                '补发', '借支', '应发工资', '公积', '保险扣款'
            ],
            'special_fields': {
                'month_fields': ['month', '月份'],  # 提取后两位转字符串
                'year_fields': ['year', '年份']    # 转字符串
            }
        },
        
        # A岗职工工资表
        'a_grade_employees': {
            'id_fields': [
                # 英文字段名（数据库字段）
                'employee_id', 'employee_type_code',
                # 中文字段名（显示字段）
                '工号', '人员类别代码'
            ],
            'string_fields': [
                # 英文字段名（数据库字段）
                'employee_name', 'department', 'employee_type',
                # 中文字段名（显示字段）
                '姓名', '部门名称', '人员类别'
                # 注意：工号和人员类别代码只在id_fields中定义，不在string_fields中重复定义
            ],
            'integer_fields': [
                # 清空，A岗职工表所有数值字段都改为浮点型
            ],
            'float_fields': [
                # 英文字段名（数据库字段）
                'position_salary_2025', 'seniority_salary_2025', 'allowance', 'balance_allowance',
                'basic_performance_2025', 'health_fee', 'living_allowance_2025', 'performance_bonus_2025',
                'provident_fund_2025', 'insurance_deduction',
                'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance',
                # 中文字段名（显示字段）
                '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴',
                '2025年基础性绩效', '卫生费', '2025年生活补贴', '2025年奖励性绩效预发',
                '2025公积金', '保险扣款',
                '车补', '补发', '借支', '应发工资', '代扣代存养老保险'
            ],
            'special_fields': {
                'month_fields': ['month', '月份'],  # 提取后两位转字符串
                'year_fields': ['year', '年份']    # 转字符串
            }
        },
        
        # 通用工资数据表（作为默认配置）
        'salary_data': {
            'id_fields': [
                '工号', '人员代码', '人员类别代码'
            ],
            'string_fields': [
                '姓名', '部门名称', '人员类别'
            ],
            'integer_fields': [
                # 通用工资字段
                '基本工资', '津贴', '奖金', '应发工资'
            ],
            'float_fields': [
                # 通用补贴字段
                '住房补贴', '交通补贴', '补发'
            ],
            'special_fields': {
                'month_fields': ['month', '月份'],
                'year_fields': ['year', '年份']
            }
        }
    }
    
    # 定义需要显示为空白的值
    EMPTY_VALUES = [None, 'None', 'nan', 'NaN', np.nan, 0, 0.0, '', ' ', 'null', 'NULL']
    
    @classmethod
    def format_table_data(cls, df: pd.DataFrame, table_type: str = 'active_employees') -> pd.DataFrame:
        """
        格式化工资表数据 - 严格按表类型处理
        
        Args:
            df (pd.DataFrame): 原始数据框
            table_type (str): 表类型，支持: 'active_employees', 'retired_employees', 
                             'pension_employees', 'a_grade_employees', 'salary_data'
                             也支持完整的数据库表名，如 'salary_data_2025_01_active_employees'
            
        Returns:
            pd.DataFrame: 格式化后的数据框
        """
        logger.info(f"开始格式化工资表数据 - 表类型: {table_type}")
        
        if df is None or df.empty:
            logger.warning("传入的数据框为空，返回空数据框")
            return df
        
        try:
            # 创建数据框副本，避免修改原始数据
            formatted_df = df.copy()
            
            # 记录原始数据信息
            original_rows = len(formatted_df)
            original_cols = len(formatted_df.columns)
            logger.info(f"原始数据: {original_rows}行, {original_cols}列")
            
            # 🔧 [新增] 表名到表类型的映射逻辑
            actual_table_type = cls._extract_table_type_from_name(table_type)
            logger.info(f"🔧 [表名映射] 输入表名: {table_type}, 提取的表类型: {actual_table_type}")
            
            # 获取字段分类配置 - 严格按表类型处理
            field_config = cls.FIELD_TYPE_MAPPING.get(actual_table_type, cls.FIELD_TYPE_MAPPING.get('salary_data', {}))
            logger.info(f"使用表类型配置: {actual_table_type}, 配置字段: {list(field_config.keys())}")
            
            # 1. 处理ID类型字段（优先处理，确保无小数点）
            id_fields = field_config.get('id_fields', [])
            
            # 🔧 [修复] 扩展ID字段列表，包含中文字段名
            extended_id_fields = list(id_fields)
            for field in formatted_df.columns:
                if field in ['工号', '人员类别代码', 'employee_id', 'employee_type_code'] and field not in extended_id_fields:
                    extended_id_fields.append(field)
            
            logger.info(f"🔧 [格式化调试] 开始处理ID字段: {extended_id_fields}")
            if extended_id_fields:
                for field in extended_id_fields:
                    if field in formatted_df.columns:
                        sample_values = formatted_df[field].head(3).tolist()
                        logger.info(f"🔧 [格式化调试] 处理前 {field} 字段样例: {sample_values}")
            cls._format_id_fields(formatted_df, extended_id_fields)
            if extended_id_fields:
                for field in extended_id_fields:
                    if field in formatted_df.columns:
                        sample_values = formatted_df[field].head(3).tolist()
                        logger.info(f"🔧 [格式化调试] 处理后 {field} 字段样例: {sample_values}")
            
            # 2. 处理字符串类型字段
            cls._format_string_fields(formatted_df, field_config.get('string_fields', []))
            
            # 3. 处理整型字段
            cls._format_integer_fields(formatted_df, field_config.get('integer_fields', []))
            
            # 4. 处理浮点型字段
            cls._format_float_fields(formatted_df, field_config.get('float_fields', []))
            
            # 5. 处理特殊字段 - 传入表类型
            formatted_df = cls._format_special_fields(formatted_df, actual_table_type)
            
            # 6. 统一处理空值
            formatted_df = cls._apply_empty_value_formatting(formatted_df)
            
            logger.info(f"数据格式化完成: {len(formatted_df)}行, {len(formatted_df.columns)}列")
            return formatted_df
            
        except Exception as e:
            logger.error(f"格式化全部在职人员工资表数据失败: {e}")
            logger.exception("详细错误信息:")
            # 返回原始数据，确保系统不会因格式化失败而崩溃
            return df
    
    @classmethod
    def _extract_table_type_from_name(cls, table_name: str) -> str:
        """
        从数据库表名中提取表类型
        
        Args:
            table_name (str): 数据库表名，如 'salary_data_2025_01_active_employees' 或 'active_employees'
            
        Returns:
            str: 表类型，如 'active_employees'
        """
        if not table_name:
            return 'salary_data'
        
        # 如果已经是表类型，直接返回
        if table_name in cls.FIELD_TYPE_MAPPING:
            return table_name
        
        # 定义表类型映射
        type_keywords = {
            'active_employees': ['active_employees', 'active_employee', '全部在职人员'],
            'retired_employees': ['retired_employees', 'retired_employee', '离休人员'],
            'pension_employees': ['pension_employees', 'pension_employee', '退休人员'],
            'a_grade_employees': ['a_grade_employees', 'a_grade_employee', 'A岗职工'],
            'b_grade_employees': ['b_grade_employees', 'b_grade_employee', 'B岗职工'],
            'other_employees': ['other_employees', 'other_employee', '其他人员']
        }
        
        # 将表名转换为小写进行匹配
        table_name_lower = table_name.lower()
        
        # 按优先级匹配（精确匹配优先）
        for table_type, keywords in type_keywords.items():
            for keyword in keywords:
                if keyword.lower() in table_name_lower:
                    logger.info(f"🔧 [表名映射] 通过关键词 '{keyword}' 匹配到表类型: {table_type}")
                    return table_type
        
        # 如果没有匹配，尝试从完整表名中提取最后一部分
        # 例如：salary_data_2025_01_active_employees -> active_employees
        if '_' in table_name:
            parts = table_name.split('_')
            if len(parts) >= 2:
                # 尝试最后一部分
                last_part = parts[-1]
                if last_part in cls.FIELD_TYPE_MAPPING:
                    logger.info(f"🔧 [表名映射] 通过最后一部分 '{last_part}' 匹配到表类型: {last_part}")
                    return last_part
                
                # 尝试最后两部分组合
                if len(parts) >= 3:
                    last_two = '_'.join(parts[-2:])
                    if last_two in cls.FIELD_TYPE_MAPPING:
                        logger.info(f"🔧 [表名映射] 通过最后两部分 '{last_two}' 匹配到表类型: {last_two}")
                        return last_two
        
        # 默认返回通用配置
        logger.warning(f"🔧 [表名映射] 无法从表名 '{table_name}' 中提取表类型，使用默认配置 'salary_data'")
        return 'salary_data'

    @classmethod
    def _format_id_fields(cls, df: pd.DataFrame, id_fields: List[str]) -> None:
        """
        专门处理ID类型字段，确保无小数点且格式正确
        
        Args:
            df (pd.DataFrame): 数据框
            id_fields (List[str]): ID字段列表
        """
        logger.debug(f"开始格式化ID字段: {id_fields}")
        
        for field in id_fields:
            if field in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据状态
                    original_sample = df[field].head(3).tolist()
                    original_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 处理ID字段 {field}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 🔧 [超强化修复] 处理所有可能的数值格式问题
                    def clean_numeric_string(val):
                        """强化的数值字符串清理函数，专门处理ID字段格式化"""
                        # 处理各种空值情况
                        if pd.isna(val) or val == '' or val is None:
                            return '0'
                        
                        # 转换为字符串并清理
                        val_str = str(val).strip()
                        
                        # 处理已经是纯整数字符串的情况
                        if val_str.isdigit():
                            return val_str
                        
                        # 处理包含小数点的数值字符串
                        try:
                            # 尝试转换为浮点数
                            float_val = float(val_str)
                            
                            # 检查是否为整数值（如 123.0）
                            if float_val.is_integer():
                                int_val = int(float_val)
                                return str(int_val)
                            else:
                                # 非整数值，强制截取整数部分
                                int_val = int(float_val)
                                logger.warning(f"🔧 [ID格式化] 非整数值 {val_str} 被截取为 {int_val}")
                                return str(int_val)
                                
                        except (ValueError, TypeError, OverflowError):
                            # 无法转换为数值，尝试提取数字部分
                            import re
                            digit_match = re.search(r'\d+', val_str)
                            if digit_match:
                                extracted_num = digit_match.group()
                                logger.warning(f"🔧 [ID格式化] 从 {val_str} 中提取数字 {extracted_num}")
                                return extracted_num
                            else:
                                # 完全无法处理，保持原样并记录警告
                                logger.warning(f"🔧 [ID格式化] 无法处理的值 {val_str}，保持原样")
                                return val_str
                    
                    # 应用清理函数
                    df[field] = df[field].apply(clean_numeric_string)
                    
                    # 🔧 [强化调试] 记录清理后的状态
                    cleaned_sample = df[field].head(3).tolist()
                    logger.info(f"🔧 [强化调试] 清理后ID字段 {field}: 样例={cleaned_sample}")
                    
                    # 特殊处理：人员类别代码需要保持两位格式
                    if field in ['employee_type_code', '人员类别代码']:
                        # 过滤掉0值（通常表示空值）
                        mask = df[field] != '0'
                        df.loc[mask, field] = df.loc[mask, field].str.zfill(2)
                        df.loc[~mask, field] = ''  # 0值设为空字符串
                    elif field in ['employee_code', '人员代码']:
                        # 🔧 [退休人员工资表专用] 人员代码特殊处理：保持原有数字，0值设为空字符串
                        df[field] = df[field].replace('0', '')
                    else:
                        # 其他ID字段（如工号）：0值设为空字符串
                        df[field] = df[field].replace('0', '')
                    
                    # 🔧 [强化调试] 记录最终状态并验证结果
                    final_sample = df[field].head(3).tolist()
                    logger.info(f"🔧 [强化调试] 最终ID字段 {field}: 样例={final_sample}")
                    
                    # 🔧 [新增验证] 验证格式化结果是否符合预期
                    problematic_values = []
                    for idx, val in enumerate(df[field].head(10)):  # 检查前10行
                        if val and '.' in str(val):  # 仍然包含小数点
                            problematic_values.append(f"行{idx}: {val}")
                    
                    if problematic_values:
                        logger.error(f"🔧 [验证失败] ID字段 {field} 仍有小数点格式: {problematic_values}")
                    else:
                        logger.info(f"🔧 [验证成功] ID字段 {field} 格式化完成，无小数点问题")
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化ID字段 {field} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时至少确保是字符串类型
                    df[field] = df[field].astype(str).replace(['nan', 'None', 'NULL'], '')
            else:
                logger.debug(f"🔧 [P3-修复] ID字段 {field} 不存在于数据框中（正常现象）")
    
    @classmethod
    def _format_string_fields(cls, df: pd.DataFrame, string_fields: List[str]) -> None:
        """
        格式化字符串类型字段
        
        Args:
            df (pd.DataFrame): 数据框
            string_fields (List[str]): 字符串字段列表
        """
        logger.info(f"🔧 [强化调试] 开始格式化字符串字段: {string_fields}")
        
        for field in string_fields:
            if field in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据样例
                    original_sample = df[field].head(3).tolist()
                    original_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 处理字符串字段 {field}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 先处理NaN和空值
                    df[field] = df[field].fillna('')
                    
                    # 🔧 [修复] 特殊处理：工号字段优先处理，避免数值转换
                    if field in ['工号', 'employee_id']:
                        def clean_employee_id(val):
                            if val in ['nan', 'None', 'NULL', '', ' ']:
                                return ''
                            # 🔧 [修复] 去除小数点格式：19990089.0 → 19990089
                            try:
                                # 如果是数值格式，转换为整数字符串
                                if '.' in str(val):
                                    numeric_val = float(val)
                                    if numeric_val == int(numeric_val):  # 检查是否为整数
                                        return str(int(numeric_val))
                                    else:
                                        return str(val).strip()
                                else:
                                    return str(val).strip()
                            except (ValueError, TypeError):
                                return str(val).strip()
                        df[field] = df[field].apply(clean_employee_id)
                        logger.info(f"🔧 [修复] 工号字段 {field} 已特殊处理，去除小数点格式")
                    # 对于数值类型的其他字符串字段，需要去掉小数点
                    elif pd.api.types.is_numeric_dtype(df[field]):
                        # 转换为数值类型处理
                        numeric_data = pd.to_numeric(df[field], errors='coerce')
                        # 处理空值
                        mask_valid = ~pd.isna(numeric_data)
                        
                        # 对于有效数值，转换为整数字符串（去掉小数点）
                        df.loc[mask_valid, field] = numeric_data[mask_valid].astype(int).astype(str)
                        # 对于无效数值，设为空字符串
                        df.loc[~mask_valid, field] = ''
                    else:
                        # 对于已经是字符串的数据，检查是否包含小数点
                        df[field] = df[field].astype(str)
                        
                        # 🔧 [退休人员工资表专用] 人员代码字段处理
                        if field in ['人员代码', 'employee_code']:
                            def clean_employee_code(val):
                                if val in ['nan', 'None', 'NULL', '', ' ', '0', '0.0']:
                                    return ''
                                try:
                                    # 去除小数点格式：19990089.0 → 19990089
                                    numeric_val = float(val)
                                    if numeric_val == 0:
                                        return ''
                                    return str(int(numeric_val))
                                except (ValueError, TypeError):
                                    return str(val).strip()
                            df[field] = df[field].apply(clean_employee_code)
                        else:
                            # 尝试识别并处理数字字符串中的小数点
                            def clean_numeric_string(val):
                                if val in ['nan', 'None', 'NULL', '', ' ']:
                                    return ''
                                try:
                                    # 尝试转换为数值
                                    numeric_val = float(val)
                                    if numeric_val == int(numeric_val):
                                        return str(int(numeric_val))
                                    else:
                                        return str(numeric_val)
                                except (ValueError, TypeError):
                                    return str(val).strip()
                            
                            df[field] = df[field].apply(clean_numeric_string)
                    
                    # 特殊处理：人员类别代码需要保持两位格式
                    if field in ['employee_type_code', '人员类别代码']:
                        def format_employee_type_code(val):
                            if val in ['', '0', 'nan', 'None']:
                                return ''
                            try:
                                # 转换为整数再格式化为两位数字
                                num_val = int(float(val))
                                if num_val == 0:
                                    return ''
                                return f"{num_val:02d}"
                            except (ValueError, TypeError):
                                return str(val).strip()
                        
                        df[field] = df[field].apply(format_employee_type_code)
                    
                    # 清理空值显示
                    df[field] = df[field].replace(['nan', 'None', 'NULL', '0.0'], '')
                    df[field] = df[field].str.strip()
                    
                    # 🔧 [强化调试] 记录最终状态
                    final_sample = df[field].head(3).tolist()
                    final_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 格式化后字符串字段 {field}: 样例={final_sample}, 类型={final_dtype}")
                    
                    # 🔧 [新增验证] 验证格式化结果
                    problematic_values = []
                    for idx, val in enumerate(df[field].head(10)):
                        if val and '.' in str(val) and field in ['工号', '人员类别代码', 'employee_id', 'employee_type_code']:
                            problematic_values.append(f"行{idx}: {val}")
                    
                    if problematic_values:
                        logger.error(f"🔧 [验证失败] 字符串字段 {field} 仍有小数点格式: {problematic_values}")
                    else:
                        logger.info(f"🔧 [验证成功] 字符串字段 {field} 格式化完成，无小数点问题")
                        
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化字符串字段 {field} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时至少确保是字符串类型
                    df[field] = df[field].astype(str).replace(['nan', 'None', 'NULL'], '')
            else:
                logger.debug(f"🔧 [P3-修复] 字符串字段 {field} 不存在于数据框中（正常现象）")
    
    @classmethod
    def _format_integer_fields(cls, df: pd.DataFrame, integer_fields: List[str]) -> None:
        """
        格式化整型字段 - 强化版本，确保强制转换和验证
        
        Args:
            df (pd.DataFrame): 数据框
            integer_fields (List[str]): 整型字段列表
        """
        logger.info(f"🔧 [强化调试] 开始格式化整型字段: {integer_fields}")
        
        for field in integer_fields:
            if field in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据状态
                    original_sample = df[field].head(3).tolist()
                    original_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 处理整型字段 {field}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 先转换为数值类型，错误值设为NaN
                    df[field] = pd.to_numeric(df[field], errors='coerce')
                    # 将NaN值填充为0
                    df[field] = df[field].fillna(0)
                    # 强制转换为整型
                    df[field] = df[field].astype(int)
                    
                    # 🔧 [强化调试] 验证转换结果
                    converted_sample = df[field].head(3).tolist()
                    converted_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 转换后整型字段 {field}: 样例={converted_sample}, 类型={converted_dtype}")
                    
                    # 🔧 [新增验证] 验证格式化结果是否符合预期
                    problematic_values = []
                    for idx, val in enumerate(df[field].head(10)):  # 检查前10行
                        if not isinstance(val, (int, np.integer)):  # 检查是否为整型
                            problematic_values.append(f"行{idx}: {val} (类型: {type(val)})")
                    
                    if problematic_values:
                        logger.error(f"🔧 [验证失败] 整型字段 {field} 类型不正确: {problematic_values}")
                    else:
                        logger.info(f"🔧 [验证成功] 整型字段 {field} 格式化完成，所有值均为整型")
                        
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化整型字段 {field} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时强制设置为0（整型）
                    df[field] = 0
                    logger.warning(f"🔧 [回退] 字段 {field} 已设置为默认值 0")
            else:
                logger.warning(f"🔧 [警告] 整型字段 {field} 不存在于数据框中")
    
    @classmethod
    def _format_float_fields(cls, df: pd.DataFrame, float_fields: List[str]) -> None:
        """
        格式化浮点型字段 - 强化版本，确保保留两位小数
        
        Args:
            df (pd.DataFrame): 数据框
            float_fields (List[str]): 浮点型字段列表
        """
        logger.info(f"🔧 [强化调试] 开始格式化浮点型字段: {float_fields}")
        
        for field in float_fields:
            if field in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据状态
                    original_sample = df[field].head(3).tolist()
                    original_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 处理浮点型字段 {field}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 先转换为数值类型，错误值设为NaN
                    df[field] = pd.to_numeric(df[field], errors='coerce')
                    # 将NaN值填充为0.0
                    df[field] = df[field].fillna(0.0)
                    # 保留两位小数
                    df[field] = df[field].round(2)
                    
                    # 🔧 [强化调试] 验证转换结果
                    converted_sample = df[field].head(3).tolist()
                    converted_dtype = str(df[field].dtype)
                    logger.info(f"🔧 [强化调试] 转换后浮点型字段 {field}: 样例={converted_sample}, 类型={converted_dtype}")
                    
                    # 🔧 [新增验证] 验证小数位数是否正确
                    sample_decimals = []
                    for val in df[field].head(5):  # 检查前5行
                        if pd.notna(val):
                            decimal_str = f"{val:.2f}"
                            decimal_count = len(decimal_str.split('.')[-1]) if '.' in decimal_str else 0
                            sample_decimals.append(decimal_count)
                    
                    if sample_decimals and all(d == 2 for d in sample_decimals):
                        logger.info(f"🔧 [验证成功] 浮点型字段 {field} 格式化完成，保留两位小数")
                    else:
                        logger.warning(f"🔧 [验证警告] 浮点型字段 {field} 小数位数可能不一致: {sample_decimals}")
                        
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化浮点型字段 {field} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时强制设置为0.0（浮点型）
                    df[field] = 0.0
                    logger.warning(f"🔧 [回退] 字段 {field} 已设置为默认值 0.0")
            else:
                logger.debug(f"🔧 [P3-修复] 浮点型字段 {field} 不存在于数据框中（正常现象）")
    
    @classmethod
    def _format_special_fields(cls, df: pd.DataFrame, table_type: str = 'active_employees') -> pd.DataFrame:
        """
        处理特殊字段（月份、年份）- 严格按表类型处理
        
        Args:
            df (pd.DataFrame): 数据框
            table_type (str): 表类型，用于获取对应的字段配置
            
        Returns:
            pd.DataFrame: 处理后的数据框
        """
        logger.info(f"🔧 [特殊字段] 开始处理表类型 {table_type} 的月份、年份字段")
        
        # 获取表类型的特殊字段配置
        field_config = cls.FIELD_TYPE_MAPPING.get(table_type, {})
        special_fields = field_config.get('special_fields', {})
        
        if not special_fields:
            logger.warning(f"🔧 [特殊字段] 表类型 {table_type} 没有配置特殊字段，跳过处理")
            return df
        
        # 获取月份字段名配置
        month_field_names = special_fields.get('month_fields', [])
        
        # 处理月份字段：提取后两位，转换为字符串
        for field_name in month_field_names:
            if field_name in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据状态
                    original_sample = df[field_name].head(3).tolist()
                    original_dtype = str(df[field_name].dtype)
                    logger.info(f"🔧 [特殊字段] 处理月份字段 {field_name}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 强化的月份提取逻辑
                    def extract_month_part(val):
                        """强化的月份提取函数，支持多种格式"""
                        if pd.isna(val) or val == '' or val is None:
                            return ''
                        
                        # 转换为字符串并清理
                        val_str = str(val).strip()
                        
                        # 处理各种月份格式
                        if val_str.isdigit():
                            # 纯数字格式
                            if len(val_str) == 1:
                                # 单位月份，前补0
                                return f"0{val_str}"
                            elif len(val_str) == 2:
                                # 双位月份，直接返回
                                return val_str
                            elif len(val_str) >= 6:
                                # 类似 202501 格式，提取后两位
                                return val_str[-2:]
                            else:
                                # 其他数字格式，取后两位
                                return val_str[-2:].zfill(2)
                        else:
                            # 非纯数字，尝试提取数字部分
                            import re
                            # 尝试提取最后的1-2位数字
                            match = re.search(r'(\d{1,2})(?!.*\d)', val_str)
                            if match:
                                month_num = match.group(1)
                                return month_num.zfill(2)  # 确保两位数
                            else:
                                logger.warning(f"🔧 [特殊字段] 无法从月份值 {val_str} 中提取数字")
                                return ''
                    
                    # 应用月份提取函数
                    df[field_name] = df[field_name].apply(extract_month_part)
                    
                    # 🔧 [强化调试] 记录处理后的状态
                    processed_sample = df[field_name].head(3).tolist()
                    logger.info(f"🔧 [特殊字段] 处理后月份字段 {field_name}: 样例={processed_sample}")
                    
                    # 🔧 [新增验证] 验证月份格式是否正确
                    invalid_months = []
                    for idx, val in enumerate(df[field_name].head(10)):  # 检查前10行
                        if val and (not val.isdigit() or len(val) != 2 or int(val) < 1 or int(val) > 12):
                            invalid_months.append(f"行{idx}: {val}")
                    
                    if invalid_months:
                        logger.warning(f"🔧 [特殊字段] 月份字段 {field_name} 包含无效格式: {invalid_months[:3]}")
                    else:
                        logger.info(f"🔧 [验证成功] 月份字段 {field_name} 格式化完成，所有值为有效月份格式")
                        
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化月份字段 {field_name} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时的回退处理
                    df[field_name] = df[field_name].astype(str).replace(['nan', 'None', 'NULL'], '')
                    logger.warning(f"🔧 [回退] 月份字段 {field_name} 已设置为字符串格式")
                break  # 只处理找到的第一个月份字段
        
        # 获取年份字段名配置
        year_field_names = special_fields.get('year_fields', [])
        
        # 处理年份字段：转换为字符串
        for field_name in year_field_names:
            if field_name in df.columns:
                try:
                    # 🔧 [强化调试] 记录原始数据状态
                    original_sample = df[field_name].head(3).tolist()
                    original_dtype = str(df[field_name].dtype)
                    logger.info(f"🔧 [特殊字段] 处理年份字段 {field_name}: 原始样例={original_sample}, 原始类型={original_dtype}")
                    
                    # 强化的年份处理逻辑
                    def format_year_value(val):
                        """强化的年份格式化函数"""
                        if pd.isna(val) or val == '' or val is None:
                            return ''
                        
                        # 转换为字符串并清理
                        val_str = str(val).strip()
                        
                        # 处理各种年份格式
                        if val_str.replace('.', '').isdigit():
                            # 数字格式（可能包含小数点）
                            try:
                                year_int = int(float(val_str))
                                # 验证年份范围的合理性（1900-2100）
                                if 1900 <= year_int <= 2100:
                                    return str(year_int)
                                else:
                                    logger.warning(f"🔧 [特殊字段] 年份值超出合理范围: {year_int}")
                                    return str(year_int)  # 仍然返回，但记录警告
                            except (ValueError, OverflowError):
                                logger.warning(f"🔧 [特殊字段] 无法转换年份值: {val_str}")
                                return ''
                        else:
                            # 非数字格式，尝试提取年份
                            import re
                            year_match = re.search(r'(20\d{2}|19\d{2})', val_str)
                            if year_match:
                                return year_match.group(1)
                            else:
                                logger.warning(f"🔧 [特殊字段] 无法从年份值 {val_str} 中提取有效年份")
                                return ''
                    
                    # 应用年份格式化函数
                    df[field_name] = df[field_name].apply(format_year_value)
                    
                    # 🔧 [强化调试] 记录处理后的状态
                    processed_sample = df[field_name].head(3).tolist()
                    logger.info(f"🔧 [特殊字段] 处理后年份字段 {field_name}: 样例={processed_sample}")
                    
                    # 🔧 [新增验证] 验证年份格式是否正确
                    invalid_years = []
                    for idx, val in enumerate(df[field_name].head(10)):  # 检查前10行
                        if val and (not val.isdigit() or len(val) != 4):
                            invalid_years.append(f"行{idx}: {val}")
                    
                    if invalid_years:
                        logger.warning(f"🔧 [特殊字段] 年份字段 {field_name} 包含无效格式: {invalid_years[:3]}")
                    else:
                        logger.info(f"🔧 [验证成功] 年份字段 {field_name} 格式化完成，所有值为有效年份格式")
                        
                except Exception as e:
                    logger.error(f"🔧 [错误] 格式化年份字段 {field_name} 失败: {e}")
                    logger.exception("详细错误信息:")
                    # 失败时的回退处理
                    df[field_name] = df[field_name].astype(str).replace(['nan', 'None', 'NULL'], '')
                    logger.warning(f"🔧 [回退] 年份字段 {field_name} 已设置为字符串格式")
                break  # 只处理找到的第一个年份字段
        
        logger.info("🔧 [特殊字段] 月份、年份字段处理完成")
        return df
    
    @classmethod
    def format_active_employee_data(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        向后兼容方法 - 格式化全部在职人员工资表数据
        
        Args:
            df (pd.DataFrame): 原始数据框
            
        Returns:
            pd.DataFrame: 格式化后的数据框
        """
        logger.info("调用向后兼容方法，转换为新的表类型处理方式")
        return cls.format_table_data(df, table_type='active_employees')
    
    @classmethod
    def _apply_empty_value_formatting(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用空值格式化，将指定的空值统一显示为空字符串
        
        Args:
            df (pd.DataFrame): 数据框
            
        Returns:
            pd.DataFrame: 处理后的数据框
        """
        logger.debug("开始应用空值格式化")
        
        try:
            # 对于数值类型的列，不处理0和0.0为空值（因为这些可能是有意义的值）
            # 只处理字符串类型的列
            for col in df.columns:
                if df[col].dtype == 'object':  # 字符串类型
                    # 将指定的空值替换为空字符串
                    mask = df[col].isin(['None', 'nan', 'NaN', 'null', 'NULL', ''])
                    df.loc[mask, col] = ''
            
            logger.debug("空值格式化完成")
            
        except Exception as e:
            logger.warning(f"应用空值格式化失败: {e}")
        
        return df
    
    @classmethod
    def _is_active_employees_table(cls, table_name: str) -> bool:
        """
        智能识别是否为全部在职人员工资表
        
        Args:
            table_name (str): 表名
            
        Returns:
            bool: 是否为全部在职人员工资表
        """
        if not table_name:
            return False
        
        # 支持多种表名格式
        table_name_lower = table_name.lower()
        
        # 直接匹配
        if table_name_lower == 'active_employees':
            return True
        
        # 包含关键字匹配
        active_employee_keywords = [
            'active_employees',  # 标准格式
            'active_employee',   # 单数形式 
            '全部在职人员',       # 中文名称
            'all_employees',     # 其他可能的英文表示
            'current_employees'  # 其他可能的英文表示
        ]
        
        for keyword in active_employee_keywords:
            if keyword in table_name_lower:
                return True
        
        # 特殊处理：salary_data开头且包含active的表
        if table_name_lower.startswith('salary_data') and 'active' in table_name_lower:
            return True
        
        return False
    
    @classmethod
    def _is_salary_data_table(cls, table_name: str) -> bool:
        """
        智能识别是否为工资数据表（包括所有类型的工资表）
        
        Args:
            table_name (str): 表名
            
        Returns:
            bool: 是否为工资数据表
        """
        if not table_name:
            return False
        
        table_name_lower = table_name.lower()
        
        # 工资数据表的关键词
        salary_keywords = [
            'salary_data',      # 标准格式
            'active_employees', # 全部在职人员
            'a_grade_employees',# A岗职工
            'retired_employees',# 离休人员  
            'pension_employees',# 退休人员
            'employees',        # 员工相关
            '工资',             # 中文关键词
            '在职',             # 中文关键词
            '离休',             # 中文关键词
            '退休'              # 中文关键词
        ]
        
        # 检查是否包含工资数据表关键词
        for keyword in salary_keywords:
            if keyword in table_name_lower:
                return True
        
        # 特殊模式匹配：salary_data_年份_月份_类型
        if table_name_lower.startswith('salary_data_') and len(table_name.split('_')) >= 4:
            return True
        
        return False
    
    @classmethod
    def format_empty_values(cls, value: Any) -> str:
        """
        统一处理单个值的空值显示
        
        Args:
            value (Any): 要处理的值
            
        Returns:
            str: 格式化后的字符串
        """
        # 检查是否为空值
        if value in cls.EMPTY_VALUES:
            return ''
        
        # 检查是否为pandas的NaN
        if pd.isna(value):
            return ''
        
        # 检查字符串形式的空值
        if isinstance(value, str):
            cleaned_value = value.strip()
            if cleaned_value.lower() in ['none', 'nan', 'null', '']:
                return ''
        
        # 返回字符串形式的值
        return str(value)
    
    @classmethod
    def format_display_value(cls, value: Any, column_name: str = '') -> str:
        """
        格式化显示值，支持浮点数字段的特殊格式化
        
        Args:
            value (Any): 要处理的值
            column_name (str): 列名，用于判断是否为浮点数字段
            
        Returns:
            str: 格式化后的字符串
        """
        # 先进行空值处理
        if value in cls.EMPTY_VALUES:
            return ''
        
        if pd.isna(value):
            return ''
        
        if isinstance(value, str):
            cleaned_value = value.strip()
            if cleaned_value.lower() in ['none', 'nan', 'null', '']:
                return ''
        
        # 定义浮点数字段（与FIELD_TYPE_MAPPING中的float_fields保持一致）
        float_fields = [
            '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', 
            '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴',
            '2025年奖励性绩效预发', '2025公积金',
            '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险',
            # 对应的英文字段名
            'position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance',
            'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 
            'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025',
            'housing_allowance', 'car_allowance', 'supplement', 'advance', 
            'total_salary', 'pension_insurance'
        ]
        
        # 如果是浮点数字段，格式化为两位小数
        if column_name and column_name in float_fields:
            try:
                # 按用户新要求：所有浮点数字段的空值和零值都统一显示为"0.00"
                
                # 转换为数值并格式化
                numeric_value = pd.to_numeric(value, errors='coerce')
                
                # 如果无法转换为数值（包括None、nan、空字符串等），显示为"0.00"
                if pd.isna(numeric_value):
                    return "0.00"
                
                # 格式化为两位小数
                return f"{numeric_value:.2f}"
            except Exception:
                # 如果格式化失败，显示为"0.00"
                return "0.00"
        
        # 返回字符串形式的值
        return str(value)
    
    @classmethod
    def format_display_value_by_table(cls, value: Any, column_name: str, table_type: str) -> str:
        """
        基于表类型的显示值格式化
        
        Args:
            value (Any): 要处理的值
            column_name (str): 列名，用于判断字段类型
            table_type (str): 表类型，用于获取字段分类配置
            
        Returns:
            str: 格式化后的字符串
        """
        # 获取表类型配置
        config = cls.FIELD_TYPE_MAPPING.get(table_type, {})
        
        # 字符串字段：空值显示为空白
        if column_name in config.get('string_fields', []):
            return cls._format_string_display_by_field(value, column_name)
        
        # 浮点数字段：空值显示为"0.00"
        elif column_name in config.get('float_fields', []):
            return cls._format_float_display(value)
        
        # 特殊字段处理
        elif cls._is_special_field(column_name, config):
            return cls._format_special_display(value, column_name, config)
        
        # 其他字段使用原有逻辑
        return cls.format_display_value(value, column_name)
    
    @classmethod
    def _format_string_display_by_field(cls, value: Any, column_name: str) -> str:
        """
        基于字段名的字符串显示格式化：空值显示为空白，特殊字段特殊处理
        
        Args:
            value (Any): 要处理的值
            column_name (str): 列名，用于特殊字段处理
            
        Returns:
            str: 格式化后的字符串
        """
        # 检查是否为空值
        if value in [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL']:
            return ''
        
        # 检查是否为pandas的NaN
        if pd.isna(value):
            return ''
        
        # 转换为字符串处理
        value_str = str(value).strip()
        
        # 检查字符串形式的空值
        if value_str.lower() in ['none', 'nan', 'null', '0.0', '']:
            return ''
        
        # 特殊处理：人员类别代码需要保持两位格式
        if column_name in ['employee_type_code', '人员类别代码']:
            try:
                # 转换为数值并格式化为两位数字
                numeric_value = float(value_str)
                if numeric_value == 0:
                    return ''  # 0值显示为空
                return f"{int(numeric_value):02d}"
            except (ValueError, TypeError):
                return value_str
        
        # 特殊处理：工号字段需要去掉小数点，但保持前导零
        if column_name in ['工号', 'employee_id']:
            # 如果是数字字符串且包含小数点，去掉小数点
            try:
                if '.' in value_str:
                    numeric_value = float(value_str)
                    if numeric_value == int(numeric_value):
                        return str(int(numeric_value))
                    else:
                        return value_str
                else:
                    return value_str  # 保持原始格式（如前导零）
            except (ValueError, TypeError):
                return value_str
        
        # 对于其他数字字符串，去掉小数点（如 "34660024.0" -> "34660024"）
        try:
            numeric_value = float(value_str)
            # 如果是整数值，去掉小数点
            if numeric_value == int(numeric_value):
                return str(int(numeric_value))
            else:
                return value_str
        except (ValueError, TypeError):
            # 不是数字，直接返回清理后的字符串
            return value_str
    
    @classmethod
    def _format_string_display(cls, value: Any) -> str:
        """
        字符串字段显示格式化：空值显示为空白，数字去掉小数点
        
        Args:
            value (Any): 要处理的值
            
        Returns:
            str: 格式化后的字符串
        """
        # 检查是否为空值（包括None、nan、0、0.0、空字符串、空格等）
        if value in [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL']:
            return ''
        
        # 检查是否为pandas的NaN
        if pd.isna(value):
            return ''
        
        # 转换为字符串处理
        value_str = str(value).strip()
        
        # 检查字符串形式的空值
        if value_str.lower() in ['none', 'nan', 'null', '0.0', '']:
            return ''
        
        # 对于数字字符串，去掉小数点（如 "34660024.0" -> "34660024"）
        try:
            # 尝试转换为数值
            numeric_value = float(value_str)
            # 如果是整数值，去掉小数点
            if numeric_value == int(numeric_value):
                return str(int(numeric_value))
            else:
                return value_str
        except (ValueError, TypeError):
            # 不是数字，直接返回清理后的字符串
            return value_str
    
    @classmethod
    def _format_float_display(cls, value: Any) -> str:
        """
        浮点数字段显示格式化：空值显示为"0.00"
        
        Args:
            value (Any): 要处理的值
            
        Returns:
            str: 格式化后的字符串，保留两位小数
        """
        try:
            # 转换为数值并格式化
            numeric_value = pd.to_numeric(value, errors='coerce')
            
            # 如果无法转换为数值（包括None、nan、空字符串等），显示为"0.00"
            if pd.isna(numeric_value):
                return "0.00"
            
            # 格式化为两位小数
            return f"{numeric_value:.2f}"
        except Exception:
            # 如果格式化失败，显示为"0.00"
            return "0.00"
    
    @classmethod
    def _is_special_field(cls, column_name: str, config: dict) -> bool:
        """
        检查是否为特殊字段（月份、年份）
        
        Args:
            column_name (str): 列名
            config (dict): 表配置
            
        Returns:
            bool: 是否为特殊字段
        """
        special_fields = config.get('special_fields', {})
        month_fields = special_fields.get('month_fields', [])
        year_fields = special_fields.get('year_fields', [])
        
        return column_name in month_fields or column_name in year_fields
    
    @classmethod
    def _format_special_display(cls, value: Any, column_name: str, config: dict) -> str:
        """
        特殊字段格式化（月份、年份）
        
        Args:
            value (Any): 要处理的值
            column_name (str): 列名
            config (dict): 表配置
            
        Returns:
            str: 格式化后的字符串
        """
        special_fields = config.get('special_fields', {})
        
        # 月份：提取后两位转字符串
        if column_name in special_fields.get('month_fields', []):
            return cls._extract_month_part(value)
        
        # 年份：转字符串
        elif column_name in special_fields.get('year_fields', []):
            return cls._format_year_value(value)
        
        return str(value)
    
    @classmethod
    def _extract_month_part(cls, value: Any) -> str:
        """
        提取月份部分（后两位）并转换为字符串
        
        Args:
            value (Any): 月份值
            
        Returns:
            str: 格式化后的月份字符串（如："01", "12"）
        """
        try:
            if pd.isna(value) or value == '' or value is None:
                return ''
            
            # 转换为字符串并清理
            val_str = str(value).strip()
            
            # 处理各种月份格式
            if val_str.isdigit():
                # 纯数字格式
                if len(val_str) == 1:
                    # 单位月份，前补0
                    return f"0{val_str}"
                elif len(val_str) == 2:
                    # 双位月份，直接返回
                    return val_str
                elif len(val_str) >= 6:
                    # 类似 202501 格式，提取后两位
                    return val_str[-2:]
                else:
                    # 其他数字格式，取后两位
                    return val_str[-2:].zfill(2)
            else:
                # 非纯数字，尝试提取数字部分
                import re
                # 尝试提取最后的1-2位数字
                match = re.search(r'(\d{1,2})(?!.*\d)', val_str)
                if match:
                    month_num = match.group(1)
                    return month_num.zfill(2)  # 确保两位数
                else:
                    return ''
        except Exception:
            return ''
    
    @classmethod
    def _format_year_value(cls, value: Any) -> str:
        """
        格式化年份值为字符串
        
        Args:
            value (Any): 年份值
            
        Returns:
            str: 格式化后的年份字符串（如："2025"）
        """
        try:
            if pd.isna(value) or value == '' or value is None:
                return ''
            
            # 转换为字符串并清理
            val_str = str(value).strip()
            
            # 处理各种年份格式
            if val_str.replace('.', '').isdigit():
                # 数字格式（可能包含小数点）
                try:
                    year_int = int(float(val_str))
                    # 验证年份范围的合理性（1900-2100）
                    if 1900 <= year_int <= 2100:
                        return str(year_int)
                    else:
                        return str(year_int)  # 仍然返回，但记录警告
                except (ValueError, OverflowError):
                    return ''
            else:
                # 非数字格式，尝试提取年份
                import re
                year_match = re.search(r'(20\d{2}|19\d{2})', val_str)
                if year_match:
                    return year_match.group(1)
                else:
                    return ''
        except Exception:
            return ''
    
    @classmethod
    def get_hidden_fields(cls, table_name: str) -> List[str]:
        """
        获取指定表应该隐藏的字段列表
        
        Args:
            table_name (str): 表名
            
        Returns:
            List[str]: 隐藏字段列表
        """
        hidden_fields_config = {
            'active_employees': ['created_at', 'updated_at', 'id', 'sequence_number']
        }
        
        return hidden_fields_config.get(table_name, [])
    
    @classmethod
    def apply_field_visibility(cls, table_name: str, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用字段可见性配置，隐藏指定字段
        
        Args:
            table_name (str): 表名
            df (pd.DataFrame): 数据框
            
        Returns:
            pd.DataFrame: 应用字段可见性后的数据框
        """
        if df is None or df.empty:
            return df
        
        try:
            hidden_fields = cls.get_hidden_fields(table_name)
            
            # 过滤掉隐藏字段
            visible_columns = [col for col in df.columns if col not in hidden_fields]
            
            # 只保留可见字段
            if visible_columns:
                filtered_df = df[visible_columns].copy()
                logger.info(f"应用字段可见性配置: 隐藏 {len(hidden_fields)} 个字段，显示 {len(visible_columns)} 个字段")
                return filtered_df
            else:
                logger.warning("应用字段可见性配置后没有可显示的字段")
                return df
                
        except Exception as e:
            logger.error(f"应用字段可见性配置失败: {e}")
            return df
    


# 模块导出
__all__ = [
    "SalaryDataFormatter"
]


if __name__ == "__main__":
    # 测试代码
    print("测试工资数据格式化模块...")
    
    # 创建测试数据
    test_data = {
        'employee_id': ['001', '002', None, '004'],
        'employee_name': ['张三', '李四', '', '王五'],
        'position_salary_2025': [5000, 6000, 'invalid', 7000],
        'housing_allowance': [1500.567, 1800.999, None, 2000.123],
        'month': ['202501', '01', '12', '202512'],
        'year': [2025, 2025, None, 2025],
        'created_at': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04']
    }
    
    test_df = pd.DataFrame(test_data)
    print("原始测试数据:")
    print(test_df)
    print("\n" + "="*50 + "\n")
    
    # 测试格式化
    formatter = SalaryDataFormatter()
    formatted_df = formatter.format_table_data('active_employees', test_df)
    
    print("格式化后的数据:")
    print(formatted_df)
    print(f"\n数据类型:")
    print(formatted_df.dtypes)
    
    print("\n工资数据格式化模块测试完成!")