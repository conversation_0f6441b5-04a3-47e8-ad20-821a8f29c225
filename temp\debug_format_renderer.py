#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试FormatRenderer的display_order应用问题
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_mock_dataframe():
    """创建模拟的A岗职工DataFrame"""
    data = {
        'employee_id': ['001', '002'],
        'employee_name': ['张三', '李四'],
        'department': ['部门A', '部门B'],
        'employee_type': ['A岗', 'A岗'],
        'employee_type_code': ['A001', 'A002'],
        'position_salary_2025': [5000.0, 5500.0],
        'insurance_deduction': [0.0, None],  # 测试空值处理
        'total_salary': [8000.0, 8500.0]
    }
    return pd.DataFrame(data)

def test_format_renderer():
    """测试FormatRenderer的display_order应用"""
    print("=== 测试FormatRenderer的display_order应用 ===")
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化组件
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        format_renderer = FormatRenderer(field_registry)
        
        # 创建测试数据
        test_df = create_mock_dataframe()
        table_type = 'a_grade_employees'
        
        print(f"原始DataFrame列: {list(test_df.columns)}")
        print(f"原始DataFrame行数: {len(test_df)}")
        
        # 获取显示字段
        display_fields = field_registry.get_display_fields(table_type)
        print(f"\n配置的显示字段: {display_fields}")
        
        # 检查匹配情况
        existing_display_fields = [field for field in display_fields if field in test_df.columns]
        print(f"匹配的字段: {existing_display_fields}")
        print(f"不匹配的字段: {[field for field in display_fields if field not in test_df.columns]}")
        
        # 应用格式渲染
        formatted_df = format_renderer.render_dataframe(test_df, table_type)
        
        print(f"\n格式化后DataFrame列: {list(formatted_df.columns)}")
        print(f"格式化后DataFrame行数: {len(formatted_df)}")
        
        # 检查保险扣款字段的格式化
        if 'insurance_deduction' in formatted_df.columns:
            insurance_values = formatted_df['insurance_deduction'].tolist()
            print(f"保险扣款字段值: {insurance_values}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_column_name_matching():
    """测试列名匹配问题"""
    print("\n=== 测试列名匹配问题 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 检查实际数据库中的列名
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        db_manager = DynamicTableManager()
        
        table_name = 'salary_data_2025_08_a_grade_employees'
        
        # 获取表结构
        try:
            sample_data = db_manager.get_dataframe_paginated_with_sort(table_name, 1, 1)
            if not sample_data.empty:
                actual_columns = list(sample_data.columns)
                print(f"数据库实际列名: {actual_columns}")
                
                # 获取配置的显示字段
                display_fields = field_registry.get_display_fields('a_grade_employees')
                print(f"配置的显示字段: {display_fields}")
                
                # 检查匹配情况
                matched = [field for field in display_fields if field in actual_columns]
                unmatched = [field for field in display_fields if field not in actual_columns]
                
                print(f"\n匹配的字段({len(matched)}): {matched}")
                print(f"不匹配的字段({len(unmatched)}): {unmatched}")
                
                return len(matched) > 0
            else:
                print("数据库中没有数据")
                return False
                
        except Exception as db_error:
            print(f"数据库查询失败: {db_error}")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始调试FormatRenderer的display_order应用问题")
    print("=" * 60)
    
    # 测试1: FormatRenderer
    test1_passed = test_format_renderer()
    
    # 测试2: 列名匹配
    test2_passed = test_column_name_matching()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  FormatRenderer测试: {'通过' if test1_passed else '失败'}")
    print(f"  列名匹配测试: {'通过' if test2_passed else '失败'}")
    
    if test1_passed and test2_passed:
        print("\n所有测试通过！")
    else:
        print("\n部分测试失败，发现了问题")