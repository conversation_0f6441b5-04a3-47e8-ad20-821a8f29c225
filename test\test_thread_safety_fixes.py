#!/usr/bin/env python3
"""
测试线程安全修复效果的脚本

测试内容：
1. 字段偏好处理的线程安全性
2. 虚拟化表格的绘制锁机制
3. 递归调用保护机制
4. ConfigSyncManager实例管理
"""

import sys
import os
import unittest
import threading
import time
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.modules.data_import.config_sync_manager import ConfigSyncManager


class TestThreadSafetyFixes(unittest.TestCase):
    """测试线程安全修复效果"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        # 确保QApplication存在
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置每个测试的环境"""
        # 创建模拟对象
        self.mock_config_manager = Mock()
        self.mock_db_manager = Mock()
        self.mock_table_manager = Mock()
        
        # 创建主窗口实例
        self.main_window = PrototypeMainWindow(
            config_manager=self.mock_config_manager,
            db_manager=self.mock_db_manager,
            dynamic_table_manager=self.mock_table_manager
        )
    
    def test_field_preference_recursive_protection(self):
        """测试字段偏好处理的递归调用保护"""
        # 创建测试数据
        test_df = pd.DataFrame({
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四'],
            'position_salary_2025': [5000.0, 6000.0]
        })
        
        # 模拟ConfigSyncManager
        mock_config_sync = Mock(spec=ConfigSyncManager)
        mock_config_sync.get_table_field_preference.return_value = ['employee_id', 'employee_name']
        self.main_window.config_sync_manager = mock_config_sync
        
        # 设置递归调用标志
        self.main_window._applying_field_preference = True
        
        # 测试递归保护
        result_df = self.main_window._apply_table_field_preference(
            test_df, 
            "test_table"
        )
        
        # 验证递归保护生效
        self.assertEqual(len(result_df.columns), 3)  # 应该返回原始数据
        self.assertFalse(mock_config_sync.get_table_field_preference.called)
        
        print("✅ 字段偏好递归调用保护测试通过")
    
    def test_field_preference_normal_processing(self):
        """测试字段偏好的正常处理"""
        # 创建测试数据
        test_df = pd.DataFrame({
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四'],
            'position_salary_2025': [5000.0, 6000.0]
        })
        
        # 模拟ConfigSyncManager
        mock_config_sync = Mock(spec=ConfigSyncManager)
        mock_config_sync.get_table_field_preference.return_value = ['employee_id', 'employee_name']
        self.main_window.config_sync_manager = mock_config_sync
        
        # 确保没有递归标志
        if hasattr(self.main_window, '_applying_field_preference'):
            self.main_window._applying_field_preference = False
        
        # 测试正常处理
        result_df = self.main_window._apply_table_field_preference(
            test_df, 
            "test_table"
        )
        
        # 验证字段过滤生效
        self.assertEqual(len(result_df.columns), 2)  # 应该只有2个字段
        self.assertIn('employee_id', result_df.columns)
        self.assertIn('employee_name', result_df.columns)
        self.assertNotIn('position_salary_2025', result_df.columns)
        
        print("✅ 字段偏好正常处理测试通过")
    
    def test_config_sync_manager_reuse(self):
        """测试ConfigSyncManager实例重用"""
        # 创建测试数据
        test_df = pd.DataFrame({
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四']
        })
        
        # 模拟ConfigSyncManager
        mock_config_sync = Mock(spec=ConfigSyncManager)
        mock_config_sync.get_table_field_preference.return_value = None
        self.main_window.config_sync_manager = mock_config_sync
        
        # 多次调用字段偏好处理
        for i in range(3):
            result_df = self.main_window._apply_table_field_preference(
                test_df, 
                f"test_table_{i}"
            )
            
            # 验证使用的是同一个实例
            self.assertIs(self.main_window.config_sync_manager, mock_config_sync)
        
        # 验证调用次数
        self.assertEqual(mock_config_sync.get_table_field_preference.call_count, 3)
        
        print("✅ ConfigSyncManager实例重用测试通过")
    
    def test_painting_lock_mechanism(self):
        """测试绘制锁机制"""
        # 模拟虚拟化表格
        if hasattr(self.main_window, 'main_workspace') and hasattr(self.main_window.main_workspace, 'expandable_table'):
            table = self.main_window.main_workspace.expandable_table
            
            # 设置绘制锁
            table._is_painting = True
            
            # 尝试设置数据
            test_data = [{'col1': 'value1', 'col2': 'value2'}]
            test_headers = ['col1', 'col2']
            
            # 这应该被跳过而不会导致递归
            table.set_data(test_data, test_headers)
            
            # 验证绘制锁仍然存在
            self.assertTrue(table._is_painting)
            
            print("✅ 绘制锁机制测试通过")
    
    def test_concurrent_field_preference_access(self):
        """测试并发字段偏好访问"""
        # 创建测试数据
        test_df = pd.DataFrame({
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四']
        })
        
        # 模拟ConfigSyncManager
        mock_config_sync = Mock(spec=ConfigSyncManager)
        mock_config_sync.get_table_field_preference.return_value = ['employee_id']
        self.main_window.config_sync_manager = mock_config_sync
        
        results = []
        errors = []
        
        def worker():
            try:
                result = self.main_window._apply_table_field_preference(
                    test_df.copy(), 
                    "test_table"
                )
                results.append(len(result.columns))
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发访问
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=5)
        
        # 验证没有错误发生
        self.assertEqual(len(errors), 0, f"并发访问出现错误: {errors}")
        
        # 验证结果一致性
        self.assertTrue(all(r == 1 for r in results), f"结果不一致: {results}")
        
        print("✅ 并发字段偏好访问测试通过")


def run_thread_safety_tests():
    """运行线程安全测试"""
    print("🔧 开始测试线程安全修复效果...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestThreadSafetyFixes)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 所有线程安全测试通过！修复效果良好。")
    else:
        print("❌ 部分线程安全测试失败，需要进一步修复。")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_thread_safety_tests()
    sys.exit(0 if success else 1)
