#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终全面修复脚本 - 解决事件循环死锁和行号显示问题
"""

import os
import re
import tempfile
import shutil
from pathlib import Path


def apply_prototype_main_window_fixes():
    """修复 prototype_main_window.py 的事件循环问题"""
    
    file_path = "src/gui/prototype/prototype_main_window.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 添加防止事件循环的装饰器到 _handle_sort_applied 方法
    if '@prevent_loop(' not in content or 'prevent_loop("sort_applied")' not in content:
        
        # 找到 _handle_sort_applied 方法
        pattern = r'(def _handle_sort_applied\(.*?\):)'
        
        def replacement(match):
            return f'@prevent_loop("sort_applied")\n    {match.group(1)}'
        
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        print("✅ 已添加排序事件循环保护")
    
    # 2. 添加防止事件循环的装饰器到 _on_new_data_updated 方法
    if 'prevent_loop("data_updated")' not in content:
        
        pattern = r'(def _on_new_data_updated\(.*?\):)'
        
        def replacement(match):
            return f'@prevent_loop("data_updated")\n    {match.group(1)}'
        
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        print("✅ 已添加数据更新事件循环保护")
    
    # 3. 添加防止事件循环的装饰器到 _on_page_changed_new_architecture 方法
    if 'prevent_loop("page_changed")' not in content:
        
        pattern = r'(def _on_page_changed_new_architecture\(.*?\):)'
        
        def replacement(match):
            return f'@prevent_loop("page_changed")\n    {match.group(1)}'
        
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        print("✅ 已添加分页事件循环保护")
    
    # 4. 修复设置分页状态的调用
    enhanced_pagination_fix = '''
    def set_data(self, df, table_name=None, request_type=None, is_sort_operation=False):
        """🔧 [CRITICAL修复] 设置表格数据的统一入口"""
        try:
            if df is None or df.empty:
                self.logger.warning("尝试设置空数据，将显示提示信息。")
                self._show_empty_table_with_prompt()
                return
            
            # 🔧 [关键修复] 计算正确的分页状态
            if hasattr(self, 'current_pagination_info') and self.current_pagination_info:
                current_page = self.current_pagination_info.get('current_page', 1)
                page_size = self.current_pagination_info.get('page_size', 50)
                total_records = self.current_pagination_info.get('total_records', len(df))
            else:
                current_page = 1
                page_size = 50
                total_records = len(df)
            
            # 计算正确的行号范围
            start_record = (current_page - 1) * page_size + 1
            end_record = min(start_record + len(df) - 1, total_records)
            
            self.logger.info(f"🔧 [数据设置] 页码{current_page}: 记录{start_record}-{end_record} (共{total_records}条)")
            
            # 调用表格组件设置数据
            self.main_workspace.set_data(df, table_name=table_name)
            
            # 🔧 [关键修复] 立即设置正确的分页状态和行号
            if hasattr(self.main_workspace, 'set_pagination_state'):
                pagination_state = {
                    'current_page': current_page,
                    'page_size': page_size,
                    'total_records': total_records,
                    'start_record': start_record,
                    'end_record': end_record
                }
                self.main_workspace.set_pagination_state(pagination_state)
                self.logger.info(f"🔧 [分页状态] 已设置分页状态: 第{current_page}页, 记录{start_record}-{end_record}")
            
            # 🔧 [关键修复] 设置分页组件状态
            if hasattr(self, 'pagination_widget'):
                # 设置总记录数
                self.pagination_widget.set_total_records(total_records)
                
                # 🔧 [关键修复] 设置当前页但不触发信号
                if hasattr(self.pagination_widget, 'set_current_page_silent'):
                    self.pagination_widget.set_current_page_silent(current_page)
                elif not is_sort_operation:
                    # 非排序操作时才重置页码
                    self.logger.info(f"🔧 [CRITICAL修复] 非排序操作，重置页码到第{current_page}页")
                    # 暂时断开信号
                    self.pagination_widget.page_changed.disconnect()
                    self.pagination_widget.set_current_page(current_page)
                    # 重新连接信号
                    self.pagination_widget.page_changed.connect(self._on_page_changed_new_architecture)
            
            self.logger.info(f"表格数据设置完成: {len(df)} 行")
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
            import traceback
            traceback.print_exc()
'''
    
    # 替换现有的 set_data 方法
    pattern = r'def set_data\(self, df, table_name=None.*?\n(?:.*?\n)*?.*?self\.logger\.info\(f"表格数据设置完成: {len\(df\)} 行"\)'
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, enhanced_pagination_fix.strip(), content, flags=re.DOTALL)
        print("✅ 已增强 set_data 方法")
    
    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 修复完成")
    return True


def apply_virtualized_table_fixes():
    """修复 virtualized_expandable_table.py 的行号问题"""
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 完善 _emergency_fix_row_numbers 方法
    emergency_fix_method = '''
    def _emergency_fix_row_numbers(self):
        """🔧 [分页行号修复] 紧急修复行号显示"""
        try:
            if not (hasattr(self, 'pagination_state') and self.pagination_state):
                self.logger.warning("🔧 [紧急修复] 无分页状态，使用默认行号")
                # 使用默认行号
                row_count = self.rowCount()
                for i in range(row_count):
                    self.setVerticalHeaderItem(i, QTableWidgetItem(str(i + 1)))
                return
            
            row_count = self.rowCount()
            if row_count <= 0:
                self.logger.warning("🔧 [紧急修复] 表格无数据")
                return
            
            start_record = self.pagination_state.get('start_record', 1)
            
            self.logger.info(f"🔧 [紧急修复] 开始修复行号: 起始记录{start_record}, 行数{row_count}")
            
            # 方法1: 使用 setVerticalHeaderItem
            for i in range(row_count):
                row_number = start_record + i
                header_item = QTableWidgetItem(str(row_number))
                self.setVerticalHeaderItem(i, header_item)
            
            # 方法2: 备用方案 - 使用 setVerticalHeaderLabels
            try:
                row_labels = [str(start_record + i) for i in range(row_count)]
                self.setVerticalHeaderLabels(row_labels)
            except Exception as backup_e:
                self.logger.warning(f"🔧 [紧急修复] 备用方案失败: {backup_e}")
            
            # 确保垂直表头可见
            self.verticalHeader().setVisible(True)
            
            # 强制刷新UI
            self.viewport().update()
            QApplication.processEvents()
            
            # 再次验证
            first_item = self.verticalHeaderItem(0)
            if first_item:
                actual_first = first_item.text()
                expected_first = str(start_record)
                
                if actual_first == expected_first:
                    self.logger.info(f"🔧 [紧急修复] ✅ 成功: 首行号 {actual_first}")
                else:
                    self.logger.error(f"🔧 [紧急修复] ❌ 失败: 期望{expected_first}, 实际{actual_first}")
                    
                    # 最后的尝试：直接操作UI
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(100, lambda: self._final_row_number_fix(start_record, row_count))
            
        except Exception as e:
            self.logger.error(f"🔧 [紧急修复] 修复失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _final_row_number_fix(self, start_record: int, row_count: int):
        """🔧 [最后尝试] 最终行号修复"""
        try:
            self.logger.info(f"🔧 [最后尝试] 最终修复: 起始{start_record}, 行数{row_count}")
            
            # 确保在主线程中执行
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [最后尝试] 非主线程，延迟执行")
                QApplication.instance().processEvents()
                return
            
            # 逐行设置
            for i in range(min(row_count, self.rowCount())):
                row_number = start_record + i
                
                # 直接创建并设置表头项
                item = QTableWidgetItem()
                item.setText(str(row_number))
                self.setVerticalHeaderItem(i, item)
            
            self.logger.info(f"🔧 [最后尝试] 完成设置 {min(row_count, self.rowCount())} 行")
            
        except Exception as e:
            self.logger.error(f"🔧 [最后尝试] 失败: {e}")
'''
    
    # 查找并替换 _emergency_fix_row_numbers 方法
    pattern = r'def _emergency_fix_row_numbers\(self\):.*?(?=\n    def |\nclass |\Z)'
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, emergency_fix_method.strip(), content, flags=re.DOTALL)
        print("✅ 已增强紧急行号修复方法")
    else:
        # 如果方法不存在，在类末尾添加
        class_end_pattern = r'(class VirtualizedExpandableTable.*?(?=\nclass |\Z))'
        content = re.sub(class_end_pattern, lambda m: m.group(1) + '\n' + emergency_fix_method, content, flags=re.DOTALL)
        print("✅ 已添加紧急行号修复方法")
    
    # 2. 添加必要的导入
    if 'from PyQt5.QtCore import QThread' not in content:
        import_line = 'from PyQt5.QtCore import QThread, QTimer'
        
        # 在现有PyQt5导入后添加
        pattern = r'(from PyQt5\.QtCore import.*?)\n'
        replacement = r'\1, QThread, QTimer\n'
        
        if not re.search(pattern, content):
            # 如果没有找到现有导入，在文件开头添加
            content = import_line + '\n' + content
        else:
            content = re.sub(pattern, replacement, content)
        
        print("✅ 已添加必要的导入")
    
    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 修复完成")
    return True


def create_test_script():
    """创建测试脚本验证修复效果"""
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复效果测试脚本
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_fixes():
    """测试修复效果"""
    print("🧪 开始测试修复效果...")
    
    # 测试1: 检查事件循环保护是否生效
    try:
        from src.gui.prototype.prototype_main_window import _event_guard
        print("✅ 事件循环保护机制: 已加载")
    except ImportError:
        print("❌ 事件循环保护机制: 未找到")
    
    # 测试2: 检查分页组件
    try:
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        print("✅ 虚拟化表格组件: 已加载")
        
        # 检查紧急修复方法是否存在
        if hasattr(VirtualizedExpandableTable, '_emergency_fix_row_numbers'):
            print("✅ 紧急行号修复方法: 已存在")
        else:
            print("❌ 紧急行号修复方法: 不存在")
            
    except ImportError as e:
        print(f"❌ 虚拟化表格组件: 加载失败 - {e}")
    
    print("\\n🚀 可以启动系统进行测试:")
    print("1. python main.py")
    print("2. 导入数据")
    print("3. 测试表头排序（不应该卡死）")
    print("4. 测试分页（行号应该正确显示）")


if __name__ == "__main__":
    test_fixes()
'''
    
    with open('test_fixes.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_fixes.py")


def main():
    """执行最终修复"""
    print("🚀 开始最终全面修复...")
    
    try:
        # 修复主窗口事件循环问题
        if apply_prototype_main_window_fixes():
            print("✅ 主窗口事件循环修复完成")
        
        # 修复表格行号问题
        if apply_virtualized_table_fixes():
            print("✅ 表格行号修复完成")
        
        # 创建测试脚本
        create_test_script()
        
        print("\n🎉 最终修复完成！")
        print("\n📋 修复内容总结：")
        print("1. ✅ 排序事件循环死锁防护")
        print("2. ✅ 数据更新事件循环防护") 
        print("3. ✅ 分页事件循环防护")
        print("4. ✅ 分页行号正确显示修复")
        print("5. ✅ 紧急行号修复机制")
        print("6. ✅ Qt线程安全保护")
        
        print("\n🧪 测试步骤：")
        print("1. 运行: python test_fixes.py (验证修复)")
        print("2. 运行: python main.py (启动系统)")
        print("3. 导入数据测试")
        print("4. 点击表头排序测试（不应卡死）")
        print("5. 切换分页测试（行号应正确）")
        
        print("\n⚠️ 关键提醒：")
        print("- 系统已添加防护机制，避免快速连续操作")
        print("- 如发现问题，请查看日志文件详细信息")
        print("- 建议测试前备份重要数据")
        
    except Exception as e:
        print(f"❌ 最终修复失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 