# 🏗️ 薪资系统格式化架构设计方案

## 目录
- [设计目标](#设计目标)
- [架构原则](#架构原则)
- [新架构设计](#新架构设计)
- [核心组件设计](#核心组件设计)
- [数据流设计](#数据流设计)
- [接口规范](#接口规范)
- [实施计划](#实施计划)
- [测试策略](#测试策略)

---

## 设计目标

### 主要目标
1. **统一格式化**：建立单一、清晰的格式化流程
2. **性能优化**：消除重复格式化，提升系统性能
3. **可维护性**：简化格式化逻辑，便于维护和扩展
4. **可配置性**：支持格式化规则的动态配置
5. **兼容性**：兼容现有数据和功能

### 量化指标
- 格式化性能提升 **80%**
- 代码复杂度降低 **70%**
- 内存使用减少 **60%**
- 测试覆盖率达到 **95%**

---

## 架构原则

### 1. 单一职责原则
- 每个组件只负责一种格式化任务
- 数据层格式化与显示层格式化分离
- 配置管理与格式化执行分离

### 2. 开闭原则
- 对扩展开放：支持新的字段类型和格式规则
- 对修改封闭：核心格式化逻辑稳定不变

### 3. 依赖倒置原则
- 高层模块不依赖低层模块，都依赖抽象
- 格式化器依赖配置接口，不依赖具体配置实现

### 4. 接口隔离原则
- 提供细粒度的格式化接口
- 客户端只依赖它需要的接口

---

## 新架构设计

### 整体架构图

```mermaid
graph TB
    A[应用层] --> B[格式化门面层]
    B --> C[主格式化管理器]
    C --> D[格式化策略层]
    D --> E[配置管理层]
    D --> F[缓存管理层]
    
    subgraph "格式化策略层"
        D1[货币格式化器]
        D2[数值格式化器]
        D3[文本格式化器]
        D4[日期格式化器]
    end
    
    subgraph "配置管理层"
        E1[字段类型配置]
        E2[格式化规则配置]
        E3[表格类型配置]
    end
    
    subgraph "缓存管理层"
        F1[格式化结果缓存]
        F2[配置缓存]
        F3[字段类型缓存]
    end
```

### 架构层次说明

#### 1. 应用层
- **作用**：业务逻辑调用格式化服务
- **组件**：主窗口、表格组件、数据服务等
- **职责**：调用格式化接口，处理格式化结果

#### 2. 格式化门面层
- **作用**：提供统一的格式化入口
- **组件**：FormatFacade
- **职责**：简化格式化调用，隐藏内部复杂性

#### 3. 主格式化管理器
- **作用**：协调各个格式化器的工作
- **组件**：MasterFormatManager
- **职责**：策略选择、状态管理、结果整合

#### 4. 格式化策略层
- **作用**：具体的格式化实现
- **组件**：各种类型的格式化器
- **职责**：执行具体的格式化逻辑

#### 5. 配置管理层
- **作用**：管理格式化规则和配置
- **组件**：各种配置管理器
- **职责**：提供格式化规则和配置信息

#### 6. 缓存管理层
- **作用**：缓存格式化结果和配置
- **组件**：各种缓存管理器
- **职责**：提升格式化性能

---

## 核心组件设计

### 1. 格式化门面 (FormatFacade)

```python
class FormatFacade:
    """
    格式化门面 - 提供统一的格式化入口
    """
    
    def __init__(self):
        self.master_manager = MasterFormatManager()
    
    def format_table_data(self, data: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """格式化表格数据"""
        return self.master_manager.format_table_data(data, table_type)
    
    def format_cell_value(self, value: Any, field_name: str, table_type: str) -> str:
        """格式化单元格值"""
        return self.master_manager.format_cell_value(value, field_name, table_type)
    
    def format_headers(self, headers: List[str], table_type: str) -> List[str]:
        """格式化表头"""
        return self.master_manager.format_headers(headers, table_type)
```

### 2. 主格式化管理器 (MasterFormatManager)

```python
class MasterFormatManager:
    """
    主格式化管理器 - 协调所有格式化操作
    """
    
    def __init__(self):
        self.config_manager = FormatConfigManager()
        self.cache_manager = FormatCacheManager()
        self.strategy_factory = FormatStrategyFactory()
        self._format_state_tracker = FormatStateTracker()
    
    def format_table_data(self, data: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """格式化表格数据的主要方法"""
        
        # 1. 检查格式化状态
        if self._format_state_tracker.is_formatted(data):
            self.logger.info("数据已格式化，跳过处理")
            return data
        
        # 2. 获取格式化配置
        config = self.config_manager.get_table_config(table_type)
        
        # 3. 检查缓存
        cache_key = self._generate_cache_key(data, table_type)
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            return cached_result
        
        # 4. 执行格式化
        formatted_data = self._execute_formatting(data, config)
        
        # 5. 标记格式化状态
        self._format_state_tracker.mark_formatted(formatted_data)
        
        # 6. 缓存结果
        self.cache_manager.set(cache_key, formatted_data)
        
        return formatted_data
    
    def _execute_formatting(self, data: pd.DataFrame, config: dict) -> pd.DataFrame:
        """执行具体的格式化逻辑"""
        formatted_data = data.copy()
        
        for column in data.columns:
            field_config = config.get('fields', {}).get(column, {})
            field_type = field_config.get('type', 'string')
            
            # 获取对应的格式化策略
            formatter = self.strategy_factory.get_formatter(field_type)
            
            # 格式化整列数据
            formatted_data[column] = formatter.format_column(
                data[column], field_config
            )
        
        return formatted_data
```

### 3. 格式化策略工厂 (FormatStrategyFactory)

```python
class FormatStrategyFactory:
    """
    格式化策略工厂 - 创建和管理格式化器
    """
    
    def __init__(self):
        self._formatters = {
            'currency': CurrencyFormatter(),
            'integer': IntegerFormatter(),
            'float': FloatFormatter(),
            'string': StringFormatter(),
            'date': DateFormatter(),
            'percentage': PercentageFormatter()
        }
    
    def get_formatter(self, field_type: str) -> BaseFormatter:
        """获取指定类型的格式化器"""
        return self._formatters.get(field_type, self._formatters['string'])
    
    def register_formatter(self, field_type: str, formatter: BaseFormatter):
        """注册新的格式化器"""
        self._formatters[field_type] = formatter
```

### 4. 基础格式化器 (BaseFormatter)

```python
class BaseFormatter(ABC):
    """
    格式化器基类 - 定义格式化器接口
    """
    
    @abstractmethod
    def format_value(self, value: Any, config: dict) -> str:
        """格式化单个值"""
        pass
    
    @abstractmethod
    def format_column(self, column: pd.Series, config: dict) -> pd.Series:
        """格式化整列数据"""
        pass
    
    def validate_config(self, config: dict) -> bool:
        """验证配置有效性"""
        return True
```

### 5. 货币格式化器 (CurrencyFormatter)

```python
class CurrencyFormatter(BaseFormatter):
    """
    货币格式化器 - 处理货币类型数据
    """
    
    def format_value(self, value: Any, config: dict) -> str:
        """格式化单个货币值"""
        if pd.isna(value) or value is None:
            return config.get('zero_display', '0.00')
        
        try:
            numeric_value = float(value)
            symbol = config.get('symbol', '¥')
            decimal_places = config.get('decimal_places', 2)
            thousand_separator = config.get('thousand_separator', ',')
            
            if numeric_value == 0:
                return config.get('zero_display', '0.00')
            
            # 格式化为指定小数位数
            formatted_value = f"{abs(numeric_value):,.{decimal_places}f}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            # 添加货币符号
            result = f"{symbol}{formatted_value}"
            
            # 处理负数
            if numeric_value < 0:
                result = f"-{result}"
            
            return result
            
        except (ValueError, TypeError):
            return config.get('error_display', '数据错误')
    
    def format_column(self, column: pd.Series, config: dict) -> pd.Series:
        """格式化整列货币数据"""
        return column.apply(lambda x: self.format_value(x, config))
```

### 6. 格式化状态跟踪器 (FormatStateTracker)

```python
class FormatStateTracker:
    """
    格式化状态跟踪器 - 跟踪数据格式化状态
    """
    
    def __init__(self):
        self._formatted_data_ids = set()
    
    def is_formatted(self, data: pd.DataFrame) -> bool:
        """检查数据是否已格式化"""
        # 检查数据属性标记
        if hasattr(data, 'attrs') and data.attrs.get('formatted'):
            return True
        
        # 检查数据特征
        if self._check_data_characteristics(data):
            return True
        
        # 检查ID缓存
        data_id = self._generate_data_id(data)
        return data_id in self._formatted_data_ids
    
    def mark_formatted(self, data: pd.DataFrame):
        """标记数据已格式化"""
        # 设置属性标记
        if hasattr(data, 'attrs'):
            data.attrs['formatted'] = True
        
        # 添加到ID缓存
        data_id = self._generate_data_id(data)
        self._formatted_data_ids.add(data_id)
    
    def _check_data_characteristics(self, data: pd.DataFrame) -> bool:
        """检查数据特征判断是否已格式化"""
        if len(data) == 0:
            return False
        
        # 检查货币字段特征
        for column in data.columns:
            if '工资' in column or '津贴' in column:
                sample_value = data.iloc[0][column]
                if isinstance(sample_value, str) and '¥' in sample_value:
                    return True
        
        return False
    
    def _generate_data_id(self, data: pd.DataFrame) -> str:
        """生成数据唯一标识"""
        import hashlib
        data_str = f"{data.shape}_{list(data.columns)}_{str(data.iloc[0].values if len(data) > 0 else [])}"
        return hashlib.md5(data_str.encode()).hexdigest()
```

### 7. 配置管理器 (FormatConfigManager)

```python
class FormatConfigManager:
    """
    格式化配置管理器 - 管理格式化配置
    """
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "config/format_config.json"
        self._config_cache = {}
        self._load_config()
    
    def get_table_config(self, table_type: str) -> dict:
        """获取表格配置"""
        return self._config_cache.get('tables', {}).get(table_type, {})
    
    def get_field_config(self, field_name: str, table_type: str) -> dict:
        """获取字段配置"""
        table_config = self.get_table_config(table_type)
        return table_config.get('fields', {}).get(field_name, {})
    
    def get_default_config(self) -> dict:
        """获取默认配置"""
        return self._config_cache.get('defaults', {})
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_cache = json.load(f)
        except FileNotFoundError:
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self._config_cache = {
            "defaults": {
                "currency": {
                    "symbol": "¥",
                    "decimal_places": 2,
                    "thousand_separator": ",",
                    "zero_display": "0.00",
                    "error_display": "数据错误"
                }
            },
            "tables": {
                "active_employees": {
                    "fields": {
                        "2025年岗位工资": {"type": "currency"},
                        "2025年薪级工资": {"type": "currency"},
                        "津贴": {"type": "currency"},
                        "工号": {"type": "string"}
                    }
                }
            }
        }
```

---

## 数据流设计

### 格式化数据流

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Facade as 格式化门面
    participant Manager as 主管理器
    participant Tracker as 状态跟踪器
    participant Cache as 缓存管理器
    participant Strategy as 格式化策略
    
    App->>Facade: format_table_data(data, type)
    Facade->>Manager: format_table_data(data, type)
    
    Manager->>Tracker: is_formatted(data)
    Tracker-->>Manager: false
    
    Manager->>Cache: get(cache_key)
    Cache-->>Manager: null
    
    Manager->>Strategy: format_column(column, config)
    Strategy-->>Manager: formatted_column
    
    Manager->>Tracker: mark_formatted(data)
    Manager->>Cache: set(cache_key, result)
    
    Manager-->>Facade: formatted_data
    Facade-->>App: formatted_data
```

### 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> 原始数据
    原始数据 --> 检查状态: 开始格式化
    检查状态 --> 已格式化: 状态=已格式化
    检查状态 --> 未格式化: 状态=未格式化
    已格式化 --> 返回数据: 跳过格式化
    未格式化 --> 执行格式化: 开始格式化
    执行格式化 --> 标记状态: 格式化完成
    标记状态 --> 缓存结果: 状态=已格式化
    缓存结果 --> 返回数据: 完成
    返回数据 --> [*]
```

---

## 接口规范

### 1. 格式化接口

```python
# 表格数据格式化
def format_table_data(data: pd.DataFrame, table_type: str) -> pd.DataFrame:
    """
    格式化表格数据
    
    Args:
        data: 原始表格数据
        table_type: 表格类型（如：active_employees）
    
    Returns:
        格式化后的表格数据
        
    Raises:
        FormatError: 格式化失败时抛出
    """

# 单元格值格式化
def format_cell_value(value: Any, field_name: str, table_type: str) -> str:
    """
    格式化单元格值
    
    Args:
        value: 原始值
        field_name: 字段名
        table_type: 表格类型
    
    Returns:
        格式化后的字符串
    """

# 表头格式化
def format_headers(headers: List[str], table_type: str) -> List[str]:
    """
    格式化表头
    
    Args:
        headers: 原始表头列表
        table_type: 表格类型
    
    Returns:
        格式化后的表头列表
    """
```

### 2. 配置接口

```python
# 获取格式化配置
def get_format_config(table_type: str, field_name: str = None) -> dict:
    """
    获取格式化配置
    
    Args:
        table_type: 表格类型
        field_name: 字段名（可选）
    
    Returns:
        格式化配置字典
    """

# 更新格式化配置
def update_format_config(table_type: str, config: dict):
    """
    更新格式化配置
    
    Args:
        table_type: 表格类型
        config: 新的配置
    """
```

### 3. 缓存接口

```python
# 缓存操作接口
def get_cached_result(cache_key: str) -> Any:
    """获取缓存结果"""

def set_cached_result(cache_key: str, result: Any, ttl: int = 3600):
    """设置缓存结果"""

def clear_cache(pattern: str = None):
    """清理缓存"""
```

---

## 实施计划

### 阶段1：基础框架搭建（第1-2天）
1. 创建核心组件骨架
2. 实现格式化门面
3. 建立配置管理基础
4. 创建测试框架

### 阶段2：核心功能实现（第3-5天）
1. 实现主格式化管理器
2. 开发各种格式化策略
3. 实现状态跟踪机制
4. 完成缓存管理功能

### 阶段3：集成测试（第6-7天）
1. 集成所有组件
2. 进行功能测试
3. 性能测试和优化
4. 修复发现的问题

### 阶段4：部署上线（第8天）
1. 替换现有格式化逻辑
2. 数据迁移和验证
3. 监控和日志配置
4. 用户验收测试

---

## 测试策略

### 1. 单元测试

```python
class TestCurrencyFormatter(unittest.TestCase):
    def setUp(self):
        self.formatter = CurrencyFormatter()
        self.config = {
            'symbol': '¥',
            'decimal_places': 2,
            'thousand_separator': ',',
            'zero_display': '0.00'
        }
    
    def test_format_positive_value(self):
        result = self.formatter.format_value(3266.0, self.config)
        self.assertEqual(result, '¥3,266.00')
    
    def test_format_zero_value(self):
        result = self.formatter.format_value(0, self.config)
        self.assertEqual(result, '0.00')
    
    def test_format_invalid_value(self):
        result = self.formatter.format_value('invalid', self.config)
        self.assertEqual(result, '数据错误')
```

### 2. 集成测试

```python
class TestFormatIntegration(unittest.TestCase):
    def setUp(self):
        self.facade = FormatFacade()
        self.test_data = pd.DataFrame({
            '工号': ['123456'],
            '2025年岗位工资': [5000.0],
            '2025年薪级工资': [3266.0]
        })
    
    def test_full_table_formatting(self):
        result = self.facade.format_table_data(self.test_data, 'active_employees')
        
        # 验证格式化结果
        self.assertEqual(result.iloc[0]['2025年岗位工资'], '¥5,000.00')
        self.assertEqual(result.iloc[0]['2025年薪级工资'], '¥3,266.00')
        
        # 验证状态标记
        self.assertTrue(result.attrs.get('formatted'))
```

### 3. 性能测试

```python
class TestFormatPerformance(unittest.TestCase):
    def test_large_data_formatting(self):
        # 创建大数据集
        large_data = pd.DataFrame({
            '2025年岗位工资': [5000.0] * 10000,
            '2025年薪级工资': [3266.0] * 10000
        })
        
        # 测试格式化性能
        start_time = time.time()
        result = self.facade.format_table_data(large_data, 'active_employees')
        end_time = time.time()
        
        # 验证性能要求（应该在100ms内完成）
        self.assertLess(end_time - start_time, 0.1)
```

---

## 质量保证

### 1. 代码质量
- **代码覆盖率**：要求达到95%以上
- **代码规范**：遵循PEP8规范
- **文档完整性**：所有公共接口都有详细文档

### 2. 性能要求
- **格式化速度**：1000行数据在50ms内完成
- **内存使用**：不超过现有实现的50%
- **缓存命中率**：达到80%以上

### 3. 稳定性要求
- **错误处理**：所有异常都有适当处理
- **数据完整性**：格式化不丢失原始数据信息
- **兼容性**：兼容现有所有数据格式

---

**文档创建时间：** 2025-07-19  
**设计负责人：** 薪资系统重构团队  
**审核状态：** 待审核  
**文档版本：** v1.0.0