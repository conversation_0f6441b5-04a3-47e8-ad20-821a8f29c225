# 表头英文显示问题完整解决方案

## 问题描述

用户反映月度工资异动处理系统中，列表展示区域的表头一直显示英文，无论如何修改都无效。具体表现为：
- 2021年1月的各个表在主界面显示中文表头
- 其他年份月份的表头零散显示中文
- 新增2016年1月数据导入后，表头仍显示英文
- 每次修改都说成功，但运行系统时修改无效

## 问题分析过程

### 1. 初步排查
- 检查了根目录的 `config.json` 文件
- 发现系统实际使用的是 `state/data/field_mappings.json` 文件
- 确认字段映射文件存在且配置完整（27个表的映射）

### 2. 代码层面分析
通过分析源码发现问题链路：
```
VirtualizedExpandableTable.set_data() 
→ 直接使用原始英文字段名设置表头
→ setHorizontalHeaderLabels(headers) ❌
→ 没有应用字段映射
```

### 3. 修复尝试1：代码层面修复
在 `src/gui/prototype/widgets/virtualized_expandable_table.py` 中修改：
```python
# 设置表头
self.setColumnCount(len(headers))
self.setHorizontalHeaderLabels(headers)

# 应用字段映射到表头显示
if hasattr(self, 'current_table_name') and self.current_table_name:
    self._refresh_header_display()
```

**结果：** 部分有效，但2016年新导入的表仍显示英文

### 4. 深入分析：字段映射不匹配问题
通过调试脚本发现核心问题：
- 系统日志显示：`表头显示刷新完成，更新了 0 个表头`
- 说明 `_refresh_header_display()` 被调用但没有找到匹配的字段映射

### 5. 根本原因发现
通过数据库字段检查脚本 `temp/check_real_field_names.py` 发现：

**数据库实际字段名（英文）：**
```
id, employee_id, employee_name, id_card, department, position,
basic_salary, performance_bonus, overtime_pay, allowance,
deduction, total_salary, month, year, created_at, updated_at
```

**字段映射配置中的源字段名（中文）：**
```
序号, 工号, 姓名, 部门名称, 人员类别代码, 人员类别,
2025年岗位工资, 2025年薪级工资, 津贴, 结余津贴, ...
```

**关键问题：** 映射配置完全不匹配！数据库字段是英文，但映射配置期望的是中文字段名。

## 解决方案

### 最终修复方案
创建 `temp/fix_2016_field_mapping_correct.py` 脚本，基于真实数据库字段结构修复映射：

```python
# 基于真实数据库字段的正确映射
real_field_mapping = {
    "id": "ID",
    "employee_id": "工号", 
    "employee_name": "姓名",
    "id_card": "身份证号",
    "department": "部门",
    "position": "职位",
    "basic_salary": "基本工资",
    "performance_bonus": "绩效奖金",
    "overtime_pay": "加班费",
    "allowance": "津贴补贴",
    "deduction": "扣除",
    "total_salary": "应发合计",
    "month": "月份",
    "year": "年份",
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

### 修复步骤
1. **检查真实数据库字段结构** - 使用 SQLite 查询获取准确字段名
2. **备份现有配置** - 防止修复失败时数据丢失
3. **更新字段映射** - 将中文→中文映射改为英文→中文映射
4. **保存配置** - 更新 `state/data/field_mappings.json`

## 修复结果

✅ **成功修复2016年1月的4个表：**
- `salary_data_2016_01_retired_employees`
- `salary_data_2016_01_pension_employees` 
- `salary_data_2016_01_active_employees`
- `salary_data_2016_01_a_grade_employees`

✅ **表头正确显示中文：**
- `employee_id` → `工号`
- `employee_name` → `姓名`
- `department` → `部门`
- `total_salary` → `应发合计`

## 技术要点总结

### 1. 问题诊断方法
- **不要盲目假设** - 必须先检查真实的数据库结构
- **追踪数据流** - 从数据库→映射配置→界面显示的完整链路
- **日志分析** - 通过系统日志发现映射应用失败的线索

### 2. 关键代码修复点
```python
# src/gui/prototype/widgets/virtualized_expandable_table.py
# 在 set_data 方法中添加：
if hasattr(self, 'current_table_name') and self.current_table_name:
    self._refresh_header_display()
```

### 3. 配置文件结构
```json
{
  "table_mappings": {
    "table_name": {
      "metadata": {...},
      "field_mappings": {
        "数据库字段名": "显示名称",
        "employee_id": "工号",
        "employee_name": "姓名"
      }
    }
  }
}
```

## 经验教训

### ❌ 错误的做法
1. **盲目假设字段名** - 没有检查真实数据库结构就创建映射
2. **硬编码映射** - 使用固定的字段映射而不基于实际数据
3. **忽略日志信息** - 没有分析 "更新了 0 个表头" 的警告信息

### ✅ 正确的做法
1. **先调查再修复** - 使用数据库查询确认真实字段结构
2. **基于事实修复** - 根据实际数据库字段创建正确映射
3. **备份保护** - 修复前备份配置文件
4. **验证结果** - 修复后确认表头正确显示

## 相关文件

### 核心文件
- `src/gui/prototype/widgets/virtualized_expandable_table.py` - 表格组件
- `state/data/field_mappings.json` - 字段映射配置
- `src/modules/data_import/config_sync_manager.py` - 映射管理

### 调试脚本
- `temp/check_real_field_names.py` - 数据库字段检查
- `temp/fix_2016_field_mapping_correct.py` - 字段映射修复
- `temp/simple_debug_header.py` - 简化调试脚本

### 备份文件
- `state/data/field_mappings_before_2016_fix_*.json` - 修复前备份

## 适用场景

这个解决方案适用于：
- 新导入数据表头显示英文的问题
- 字段映射配置与数据库结构不匹配的问题
- 需要批量修复多个表字段映射的场景

## 预防措施

为避免类似问题：
1. **数据导入时自动检测字段结构**
2. **映射配置验证机制**
3. **字段映射不匹配时的警告提示**
4. **统一的字段映射生成规则**

---

*本文档记录了2025年6月25日表头英文显示问题的完整解决过程，为类似问题提供参考。* 