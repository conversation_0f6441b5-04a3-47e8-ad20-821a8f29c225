#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的格式化验证测试 - 避免Unicode显示问题
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_retired_employees_format():
    """简化测试离休人员表格式化"""
    print("=== 离休人员工资表格式化简化测试 ===")
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 初始化
        format_manager = UnifiedFormatManager(
            config_path="state/format_config.json",
            field_mapping_path="state/data/field_mappings.json"
        )
        
        # 创建测试数据
        test_data = {
            'employee_id': ['001', '002', '003'],
            'employee_name': ['张三', '李四', '王五'],
            'department': ['财务部', '人事部', '技术部'],
            'basic_retirement_salary': [5000.123, None, 0],
            'balance_allowance': [1200.50, np.nan, '0'],
            'living_allowance': [800.25, '', None],
            'month': ['202507', '07', '7'],
            'year': ['2025', 2025, '25'],
            'remarks': ['正常', '', None],
            # 应该被隐藏的字段
            'id': [1, 2, 3],
            'sequence_number': [1, 2, 3],
            'created_at': ['2025-07-30', '2025-07-30', '2025-07-30'],
            'updated_at': ['2025-07-30', '2025-07-30', '2025-07-30']
        }
        
        original_df = pd.DataFrame(test_data)
        print(f"原始数据: {len(original_df)} 行 x {len(original_df.columns)} 列")
        
        # 执行格式化
        formatted_headers, formatted_data = format_manager.format_table_complete(
            original_df, "retired_employees"
        )
        
        print(f"格式化后: {len(formatted_data)} 行 x {len(formatted_data.columns)} 列")
        print(f"格式化表头: {formatted_headers}")
        
        # 检查隐藏字段功能
        hidden_check = len(original_df.columns) > len(formatted_data.columns)
        print(f"隐藏字段功能: {'正常' if hidden_check else '异常'}")
        
        # 检查浮点数格式化
        print("\n=== 数据内容检查 ===")
        for col in formatted_data.columns:
            values = formatted_data[col].tolist()[:3]  # 只显示前3个值
            print(f"{col}: {values}")
        
        # 验证基本离休费字段的格式化
        salary_col = None
        for col in formatted_data.columns:
            if '离休费' in col or '基本' in col:
                salary_col = col
                break
        
        if salary_col:
            salary_values = formatted_data[salary_col].tolist()
            print(f"\n工资字段({salary_col})格式化检查:")
            for i, val in enumerate(salary_values):
                print(f"  行{i+1}: {val} (类型: {type(val)})")
                # 检查是否符合格式要求
                if val == '0.00':
                    print(f"    OK - 空值正确显示为0.00")
                elif isinstance(val, str) and '.' in val:
                    decimal_places = len(val.split('.')[-1])
                    if decimal_places == 2:
                        print(f"    OK - 小数位数正确(2位)")
                    else:
                        print(f"    WARN - 小数位数不正确({decimal_places}位)")
        
        print(f"\n测试完成!")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_retired_employees_format()
    print(f"\n整体结果: {'成功' if success else '失败'}")