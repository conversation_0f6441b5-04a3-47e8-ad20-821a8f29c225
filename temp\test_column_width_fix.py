#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列宽保存恢复功能

验证分页切换时列宽是否能够正确保存和恢复
"""

import sys
import os
import time
import pandas as pd

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置日志
from src.utils.log_config import setup_logger
logger = setup_logger("ColumnWidthTest")

def test_state_manager():
    """测试StateManager的列宽保存/恢复功能"""
    try:
        from src.gui.state_manager import StateManager
        from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
        from PyQt5.QtCore import Qt
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 创建状态管理器
        state_manager = StateManager()
        
        # 创建测试表格
        table = QTableWidget(3, 4)
        table.setHorizontalHeaderLabels(['工号', '姓名', '部门', '工资'])
        
        # 添加测试数据
        test_data = [
            ['001', '张三', '技术部', '8000'],
            ['002', '李四', '财务部', '7500'],
            ['003', '王五', '人事部', '7000']
        ]
        
        for row, row_data in enumerate(test_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                table.setItem(row, col, item)
        
        # 设置自定义列宽
        header = table.horizontalHeader()
        custom_widths = [120, 100, 150, 110]
        for i, width in enumerate(custom_widths):
            header.resizeSection(i, width)
        
        logger.info("设置自定义列宽:")
        for i, width in enumerate(custom_widths):
            actual_width = header.sectionSize(i)
            logger.info(f"  列{i}: 设置={width}, 实际={actual_width}")
        
        # 测试保存状态
        success = state_manager.save_table_state_by_name("test_table", table)
        logger.info(f"保存状态结果: {success}")
        
        # 重置列宽（模拟表格重新渲染）
        for i in range(table.columnCount()):
            header.resizeSection(i, 75)  # 重置为默认宽度
        
        logger.info("重置后的列宽:")
        for i in range(table.columnCount()):
            width = header.sectionSize(i)
            logger.info(f"  列{i}: {width}")
        
        # 测试恢复状态
        success = state_manager.restore_table_state_by_name("test_table", table)
        logger.info(f"恢复状态结果: {success}")
        
        logger.info("恢复后的列宽:")
        for i in range(table.columnCount()):
            width = header.sectionSize(i)
            expected = custom_widths[i]
            status = "✅" if width == expected else "❌"
            logger.info(f"  列{i}: {width} (期望: {expected}) {status}")
        
        # 验证结果
        all_correct = True
        for i in range(table.columnCount()):
            actual = header.sectionSize(i)
            expected = custom_widths[i]
            if actual != expected:
                all_correct = False
                break
        
        logger.info(f"测试结果: {'✅ 通过' if all_correct else '❌ 失败'}")
        
        return all_correct
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_main_window_state_cache():
    """测试主窗口的状态缓存功能"""
    try:
        logger.info("测试主窗口状态缓存功能...")
        
        # 模拟主窗口的状态缓存
        table_ui_state_cache = {}
        
        # 模拟保存状态
        test_state = {
            'column_widths': [120, 100, 150, 110],
            'column_order': [0, 1, 2, 3],
            'sort_column': -1,
            'sort_order': 0,
            'selection': []
        }
        
        table_name = "salary_data_2025_08_active_employees"
        table_ui_state_cache[table_name] = test_state
        
        logger.info(f"已保存状态: {table_name}")
        logger.info(f"  列宽: {test_state['column_widths']}")
        
        # 模拟恢复状态
        if table_name in table_ui_state_cache:
            saved_state = table_ui_state_cache[table_name]
            logger.info(f"恢复状态成功: {table_name}")
            logger.info(f"  列宽: {saved_state['column_widths']}")
            return True
        else:
            logger.error("未找到保存的状态")
            return False
            
    except Exception as e:
        logger.error(f"状态缓存测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔧 开始测试列宽保存恢复功能")
    logger.info("=" * 50)
    
    # 测试1: StateManager功能
    logger.info("测试1: StateManager列宽保存恢复")
    test1_result = test_state_manager()
    
    logger.info("-" * 30)
    
    # 测试2: 主窗口状态缓存
    logger.info("测试2: 主窗口状态缓存")
    test2_result = test_main_window_state_cache()
    
    logger.info("=" * 50)
    logger.info("测试结果总结:")
    logger.info(f"  StateManager测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    logger.info(f"  状态缓存测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    overall_result = test1_result and test2_result
    logger.info(f"  整体结果: {'✅ 所有测试通过' if overall_result else '❌ 部分测试失败'}")
    
    if overall_result:
        logger.info("🎉 列宽保存恢复功能修复成功！")
    else:
        logger.warning("⚠️ 还有部分功能需要进一步调试")
    
    return overall_result

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生未预期的错误: {e}")
        sys.exit(1)