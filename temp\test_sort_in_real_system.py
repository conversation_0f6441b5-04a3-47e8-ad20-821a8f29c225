#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实系统排序功能测试

目标：在真实系统环境中追踪排序功能的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_sort_event_flow():
    """测试排序事件流程"""
    print("\n🔧 真实系统排序事件流程测试")
    print("="*60)
    
    try:
        print("📋 测试计划:")
        print("1. 模拟表头点击")
        print("2. 追踪排序请求事件")
        print("3. 检查数据库查询")
        print("4. 验证UI更新")
        
        # 测试1：检查表头点击处理方法是否存在
        print("\n🔍 [步骤1] 检查表头点击处理方法:")
        
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        
        # 检查关键方法是否存在
        methods_to_check = [
            '_on_header_clicked',
            '_notify_sort_applied', 
            '_publish_sort_apply_event',
            '_apply_column_sort_state'
        ]
        
        for method_name in methods_to_check:
            if hasattr(VirtualizedExpandableTable, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法缺失")
        
        # 测试2：检查事件处理器是否正确注册
        print("\n🔍 [步骤2] 检查事件处理器注册:")
        
        # 查看TableDataService的实际构造函数
        from src.services.table_data_service import TableDataService
        import inspect
        sig = inspect.signature(TableDataService.__init__)
        print(f"  📝 TableDataService构造函数签名: {sig}")
        
        # 测试3：检查主窗口是否正确处理排序事件
        print("\n🔍 [步骤3] 检查主窗口排序处理:")
        
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        sort_methods = [
            '_handle_sort_applied',
            '_publish_sort_request_event', 
            '_on_new_data_updated'
        ]
        
        for method_name in sort_methods:
            if hasattr(PrototypeMainWindow, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法缺失")
        
        # 测试4：检查数据库管理器初始化
        print("\n🔍 [步骤4] 检查数据库管理器:")
        
        try:
            from src.modules.data_storage.database_manager import get_database_manager
            db_manager = get_database_manager()
            print(f"  ✅ 数据库管理器类型: {type(db_manager)}")
            print(f"  📝 数据库管理器方法: {[m for m in dir(db_manager) if not m.startswith('_')][:10]}...")
        except Exception as e:
            print(f"  ❌ 数据库管理器初始化失败: {e}")
        
        # 测试5：检查事件总线在主窗口中的状态
        print("\n🔍 [步骤5] 检查事件总线集成:")
        
        from src.core.event_bus import EventBus
        print(f"  ✅ EventBus类可导入")
        
        # 检查事件类型定义
        try:
            from src.core.event_bus import SortRequestEvent
            print(f"  ✅ SortRequestEvent事件类存在")
        except ImportError as e:
            print(f"  ❌ SortRequestEvent事件类缺失: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_sort_test():
    """创建简单的排序测试"""
    print("\n🔧 简单排序测试")
    print("="*40)
    
    try:
        # 创建测试数据
        import pandas as pd
        
        test_data = pd.DataFrame({
            'employee_id': ['003', '001', '002', '005', '004'],
            'name': ['王五', '张三', '李四', '赵七', '钱六'],
            'salary': [3000, 5000, 4000, 2000, 6000]
        })
        
        print("📊 原始数据:")
        print(test_data[['employee_id', 'salary']].to_string(index=False))
        
        # 测试pandas排序
        sorted_data = test_data.sort_values('employee_id', ascending=True)
        print("\n📊 按工号排序后:")
        print(sorted_data[['employee_id', 'salary']].to_string(index=False))
        
        # 验证排序是否正确
        expected_order = ['001', '002', '003', '004', '005']
        actual_order = sorted_data['employee_id'].tolist()
        
        if actual_order == expected_order:
            print("✅ Pandas排序功能正常")
        else:
            print(f"❌ Pandas排序异常: 期望{expected_order}, 实际{actual_order}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单排序测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 真实系统排序功能诊断")
    print("="*60)
    
    tests = [
        ("真实系统排序事件流程", test_sort_event_flow),
        ("简单排序功能", create_simple_sort_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n💥 {test_name} 测试异常: {e}")
    
    print("\n" + "="*60)
    print("📊 诊断结果")
    print("="*60)
    print(f"总计: {passed}/{total} 测试通过")
    
    print("\n💡 **排序功能失效的可能原因分析：**")
    print("1. 🔴 数据库层初始化失败 - 无法执行排序查询")
    print("2. 🟡 事件处理器未正确注册 - 排序请求无法到达数据层")
    print("3. 🟡 UI更新机制失效 - 排序完成但界面未刷新")
    print("4. 🟢 事件发布机制正常 - 表头点击可以触发事件")
    
    print("\n🎯 **建议修复方向：**")
    print("优先级1: 修复数据库管理器初始化问题")
    print("优先级2: 确保TableDataService正确处理排序请求")
    print("优先级3: 验证UI数据更新流程")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 