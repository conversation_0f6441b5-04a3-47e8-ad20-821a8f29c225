#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证P0修复后的启动状态
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger

def test_import_modules():
    """测试关键模块是否能正常导入"""
    logger = setup_logger("StartupTest")
    
    try:
        # 测试导入主要模块
        logger.info("测试导入主要模块...")
        
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        logger.info("✅ VirtualizedExpandableTable 导入成功")
        
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        logger.info("✅ PrototypeMainWindow 导入成功")
        
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ 语法错误: {e}")
        return False
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 其他错误: {e}")
        return False

def test_column_width_manager():
    """测试列宽管理器功能"""
    logger = setup_logger("StartupTest")
    
    try:
        from src.gui.prototype.widgets.virtualized_expandable_table import ColumnWidthManager
        from PyQt5.QtWidgets import QTableWidget, QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建测试表格
        table = QTableWidget()
        table.setRowCount(5)
        table.setColumnCount(4)
        
        # 创建列宽管理器
        manager = ColumnWidthManager(table)
        logger.info("✅ ColumnWidthManager 创建成功")
        
        # 测试配置文件路径
        config_file = manager.config_file
        logger.info(f"✅ 配置文件路径: {config_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 列宽管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_logger("StartupTest")
    
    logger.info("🔧 开始启动修复验证")
    logger.info("=" * 40)
    
    tests = [
        ("模块导入测试", test_import_modules),
        ("列宽管理器测试", test_column_width_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    logger.info("\n" + "=" * 40)
    logger.info(f"🔧 启动修复验证完成: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 启动修复验证成功！程序可以正常运行")
        return True
    else:
        logger.warning(f"⚠️ 有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
