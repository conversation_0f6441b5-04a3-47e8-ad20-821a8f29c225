"""
月度工资异动处理系统 - 基准工资数据管理器

本模块负责：
- 基准工资数据的建立和维护
- 基准数据的查询和更新
- 基准数据的版本管理
- 基准数据的完整性验证

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date
from pathlib import Path

from src.utils.log_config import get_module_logger
from src.core.config_manager import ConfigManager

logger = get_module_logger(__name__)


class BaselineManager:
    """基准工资数据管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化基准工资数据管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.baseline_data: Optional[pd.DataFrame] = None
        self.baseline_version: Optional[str] = None
        self.last_update_time: Optional[datetime] = None
        
        logger.info("基准工资数据管理器初始化完成")
    
    def create_baseline_from_data(
        self,
        salary_data: pd.DataFrame,
        period: str,
        save_as_baseline: bool = True
    ) -> bool:
        """
        从工资数据创建基准数据
        
        Args:
            salary_data: 工资数据DataFrame
            period: 数据周期 (如 "2024-01")
            save_as_baseline: 是否保存为基准数据
            
        Returns:
            bool: 创建是否成功
        """
        try:
            logger.info(f"开始创建基准数据，数据周期: {period}")
            
            # 验证数据格式
            if not self._validate_salary_data(salary_data):
                logger.error("工资数据格式验证失败")
                return False
            
            # 预处理数据 - 清理和标准化
            processed_data = self._preprocess_salary_data(salary_data)
            
            # 创建基准数据结构
            baseline_data = self._create_baseline_structure(processed_data, period)
            
            # 设置基准数据
            self.baseline_data = baseline_data
            self.baseline_version = f"{period}_baseline"
            self.last_update_time = datetime.now()
            
            logger.info(f"基准数据创建成功，包含 {len(baseline_data)} 条记录")
            
            # 保存基准数据
            if save_as_baseline:
                success = self._save_baseline_data()
                if success:
                    logger.info("基准数据已保存到数据库")
                else:
                    logger.warning("基准数据保存失败，但内存中的基准数据仍可用")
            
            return True
            
        except Exception as e:
            logger.error(f"创建基准数据失败: {str(e)}")
            return False
    
    def load_baseline_data(self, version: Optional[str] = None) -> bool:
        """
        加载基准数据
        
        Args:
            version: 基准数据版本，None表示加载最新版本
            
        Returns:
            bool: 加载是否成功
        """
        try:
            logger.info(f"开始加载基准数据，版本: {version or '最新'}")
            
            # 从数据库加载基准数据
            baseline_data = self._load_baseline_from_db(version)
            
            if baseline_data is None or baseline_data.empty:
                logger.warning("未找到基准数据")
                return False
            
            self.baseline_data = baseline_data
            self.baseline_version = version or "latest"
            self.last_update_time = datetime.now()
            
            logger.info(f"基准数据加载成功，包含 {len(baseline_data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"加载基准数据失败: {str(e)}")
            return False
    
    def get_baseline_for_person(
        self,
        person_id: str,
        id_type: str = "工号"
    ) -> Optional[Dict[str, Any]]:
        """
        获取指定人员的基准工资数据
        
        Args:
            person_id: 人员标识 (工号、身份证号等)
            id_type: 标识类型 ("工号", "身份证号", "姓名")
            
        Returns:
            Dict[str, Any]: 基准工资数据字典，None表示未找到
        """
        try:
            if self.baseline_data is None:
                logger.warning("基准数据未加载，无法查询")
                return None
            
            # 根据不同标识类型查询
            query_field = self._get_query_field(id_type)
            if query_field not in self.baseline_data.columns:
                logger.error(f"基准数据中缺少字段: {query_field}")
                return None
            
            # 查询基准数据
            mask = self.baseline_data[query_field] == person_id
            results = self.baseline_data[mask]
            
            if results.empty:
                logger.debug(f"未找到人员 {person_id} 的基准数据")
                return None
            
            if len(results) > 1:
                logger.warning(f"人员 {person_id} 存在多条基准数据，返回第一条")
            
            # 转换为字典格式
            baseline_record = results.iloc[0].to_dict()
            
            logger.debug(f"成功获取人员 {person_id} 的基准数据")
            return baseline_record
            
        except Exception as e:
            logger.error(f"获取基准数据失败: {str(e)}")
            return None
    
    def update_baseline_for_person(
        self,
        person_id: str,
        updates: Dict[str, Any],
        id_type: str = "工号"
    ) -> bool:
        """
        更新指定人员的基准工资数据
        
        Args:
            person_id: 人员标识
            updates: 更新的字段和值
            id_type: 标识类型
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if self.baseline_data is None:
                logger.error("基准数据未加载，无法更新")
                return False
            
            # 查找人员记录
            query_field = self._get_query_field(id_type)
            mask = self.baseline_data[query_field] == person_id
            
            if not mask.any():
                logger.warning(f"未找到人员 {person_id}，无法更新基准数据")
                return False
            
            # 更新数据
            for field, value in updates.items():
                if field in self.baseline_data.columns:
                    self.baseline_data.loc[mask, field] = value
                    logger.debug(f"更新人员 {person_id} 的 {field}: {value}")
                else:
                    logger.warning(f"字段 {field} 不存在于基准数据中")
            
            # 更新时间戳
            self.last_update_time = datetime.now()
            
            logger.info(f"成功更新人员 {person_id} 的基准数据")
            return True
            
        except Exception as e:
            logger.error(f"更新基准数据失败: {str(e)}")
            return False
    
    def get_baseline_statistics(self) -> Dict[str, Any]:
        """
        获取基准数据统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            if self.baseline_data is None:
                return {"error": "基准数据未加载"}
            
            stats = {
                "总记录数": len(self.baseline_data),
                "版本": self.baseline_version,
                "最后更新时间": self.last_update_time.strftime("%Y-%m-%d %H:%M:%S") if self.last_update_time else "未知",
                "数据列数": len(self.baseline_data.columns),
                "数据列": list(self.baseline_data.columns)
            }
            
            # 计算数值字段的统计信息
            numeric_columns = self.baseline_data.select_dtypes(include=['number']).columns
            for col in numeric_columns:
                if '工资' in col or '金额' in col or '津贴' in col:
                    stats[f"{col}_统计"] = {
                        "平均值": round(self.baseline_data[col].mean(), 2),
                        "最大值": round(self.baseline_data[col].max(), 2),
                        "最小值": round(self.baseline_data[col].min(), 2),
                        "总和": round(self.baseline_data[col].sum(), 2)
                    }
            
            logger.info("基准数据统计信息计算完成")
            return stats
            
        except Exception as e:
            logger.error(f"计算基准数据统计信息失败: {str(e)}")
            return {"error": str(e)}
    
    def _validate_salary_data(self, data: pd.DataFrame) -> bool:
        """
        验证工资数据格式
        
        Args:
            data: 工资数据DataFrame
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查基本要求
            if data.empty:
                logger.error("工资数据为空")
                return False
            
            # 检查必需字段
            required_fields = ["工号", "姓名"]  # 可以从配置中读取
            missing_fields = [field for field in required_fields if field not in data.columns]
            if missing_fields:
                logger.error(f"缺少必需字段: {missing_fields}")
                return False
            
            # 检查数据完整性
            for field in required_fields:
                null_count = data[field].isnull().sum()
                if null_count > 0:
                    logger.warning(f"字段 {field} 有 {null_count} 个空值")
            
            logger.info("工资数据格式验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def _preprocess_salary_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预处理工资数据
        
        Args:
            data: 原始工资数据
            
        Returns:
            pd.DataFrame: 预处理后的数据
        """
        try:
            processed_data = data.copy()
            
            # 清理字符串字段的空格
            string_columns = processed_data.select_dtypes(include=['object']).columns
            for col in string_columns:
                processed_data[col] = processed_data[col].astype(str).str.strip()
            
            # 标准化数值字段
            numeric_columns = processed_data.select_dtypes(include=['number']).columns
            for col in numeric_columns:
                processed_data[col] = pd.to_numeric(processed_data[col], errors='coerce')
            
            # 添加预处理时间戳
            processed_data['_processed_time'] = datetime.now()
            
            logger.info(f"数据预处理完成，处理 {len(processed_data)} 条记录")
            return processed_data
            
        except Exception as e:
            logger.error(f"数据预处理失败: {str(e)}")
            return data
    
    def _create_baseline_structure(self, data: pd.DataFrame, period: str) -> pd.DataFrame:
        """
        创建基准数据结构
        
        Args:
            data: 预处理后的工资数据
            period: 数据周期
            
        Returns:
            pd.DataFrame: 基准数据
        """
        try:
            baseline_data = data.copy()
            
            # 添加基准数据元信息
            baseline_data['_baseline_period'] = period
            baseline_data['_baseline_created'] = datetime.now()
            baseline_data['_baseline_version'] = f"{period}_baseline"
            
            # 添加基准数据标识
            baseline_data['_is_baseline'] = True
            
            logger.info(f"基准数据结构创建完成，周期: {period}")
            return baseline_data
            
        except Exception as e:
            logger.error(f"创建基准数据结构失败: {str(e)}")
            return data
    
    def _save_baseline_data(self) -> bool:
        """
        保存基准数据到数据库
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 这里应该调用数据存储模块的接口
            # 暂时记录日志，实际实现需要与DataStorage模块集成
            logger.info("基准数据保存功能暂未实现，需要集成数据存储模块")
            return True
            
        except Exception as e:
            logger.error(f"保存基准数据失败: {str(e)}")
            return False
    
    def _load_baseline_from_db(self, version: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从数据库加载基准数据
        
        Args:
            version: 版本号
            
        Returns:
            pd.DataFrame: 基准数据
        """
        try:
            # 这里应该调用数据存储模块的接口
            # 暂时返回None，实际实现需要与DataStorage模块集成
            logger.info("基准数据加载功能暂未实现，需要集成数据存储模块")
            return None
            
        except Exception as e:
            logger.error(f"从数据库加载基准数据失败: {str(e)}")
            return None
    
    def _get_query_field(self, id_type: str) -> str:
        """
        根据标识类型获取查询字段名
        
        Args:
            id_type: 标识类型
            
        Returns:
            str: 查询字段名
        """
        field_mapping = {
            "工号": "工号",
            "身份证号": "身份证号",
            "姓名": "姓名"
        }
        return field_mapping.get(id_type, "工号") 