#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的表类型映射和配置生成
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_table_type_mapping():
    """测试表类型映射修复"""
    print("测试表类型映射修复")
    
    try:
        from src.services.table_data_service import TableDataService
        from src.core.unified_state_manager import get_unified_state_manager
        
        # 初始化服务
        state_manager = get_unified_state_manager()
        table_service = TableDataService(state_manager)
        
        # 测试A岗职工表类型映射
        test_table_name = "salary_data_2025_08_a_grade_employees"
        table_type = table_service._extract_table_type_from_name(test_table_name)
        
        print(f"表名: {test_table_name}")
        print(f"映射结果: {table_type}")
        
        if table_type == 'a_grade_employees':
            print("SUCCESS: A岗职工表类型映射修复成功!")
            return True
        else:
            print(f"FAILED: A岗职工表类型映射仍然错误: {table_type}")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试修复效果")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_table_type_mapping()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  表类型映射测试: {'通过' if test1_passed else '失败'}")
    
    if test1_passed:
        print("所有修复测试通过！系统已经正确修复")
    else:
        print("部分测试失败，需要进一步检查")