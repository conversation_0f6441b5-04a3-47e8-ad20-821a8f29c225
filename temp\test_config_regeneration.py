#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置重新生成是否正确
"""

import sys
import json
from pathlib import Path

sys.path.append('.')

try:
    # 模拟系统启动时的配置初始化
    from src.modules.data_import.config_sync_manager import ConfigSyncManager
    
    # 创建配置管理器，这会触发配置重新生成
    config_manager = ConfigSyncManager()
    
    print("=== Config Regeneration Test ===")
    
    # 检查配置文件是否已重新生成
    config_path = Path("state/data/field_mappings.json")
    if config_path.exists():
        print("Config file regenerated successfully")
        
        # 读取并检查内容
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        table_mappings = config_data.get("table_mappings", {})
        
        # 检查关键配置
        if "active_employees" in table_mappings:
            active_config = table_mappings["active_employees"]
            if isinstance(active_config, dict):
                # 检查第一层映射
                fields = list(active_config.keys())
                if "人员类别" in fields and "人员类别代码" in fields:
                    pos_type = fields.index("人员类别")
                    pos_code = fields.index("人员类别代码")
                    print(f"active_employees first level: 人员类别({pos_type+1}) vs 人员类别代码({pos_code+1})")
                    if pos_type < pos_code:
                        print("  CORRECT order in first level")
                    else:
                        print("  WRONG order in first level")
                
                # 检查field_mappings
                if "field_mappings" in active_config:
                    field_mappings = active_config["field_mappings"]
                    mapping_fields = list(field_mappings.keys())
                    if "employee_type" in mapping_fields and "employee_type_code" in mapping_fields:
                        pos_type = mapping_fields.index("employee_type")
                        pos_code = mapping_fields.index("employee_type_code")
                        print(f"active_employees field_mappings: employee_type({pos_type+1}) vs employee_type_code({pos_code+1})")
                        if pos_type < pos_code:
                            print("  CORRECT order in field_mappings")
                        else:
                            print("  WRONG order in field_mappings")
        
        print("\nChecking template fields:")
        # 检查模板是否正确
        templates = config_data.get("field_templates", {})
        if "全部在职人员工资表" in templates:
            template = templates["全部在职人员工资表"]
            template_fields = list(template.keys())
            if "employee_type" in template_fields and "employee_type_code" in template_fields:
                pos_type = template_fields.index("employee_type")
                pos_code = template_fields.index("employee_type_code")
                print(f"Template 全部在职人员工资表: employee_type({pos_type+1}) vs employee_type_code({pos_code+1})")
                if pos_type < pos_code:
                    print("  TEMPLATE FIXED!")
                else:
                    print("  TEMPLATE STILL WRONG!")
            
    else:
        print("ERROR: Config file not regenerated")
        
except Exception as e:
    print(f"Error during test: {e}")
    import traceback
    traceback.print_exc()