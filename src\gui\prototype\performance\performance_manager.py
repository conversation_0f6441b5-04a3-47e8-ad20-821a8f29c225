#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能管理器
统一管理所有性能优化组件，提供集中的性能监控和缓存管理
"""

import time
from typing import Dict, Any, Optional, List
import pandas as pd
from src.utils.log_config import setup_logger
from .data_preload_cache import DataPreloadCache, TableStateCache
from .header_config_cache import HeaderConfigCache, FieldMappingCache


class PerformanceManager:
    """性能管理器
    
    统一管理所有性能优化组件，提供集中的性能监控和缓存管理
    """
    
    def __init__(self, 
                 data_cache_size: int = 100,
                 data_cache_ttl: int = 300):
        """初始化性能管理器
        
        Args:
            data_cache_size: 数据缓存最大条目数
            data_cache_ttl: 数据缓存TTL（秒）
        """
        self.logger = setup_logger(__name__ + ".PerformanceManager")
        
        # 初始化各种缓存组件
        self.data_cache = DataPreloadCache(max_size=data_cache_size, ttl_seconds=data_cache_ttl)
        self.state_cache = TableStateCache()
        self.header_cache = HeaderConfigCache()
        self.mapping_cache = FieldMappingCache()
        
        # 性能监控
        self.performance_stats = {
            'data_load_times': [],
            'cache_operations': 0,
            'total_requests': 0
        }
        
        self.logger.info("🚀 性能管理器初始化完成")
    
    # ==================== 数据缓存相关方法 ====================
    
    def get_cached_data(self, table_name: str, page: int, per_page: int,
                       sort_columns: Optional[list] = None,
                       filters: Optional[dict] = None) -> Optional[pd.DataFrame]:
        """获取缓存的数据
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页条数
            sort_columns: 排序列
            filters: 过滤条件
            
        Returns:
            缓存的DataFrame或None
        """
        start_time = time.time()
        
        result = self.data_cache.get_cached_data(
            table_name, page, per_page, sort_columns, filters
        )
        
        self.performance_stats['cache_operations'] += 1
        self.performance_stats['total_requests'] += 1
        
        elapsed_time = (time.time() - start_time) * 1000
        self.logger.debug(f"🔧 [性能] 数据缓存查询耗时: {elapsed_time:.2f}ms")
        
        return result
    
    def cache_data(self, table_name: str, page: int, per_page: int, data: pd.DataFrame,
                   sort_columns: Optional[list] = None,
                   filters: Optional[dict] = None) -> None:
        """缓存数据
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页条数
            data: 要缓存的DataFrame
            sort_columns: 排序列
            filters: 过滤条件
        """
        start_time = time.time()
        
        self.data_cache.cache_data(
            table_name, page, per_page, data, sort_columns, filters
        )
        
        self.performance_stats['cache_operations'] += 1
        
        elapsed_time = (time.time() - start_time) * 1000
        self.logger.debug(f"🔧 [性能] 数据缓存存储耗时: {elapsed_time:.2f}ms")
    
    def invalidate_table_data(self, table_name: str) -> int:
        """使指定表的数据缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            被移除的缓存条目数
        """
        return self.data_cache.invalidate_table(table_name)
    
    # ==================== 状态缓存相关方法 ====================
    
    def get_cached_table_state(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取缓存的表格状态
        
        Args:
            table_name: 表名
            
        Returns:
            缓存的状态字典或None
        """
        return self.state_cache.get_cached_state(table_name)
    
    def cache_table_state(self, table_name: str, state: Dict[str, Any]) -> None:
        """缓存表格状态
        
        Args:
            table_name: 表名
            state: 状态字典
        """
        self.state_cache.cache_state(table_name, state)
    
    def invalidate_table_state(self, table_name: str) -> bool:
        """使指定表的状态缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功移除缓存
        """
        return self.state_cache.invalidate_state(table_name)
    
    # ==================== 表头缓存相关方法 ====================
    
    def get_cached_header_config(self, columns: List[str]) -> Optional[Dict[str, Any]]:
        """获取缓存的表头配置
        
        Args:
            columns: 列名列表
            
        Returns:
            缓存的表头配置或None
        """
        return self.header_cache.get_cached_header_config(columns)
    
    def cache_header_config(self, columns: List[str], config: Dict[str, Any]) -> None:
        """缓存表头配置
        
        Args:
            columns: 列名列表
            config: 表头配置字典
        """
        self.header_cache.cache_header_config(columns, config)
    
    def get_cached_sort_indicators(self, table_name: str) -> Optional[Dict[int, str]]:
        """获取缓存的排序指示器状态
        
        Args:
            table_name: 表名
            
        Returns:
            排序指示器状态字典或None
        """
        return self.header_cache.get_cached_sort_indicators(table_name)
    
    def cache_sort_indicators(self, table_name: str, indicators: Dict[int, str]) -> None:
        """缓存排序指示器状态
        
        Args:
            table_name: 表名
            indicators: 排序指示器状态字典
        """
        self.header_cache.cache_sort_indicators(table_name, indicators)
    
    def update_sort_indicator(self, table_name: str, column_index: int, sort_state: str) -> None:
        """更新单个排序指示器
        
        Args:
            table_name: 表名
            column_index: 列索引
            sort_state: 排序状态
        """
        self.header_cache.update_sort_indicator(table_name, column_index, sort_state)
    
    # ==================== 字段映射缓存相关方法 ====================
    
    def get_cached_field_mapping(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取缓存的字段映射
        
        Args:
            table_name: 表名
            
        Returns:
            缓存的字段映射或None
        """
        return self.mapping_cache.get_cached_mapping(table_name)
    
    def cache_field_mapping(self, table_name: str, mapping: Dict[str, Any]) -> None:
        """缓存字段映射
        
        Args:
            table_name: 表名
            mapping: 字段映射字典
        """
        self.mapping_cache.cache_mapping(table_name, mapping)
    
    def invalidate_field_mapping(self, table_name: str) -> bool:
        """使指定表的字段映射缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功移除缓存
        """
        return self.mapping_cache.invalidate_mapping(table_name)
    
    # ==================== 统一管理方法 ====================
    
    def invalidate_all_table_cache(self, table_name: str) -> None:
        """使指定表的所有缓存失效
        
        Args:
            table_name: 表名
        """
        data_removed = self.invalidate_table_data(table_name)
        state_removed = self.invalidate_table_state(table_name)
        mapping_removed = self.invalidate_field_mapping(table_name)
        
        self.header_cache.invalidate_table_cache(table_name)
        
        total_removed = data_removed + (1 if state_removed else 0) + (1 if mapping_removed else 0)
        
        if total_removed > 0:
            self.logger.info(f"🔧 [性能] 表 {table_name} 的所有缓存已失效，共移除 {total_removed} 个条目")
    
    def clear_all_cache(self) -> None:
        """清空所有缓存"""
        self.data_cache.clear_cache()
        self.state_cache.clear_cache()
        self.header_cache.clear_all_cache()
        self.mapping_cache.clear_cache()
        
        self.logger.info("🔧 [性能] 所有缓存已清空")
    
    def record_data_load_time(self, load_time_ms: float) -> None:
        """记录数据加载时间
        
        Args:
            load_time_ms: 加载时间（毫秒）
        """
        self.performance_stats['data_load_times'].append(load_time_ms)
        
        # 只保留最近100次的记录
        if len(self.performance_stats['data_load_times']) > 100:
            self.performance_stats['data_load_times'] = self.performance_stats['data_load_times'][-100:]
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合性能统计信息
        
        Returns:
            包含所有性能统计信息的字典
        """
        data_stats = self.data_cache.get_cache_stats()
        header_stats = self.header_cache.get_cache_stats()
        mapping_stats = self.mapping_cache.get_cache_stats()
        
        # 计算平均加载时间
        load_times = self.performance_stats['data_load_times']
        avg_load_time = sum(load_times) / len(load_times) if load_times else 0
        
        return {
            'data_cache': data_stats,
            'header_cache': header_stats,
            'mapping_cache': mapping_stats,
            'performance': {
                'avg_load_time_ms': f"{avg_load_time:.2f}",
                'cache_operations': self.performance_stats['cache_operations'],
                'total_requests': self.performance_stats['total_requests'],
                'recent_load_samples': len(load_times)
            }
        }
    
    def log_comprehensive_stats(self) -> None:
        """记录综合性能统计信息到日志"""
        self.data_cache.log_cache_stats()
        self.header_cache.log_cache_stats()
        self.mapping_cache.log_cache_stats()
        
        stats = self.get_comprehensive_stats()
        perf_stats = stats['performance']
        
        self.logger.info(f"🔧 [性能总览] 平均加载时间: {perf_stats['avg_load_time_ms']}ms, "
                        f"缓存操作: {perf_stats['cache_operations']}, "
                        f"总请求: {perf_stats['total_requests']}")


# 全局性能管理器实例
_performance_manager: Optional[PerformanceManager] = None


def get_performance_manager() -> PerformanceManager:
    """获取全局性能管理器实例
    
    Returns:
        性能管理器实例
    """
    global _performance_manager
    if _performance_manager is None:
        _performance_manager = PerformanceManager()
    return _performance_manager


def reset_performance_manager() -> None:
    """重置全局性能管理器实例（主要用于测试）"""
    global _performance_manager
    _performance_manager = None
