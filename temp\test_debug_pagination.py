#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页调用调试测试脚本
"""

def test_pagination_calls():
    """测试分页调用是否发生"""
    print("🔍 分页调用调试测试")
    print("\n启动系统后，请：")
    print("1. 导入数据")
    print("2. 切换分页")
    print("3. 查看日志中的以下标记：")
    print("   - 🔍 [调试] 即将调用set_pagination_state")
    print("   - 🔍 [调试] set_pagination_state调用完成")
    print("   - 🔍 [表格调试] set_pagination_state 开始")
    print("   - 🔧 [分页行号修复] 接收分页状态")
    print("\n如果看不到这些日志，说明调用路径有问题")


if __name__ == "__main__":
    test_pagination_calls()
