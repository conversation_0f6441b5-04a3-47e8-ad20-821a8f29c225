#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的修复验证脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试关键模块导入"""
    print("🔧 [测试] 开始测试模块导入...")
    
    try:
        # 测试表格组件导入
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        print("✅ VirtualizedExpandableTable 导入成功")
        
        # 测试数据库管理器导入
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        print("✅ DynamicTableManager 导入成功")
        
        # 测试线程安全工具导入
        from src.utils.thread_safe_timer import ThreadSafeTimer
        print("✅ ThreadSafeTimer 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_table_attributes():
    """测试表格属性（不创建GUI）"""
    print("🔧 [测试] 开始测试表格属性...")
    
    try:
        # 检查源代码中是否包含修复的属性初始化
        table_file = project_root / "src" / "gui" / "prototype" / "widgets" / "virtualized_expandable_table.py"
        
        if not table_file.exists():
            print(f"❌ 表格文件不存在: {table_file}")
            return False
        
        with open(table_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复是否存在
        fixes_to_check = [
            "_use_unified_format = False",
            "_format_manager = None", 
            "P0-CRITICAL修复",
            "_render_data_with_fallback_method"
        ]
        
        missing_fixes = []
        for fix in fixes_to_check:
            if fix not in content:
                missing_fixes.append(fix)
        
        if missing_fixes:
            print(f"❌ 缺少修复: {missing_fixes}")
            return False
        else:
            print("✅ 所有关键修复都已应用")
            return True
            
    except Exception as e:
        print(f"❌ 属性测试失败: {e}")
        return False

def test_main_window_fixes():
    """测试主窗口修复"""
    print("🔧 [测试] 开始测试主窗口修复...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        if not main_window_file.exists():
            print(f"❌ 主窗口文件不存在: {main_window_file}")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查时序修复
        timing_fixes = [
            "_update_navigation_if_needed_immediate",
            "_navigate_and_refresh_data",
            "P1-CRITICAL修复"
        ]
        
        missing_fixes = []
        for fix in timing_fixes:
            if fix not in content:
                missing_fixes.append(fix)
        
        if missing_fixes:
            print(f"❌ 缺少时序修复: {missing_fixes}")
            return False
        else:
            print("✅ 时序修复已正确应用")
            return True
            
    except Exception as e:
        print(f"❌ 主窗口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 [开始] 简单修复验证测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("表格属性修复测试", test_table_attributes), 
        ("主窗口修复测试", test_main_window_fixes)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔧 [执行] {test_name}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🔧 [结果] 通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        print("🎉 [成功] 所有验证都通过！修复已正确应用。")
        print("\n📋 [建议] 现在可以启动系统测试数据导入功能：")
        print("  1. 启动系统: python src/main.py")
        print("  2. 导入测试数据")
        print("  3. 检查主界面是否正确显示数据")
        return True
    else:
        print(f"⚠️ [警告] 有 {failed} 个验证失败。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
