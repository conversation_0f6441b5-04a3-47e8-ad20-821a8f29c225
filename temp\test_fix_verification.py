#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证测试脚本
测试ArchitectureFactory的get_unified_format_manager方法是否正常工作
"""

import sys
import os
sys.path.append('.')

def test_architecture_factory_fix():
    """测试架构工厂修复"""
    print("🔍 测试ArchitectureFactory修复...")
    
    try:
        # 导入必要的模块
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        print("✅ 模块导入成功")
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化数据库管理器
        db_manager = DynamicTableManager("data/salary_system.db")
        
        print("✅ 核心组件初始化成功")
        
        # 获取架构工厂
        factory = get_architecture_factory(db_manager, config_manager)
        
        print("✅ 架构工厂获取成功")
        
        # 初始化架构
        if factory.initialize_architecture():
            print("✅ 架构初始化成功")
        else:
            print("❌ 架构初始化失败")
            return False
        
        # 测试get_unified_format_manager方法
        try:
            unified_format_manager = factory.get_unified_format_manager()
            print("✅ get_unified_format_manager方法调用成功")
            print(f"✅ UnifiedFormatManager实例: {type(unified_format_manager)}")
            
            # 测试格式管理器的基本功能
            system_status = unified_format_manager.get_system_status()
            print(f"✅ 系统状态: {system_status}")
            
            return True
            
        except Exception as e:
            print(f"❌ get_unified_format_manager方法调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_format_manager_functionality():
    """测试统一格式管理器功能"""
    print("\n🔍 测试UnifiedFormatManager功能...")
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 创建格式管理器实例
        format_manager = UnifiedFormatManager(
            config_path="state/format_config.json",
            field_mapping_path="state/data/field_mappings.json"
        )
        
        print("✅ UnifiedFormatManager实例创建成功")
        
        # 测试基本功能
        supported_types = format_manager.get_supported_table_types()
        print(f"✅ 支持的表类型: {len(supported_types)}")
        
        # 测试字段映射获取
        field_mappings = format_manager.get_field_mappings('active_employees')
        print(f"✅ 在职员工字段映射: {len(field_mappings)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"❌ UnifiedFormatManager功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚨 架构修复验证测试")
    print("=" * 50)
    
    # 测试1: ArchitectureFactory修复
    result1 = test_architecture_factory_fix()
    
    # 测试2: UnifiedFormatManager功能
    result2 = test_unified_format_manager_functionality()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    tests = [
        ("ArchitectureFactory修复", result1),
        ("UnifiedFormatManager功能", result2)
    ]
    
    success_count = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(tests)} 测试通过")
    
    if success_count == len(tests):
        print("\n🎉 所有测试通过！修复成功！")
        print("\n📋 修复摘要:")
        print("  ✅ ArchitectureFactory.get_unified_format_manager() 方法已添加")
        print("  ✅ UnifiedFormatManager可以正常创建和使用")
        print("  ✅ 事件总线和状态管理器集成正常")
        print("  ✅ 新架构格式化系统可用")
        return True
    else:
        print(f"\n⚠️  {len(tests) - success_count} 项测试失败，修复未完全成功")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)