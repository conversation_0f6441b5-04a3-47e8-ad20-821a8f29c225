# -*- mode: python ; coding: utf-8 -*-


block_cipher = None


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('C:\\test\\salary_changes\\salary_changes\\template\\关于发放教职工薪酬的请示-OA流程\\审议2025年5月教职工薪酬发放事宜-最终版 - OA流程 - 最终版.docx', 'template\\关于发放教职工薪酬的请示-OA流程'), ('C:\\test\\salary_changes\\salary_changes\\template\\关于发放教职工薪酬的请示-部务会\\审议2025年5月教职工薪酬发放事宜-最终版-2025.5.13.docx', 'template\\关于发放教职工薪酬的请示-部务会'), ('C:\\test\\salary_changes\\salary_changes\\template\\工资审批表\\2025年5月工资呈报表 -最终版.xlsx', 'template\\工资审批表'), ('C:\\test\\salary_changes\\salary_changes\\config.json', '.'), ('C:\\test\\salary_changes\\salary_changes\\user_preferences.json', '.'), ('C:\\test\\salary_changes\\salary_changes\\test_config.json', '.')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'src.core.application_service', 'src.core.config_manager', 'src.gui.modern_main_window', 'src.gui.modern_style', 'src.gui.toast_system', 'src.gui.main_window', 'src.gui.windows', 'src.gui.dialogs', 'src.gui.widgets', 'src.modules.data_import.excel_importer', 'src.modules.change_detection.change_detector', 'src.modules.report_generation.report_manager', 'src.modules.data_storage.database_manager', 'src.modules.system_config.config_manager', 'src.modules.system_config', 'src.utils.log_config'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['pytest', 'pytest_cov', 'pytest_qt', '_pytest', 'black', 'flake8', 'mypy', 'sphinx', 'pydoc', 'tkinter', 'turtle', 'test', 'unittest', 'distutils', 'IPython', 'jupyter', 'notebook', 'matplotlib.tests', 'pandas.tests', 'numpy.tests'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='salary_changes_system',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='C:\\test\\salary_changes\\salary_changes\\version_info.txt',
)
