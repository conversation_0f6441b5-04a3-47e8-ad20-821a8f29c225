#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境性能监控模块

功能：
- 实时性能指标收集
- 性能告警机制
- 性能报告生成
- 系统健康状态监控
"""

import psutil
import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

from .log_config import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_usage: float
    memory_percent: float
    disk_usage: float
    process_count: int
    response_time: float
    throughput: float


@dataclass
class AlertThreshold:
    """告警阈值配置"""
    cpu_percent: float = 80.0
    memory_percent: float = 85.0
    disk_usage_percent: float = 90.0
    response_time_ms: float = 3000.0
    memory_usage_mb: float = 1024.0


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化性能监控器
        
        Args:
            config: 监控配置参数
        """
        self.config = config or {}
        self.alert_threshold = AlertThreshold(**self.config.get('thresholds', {}))
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 监控间隔（秒）
        self.monitor_interval = self.config.get('monitor_interval', 30)
        
        # 历史数据保留时间（小时）
        self.retention_hours = self.config.get('retention_hours', 24)
        
        # 性能日志文件
        self.log_dir = Path(self.config.get('log_dir', 'logs'))
        self.log_dir.mkdir(exist_ok=True)
        self.performance_log = self.log_dir / 'performance_monitor.log'
        
        logger.info("性能监控器初始化完成")
    
    def start_monitoring(self) -> None:
        """开始性能监控"""
        if self.monitoring:
            logger.warning("性能监控已在运行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("性能监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("性能监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self._store_metrics(metrics)
                self._check_alerts(metrics)
                self._cleanup_old_metrics()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                time.sleep(self.monitor_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_usage = memory.used / (1024 * 1024)  # MB
        memory_percent = memory.percent
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 响应时间（模拟）
        response_time = self._measure_response_time()
        
        # 吞吐量（模拟）
        throughput = self._calculate_throughput()
        
        metrics = PerformanceMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_usage=memory_usage,
            memory_percent=memory_percent,
            disk_usage=disk_usage,
            process_count=process_count,
            response_time=response_time,
            throughput=throughput
        )
        
        return metrics
    
    def _measure_response_time(self) -> float:
        """测量系统响应时间"""
        # 模拟响应时间测量
        start_time = time.time()
        
        # 执行一个简单的系统操作
        try:
            psutil.cpu_percent()
            psutil.virtual_memory()
        except Exception:
            pass
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        
        return response_time
    
    def _calculate_throughput(self) -> float:
        """计算系统吞吐量"""
        # 简化的吞吐量计算
        # 实际应用中应该基于具体的业务指标
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent
        
        # 基于资源使用情况计算相对吞吐量
        throughput = max(0, 100 - (cpu_usage + memory_usage) / 2)
        
        return throughput
    
    def _store_metrics(self, metrics: PerformanceMetrics) -> None:
        """存储性能指标"""
        self.metrics_history.append(metrics)
        
        # 写入日志文件
        with open(self.performance_log, 'a', encoding='utf-8') as f:
            f.write(f"{json.dumps(asdict(metrics), ensure_ascii=False)}\n")
    
    def _check_alerts(self, metrics: PerformanceMetrics) -> None:
        """检查告警条件"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_percent > self.alert_threshold.cpu_percent:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        # 内存告警
        if metrics.memory_percent > self.alert_threshold.memory_percent:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        if metrics.memory_usage > self.alert_threshold.memory_usage_mb:
            alerts.append(f"内存使用量过高: {metrics.memory_usage:.1f}MB")
        
        # 磁盘告警
        if metrics.disk_usage > self.alert_threshold.disk_usage_percent:
            alerts.append(f"磁盘使用率过高: {metrics.disk_usage:.1f}%")
        
        # 响应时间告警
        if metrics.response_time > self.alert_threshold.response_time_ms:
            alerts.append(f"响应时间过长: {metrics.response_time:.1f}ms")
        
        # 发送告警
        if alerts:
            self._send_alerts(alerts)
    
    def _send_alerts(self, alerts: List[str]) -> None:
        """发送告警通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for alert in alerts:
            logger.warning(f"[性能告警] {timestamp} - {alert}")
        
        # 可以在这里集成邮件、短信等告警通知方式
        # self._send_email_alert(alerts)
        # self._send_sms_alert(alerts)
    
    def _cleanup_old_metrics(self) -> None:
        """清理过期的性能指标"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        cutoff_timestamp = cutoff_time.isoformat()
        
        # 清理内存中的历史数据
        self.metrics_history = [
            m for m in self.metrics_history 
            if m.timestamp > cutoff_timestamp
        ]
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if not self.metrics_history:
            return None
        return self.metrics_history[-1]
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取性能指标摘要
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            性能指标摘要
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        cutoff_timestamp = cutoff_time.isoformat()
        
        recent_metrics = [
            m for m in self.metrics_history 
            if m.timestamp > cutoff_timestamp
        ]
        
        if not recent_metrics:
            return {}
        
        # 计算统计值
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        response_times = [m.response_time for m in recent_metrics]
        throughputs = [m.throughput for m in recent_metrics]
        
        summary = {
            'time_range_hours': hours,
            'sample_count': len(recent_metrics),
            'cpu_percent': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_percent': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'response_time_ms': {
                'avg': sum(response_times) / len(response_times),
                'max': max(response_times),
                'min': min(response_times)
            },
            'throughput': {
                'avg': sum(throughputs) / len(throughputs),
                'max': max(throughputs),
                'min': min(throughputs)
            }
        }
        
        return summary
    
    def generate_performance_report(self, hours: int = 24) -> str:
        """生成性能报告
        
        Args:
            hours: 报告时间范围（小时）
            
        Returns:
            性能报告内容
        """
        summary = self.get_metrics_summary(hours)
        current_metrics = self.get_current_metrics()
        
        if not summary or not current_metrics:
            return "暂无性能数据"
        
        report = f"""
# 系统性能报告

## 报告时间范围
- 统计时间: 最近 {hours} 小时
- 采样数量: {summary['sample_count']} 个
- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 当前状态
- CPU使用率: {current_metrics.cpu_percent:.1f}%
- 内存使用率: {current_metrics.memory_percent:.1f}%
- 内存使用量: {current_metrics.memory_usage:.1f}MB
- 磁盘使用率: {current_metrics.disk_usage:.1f}%
- 响应时间: {current_metrics.response_time:.1f}ms
- 系统吞吐量: {current_metrics.throughput:.1f}

## 性能统计 (最近{hours}小时)

### CPU使用率
- 平均: {summary['cpu_percent']['avg']:.1f}%
- 最高: {summary['cpu_percent']['max']:.1f}%
- 最低: {summary['cpu_percent']['min']:.1f}%

### 内存使用率
- 平均: {summary['memory_percent']['avg']:.1f}%
- 最高: {summary['memory_percent']['max']:.1f}%
- 最低: {summary['memory_percent']['min']:.1f}%

### 响应时间
- 平均: {summary['response_time_ms']['avg']:.1f}ms
- 最长: {summary['response_time_ms']['max']:.1f}ms
- 最短: {summary['response_time_ms']['min']:.1f}ms

### 系统吞吐量
- 平均: {summary['throughput']['avg']:.1f}
- 最高: {summary['throughput']['max']:.1f}
- 最低: {summary['throughput']['min']:.1f}

## 性能建议
"""
        
        # 添加性能建议
        if summary['cpu_percent']['avg'] > 70:
            report += "- ⚠️ CPU使用率较高，建议优化计算密集型操作\n"
        
        if summary['memory_percent']['avg'] > 75:
            report += "- ⚠️ 内存使用率较高，建议检查内存泄漏\n"
        
        if summary['response_time_ms']['avg'] > 2000:
            report += "- ⚠️ 响应时间较长，建议优化性能瓶颈\n"
        
        if summary['throughput']['avg'] < 50:
            report += "- ⚠️ 系统吞吐量较低，建议检查系统负载\n"
        
        return report


# 全局性能监控实例
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        # 从配置文件加载配置
        config = {
            'monitor_interval': 30,  # 30秒监控间隔
            'retention_hours': 24,   # 保留24小时数据
            'thresholds': {
                'cpu_percent': 80.0,
                'memory_percent': 85.0,
                'disk_usage_percent': 90.0,
                'response_time_ms': 3000.0,
                'memory_usage_mb': 1024.0
            }
        }
        _performance_monitor = PerformanceMonitor(config)
    
    return _performance_monitor


def start_performance_monitoring() -> None:
    """启动性能监控"""
    monitor = get_performance_monitor()
    monitor.start_monitoring()


def stop_performance_monitoring() -> None:
    """停止性能监控"""
    global _performance_monitor
    if _performance_monitor:
        _performance_monitor.stop_monitoring()


if __name__ == "__main__":
    # 测试性能监控
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    try:
        # 运行5分钟测试
        time.sleep(300)
        
        # 生成报告
        report = monitor.generate_performance_report(hours=1)
        print(report)
        
    finally:
        monitor.stop_monitoring() 