#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一批次修复验证脚本
验证P0-新2、P0-新4、P0-新5修复效果

执行方式：
python temp/batch1_fix_validation.py
"""

import sys
import os
import re
from pathlib import Path

def validate_p0_new2_data_type_fix():
    """验证P0-新2: 数据类型比较错误修复"""
    print("\n🔧 P0-新2验证: 数据类型比较错误修复")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_found = 0
        
        # 1. 检查列宽数据类型转换
        if "width_int = int(width) if isinstance(width, (str, float)) else width" in content:
            print("✅ 找到列宽数据类型转换修复")
            fixes_found += 1
        
        # 2. 检查排序列数据类型转换
        if "sort_column_int = int(sort_column) if isinstance(sort_column, (str, float)) else sort_column" in content:
            print("✅ 找到排序列数据类型转换修复")
            fixes_found += 1
            
        # 3. 检查错误处理
        if "🔧 [P0-新2修复] 无效的列宽值" in content:
            print("✅ 找到列宽错误处理")
            fixes_found += 1
            
        # 4. 检查排序列错误处理
        if "🔧 [P0-新2修复] 无效的排序列值" in content:
            print("✅ 找到排序列错误处理")
            fixes_found += 1
            
        # 5. 检查修复标识
        if "🔧 [P0-新2修复]" in content:
            print("✅ 找到P0-新2修复标识")
            fixes_found += 1
            
        if fixes_found >= 4:
            print(f"✅ P0-新2修复验证通过: {fixes_found}/5 项检查通过")
            return True
        else:
            print(f"❌ P0-新2修复验证失败: 仅{fixes_found}/5 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P0-新2验证异常: {e}")
        return False

def validate_p0_new4_method_params_fix():
    """验证P0-新4: UnifiedStateManager方法调用参数修复"""
    print("\n🔧 P0-新4验证: UnifiedStateManager方法调用参数修复")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_found = 0
        
        # 1. 检查正确的方法调用（只传递table_name）
        if "success = self.state_manager.restore_table_state_by_name(table_name)" in content:
            print("✅ 找到正确的方法调用: 只传递table_name参数")
            fixes_found += 1
        
        # 2. 检查不再传递多余的table_widget参数
        incorrect_call = "self.state_manager.restore_table_state_by_name(table_name, table_widget)"
        if incorrect_call not in content:
            print("✅ 确认已移除错误的table_widget参数")
            fixes_found += 1
            
        # 3. 检查修复标识
        if "🔧 [P0-新4修复]" in content:
            print("✅ 找到P0-新4修复标识")
            fixes_found += 1
            
        # 4. 检查日志消息更新
        if "🔧 [P0-新4修复] 通过StateManager恢复状态成功" in content:
            print("✅ 找到更新的成功日志消息")
            fixes_found += 1
            
        if fixes_found >= 3:
            print(f"✅ P0-新4修复验证通过: {fixes_found}/4 项检查通过")
            return True
        else:
            print(f"❌ P0-新4修复验证失败: 仅{fixes_found}/4 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P0-新4验证异常: {e}")
        return False

def validate_p0_new5_header_params_fix():
    """验证P0-新5: HeaderUpdateManager.force_update_all()参数修复"""
    print("\n🔧 P0-新5验证: HeaderUpdateManager.force_update_all()参数修复")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_found = 0
        
        # 1. 检查参数准备
        if "headers = list(df_final.columns)" in content:
            print("✅ 找到headers参数准备")
            fixes_found += 1
        
        # 2. 检查start_record参数准备
        if "start_record = 1  # 非分页模式起始记录为1" in content:
            print("✅ 找到start_record参数准备")
            fixes_found += 1
            
        # 3. 检查count参数准备
        if "count = len(df_final)" in content:
            print("✅ 找到count参数准备")
            fixes_found += 1
            
        # 4. 检查正确的方法调用
        method_call_pattern = r"force_update_all\(\s*headers=headers,\s*start_record=start_record,\s*count=count\s*\)"
        if re.search(method_call_pattern, content, re.MULTILINE):
            print("✅ 找到正确的force_update_all方法调用")
            fixes_found += 1
            
        # 5. 检查修复标识
        if "🔧 [P0-新5修复]" in content:
            print("✅ 找到P0-新5修复标识")
            fixes_found += 1
            
        # 6. 检查不再有无参数调用
        if "force_update_all()" not in content:
            print("✅ 确认已移除无参数的force_update_all()调用")
            fixes_found += 1
            
        if fixes_found >= 5:
            print(f"✅ P0-新5修复验证通过: {fixes_found}/6 项检查通过")
            return True
        else:
            print(f"❌ P0-新5修复验证失败: 仅{fixes_found}/6 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P0-新5验证异常: {e}")
        return False

def validate_no_remaining_errors():
    """验证修复后不应再出现的错误模式"""
    print("\n🔧 负面验证: 确认已修复的错误模式不再存在")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        error_patterns = [
            # P0-新2相关错误模式
            (r"width\s*>\s*0", "应该使用width_int > 0"),
            (r"sort_column\s*>=\s*0", "应该使用sort_column_int >= 0"), 
            
            # P0-新4相关错误模式
            ("restore_table_state_by_name(table_name, table_widget)", "应该只传递table_name"),
            
            # P0-新5相关错误模式
            ("force_update_all()", "应该传递headers, start_record, count参数"),
        ]
        
        remaining_errors = 0
        for pattern, description in error_patterns:
            if re.search(pattern, content):
                print(f"❌ 发现未修复的错误模式: {description}")
                remaining_errors += 1
            else:
                print(f"✅ 已修复: {description}")
        
        if remaining_errors == 0:
            print("✅ 负面验证通过: 所有已知错误模式已修复")
            return True
        else:
            print(f"❌ 负面验证失败: 仍有{remaining_errors}个错误模式未修复")
            return False
            
    except Exception as e:
        print(f"❌ 负面验证异常: {e}")
        return False

def main():
    """主验证函数"""
    print("🔧 第一批次修复验证测试")
    print("=" * 60)
    print("验证P0-新2、P0-新4、P0-新5修复效果")
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    tests = [
        ("P0-新2: 数据类型比较错误修复", validate_p0_new2_data_type_fix),
        ("P0-新4: UnifiedStateManager方法调用参数修复", validate_p0_new4_method_params_fix),
        ("P0-新5: HeaderUpdateManager参数修复", validate_p0_new5_header_params_fix),
        ("负面验证: 确认错误模式已修复", validate_no_remaining_errors),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n>>> 执行验证: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} - 验证通过")
            else:
                print(f"❌ {test_name} - 验证失败")
        except Exception as e:
            print(f"💥 {test_name} - 验证异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 第一批次修复验证结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} - {test_name}")
    
    print(f"\n📊 总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 第一批次修复验证全部通过！")
        print("✅ 数据类型比较错误已修复")
        print("✅ UnifiedStateManager方法调用参数已修复") 
        print("✅ HeaderUpdateManager参数缺失已修复")
        print("✅ 相关ERROR日志应显著减少")
        print("\n🚀 建议重新启动系统测试修复效果！")
        return True
    else:
        print(f"\n⚠️ {total - passed} 项验证失败，需要进一步检查")
        print("📋 建议检查失败项目的具体修复内容")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)