#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度工资异动处理系统 - PyInstaller打包配置

ARCHIVE阶段 - 打包部署配置
创建时间: 2025-01-22
功能: 配置PyInstaller打包参数和资源文件
"""

import os
import sys
from pathlib import Path
import PyInstaller.__main__

# 项目配置
PROJECT_NAME = "月度工资异动处理系统"
EXECUTABLE_NAME = "salary_changes_system"
VERSION = "1.0.0"
COMPANY_NAME = "月度工资异动处理系统开发团队"

# 路径配置
CURRENT_DIR = Path(__file__).parent
DIST_DIR = CURRENT_DIR / "dist"
BUILD_DIR = CURRENT_DIR / "build"
SPEC_FILE = CURRENT_DIR / f"{EXECUTABLE_NAME}.spec"

def get_data_files():
    """获取需要打包的数据文件列表"""
    data_files = []
    
    # 模板文件
    template_dir = CURRENT_DIR / "template"
    if template_dir.exists():
        for root, dirs, files in os.walk(template_dir):
            for file in files:
                if file.endswith(('.docx', '.xlsx', '.xls', '.doc')):
                    src_path = Path(root) / file
                    rel_path = src_path.relative_to(CURRENT_DIR)
                    dest_path = str(rel_path.parent)
                    data_files.append((str(src_path), dest_path))
    
    # 配置文件
    config_files = [
        "config.json",
        "user_preferences.json",
        "test_config.json"
    ]
    
    for config_file in config_files:
        config_path = CURRENT_DIR / config_file
        if config_path.exists():
            data_files.append((str(config_path), "."))
    
    # 资源文件夹（如果存在）
    resources_dir = CURRENT_DIR / "resources"
    if resources_dir.exists():
        for root, dirs, files in os.walk(resources_dir):
            for file in files:
                src_path = Path(root) / file
                rel_path = src_path.relative_to(CURRENT_DIR)
                dest_path = str(rel_path.parent)
                data_files.append((str(src_path), dest_path))
    
    return data_files

def get_hidden_imports():
    """获取隐藏导入模块列表"""
    hidden_imports = [
        # PyQt5核心模块 - 必需的基础导入
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        
        # 项目核心模块
        'src.core.application_service',
        'src.core.config_manager',
        
        # 现代化GUI模块 - 新增
        'src.gui.modern_main_window',
        'src.gui.modern_style',
        'src.gui.toast_system',
        'src.gui.main_window',
        'src.gui.windows',
        'src.gui.dialogs',
        'src.gui.widgets',
        
        # 业务模块
        'src.modules.data_import.excel_importer',
        'src.modules.change_detection.change_detector',
        'src.modules.report_generation.report_manager',
        'src.modules.data_storage.database_manager',
        'src.modules.system_config.config_manager',
        'src.modules.system_config',
        
        # 工具模块
        'src.utils.log_config',
        # 移除不存在的模块: 'src.utils.validators', 'src.utils.helpers'
    ]
    
    return hidden_imports

def get_exclude_modules():
    """获取需要排除的模块列表"""
    exclude_modules = [
        # 测试相关
        'pytest',
        'pytest_cov',
        'pytest_qt',
        '_pytest',
        
        # 开发工具
        'black',
        'flake8',
        'mypy',
        
        # 文档工具
        'sphinx',
        'pydoc',
        
        # 不需要的标准库
        'tkinter',
        'turtle',
        'test',
        'unittest',
        'distutils',
        
        # 不需要的第三方库
        'IPython',
        'jupyter',
        'notebook',
        'matplotlib.tests',
        'pandas.tests',
        'numpy.tests'
    ]
    
    return exclude_modules

def create_version_info():
    """创建版本信息文件"""
    version_info_content = f'''
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'{COMPANY_NAME}'),
            StringStruct(u'FileDescription', u'{PROJECT_NAME}'),
            StringStruct(u'FileVersion', u'{VERSION}'),
            StringStruct(u'InternalName', u'{EXECUTABLE_NAME}'),
            StringStruct(u'LegalCopyright', u'Copyright © 2025 {COMPANY_NAME}'),
            StringStruct(u'OriginalFilename', u'{EXECUTABLE_NAME}.exe'),
            StringStruct(u'ProductName', u'{PROJECT_NAME}'),
            StringStruct(u'ProductVersion', u'{VERSION}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    version_file = CURRENT_DIR / "version_info.txt"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_info_content)
    
    return str(version_file)

def build_executable():
    """构建可执行文件"""
    print("🚀 开始构建可执行文件...")
    
    # 创建版本信息文件
    version_info_file = create_version_info()
    
    # 获取配置参数
    data_files = get_data_files()
    hidden_imports = get_hidden_imports()
    exclude_modules = get_exclude_modules()
    
    # 构建PyInstaller参数 - 使用更安全的配置
    pyinstaller_args = [
        'main.py',  # 主入口文件
        '--name', EXECUTABLE_NAME,
        '--onefile',  # 单文件模式
        '--console',  # 启用控制台，查看错误信息
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不确认覆盖
        '--noupx',  # 不使用UPX压缩，避免兼容性问题
        f'--distpath={DIST_DIR}',
        f'--workpath={BUILD_DIR}',
        f'--specpath={CURRENT_DIR}',
        f'--version-file={version_info_file}',
    ]
    
    # 添加数据文件
    for src, dest in data_files:
        if os.name == 'nt':  # Windows
            pyinstaller_args.extend(['--add-data', f'{src};{dest}'])
        else:  # Unix-like
            pyinstaller_args.extend(['--add-data', f'{src}:{dest}'])
    
    # 添加隐藏导入
    for module in hidden_imports:
        pyinstaller_args.extend(['--hidden-import', module])
    
    # 添加排除模块
    for module in exclude_modules:
        pyinstaller_args.extend(['--exclude-module', module])
    
    # 添加图标（如果存在）
    icon_path = CURRENT_DIR / "resources" / "icons" / "app_icon.ico"
    if icon_path.exists():
        pyinstaller_args.extend(['--icon', str(icon_path)])
    
    # 注释掉运行时Hook，先尝试简单打包
    # pyinstaller_args.extend(['--runtime-hook', 'runtime_hook.py'])
    
    print(f"📦 构建参数配置完成")
    print(f"📊 数据文件: {len(data_files)} 个")
    print(f"📊 隐藏导入: {len(hidden_imports)} 个")
    print(f"📊 排除模块: {len(exclude_modules)} 个")
    
    try:
        # 执行PyInstaller
        print("🔧 正在执行PyInstaller...")
        PyInstaller.__main__.run(pyinstaller_args)
        
        # 检查构建结果
        exe_path = DIST_DIR / f"{EXECUTABLE_NAME}.exe"
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ 构建成功！")
            print(f"📁 输出文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            return True
        else:
            print("❌ 构建失败：未找到输出文件")
            return False
            
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        return False
    
    finally:
        # 清理版本信息文件
        if Path(version_info_file).exists():
            Path(version_info_file).unlink()

def test_executable():
    """测试可执行文件"""
    print("🧪 测试可执行文件...")
    
    exe_path = DIST_DIR / f"{EXECUTABLE_NAME}.exe"
    if not exe_path.exists():
        print("❌ 可执行文件不存在")
        return False
    
    try:
        # 这里可以添加自动化测试逻辑
        print("✅ 可执行文件存在且可访问")
        print("💡 建议手动测试应用程序功能")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print(f"🏗️  {PROJECT_NAME} - PyInstaller打包工具")
    print(f"📅 版本: {VERSION}")
    print(f"🎯 目标: Windows GUI应用程序")
    print("=" * 50)
    
    # 检查环境
    print("🔍 检查构建环境...")
    if not Path("main.py").exists():
        print("❌ 未找到主程序文件 main.py")
        return False
    
    if not Path("requirements.txt").exists():
        print("❌ 未找到依赖文件 requirements.txt")
        return False
    
    # 创建输出目录
    DIST_DIR.mkdir(exist_ok=True)
    BUILD_DIR.mkdir(exist_ok=True)
    
    # 构建可执行文件
    if build_executable():
        print("\n🎉 打包完成！")
        
        # 测试可执行文件
        if test_executable():
            print("\n✅ 打包和测试都成功完成！")
            print(f"📦 可执行文件位置: {DIST_DIR / f'{EXECUTABLE_NAME}.exe'}")
            print("🚀 现在可以在目标系统上运行应用程序")
            return True
        else:
            print("\n⚠️  打包成功但测试失败")
            return False
    else:
        print("\n❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 