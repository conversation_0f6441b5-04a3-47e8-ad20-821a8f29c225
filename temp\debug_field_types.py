
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.modules.format_management.field_registry import FieldRegistry

registry = FieldRegistry("state/data/field_mappings.json")
result = registry._validate_configuration_consistency()
print("配置验证:", result["summary"])

# 测试字段类型
field_type = registry.get_field_type("allowance", "pension_employees")
print("allowance字段类型:", field_type)

