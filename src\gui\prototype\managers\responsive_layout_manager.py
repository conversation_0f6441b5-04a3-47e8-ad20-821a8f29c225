"""
PyQt5重构高保真原型 - 响应式布局管理器

基于QResizeEvent的断点检测系统，支持1024px-1920px+的响应式布局适配。
实现了防抖优化、断点检测、布局配置管理等核心功能。

主要功能:
1. 5个响应式断点检测与切换
2. 防抖优化避免频繁布局更新  
3. 三段式布局适配管理
4. 组件参数动态调整
5. 与现有ModernMainWindow兼容
"""

from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QSize
from PyQt5.QtWidgets import QWidget
from typing import Dict, Any, Optional
import logging

from src.utils.log_config import setup_logger


class ResponsiveLayoutManager(QObject):
    """
    响应式布局管理器
    
    基于QResizeEvent的断点检测系统，实现Material Design响应式设计规范。
    支持5个断点的自动切换和布局参数的动态调整。
    """
    
    # 断点变化信号
    breakpoint_changed = pyqtSignal(str, dict)  # (breakpoint_name, config)
    layout_applied = pyqtSignal(str)  # (breakpoint_name)
    
    # 响应式断点定义 (基于Material Design规范)
    BREAKPOINTS = {
        'xs': 1024,   # 最小支持尺寸 (Small desktop)
        'sm': 1200,   # 小屏 (Medium desktop)  
        'md': 1400,   # 中屏 (Large desktop)
        'lg': 1600,   # 大屏 (Extra large desktop)
        'xl': 1920    # 超大屏 (Full HD+)
    }
    
    # 断点特定的布局配置
    LAYOUT_CONFIGS = {
        'xs': {
            'sidebar_width': 0,          # 隐藏侧边栏
            'sidebar_mode': 'drawer',    # 抽屉模式
            'font_scale': 0.9,           # 字体缩放90%
            'padding_scale': 0.8,        # 间距缩放80%
            'table_density': 'compact',  # 紧凑表格密度
            'button_size': 'small',      # 小按钮
            'header_height': 50,         # 较小的头部高度
            'footer_height': 40          # 较小的底部高度
        },
        'sm': {
            'sidebar_width': 260,
            'sidebar_mode': 'fixed',
            'font_scale': 0.95,
            'padding_scale': 0.9,
            'table_density': 'normal',
            'button_size': 'medium',
            'header_height': 55,
            'footer_height': 45
        },
        'md': {
            'sidebar_width': 280,
            'sidebar_mode': 'fixed',
            'font_scale': 1.0,
            'padding_scale': 1.0,
            'table_density': 'normal',
            'button_size': 'medium',
            'header_height': 60,
            'footer_height': 50
        },
        'lg': {
            'sidebar_width': 320,
            'sidebar_mode': 'fixed',
            'font_scale': 1.05,
            'padding_scale': 1.1,
            'table_density': 'comfortable',
            'button_size': 'large',
            'header_height': 65,
            'footer_height': 55
        },
        'xl': {
            'sidebar_width': 320,
            'sidebar_mode': 'fixed',
            'font_scale': 1.1,
            'padding_scale': 1.2,
            'table_density': 'comfortable',
            'button_size': 'large',
            'header_height': 70,
            'footer_height': 60
        }
    }
    
    def __init__(self, main_window: QWidget):
        """
        初始化响应式布局管理器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.current_breakpoint: Optional[str] = None
        self.current_config: Dict[str, Any] = {}
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        
        # 防抖定时器 (100ms防抖，避免频繁resize触发)
        self.debounce_timer = QTimer()
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.timeout.connect(self._apply_layout)
        
        # 布局应用器映射
        self.layout_appliers = {}
        
        self.logger.info("响应式布局管理器初始化完成")
    
    def register_layout_applier(self, component_name: str, applier_callback):
        """
        注册布局应用器
        
        Args:
            component_name: 组件名称
            applier_callback: 布局应用回调函数，接收(breakpoint, config)参数
        """
        self.layout_appliers[component_name] = applier_callback
        self.logger.debug(f"注册布局应用器: {component_name}")
    
    def handle_resize(self, size: QSize):
        """
        处理窗口大小变化事件
        
        Args:
            size: 新的窗口大小
        """
        self.pending_size = size
        # 启动防抖定时器，避免频繁resize时重复计算
        self.debounce_timer.start(100)  # 100ms防抖延迟
    
    def _determine_breakpoint(self, width: int) -> str:
        """
        根据宽度确定当前断点
        
        Args:
            width: 窗口宽度
            
        Returns:
            断点名称 ('xs', 'sm', 'md', 'lg', 'xl')
        """
        if width >= self.BREAKPOINTS['xl']:
            return 'xl'
        elif width >= self.BREAKPOINTS['lg']:
            return 'lg'
        elif width >= self.BREAKPOINTS['md']:
            return 'md'
        elif width >= self.BREAKPOINTS['sm']:
            return 'sm'
        else:
            return 'xs'
    
    def _apply_layout(self):
        """
        应用布局配置（防抖后执行）
        """
        if not hasattr(self, 'pending_size'):
            return
            
        width = self.pending_size.width()
        new_breakpoint = self._determine_breakpoint(width)
        
        # 检查断点是否发生变化
        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint
            self.current_config = self.LAYOUT_CONFIGS[new_breakpoint].copy()
            
            self.logger.info(f"断点切换: {new_breakpoint} (宽度: {width}px)")
            
            # 发射断点变化信号
            self.breakpoint_changed.emit(new_breakpoint, self.current_config)
            
            # 应用布局到所有注册的组件
            self._apply_to_components()
            
            # 发射布局应用完成信号
            self.layout_applied.emit(new_breakpoint)
    
    def _apply_to_components(self):
        """
        将当前布局配置应用到所有注册的组件
        """
        for component_name, applier in self.layout_appliers.items():
            try:
                applier(self.current_breakpoint, self.current_config)
                self.logger.debug(f"布局应用成功: {component_name}")
            except Exception as e:
                self.logger.error(f"布局应用失败 {component_name}: {e}")
    
    def force_update(self):
        """
        强制更新布局（无防抖）
        """
        if self.main_window:
            size = self.main_window.size()
            self.pending_size = size
            self._apply_layout()
    
    def get_current_breakpoint(self) -> Optional[str]:
        """
        获取当前断点
        
        Returns:
            当前断点名称，如果尚未初始化则返回None
        """
        return self.current_breakpoint
    
    def get_current_config(self) -> Dict[str, Any]:
        """
        获取当前布局配置
        
        Returns:
            当前布局配置字典
        """
        return self.current_config.copy()
    
    def is_mobile_layout(self) -> bool:
        """
        判断当前是否为移动端布局
        
        Returns:
            True表示移动端布局(xs断点)
        """
        return self.current_breakpoint == 'xs'
    
    def is_desktop_layout(self) -> bool:
        """
        判断当前是否为桌面端布局
        
        Returns:
            True表示桌面端布局(sm及以上断点)
        """
        return self.current_breakpoint in ['sm', 'md', 'lg', 'xl']
    
    def get_sidebar_width(self) -> int:
        """
        获取当前断点下的侧边栏宽度
        
        Returns:
            侧边栏宽度像素值
        """
        return self.current_config.get('sidebar_width', 280)
    
    def get_font_scale(self) -> float:
        """
        获取当前断点下的字体缩放比例
        
        Returns:
            字体缩放比例
        """
        return self.current_config.get('font_scale', 1.0)
    
    def get_padding_scale(self) -> float:
        """
        获取当前断点下的间距缩放比例
        
        Returns:
            间距缩放比例
        """
        return self.current_config.get('padding_scale', 1.0)


class ResponsiveLayoutApplier:
    """
    响应式布局应用器助手类
    
    提供标准的布局应用方法，简化组件的响应式适配实现。
    """
    
    def __init__(self, widget: QWidget):
        """
        初始化布局应用器
        
        Args:
            widget: 要应用响应式布局的部件
        """
        self.widget = widget
        self.logger = setup_logger(f"{__name__}.{widget.__class__.__name__}")
    
    def apply_font_scale(self, scale: float):
        """
        应用字体缩放
        
        Args:
            scale: 缩放比例
        """
        if hasattr(self.widget, 'font'):
            font = self.widget.font()
            base_size = 12  # 基础字体大小
            new_size = int(base_size * scale)
            font.setPointSize(new_size)
            self.widget.setFont(font)
    
    def apply_padding_scale(self, scale: float):
        """
        应用间距缩放
        
        Args:
            scale: 缩放比例
        """
        if hasattr(self.widget, 'layout') and self.widget.layout():
            layout = self.widget.layout()
            base_margin = 10  # 基础边距
            base_spacing = 5   # 基础间距
            
            new_margin = int(base_margin * scale)
            new_spacing = int(base_spacing * scale)
            
            layout.setContentsMargins(new_margin, new_margin, new_margin, new_margin)
            layout.setSpacing(new_spacing)
    
    def apply_size_constraints(self, config: Dict[str, Any]):
        """
        应用尺寸约束
        
        Args:
            config: 布局配置
        """
        if 'min_width' in config:
            self.widget.setMinimumWidth(config['min_width'])
        if 'max_width' in config:
            self.widget.setMaximumWidth(config['max_width'])
        if 'min_height' in config:
            self.widget.setMinimumHeight(config['min_height'])
        if 'max_height' in config:
            self.widget.setMaximumHeight(config['max_height']) 