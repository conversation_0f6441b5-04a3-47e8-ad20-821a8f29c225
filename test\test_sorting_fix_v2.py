#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序功能修复测试脚本 V2
测试表名获取、页码获取和信号连接修复
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_table_name_resolution():
    """测试表名获取逻辑"""
    print("🔧 开始测试表名获取逻辑...")
    
    # 模拟主窗口类
    class MockMainWindow:
        def __init__(self, scenario):
            self.scenario = scenario
            if scenario == "with_current_table_name":
                self.current_table_name = "test_table_main"
            elif scenario == "with_workspace_table_name":
                self.current_table_name = None
                self.main_workspace = MockWorkspace("test_table_workspace")
            elif scenario == "with_table_component_name":
                self.current_table_name = None
                self.main_workspace = MockWorkspaceWithTable("test_table_component")
            else:  # no_table_name
                self.current_table_name = None
        
        def _get_table_name_for_sort(self, table_name=None):
            """模拟获取表名的逻辑"""
            # 🔧 [排序修复] 多层级获取表名
            current_table_name = table_name or getattr(self, 'current_table_name', None)
            
            # 如果还是没有表名，尝试从工作区获取
            if not current_table_name and hasattr(self, 'main_workspace'):
                current_table_name = getattr(self.main_workspace, 'current_table_name', None)
            
            # 如果还是没有表名，尝试从表格组件获取
            if not current_table_name and hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'expandable_table'):
                current_table_name = getattr(self.main_workspace.expandable_table, 'current_table_name', None)
            
            return current_table_name
    
    class MockWorkspace:
        def __init__(self, table_name):
            self.current_table_name = table_name
    
    class MockWorkspaceWithTable:
        def __init__(self, table_name):
            self.current_table_name = None
            self.expandable_table = MockTable(table_name)
    
    class MockTable:
        def __init__(self, table_name):
            self.current_table_name = table_name
    
    # 测试不同场景
    scenarios = [
        ("with_current_table_name", "test_table_main"),
        ("with_workspace_table_name", "test_table_workspace"),
        ("with_table_component_name", "test_table_component"),
        ("no_table_name", None)
    ]
    
    for scenario, expected in scenarios:
        mock_window = MockMainWindow(scenario)
        result = mock_window._get_table_name_for_sort()
        
        if result == expected:
            print(f"✅ 表名获取测试通过: {scenario} -> {result}")
        else:
            print(f"❌ 表名获取测试失败: {scenario} -> 期望{expected}, 实际{result}")
            return False
    
    return True

def test_page_number_resolution():
    """测试页码获取逻辑"""
    print("\n🔧 开始测试页码获取逻辑...")
    
    # 模拟主窗口类
    class MockMainWindow:
        def __init__(self, scenario, page_num):
            self.scenario = scenario
            if scenario == "with_pagination_method":
                self.main_workspace = MockWorkspaceWithPaginationMethod(page_num)
            elif scenario == "with_pagination_attribute":
                self.main_workspace = MockWorkspaceWithPaginationAttribute(page_num)
            elif scenario == "with_pagination_state":
                self.main_workspace = MockWorkspaceWithPaginationState(page_num)
            elif scenario == "with_instance_variable":
                self.current_page = page_num
            else:  # no_page_info
                pass
        
        def _get_current_page_number(self):
            """模拟获取当前页码的逻辑"""
            try:
                # 方法1: 从分页组件的方法获取
                if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'pagination_widget'):
                    pagination_widget = self.main_workspace.pagination_widget
                    if hasattr(pagination_widget, 'get_current_page'):
                        return pagination_widget.get_current_page()
                    elif hasattr(pagination_widget, 'current_page'):
                        return pagination_widget.current_page
                    elif hasattr(pagination_widget, 'state') and hasattr(pagination_widget.state, 'current_page'):
                        return pagination_widget.state.current_page

                # 方法2: 从实例变量获取
                if hasattr(self, 'current_page'):
                    return self.current_page

                # 方法3: 默认值
                return 1
                
            except Exception:
                return 1
    
    class MockWorkspaceWithPaginationMethod:
        def __init__(self, page_num):
            self.pagination_widget = MockPaginationWithMethod(page_num)
    
    class MockWorkspaceWithPaginationAttribute:
        def __init__(self, page_num):
            self.pagination_widget = MockPaginationWithAttribute(page_num)
    
    class MockWorkspaceWithPaginationState:
        def __init__(self, page_num):
            self.pagination_widget = MockPaginationWithState(page_num)
    
    class MockPaginationWithMethod:
        def __init__(self, page_num):
            self.page_num = page_num
        
        def get_current_page(self):
            return self.page_num
    
    class MockPaginationWithAttribute:
        def __init__(self, page_num):
            self.current_page = page_num
    
    class MockPaginationWithState:
        def __init__(self, page_num):
            self.state = MockState(page_num)
    
    class MockState:
        def __init__(self, page_num):
            self.current_page = page_num
    
    # 测试不同场景
    scenarios = [
        ("with_pagination_method", 3, 3),
        ("with_pagination_attribute", 5, 5),
        ("with_pagination_state", 7, 7),
        ("with_instance_variable", 9, 9),
        ("no_page_info", 0, 1)  # 默认值
    ]
    
    for scenario, input_page, expected in scenarios:
        mock_window = MockMainWindow(scenario, input_page)
        result = mock_window._get_current_page_number()
        
        if result == expected:
            print(f"✅ 页码获取测试通过: {scenario} -> {result}")
        else:
            print(f"❌ 页码获取测试失败: {scenario} -> 期望{expected}, 实际{result}")
            return False
    
    return True

def test_signal_connection_fix():
    """测试信号连接修复"""
    print("\n🔧 开始测试信号连接修复...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QTableWidget
        from PyQt5.QtCore import Qt
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建测试表格
        table = QTableWidget(5, 3)
        table.setHorizontalHeaderLabels(['列1', '列2', '列3'])
        
        # 测试表头信号连接
        header = table.horizontalHeader()
        
        # 记录信号触发次数
        click_count = 0
        
        def test_header_clicked(logical_index):
            nonlocal click_count
            click_count += 1
            print(f"  表头点击信号触发: 列{logical_index}, 触发次数: {click_count}")
        
        # 测试重复连接问题
        try:
            # 第一次连接
            header.sectionClicked.connect(test_header_clicked, Qt.DirectConnection)
            print("✅ 第一次信号连接成功")
            
            # 模拟重复连接（这应该被避免）
            # header.sectionClicked.connect(test_header_clicked, Qt.DirectConnection)
            # print("⚠️ 重复信号连接（应该避免）")
            
            # 测试断开和重新连接
            header.sectionClicked.disconnect()
            header.sectionClicked.connect(test_header_clicked, Qt.DirectConnection)
            print("✅ 信号断开和重新连接成功")
            
        except Exception as conn_error:
            print(f"❌ 信号连接测试失败: {conn_error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 信号连接测试失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始排序功能修复测试 V2...")
    
    # 测试表名获取
    test1_success = test_table_name_resolution()
    
    # 测试页码获取
    test2_success = test_page_number_resolution()
    
    # 测试信号连接
    test3_success = test_signal_connection_fix()
    
    if test1_success and test2_success and test3_success:
        print("\n🎉 所有排序功能修复测试通过！")
        print("✅ 表名获取逻辑修复完成")
        print("✅ 页码获取逻辑修复完成")
        print("✅ 信号连接问题修复完成")
        sys.exit(0)
    else:
        print("\n💥 排序功能修复测试失败！")
        sys.exit(1)
