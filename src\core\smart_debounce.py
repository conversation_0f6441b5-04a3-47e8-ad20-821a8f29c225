#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能防抖系统 - Smart Debounce System

实现基于数据量和操作类型的智能防抖策略，显著提升用户体验。

设计目标：
- 分页操作：50条记录从5.2秒降低到0.32秒（94%性能提升）  
- 防抖延迟：从固定200ms优化到20-200ms智能调整
- 用户感知：从卡顿变为流畅操作

Author: Claude Code
Created: 2025-07-26
"""

import time
import os
import logging
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass

# 环境感知日志系统
class LogLevel(Enum):
    DEVELOPMENT = "DEBUG"
    PRODUCTION = "INFO"
    TESTING = "WARNING"

class EnvironmentAwareLogger:
    """环境感知的日志器"""
    
    def __init__(self, name: str):
        self.name = name
        self.env = os.getenv('SALARY_SYSTEM_ENV', 'development').lower()
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """根据环境配置日志级别"""
        if self.env == 'production':
            level = LogLevel.PRODUCTION.value
        elif self.env == 'testing':
            level = LogLevel.TESTING.value
        else:
            level = LogLevel.DEVELOPMENT.value
            
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, level))
        
        # 如果没有处理器，添加一个
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-7s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def debug_debounce(self, operation: str, details: dict):
        """开发环境详细防抖日志"""
        if self.env == 'development':
            self.logger.debug(
                f"[智能防抖-{operation}] "
                f"记录数: {details.get('record_count', 0)}, "
                f"延迟: {details.get('delay_ms', 0)}ms, "
                f"优化: {details.get('optimization', 'N/A')}"
            )
    
    def info_performance(self, operation: str, old_delay: float, new_delay: float, improvement: float):
        """生产环境性能日志"""
        self.logger.info(
            f"[防抖优化-{operation}] {old_delay}ms→{new_delay}ms, 提升{improvement:.1f}%"
        )
        
    def warning_slow_operation(self, operation: str, delay_ms: float):
        """慢操作警告"""
        if delay_ms > 500:  # 超过500ms警告
            self.logger.warning(
                f"[防抖警告] {operation}延迟过长: {delay_ms:.1f}ms"
            )

@dataclass
class DebounceMetrics:
    """防抖性能指标"""
    operation_count: int = 0
    total_delay_saved: float = 0  # ms
    avg_delay_before: float = 0
    avg_delay_after: float = 0
    improvement_percentage: float = 0

class SmartDebounceConfig:
    """智能防抖配置"""
    
    # 基于数据量的动态延迟（毫秒）
    DELAY_BY_RECORD_COUNT = {
        (0, 50):     20,    # 小数据集：极速响应（目标：总响应<500ms）
        (51, 200):   50,    # 中数据集：快速响应
        (201, 500):  100,   # 大数据集：平衡响应
        (501, 1000): 150,   # 超大数据集：防止卡死
        (1001, 9999): 200,  # 巨型数据集：最大延迟
    }
    
    # 基于操作类型的延迟（毫秒）
    DELAY_BY_OPERATION = {
        'page_change':    20,   # 分页：用户期望立即响应
        'sort_change':    50,   # 排序：可以稍微等待
        'filter_change':  100,  # 筛选：复杂操作允许等待
        'search':         150,  # 搜索：打字时防抖
        'export':         200,  # 导出：大操作可以等待
        'import':         200,  # 导入：大操作可以等待
    }
    
    # 性能优化阈值
    PERFORMANCE_THRESHOLDS = {
        'excellent': 100,   # ms - 优秀响应时间
        'good': 300,        # ms - 良好响应时间
        'acceptable': 500,  # ms - 可接受响应时间
        'slow': 1000,       # ms - 慢响应警告阈值
    }

class SmartDebounceManager:
    """智能防抖管理器"""
    
    def __init__(self):
        self.logger = EnvironmentAwareLogger("SmartDebounceManager")
        self.metrics = DebounceMetrics()
        self._operation_history = {}  # 操作历史记录
        
        self.logger.debug_debounce("初始化", {
            "system": "SmartDebounceManager",
            "config_loaded": True
        })
    
    def get_optimal_delay(self, operation_type: str, record_count: int = 0, 
                         current_load: Optional[float] = None) -> int:
        """
        获取最优防抖延迟
        
        Args:
            operation_type: 操作类型
            record_count: 记录数量
            current_load: 当前系统负载(0.0-1.0)
            
        Returns:
            int: 最优延迟时间（毫秒）
        """
        
        # 基础延迟：操作类型
        base_delay = SmartDebounceConfig.DELAY_BY_OPERATION.get(operation_type, 100)
        
        # 数据量调整
        record_delay = base_delay
        for (min_count, max_count), delay_ms in SmartDebounceConfig.DELAY_BY_RECORD_COUNT.items():
            if min_count <= record_count <= max_count:
                record_delay = min(base_delay, delay_ms)
                break
        
        # 系统负载调整（如果提供）
        if current_load is not None:
            load_multiplier = 1.0 + (current_load * 0.5)  # 最大增加50%延迟
            record_delay = int(record_delay * load_multiplier)
        
        # 确保最终延迟在合理范围内
        final_delay = max(10, min(record_delay, 500))  # 10ms-500ms
        
        # 记录优化效果
        old_delay = 200  # 原系统的固定延迟
        improvement = ((old_delay - final_delay) / old_delay) * 100
        
        self.logger.debug_debounce(operation_type, {
            'record_count': record_count,
            'delay_ms': final_delay,
            'optimization': f"{improvement:.1f}%",
            'base_delay': base_delay,
            'record_delay': record_delay
        })
        
        # 更新性能指标
        self._update_metrics(operation_type, old_delay, final_delay)
        
        return final_delay
    
    def _update_metrics(self, operation_type: str, old_delay: float, new_delay: float):
        """更新性能指标"""
        self.metrics.operation_count += 1
        
        delay_saved = old_delay - new_delay
        self.metrics.total_delay_saved += delay_saved
        
        # 计算平均值
        self.metrics.avg_delay_before = (
            (self.metrics.avg_delay_before * (self.metrics.operation_count - 1) + old_delay) / 
            self.metrics.operation_count
        )
        self.metrics.avg_delay_after = (
            (self.metrics.avg_delay_after * (self.metrics.operation_count - 1) + new_delay) / 
            self.metrics.operation_count
        )
        
        # 计算改善百分比
        if self.metrics.avg_delay_before > 0:
            self.metrics.improvement_percentage = (
                (self.metrics.avg_delay_before - self.metrics.avg_delay_after) / 
                self.metrics.avg_delay_before * 100
            )
        
        # 性能日志（每10次操作记录一次）
        if self.metrics.operation_count % 10 == 0:
            self.logger.info_performance(
                operation_type,
                self.metrics.avg_delay_before,
                self.metrics.avg_delay_after,
                self.metrics.improvement_percentage
            )
        
        # 慢操作警告
        self.logger.warning_slow_operation(operation_type, new_delay)
    
    def should_process_immediately(self, operation_type: str, record_count: int = 0) -> bool:
        """
        判断是否应该立即处理（跳过防抖）
        
        某些情况下，用户期望立即响应，应该跳过防抖机制
        """
        immediate_conditions = [
            # 小数据集的分页操作
            operation_type == 'page_change' and record_count <= 20,
            # 单页数据的排序
            operation_type == 'sort_change' and record_count <= 10,
            # 空数据集操作
            record_count == 0
        ]
        
        should_immediate = any(immediate_conditions)
        
        if should_immediate:
            self.logger.debug_debounce(operation_type, {
                'record_count': record_count,
                'delay_ms': 0,
                'optimization': '立即处理',
                'reason': '满足立即处理条件'
            })
        
        return should_immediate
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            'total_operations': self.metrics.operation_count,
            'total_delay_saved_ms': self.metrics.total_delay_saved,
            'avg_delay_before_ms': round(self.metrics.avg_delay_before, 2),
            'avg_delay_after_ms': round(self.metrics.avg_delay_after, 2),
            'improvement_percentage': round(self.metrics.improvement_percentage, 2),
            'estimated_user_time_saved_seconds': round(self.metrics.total_delay_saved / 1000, 2)
        }
    
    def reset_metrics(self):
        """重置性能指标"""
        self.metrics = DebounceMetrics()
        self.logger.info("📊 [防抖指标] 性能指标已重置")

# 全局单例实例
_smart_debounce_manager = None

def get_smart_debounce_manager() -> SmartDebounceManager:
    """获取全局智能防抖管理器单例"""
    global _smart_debounce_manager
    if _smart_debounce_manager is None:
        _smart_debounce_manager = SmartDebounceManager()
    return _smart_debounce_manager

# 便捷函数
def get_optimal_debounce_delay(operation_type: str, record_count: int = 0) -> int:
    """
    获取最优防抖延迟的便捷函数
    
    Examples:
        # 分页操作，50条记录
        delay = get_optimal_debounce_delay('page_change', 50)  # 返回20ms
        
        # 排序操作，200条记录  
        delay = get_optimal_debounce_delay('sort_change', 200)  # 返回50ms
    """
    manager = get_smart_debounce_manager()
    return manager.get_optimal_delay(operation_type, record_count)

def should_process_immediately(operation_type: str, record_count: int = 0) -> bool:
    """
    判断是否应该立即处理的便捷函数
    """
    manager = get_smart_debounce_manager()
    return manager.should_process_immediately(operation_type, record_count)

if __name__ == "__main__":
    # 测试智能防抖系统
    print("[智能防抖系统测试]")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        ('page_change', 50),    # 分页50条
        ('page_change', 200),   # 分页200条
        ('sort_change', 50),    # 排序50条
        ('sort_change', 500),   # 排序500条
        ('search', 100),        # 搜索100条
    ]
    
    manager = get_smart_debounce_manager()
    
    for operation, count in test_cases:
        delay = manager.get_optimal_delay(operation, count)
        immediate = manager.should_process_immediately(operation, count)
        
        print(f"{operation:15} | {count:4}条 | {delay:3}ms | {'立即' if immediate else '防抖'}")
    
    print("\n[性能报告]:")
    report = manager.get_performance_report()
    for key, value in report.items():
        print(f"  {key}: {value}")