#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法检查脚本

检查项目中所有Python文件的语法错误，特别关注：
1. 缩进错误
2. try/except块匹配
3. 括号、引号匹配
4. 基本语法错误
"""

import ast
import os
import sys
from pathlib import Path

def check_python_syntax(file_path):
    """检查单个Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用AST解析检查语法
        ast.parse(content)
        return True, None
        
    except SyntaxError as e:
        return False, f"语法错误 (行 {e.lineno}): {e.msg}"
    except IndentationError as e:
        return False, f"缩进错误 (行 {e.lineno}): {e.msg}"
    except Exception as e:
        return False, f"其他错误: {str(e)}"

def check_project_syntax():
    """检查整个项目的语法"""
    print("🔍 Python语法检查开始")
    print("="*60)
    
    # 要检查的目录
    check_dirs = ['src', 'temp', 'test', 'scripts']
    total_files = 0
    error_files = 0
    
    for check_dir in check_dirs:
        if not os.path.exists(check_dir):
            print(f"⚠️  目录不存在，跳过: {check_dir}")
            continue
            
        print(f"\n📁 检查目录: {check_dir}")
        print("-" * 40)
        
        # 获取所有Python文件
        python_files = list(Path(check_dir).rglob('*.py'))
        
        for py_file in python_files:
            total_files += 1
            relative_path = py_file.relative_to(Path.cwd())
            
            is_valid, error_msg = check_python_syntax(py_file)
            
            if is_valid:
                print(f"✅ {relative_path}")
            else:
                print(f"❌ {relative_path}")
                print(f"   错误详情: {error_msg}")
                error_files += 1
    
    print("\n" + "="*60)
    print("📊 语法检查结果:")
    print(f"  总文件数: {total_files}")
    print(f"  错误文件数: {error_files}")
    print(f"  成功率: {((total_files - error_files) / total_files * 100):.1f}%" if total_files > 0 else "N/A")
    
    if error_files == 0:
        print("🎉 所有Python文件语法检查通过！")
        return True
    else:
        print(f"⚠️  发现 {error_files} 个文件存在语法错误，请及时修复")
        return False

def check_specific_files():
    """检查特定的关键文件"""
    print("\n🎯 重点文件语法检查:")
    print("-" * 40)
    
    key_files = [
        'src/gui/prototype/prototype_main_window.py',
        'src/gui/prototype/widgets/virtualized_expandable_table.py',
        'main.py'
    ]
    
    all_good = True
    
    for file_path in key_files:
        if os.path.exists(file_path):
            is_valid, error_msg = check_python_syntax(file_path)
            if is_valid:
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}")
                print(f"   错误详情: {error_msg}")
                all_good = False
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    return all_good

if __name__ == "__main__":
    print("🚨 语法检查工具 - 修复验证")
    print("检查最近修改的文件语法是否正确")
    print()
    
    # 首先检查重点文件
    key_files_ok = check_specific_files()
    
    # 然后检查所有文件
    all_files_ok = check_project_syntax()
    
    if key_files_ok and all_files_ok:
        print("\n✅ 语法检查全部通过！系统应该可以正常启动。")
        sys.exit(0)
    else:
        print("\n❌ 发现语法错误！请先修复后再运行系统。")
        sys.exit(1) 