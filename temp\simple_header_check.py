#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查表头顺序 - 避免Unicode问题
"""

import sys
import os
sys.path.append('.')

def main():
    print("=== 表头顺序检查 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        registry = FieldRegistry('state/data/field_mappings.json')
        
        # 检查 active_employees
        display_fields = registry.get_display_fields('active_employees')
        
        print(f"active_employees 字段总数: {len(display_fields)}")
        
        # 查找人员类别字段
        type_pos = None
        code_pos = None
        
        for i, field in enumerate(display_fields):
            if field == '人员类别':
                type_pos = i
                print(f"人员类别位置: {i+1}")
            elif field == '人员类别代码':
                code_pos = i  
                print(f"人员类别代码位置: {i+1}")
        
        print(f"前10个字段:")
        for i in range(min(10, len(display_fields))):
            print(f"  {i+1}: {display_fields[i]}")
            
        if type_pos is not None and code_pos is not None:
            if type_pos < code_pos:
                print(f"SUCCESS: 顺序正确")
            else:
                print(f"ERROR: 顺序错误")
        else:
            print(f"WARNING: 字段缺失")
            
        # 检查是否有缓存文件
        cache_files = [
            'state/sort_states.json',
            'state/column_widths.json'
        ]
        
        print(f"\n缓存文件状态:")
        for cache_file in cache_files:
            exists = os.path.exists(cache_file)
            print(f"  {cache_file}: {'存在' if exists else '不存在'}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()