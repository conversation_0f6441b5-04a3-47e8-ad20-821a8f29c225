# 模块详细设计

## 1. 模块架构总览

### 1.1 模块分层架构

```mermaid
graph TB
    subgraph "用户界面层"
        A[MainWindow 主窗口]
        B[FileDialog 文件对话框]
        C[ProgressDialog 进度对话框]
        D[SettingsDialog 设置对话框]
    end
    
    subgraph "业务逻辑层"
        E[MainController 主控制器]
        F[PersonMatcher 人员匹配器]
        G[SalaryCalculator 工资计算器]
        H[ReportGenerator 报表生成器]
        I[TemplateManager 模板管理器]
    end
    
    subgraph "数据访问层"
        J[ExcelProcessor Excel处理器]
        K[DatabaseManager 数据库管理器]
        L[DataSynchronizer 数据同步器]
        M[ConfigManager 配置管理器]
    end
    
    subgraph "工具支持层"
        N[LogManager 日志管理器]
        O[Validator 数据验证器]
        P[ExceptionHandler 异常处理器]
        Q[FileManager 文件管理器]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    E --> G
    E --> H
    E --> I
    
    F --> J
    F --> K
    G --> J
    G --> K
    H --> J
    H --> L
    I --> M
    
    J --> N
    K --> N
    L --> O
    M --> P
    
    style A,B,C,D fill:#e3f2fd
    style E,F,G,H,I fill:#f3e5f5
    style J,K,L,M fill:#e8f5e8
    style N,O,P,Q fill:#fff3e0
```

## 2. 核心模块详细设计

### 2.1 主控制器模块 (MainController)

#### 2.1.1 模块职责
- 协调各业务模块的工作流程
- 管理应用程序的生命周期
- 处理用户交互事件
- 控制数据流转和状态管理

#### 2.1.2 类设计

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Callable
from enum import Enum
import asyncio
from dataclasses import dataclass

class ProcessingState(Enum):
    """处理状态枚举"""
    IDLE = "idle"
    LOADING = "loading"
    MATCHING = "matching"
    CALCULATING = "calculating"
    GENERATING = "generating"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"

@dataclass
class ProcessingContext:
    """处理上下文数据"""
    salary_file: str = ""
    change_file: str = ""
    output_dir: str = ""
    processing_month: str = ""
    enabled_rules: List[str] = None
    batch_id: str = ""
    start_time: datetime = None
    
    def __post_init__(self):
        if self.enabled_rules is None:
            self.enabled_rules = []

class MainController:
    """主控制器"""
    
    def __init__(self):
        self.state = ProcessingState.IDLE
        self.context = ProcessingContext()
        self.progress_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # 初始化各模块
        self.excel_processor = ExcelProcessor()
        self.person_matcher = PersonMatcher()
        self.salary_calculator = SalaryCalculator()
        self.report_generator = ReportGenerator()
        self.template_manager = TemplateManager()
        self.database_manager = DatabaseManager()
        
        # 日志和异常处理
        self.logger = LogManager().get_logger(__name__)
        self.exception_handler = ExceptionHandler()
        
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
        
    def set_error_callback(self, callback: Callable[[Exception], None]):
        """设置错误回调函数"""
        self.error_callback = callback
    
    async def execute_workflow(self, context: ProcessingContext) -> ProcessingResult:
        """执行主工作流程"""
        try:
            self.context = context
            self.context.batch_id = self._generate_batch_id()
            self.context.start_time = datetime.now()
            
            self.logger.info(f"开始执行工作流程，批次ID: {self.context.batch_id}")
            
            # 阶段1: 数据加载和验证
            self._update_state(ProcessingState.LOADING)
            await self._load_and_validate_data()
            
            # 阶段2: 人员匹配
            self._update_state(ProcessingState.MATCHING)
            matching_results = await self._execute_person_matching()
            
            # 阶段3: 异动计算
            self._update_state(ProcessingState.CALCULATING)
            calculation_results = await self._execute_salary_calculation(matching_results)
            
            # 阶段4: 报表生成
            self._update_state(ProcessingState.GENERATING)
            report_results = await self._generate_reports(calculation_results)
            
            # 阶段5: 完成处理
            self._update_state(ProcessingState.COMPLETED)
            return self._create_success_result(report_results)
            
        except Exception as e:
            self._update_state(ProcessingState.ERROR)
            self.logger.error(f"工作流程执行失败: {str(e)}")
            
            if self.error_callback:
                self.error_callback(e)
            
            return self._create_error_result(e)
    
    async def _load_and_validate_data(self):
        """加载和验证数据"""
        self._update_progress(10, "正在读取工资表文件...")
        
        # 读取工资表
        salary_data = await self.excel_processor.read_salary_file(self.context.salary_file)
        if not salary_data:
            raise DataLoadException("工资表文件读取失败")
        
        self._update_progress(30, "正在读取异动人员文件...")
        
        # 读取异动人员表
        change_data = await self.excel_processor.read_change_file(self.context.change_file)
        if not change_data:
            raise DataLoadException("异动人员文件读取失败")
        
        self._update_progress(50, "正在验证数据完整性...")
        
        # 数据验证
        validation_result = self._validate_data(salary_data, change_data)
        if not validation_result.is_valid:
            raise DataValidationException(f"数据验证失败: {validation_result.errors}")
        
        # 保存到数据库
        await self.database_manager.save_salary_data(salary_data, self.context.processing_month)
        await self.database_manager.save_change_data(change_data, self.context.processing_month)
        
        self._update_progress(70, "数据加载完成")
    
    async def _execute_person_matching(self) -> MatchingResults:
        """执行人员匹配"""
        self._update_progress(10, "开始人员匹配...")
        
        # 获取数据
        salary_persons = await self.database_manager.get_salary_persons(self.context.processing_month)
        change_persons = await self.database_manager.get_change_persons(self.context.processing_month)
        
        # 执行匹配
        matching_results = await self.person_matcher.match_persons(
            change_persons, salary_persons, 
            progress_callback=lambda p, m: self._update_progress(10 + p * 0.8, m)
        )
        
        # 处理需要手动确认的匹配
        if matching_results.pending_confirmations:
            confirmed_results = await self._handle_manual_confirmations(matching_results.pending_confirmations)
            matching_results.apply_confirmations(confirmed_results)
        
        # 保存匹配结果
        await self.database_manager.save_matching_results(matching_results)
        
        self._update_progress(100, f"人员匹配完成，成功匹配 {matching_results.success_count} 人")
        return matching_results
    
    async def _execute_salary_calculation(self, matching_results: MatchingResults) -> CalculationResults:
        """执行工资计算"""
        self._update_progress(10, "开始工资异动计算...")
        
        # 获取异动规则
        rules = await self.database_manager.get_change_rules(self.context.enabled_rules)
        
        # 执行计算
        calculation_results = await self.salary_calculator.calculate_changes(
            matching_results, rules,
            progress_callback=lambda p, m: self._update_progress(10 + p * 0.8, m)
        )
        
        # 验证计算结果
        validation_result = self._validate_calculation_results(calculation_results)
        if not validation_result.is_valid:
            raise CalculationException(f"计算结果验证失败: {validation_result.errors}")
        
        # 保存计算结果
        await self.database_manager.save_calculation_results(calculation_results)
        
        self._update_progress(100, f"工资计算完成，处理 {calculation_results.processed_count} 人")
        return calculation_results
    
    async def _generate_reports(self, calculation_results: CalculationResults) -> ReportResults:
        """生成报表"""
        self._update_progress(10, "正在生成异动汇总表...")
        
        # 生成异动汇总表
        summary_report = await self.report_generator.generate_summary_report(
            calculation_results, self.context.output_dir
        )
        
        self._update_progress(40, "正在生成工资审批表...")
        
        # 生成工资审批表
        approval_report = await self.report_generator.generate_approval_report(
            calculation_results, self.context.output_dir
        )
        
        self._update_progress(70, "正在生成请示文档...")
        
        # 生成请示文档
        request_documents = await self.template_manager.generate_request_documents(
            calculation_results, self.context.output_dir
        )
        
        self._update_progress(100, "报表生成完成")
        
        return ReportResults(
            summary_report=summary_report,
            approval_report=approval_report,
            request_documents=request_documents
        )
    
    def _update_state(self, new_state: ProcessingState):
        """更新处理状态"""
        old_state = self.state
        self.state = new_state
        self.logger.info(f"状态变更: {old_state.value} -> {new_state.value}")
    
    def _update_progress(self, percentage: int, message: str):
        """更新进度"""
        self.logger.debug(f"进度更新: {percentage}% - {message}")
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _generate_batch_id(self) -> str:
        """生成批次ID"""
        import uuid
        return f"BATCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
    
    def cancel_processing(self):
        """取消处理"""
        if self.state in [ProcessingState.LOADING, ProcessingState.MATCHING, 
                         ProcessingState.CALCULATING, ProcessingState.GENERATING]:
            self._update_state(ProcessingState.CANCELLED)
            self.logger.info("用户取消了处理流程")
```

#### 2.1.3 接口定义

```python
class IMainController(ABC):
    """主控制器接口"""
    
    @abstractmethod
    async def execute_workflow(self, context: ProcessingContext) -> ProcessingResult:
        """执行主工作流程"""
        pass
    
    @abstractmethod
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """设置进度回调"""
        pass
    
    @abstractmethod
    def cancel_processing(self):
        """取消处理"""
        pass
    
    @abstractmethod
    def get_current_state(self) -> ProcessingState:
        """获取当前状态"""
        pass
```

### 2.2 人员匹配器模块 (PersonMatcher)

#### 2.2.1 模块职责
- 实现多字段人员匹配算法
- 提供模糊匹配和精确匹配功能
- 管理匹配结果和置信度评估
- 处理手动确认流程

#### 2.2.2 类设计

```python
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Optional
import re

@dataclass
class MatchResult:
    """匹配结果"""
    source_person: ChangePersonRecord
    matched_person: Optional[SalaryPersonRecord]
    confidence: float
    match_method: str
    match_details: Dict[str, Any]
    
    @property
    def is_matched(self) -> bool:
        return self.matched_person is not None

@dataclass
class MatchingResults:
    """匹配结果集合"""
    total_count: int
    success_count: int
    failed_count: int
    pending_count: int
    results: List[MatchResult]
    pending_confirmations: List[MatchResult]
    
    def get_success_rate(self) -> float:
        return self.success_count / self.total_count if self.total_count > 0 else 0.0

class PersonMatcher:
    """人员匹配器"""
    
    def __init__(self, config: MatchingConfig = None):
        self.config = config or MatchingConfig()
        self.logger = LogManager().get_logger(__name__)
        
        # 匹配策略
        self.strategies = [
            EmployeeIdExactMatcher(),
            IdCardExactMatcher(),
            NameFuzzyMatcher(self.config.fuzzy_threshold),
            ComprehensiveMatcher()
        ]
    
    async def match_persons(self, change_persons: List[ChangePersonRecord], 
                           salary_persons: List[SalaryPersonRecord],
                           progress_callback: Optional[Callable] = None) -> MatchingResults:
        """执行人员匹配"""
        
        # 建立索引以提高匹配效率
        salary_index = self._build_salary_index(salary_persons)
        
        results = []
        pending_confirmations = []
        
        total_count = len(change_persons)
        
        for i, change_person in enumerate(change_persons):
            if progress_callback:
                progress = int((i + 1) / total_count * 100)
                progress_callback(progress, f"正在匹配第 {i+1}/{total_count} 个人员: {change_person.name}")
            
            # 执行匹配
            match_result = await self._match_single_person(change_person, salary_index)
            
            # 根据置信度决定是否需要手动确认
            if match_result.is_matched and match_result.confidence >= self.config.auto_confirm_threshold:
                results.append(match_result)
            elif match_result.confidence >= self.config.manual_confirm_threshold:
                pending_confirmations.append(match_result)
            else:
                # 匹配失败
                match_result.matched_person = None
                match_result.confidence = 0.0
                results.append(match_result)
        
        # 统计结果
        success_count = sum(1 for r in results if r.is_matched)
        failed_count = len(results) - success_count
        pending_count = len(pending_confirmations)
        
        return MatchingResults(
            total_count=total_count,
            success_count=success_count,
            failed_count=failed_count,
            pending_count=pending_count,
            results=results,
            pending_confirmations=pending_confirmations
        )
    
    async def _match_single_person(self, change_person: ChangePersonRecord, 
                                  salary_index: SalaryPersonIndex) -> MatchResult:
        """匹配单个人员"""
        
        # 依次尝试各种匹配策略
        for strategy in self.strategies:
            match_result = strategy.match(change_person, salary_index)
            
            if match_result and match_result.confidence > 0:
                self.logger.debug(f"使用策略 {strategy.__class__.__name__} 匹配到人员: "
                                f"{change_person.name} -> {match_result.matched_person.name}, "
                                f"置信度: {match_result.confidence}")
                return match_result
        
        # 未找到匹配
        return MatchResult(
            source_person=change_person,
            matched_person=None,
            confidence=0.0,
            match_method="no_match",
            match_details={"reason": "未找到匹配的人员"}
        )
    
    def _build_salary_index(self, salary_persons: List[SalaryPersonRecord]) -> SalaryPersonIndex:
        """建立工资表人员索引"""
        index = SalaryPersonIndex()
        
        for person in salary_persons:
            # 工号索引
            if person.employee_id:
                index.employee_id_index[person.employee_id] = person
            
            # 身份证索引
            if person.id_card:
                normalized_id = self._normalize_id_card(person.id_card)
                index.id_card_index[normalized_id] = person
            
            # 姓名索引
            if person.name:
                normalized_name = self._normalize_name(person.name)
                if normalized_name not in index.name_index:
                    index.name_index[normalized_name] = []
                index.name_index[normalized_name].append(person)
        
        return index
    
    def _normalize_id_card(self, id_card: str) -> str:
        """规范化身份证号"""
        # 移除空格和连字符
        normalized = re.sub(r'[\s\-]', '', id_card.upper())
        return normalized
    
    def _normalize_name(self, name: str) -> str:
        """规范化姓名"""
        # 移除空格和特殊字符
        normalized = re.sub(r'\s+', '', name.strip())
        return normalized

class SalaryPersonIndex:
    """工资表人员索引"""
    
    def __init__(self):
        self.employee_id_index: Dict[str, SalaryPersonRecord] = {}
        self.id_card_index: Dict[str, SalaryPersonRecord] = {}
        self.name_index: Dict[str, List[SalaryPersonRecord]] = {}

# 匹配策略实现
class MatchingStrategy(ABC):
    """匹配策略基类"""
    
    @abstractmethod
    def match(self, change_person: ChangePersonRecord, 
              salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
        pass

class EmployeeIdExactMatcher(MatchingStrategy):
    """工号精确匹配"""
    
    def match(self, change_person: ChangePersonRecord, 
              salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
        
        if not change_person.employee_id:
            return None
        
        matched_person = salary_index.employee_id_index.get(change_person.employee_id)
        
        if matched_person:
            return MatchResult(
                source_person=change_person,
                matched_person=matched_person,
                confidence=1.0,
                match_method="employee_id_exact",
                match_details={
                    "matched_field": "employee_id",
                    "matched_value": change_person.employee_id
                }
            )
        
        return None

class IdCardExactMatcher(MatchingStrategy):
    """身份证精确匹配"""
    
    def match(self, change_person: ChangePersonRecord, 
              salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
        
        if not change_person.id_card:
            return None
        
        normalized_id = self._normalize_id_card(change_person.id_card)
        matched_person = salary_index.id_card_index.get(normalized_id)
        
        if matched_person:
            return MatchResult(
                source_person=change_person,
                matched_person=matched_person,
                confidence=0.95,
                match_method="id_card_exact",
                match_details={
                    "matched_field": "id_card",
                    "matched_value": normalized_id
                }
            )
        
        return None
    
    def _normalize_id_card(self, id_card: str) -> str:
        return re.sub(r'[\s\-]', '', id_card.upper())

class NameFuzzyMatcher(MatchingStrategy):
    """姓名模糊匹配"""
    
    def __init__(self, threshold: float = 0.8):
        self.threshold = threshold
    
    def match(self, change_person: ChangePersonRecord, 
              salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
        
        if not change_person.name:
            return None
        
        best_match = None
        best_similarity = 0.0
        
        # 搜索所有姓名
        for name, persons in salary_index.name_index.items():
            similarity = SequenceMatcher(None, change_person.name, name).ratio()
            
            if similarity > best_similarity and similarity >= self.threshold:
                best_similarity = similarity
                best_match = persons[0]  # 取第一个匹配的人员
        
        if best_match:
            return MatchResult(
                source_person=change_person,
                matched_person=best_match,
                confidence=best_similarity,
                match_method="name_fuzzy",
                match_details={
                    "matched_field": "name",
                    "similarity": best_similarity,
                    "matched_name": best_match.name
                }
            )
        
        return None
```

### 2.3 工资计算器模块 (SalaryCalculator)

#### 2.3.1 模块职责
- 实现各种异动类型的工资计算逻辑
- 管理计算规则和配置
- 提供计算结果验证机制
- 支持批量计算和进度反馈

#### 2.3.2 类设计

```python
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Any
import json

@dataclass
class CalculationContext:
    """计算上下文"""
    employee: SalaryPersonRecord
    change_data: ChangePersonRecord
    rule: ChangeRule
    processing_month: str
    additional_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            self.additional_params = {}

@dataclass
class CalculationResult:
    """计算结果"""
    employee_id: str
    original_salary: Dict[str, Decimal]
    modified_salary: Dict[str, Decimal]
    change_amount: Decimal
    change_details: Dict[str, Any]
    calculation_method: str
    is_successful: bool
    error_message: Optional[str] = None

class SalaryCalculator:
    """工资计算器"""
    
    def __init__(self):
        self.logger = LogManager().get_logger(__name__)
        self.precision = Decimal('0.01')  # 精度到分
        
        # 注册计算器
        self.calculators = {
            "clear": ClearSalaryCalculator(),
            "replace": ReplaceSalaryCalculator(),
            "subtract": SubtractSalaryCalculator(),
            "add": AddSalaryCalculator(),
            "calculate": FormulaSalaryCalculator()
        }
        
        # 验证器
        self.validators = [
            NegativeSalaryValidator(),
            ExcessiveChangeValidator(),
            DataConsistencyValidator()
        ]
    
    async def calculate_changes(self, matching_results: MatchingResults, 
                               rules: List[ChangeRule],
                               progress_callback: Optional[Callable] = None) -> CalculationResults:
        """批量计算工资变动"""
        
        # 构建规则映射
        rule_map = {rule.change_type: rule for rule in rules}
        
        results = []
        successful_count = 0
        error_count = 0
        
        # 获取成功匹配的人员
        matched_persons = [r for r in matching_results.results if r.is_matched]
        total_count = len(matched_persons)
        
        for i, match_result in enumerate(matched_persons):
            if progress_callback:
                progress = int((i + 1) / total_count * 100)
                progress_callback(progress, f"正在计算第 {i+1}/{total_count} 个人员: {match_result.source_person.name}")
            
            try:
                # 查找对应的计算规则
                rule = rule_map.get(match_result.source_person.change_type)
                if not rule:
                    raise CalculationException(f"未找到异动类型 {match_result.source_person.change_type} 的计算规则")
                
                # 执行计算
                calc_result = await self._calculate_single_change(
                    match_result.matched_person,
                    match_result.source_person,
                    rule
                )
                
                results.append(calc_result)
                
                if calc_result.is_successful:
                    successful_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                self.logger.error(f"计算工资变动失败 - 员工: {match_result.source_person.name}, 错误: {str(e)}")
                
                error_result = CalculationResult(
                    employee_id=match_result.matched_person.employee_id,
                    original_salary={},
                    modified_salary={},
                    change_amount=Decimal('0'),
                    change_details={},
                    calculation_method="error",
                    is_successful=False,
                    error_message=str(e)
                )
                
                results.append(error_result)
                error_count += 1
        
        return CalculationResults(
            total_count=total_count,
            successful_count=successful_count,
            error_count=error_count,
            results=results,
            processing_month=matching_results.processing_month
        )
    
    async def _calculate_single_change(self, employee: SalaryPersonRecord,
                                      change_data: ChangePersonRecord,
                                      rule: ChangeRule) -> CalculationResult:
        """计算单个人员的工资变动"""
        
        # 创建计算上下文
        context = CalculationContext(
            employee=employee,
            change_data=change_data,
            rule=rule,
            processing_month=change_data.processing_month
        )
        
        # 获取对应的计算器
        calculator = self.calculators.get(rule.operation_type)
        if not calculator:
            raise CalculationException(f"不支持的计算类型: {rule.operation_type}")
        
        # 执行计算前验证
        self._validate_before_calculation(context)
        
        # 执行计算
        original_salary = self._get_salary_dict(employee)
        modified_salary = calculator.calculate(context, original_salary.copy())
        
        # 计算变动金额
        change_amount = self._calculate_total_change(original_salary, modified_salary)
        
        # 创建计算结果
        result = CalculationResult(
            employee_id=employee.employee_id,
            original_salary=original_salary,
            modified_salary=modified_salary,
            change_amount=change_amount,
            change_details=calculator.get_calculation_details(context),
            calculation_method=rule.operation_type,
            is_successful=True
        )
        
        # 执行计算后验证
        self._validate_after_calculation(result)
        
        return result
    
    def _get_salary_dict(self, employee: SalaryPersonRecord) -> Dict[str, Decimal]:
        """获取员工工资字典"""
        return {
            'basic_salary': Decimal(str(employee.basic_salary)),
            'grade_salary': Decimal(str(employee.grade_salary)),
            'allowance': Decimal(str(employee.allowance)),
            'performance_basic': Decimal(str(employee.performance_basic)),
            'performance_reward': Decimal(str(employee.performance_reward)),
            'subsidy_transport': Decimal(str(employee.subsidy_transport)),
            'subsidy_housing': Decimal(str(employee.subsidy_housing)),
            'gross_salary': Decimal(str(employee.gross_salary)),
            'net_salary': Decimal(str(employee.net_salary))
        }
    
    def _calculate_total_change(self, original: Dict[str, Decimal], 
                               modified: Dict[str, Decimal]) -> Decimal:
        """计算总变动金额"""
        original_total = original.get('gross_salary', Decimal('0'))
        modified_total = modified.get('gross_salary', Decimal('0'))
        return modified_total - original_total
    
    def _validate_before_calculation(self, context: CalculationContext):
        """计算前验证"""
        # 检查必要字段
        if not context.employee.employee_id:
            raise ValidationException("员工工号不能为空")
        
        if not context.rule:
            raise ValidationException("计算规则不能为空")
    
    def _validate_after_calculation(self, result: CalculationResult):
        """计算后验证"""
        for validator in self.validators:
            validation_result = validator.validate(result)
            if not validation_result.is_valid:
                self.logger.warning(f"计算结果验证警告: {validation_result.message}")

# 具体计算器实现
class SalaryCalculatorBase(ABC):
    """工资计算器基类"""
    
    @abstractmethod
    def calculate(self, context: CalculationContext, 
                  salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
        pass
    
    def get_calculation_details(self, context: CalculationContext) -> Dict[str, Any]:
        """获取计算详情"""
        return {
            "rule_name": context.rule.rule_name,
            "change_type": context.change_data.change_type,
            "effective_date": str(context.change_data.effective_date),
            "calculation_time": datetime.now().isoformat()
        }

class ClearSalaryCalculator(SalaryCalculatorBase):
    """清零计算器 - 用于离职、调离等"""
    
    def calculate(self, context: CalculationContext, 
                  salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
        
        # 解析影响字段
        affected_fields = json.loads(context.rule.affected_fields)
        
        # 清零指定字段
        for field in affected_fields:
            if field in salary_data:
                salary_data[field] = Decimal('0')
        
        # 重新计算应发工资
        salary_data['gross_salary'] = self._recalculate_gross_salary(salary_data)
        
        return salary_data
    
    def _recalculate_gross_salary(self, salary_data: Dict[str, Decimal]) -> Decimal:
        """重新计算应发工资"""
        return (salary_data.get('basic_salary', Decimal('0')) +
                salary_data.get('grade_salary', Decimal('0')) +
                salary_data.get('allowance', Decimal('0')) +
                salary_data.get('performance_basic', Decimal('0')) +
                salary_data.get('performance_reward', Decimal('0')) +
                salary_data.get('subsidy_transport', Decimal('0')) +
                salary_data.get('subsidy_housing', Decimal('0')))

class SubtractSalaryCalculator(SalaryCalculatorBase):
    """扣减计算器 - 用于考核扣款、请假扣款等"""
    
    def calculate(self, context: CalculationContext, 
                  salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
        
        rule_config = json.loads(context.rule.rule_config)
        
        if rule_config.get('fixed_amount'):
            # 固定金额扣减
            deduction_amount = Decimal(str(context.change_data.change_amount or 0))
            salary_data['gross_salary'] -= deduction_amount
            
        elif rule_config.get('proportional'):
            # 按比例扣减
            ratio = Decimal(str(context.change_data.deduction_ratio or 0))
            affected_fields = json.loads(context.rule.affected_fields)
            
            for field in affected_fields:
                if field in salary_data:
                    salary_data[field] *= (Decimal('1') - ratio)
            
            # 重新计算应发工资
            salary_data['gross_salary'] = self._recalculate_gross_salary(salary_data)
        
        # 确保不为负数
        for field, value in salary_data.items():
            if value < Decimal('0'):
                salary_data[field] = Decimal('0')
        
        return salary_data
```

### 2.4 报表生成器模块 (ReportGenerator)

#### 2.4.1 模块职责
- 生成各种格式的报表文档
- 处理数据汇总和统计分析
- 管理报表模板和样式
- 支持多种输出格式

#### 2.4.2 类设计

```python
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

class ReportGenerator:
    """报表生成器"""
    
    def __init__(self):
        self.logger = LogManager().get_logger(__name__)
        
        # 报表样式配置
        self.styles = {
            'header': {
                'font': Font(name='微软雅黑', size=12, bold=True, color='FFFFFF'),
                'fill': PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid'),
                'alignment': Alignment(horizontal='center', vertical='center'),
                'border': Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            },
            'data': {
                'font': Font(name='微软雅黑', size=10),
                'alignment': Alignment(horizontal='center', vertical='center'),
                'border': Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            }
        }
    
    async def generate_summary_report(self, calculation_results: CalculationResults,
                                     output_dir: str) -> str:
        """生成异动汇总报表"""
        
        # 准备数据
        summary_data = self._prepare_summary_data(calculation_results)
        
        # 创建Excel文件
        output_file = os.path.join(output_dir, f"{calculation_results.processing_month}_异动汇总表.xlsx")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 写入汇总统计
            summary_df = pd.DataFrame([{
                '处理月份': calculation_results.processing_month,
                '总处理人数': calculation_results.total_count,
                '成功处理': calculation_results.successful_count,
                '失败处理': calculation_results.error_count,
                '总变动金额': summary_data['total_change_amount'],
                '增加金额': summary_data['increase_amount'],
                '减少金额': summary_data['decrease_amount']
            }])
            summary_df.to_excel(writer, sheet_name='处理汇总', index=False)
            
            # 写入异动类型统计
            type_summary_df = pd.DataFrame(summary_data['type_summary'])
            type_summary_df.to_excel(writer, sheet_name='异动类型统计', index=False)
            
            # 写入部门统计
            dept_summary_df = pd.DataFrame(summary_data['department_summary'])
            dept_summary_df.to_excel(writer, sheet_name='部门统计', index=False)
            
            # 写入详细记录
            detail_df = pd.DataFrame(summary_data['detail_records'])
            detail_df.to_excel(writer, sheet_name='异动明细', index=False)
        
        # 应用样式
        self._apply_excel_styles(output_file)
        
        self.logger.info(f"异动汇总报表已生成: {output_file}")
        return output_file
    
    def _prepare_summary_data(self, calculation_results: CalculationResults) -> Dict[str, Any]:
        """准备汇总数据"""
        
        # 统计各类数据
        total_change_amount = Decimal('0')
        increase_amount = Decimal('0')
        decrease_amount = Decimal('0')
        
        type_stats = {}
        dept_stats = {}
        detail_records = []
        
        for result in calculation_results.results:
            if not result.is_successful:
                continue
            
            # 累计变动金额
            change_amount = result.change_amount
            total_change_amount += change_amount
            
            if change_amount > 0:
                increase_amount += change_amount
            else:
                decrease_amount += abs(change_amount)
            
            # 按异动类型统计
            change_type = result.change_details.get('change_type', '未知')
            if change_type not in type_stats:
                type_stats[change_type] = {
                    'count': 0,
                    'total_amount': Decimal('0')
                }
            type_stats[change_type]['count'] += 1
            type_stats[change_type]['total_amount'] += change_amount
            
            # 按部门统计
            department = result.change_details.get('department', '未知')
            if department not in dept_stats:
                dept_stats[department] = {
                    'count': 0,
                    'total_amount': Decimal('0')
                }
            dept_stats[department]['count'] += 1
            dept_stats[department]['total_amount'] += change_amount
            
            # 详细记录
            detail_records.append({
                '工号': result.employee_id,
                '姓名': result.change_details.get('employee_name', ''),
                '部门': department,
                '异动类型': change_type,
                '原工资': float(result.original_salary.get('gross_salary', 0)),
                '新工资': float(result.modified_salary.get('gross_salary', 0)),
                '变动金额': float(change_amount),
                '生效日期': result.change_details.get('effective_date', ''),
                '处理时间': result.change_details.get('calculation_time', '')
            })
        
        # 转换统计数据
        type_summary = []
        for change_type, stats in type_stats.items():
            type_summary.append({
                '异动类型': change_type,
                '人数': stats['count'],
                '变动总金额': float(stats['total_amount']),
                '平均变动': float(stats['total_amount'] / stats['count']) if stats['count'] > 0 else 0,
                '占比': f"{stats['count'] / len(detail_records) * 100:.1f}%" if detail_records else "0%"
            })
        
        department_summary = []
        for department, stats in dept_stats.items():
            department_summary.append({
                '部门': department,
                '人数': stats['count'],
                '变动总金额': float(stats['total_amount']),
                '平均变动': float(stats['total_amount'] / stats['count']) if stats['count'] > 0 else 0
            })
        
        return {
            'total_change_amount': float(total_change_amount),
            'increase_amount': float(increase_amount),
            'decrease_amount': float(decrease_amount),
            'type_summary': type_summary,
            'department_summary': department_summary,
            'detail_records': detail_records
        }
    
    def _apply_excel_styles(self, file_path: str):
        """应用Excel样式"""
        wb = openpyxl.load_workbook(file_path)
        
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # 应用标题行样式
            for cell in ws[1]:
                cell.font = self.styles['header']['font']
                cell.fill = self.styles['header']['fill']
                cell.alignment = self.styles['header']['alignment']
                cell.border = self.styles['header']['border']
            
            # 应用数据行样式
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.font = self.styles['data']['font']
                    cell.alignment = self.styles['data']['alignment']
                    cell.border = self.styles['data']['border']
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
        
        wb.save(file_path)
```

这个模块详细设计文档提供了系统核心模块的完整设计规范，包括接口定义、类结构、数据流和实现细节，为后续的代码实现提供了详细的技术指导。 