#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入显示修复验证测试

测试修复后的系统是否能正确显示导入的数据
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_fix_verification.log')
        ]
    )
    return logging.getLogger(__name__)

def test_virtualized_table_attributes():
    """测试VirtualizedExpandableTable属性初始化"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试VirtualizedExpandableTable属性初始化")
    
    try:
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        from PyQt5.QtWidgets import QApplication, QWidget
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建表格实例
        parent = QWidget()
        table = VirtualizedExpandableTable(parent)
        
        # 验证关键属性是否存在
        required_attributes = [
            '_format_manager',
            '_use_unified_format', 
            '_data_pre_formatted',
            '_format_cache_dirty',
            '_table_format_config'
        ]
        
        missing_attributes = []
        for attr in required_attributes:
            if not hasattr(table, attr):
                missing_attributes.append(attr)
        
        if missing_attributes:
            logger.error(f"🔧 [测试失败] 缺少属性: {missing_attributes}")
            return False
        else:
            logger.info("🔧 [测试通过] 所有必需属性都已正确初始化")
            
        # 验证属性值
        logger.info(f"🔧 [测试] _use_unified_format = {table._use_unified_format}")
        logger.info(f"🔧 [测试] _format_manager = {type(table._format_manager)}")
        
        # 测试格式化方法是否能正常工作
        try:
            test_value = table._format_cell_value("123.45", "测试列")
            logger.info(f"🔧 [测试通过] 格式化方法正常工作，结果: {test_value}")
        except Exception as e:
            logger.error(f"🔧 [测试失败] 格式化方法失败: {e}")
            return False
        
        # 测试备用渲染方法
        try:
            test_data = [{"列1": "值1", "列2": "值2"}]
            test_headers = ["列1", "列2"]
            table._render_data_with_fallback_method(test_data, test_headers)
            logger.info("🔧 [测试通过] 备用渲染方法正常工作")
        except Exception as e:
            logger.error(f"🔧 [测试失败] 备用渲染方法失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"🔧 [测试失败] VirtualizedExpandableTable测试失败: {e}")
        return False

def test_database_tables():
    """测试数据库表是否存在"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试数据库表")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 创建数据库管理器
        db_path = project_root / "data" / "db" / "salary_system.db"
        if not db_path.exists():
            logger.warning(f"🔧 [测试] 数据库文件不存在: {db_path}")
            return True  # 这不是错误，可能是首次运行
        
        table_manager = DynamicTableManager(str(db_path))
        
        # 获取表列表
        tables = table_manager.get_table_list()
        logger.info(f"🔧 [测试] 数据库中的表数量: {len(tables)}")
        
        # 查找工资数据表
        salary_tables = [t for t in tables if t.get('table_name', '').startswith('salary_data_')]
        logger.info(f"🔧 [测试] 工资数据表数量: {len(salary_tables)}")
        
        if salary_tables:
            for table in salary_tables[:3]:  # 只显示前3个
                logger.info(f"🔧 [测试] 发现表: {table.get('table_name')}")
        
        return True
        
    except Exception as e:
        logger.error(f"🔧 [测试失败] 数据库表测试失败: {e}")
        return False

def test_navigation_refresh():
    """测试导航面板刷新功能"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试导航面板刷新功能")
    
    try:
        from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
        from PyQt5.QtWidgets import QApplication, QWidget
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 这里只测试类是否能正确导入和基本初始化
        # 完整的UI测试需要更复杂的设置
        logger.info("🔧 [测试通过] 导航面板类导入成功")
        return True
        
    except Exception as e:
        logger.error(f"🔧 [测试失败] 导航面板测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_test_logging()
    logger.info("🔧 [开始] 数据导入显示修复验证测试")
    
    test_results = []
    
    # 测试1: VirtualizedExpandableTable属性初始化
    logger.info("=" * 60)
    result1 = test_virtualized_table_attributes()
    test_results.append(("VirtualizedExpandableTable属性初始化", result1))
    
    # 测试2: 数据库表检查
    logger.info("=" * 60)
    result2 = test_database_tables()
    test_results.append(("数据库表检查", result2))
    
    # 测试3: 导航面板刷新
    logger.info("=" * 60)
    result3 = test_navigation_refresh()
    test_results.append(("导航面板刷新", result3))
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("🔧 [测试结果汇总]")
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"🔧 [总结] 通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        logger.info("🎉 [成功] 所有测试都通过了！修复应该已经生效。")
        return True
    else:
        logger.error(f"⚠️ [警告] 有 {failed} 个测试失败，可能需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
