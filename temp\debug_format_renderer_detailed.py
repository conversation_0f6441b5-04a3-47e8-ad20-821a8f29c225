#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入调试FormatRenderer为什么A岗职工没有应用display_order的脚本
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def debug_format_renderer_step_by_step():
    """逐步调试FormatRenderer的render_dataframe方法"""
    print("=== 逐步调试FormatRenderer ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化field_registry
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        table_type = 'a_grade_employees'
        
        print(f"步骤1: 测试table_type = '{table_type}'")
        
        # 测试get_display_fields
        display_fields = field_registry.get_display_fields(table_type)
        print(f"步骤2: get_display_fields返回 = {len(display_fields)}个字段")
        print(f"        前5个字段: {display_fields[:5] if display_fields else '无'}")
        
        if not display_fields:
            print("❌ display_fields为空，这就是问题所在！")
            return False
        
        # 模拟真实的formatted_df列名（根据日志，A岗职工有26列）
        real_columns = [
            'employee_id', 'employee_name', 'department', 'employee_type', 
            'employee_type_code', 'position_salary_2025', 'seniority_salary_2025',
            'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee',
            'living_allowance_2025', 'car_allowance', 'performance_bonus_2025',
            'supplement', 'advance', 'total_salary', 'provident_fund_2025',
            'insurance_deduction', 'pension_insurance', 'year', 'month',
            'id', 'created_at', 'updated_at', 'sequence_number'
        ]
        
        print(f"步骤3: 模拟formatted_df.columns = {len(real_columns)}个列")
        print(f"        前5个列: {real_columns[:5]}")
        
        # 模拟FormatRenderer中的逻辑
        existing_display_fields = [field for field in display_fields if field in real_columns]
        print(f"步骤4: existing_display_fields = {len(existing_display_fields)}个字段")
        print(f"        前5个字段: {existing_display_fields[:5] if existing_display_fields else '无'}")
        
        if not existing_display_fields:
            print("❌ existing_display_fields为空，display_order不会被应用！")
            
            # 分析原因
            not_found = [field for field in display_fields if field not in real_columns]
            print(f"配置中有但DataFrame中没有的字段: {not_found}")
            
            return False
        else:
            print("✅ existing_display_fields不为空，应该会应用display_order")
            
            # 检查字段顺序
            print(f"步骤5: 字段重排序结果:")
            print(f"        重排前5个字段: {real_columns[:5]}")
            print(f"        重排后5个字段: {existing_display_fields[:5]}")
            
            # 特别检查人员类别代码位置
            if 'employee_type_code' in existing_display_fields:
                old_pos = real_columns.index('employee_type_code') if 'employee_type_code' in real_columns else -1
                new_pos = existing_display_fields.index('employee_type_code')
                print(f"        人员类别代码位置: {old_pos} -> {new_pos}")
            
            return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_actual_table_name():
    """测试使用实际表名而不是通用类型"""
    print("\n=== 测试实际表名 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json" 
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 测试实际表名
        actual_table_name = 'salary_data_2025_08_a_grade_employees'
        
        print(f"测试表名: {actual_table_name}")
        
        display_fields = field_registry.get_display_fields(actual_table_name)
        print(f"get_display_fields返回: {len(display_fields)}个字段")
        
        if display_fields:
            print(f"前10个字段: {display_fields[:10]}")
            return True
        else:
            print("❌ 实际表名也没有返回display_fields")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_format_renderer_constructor():
    """检查FormatRenderer的构造函数参数"""
    print("\n=== 检查FormatRenderer构造 ===")
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 检查FormatRenderer构造函数
        import inspect
        sig = inspect.signature(FormatRenderer.__init__)
        print(f"FormatRenderer.__init__参数: {list(sig.parameters.keys())}")
        
        # 尝试创建FormatRenderer实例
        try:
            # 基于之前的错误，可能需要两个参数
            format_renderer = FormatRenderer(field_registry, field_registry)
            print("✅ FormatRenderer创建成功（两个参数）")
            return format_renderer
        except:
            try:
                format_renderer = FormatRenderer(field_registry)
                print("✅ FormatRenderer创建成功（一个参数）")
                return format_renderer
            except Exception as e2:
                print(f"❌ FormatRenderer创建失败: {e2}")
                return None
        
    except Exception as e:
        print(f"检查失败: {e}")
        return None

if __name__ == '__main__':
    print("开始深入调试FormatRenderer")
    print("=" * 60)
    
    # 测试1: 逐步调试
    test1_passed = debug_format_renderer_step_by_step()
    
    # 测试2: 实际表名
    test2_passed = test_with_actual_table_name()
    
    # 测试3: 构造函数
    format_renderer = check_format_renderer_constructor()
    
    print("\n" + "=" * 60)
    print("调试结果汇总:")
    print(f"  FormatRenderer逻辑测试: {'通过' if test1_passed else '失败'}")
    print(f"  实际表名测试: {'通过' if test2_passed else '失败'}")
    print(f"  FormatRenderer构造: {'成功' if format_renderer else '失败'}")
    
    if test1_passed and test2_passed and format_renderer:
        print("\n结论: FormatRenderer理论上应该正常工作")
        print("问题可能在于实际运行时的table_type传入不正确")
    else:
        print("\n结论: 发现了FormatRenderer的具体问题")