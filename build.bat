@echo off
:: 月度工资异动处理系统 - 快速打包脚本
:: ARCHIVE阶段 - 打包部署
:: 创建时间: 2025-01-22

echo ========================================
echo 月度工资异动处理系统 - 快速打包工具
echo ARCHIVE阶段 - 打包部署
echo ========================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 启用延迟变量展开
setlocal EnableDelayedExpansion

:: 检查Python环境
echo 检查Python环境...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    echo 请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)

:: 检查PyInstaller
echo 检查PyInstaller...
python -c "import PyInstaller" > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到PyInstaller
    echo 正在安装PyInstaller...
    pip install PyInstaller==5.13.0
    if %errorlevel% neq 0 (
        echo PyInstaller安装失败
        pause
        exit /b 1
    )
)

:: 清理之前的构建文件
echo 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

:: 执行打包
echo 开始打包...
python build_config.py

:: 检查结果
if exist "dist\salary_changes_system.exe" (
    echo 打包成功！
    echo 可执行文件位置: dist\salary_changes_system.exe
    
    :: 获取文件大小
    for %%I in ("dist\salary_changes_system.exe") do (
        set /a size=%%~zI/1024/1024
        echo 文件大小: !size! MB
    )
    
    echo 建议测试可执行文件功能
    echo.
    echo ARCHIVE阶段 - 打包部署任务完成！
) else (
    echo 打包失败
    echo 请检查错误信息并重试
)

echo.
echo 按任意键退出...
pause > nul 