#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化集成测试
遵循CLAUDE.md要求：数据流追踪日志，验证85%性能提升目标
"""

import sys
import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from PyQt5.QtWidgets import QApplication, QTableWidget
    from PyQt5.QtCore import Qt
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    QApplication = Mock
    QTableWidget = Mock
    Qt = Mock

from src.core.smart_pagination_strategy import SmartPaginationStrategy, PaginationDecision
from src.gui.prototype.widgets.optimized_table_renderer import OptimizedTableRenderer
from src.core.request_deduplication_manager import RequestDeduplicationManager
from src.core.performance_metrics_collector import get_performance_metrics_collector, PerformanceMetrics
from src.gui.widgets.loading_indicator import get_loading_indicator_manager


class TestPerformanceOptimizationIntegration(unittest.TestCase):
    """性能优化集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if PYQT_AVAILABLE:
            cls.app = QApplication.instance()
            if cls.app is None:
                cls.app = QApplication(sys.argv)
        
        print("[测试开始] 性能优化集成测试 - 验证85%性能提升目标")
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.strategy_manager = SmartPaginationStrategy()
        self.dedup_manager = RequestDeduplicationManager()
        self.metrics_collector = get_performance_metrics_collector()
        self.loading_manager = get_loading_indicator_manager()
        
        # 测试数据生成
        self.small_dataset = self._generate_test_data(50)  # 小数据集
        self.medium_dataset = self._generate_test_data(100)  # 中等数据集
        self.large_dataset = self._generate_test_data(500)  # 大数据集
        self.headers = ['姓名', '部门', '岗位', '基本工资', '绩效工资', '津贴补贴', '扣款项目', '实发工资']
    
    def _generate_test_data(self, size: int) -> List[Dict]:
        """生成测试数据"""
        data = []
        for i in range(size):
            data.append({
                '姓名': f'员工{i+1:03d}',
                '部门': f'部门{(i % 10) + 1}',
                '岗位': f'岗位{(i % 5) + 1}',
                '基本工资': 5000 + (i % 3000),
                '绩效工资': 1000 + (i % 2000),
                '津贴补贴': 500 + (i % 1000),
                '扣款项目': i % 200,
                '实发工资': 6000 + (i % 4000)
            })
        return data
    
    def test_smart_pagination_strategy_decision(self):
        """测试智能分页策略决策"""
        print("\n[测试] 智能分页策略决策")
        
        # 测试小数据集 - 应该选择全显示
        decision_small = self.strategy_manager.should_use_pagination(
            total_records=50, page_size=20
        )
        self.assertFalse(decision_small.use_pagination)
        self.assertEqual(decision_small.strategy, 'full_display')
        print(f"  ✓ 小数据集(50条): {decision_small.strategy} - {decision_small.reason}")
        
        # 测试大数据集 - 应该选择分页
        decision_large = self.strategy_manager.should_use_pagination(
            total_records=200, page_size=20
        )
        self.assertTrue(decision_large.use_pagination)
        self.assertEqual(decision_large.strategy, 'paginated_display')
        print(f"  ✓ 大数据集(200条): {decision_large.strategy} - {decision_large.reason}")
        
        # 测试边界情况
        decision_boundary = self.strategy_manager.should_use_pagination(
            total_records=75, page_size=20
        )
        self.assertFalse(decision_boundary.use_pagination)
        print(f"  ✓ 边界数据集(75条): {decision_boundary.strategy} - {decision_boundary.reason}")
    
    @unittest.skipIf(not PYQT_AVAILABLE, "PyQt5 not available")
    def test_optimized_table_rendering_performance(self):
        """测试优化表格渲染性能"""
        print("\n[测试] 优化表格渲染性能")
        
        # 创建模拟表格组件
        table_widget = QTableWidget()
        
        # 测试小数据集渲染
        start_time = time.time()
        elapsed_ms = OptimizedTableRenderer.render_small_dataset(
            table_widget, self.small_dataset, self.headers
        )
        actual_time = (time.time() - start_time) * 1000
        
        print(f"  ✓ 小数据集渲染({len(self.small_dataset)}行): {elapsed_ms:.1f}ms")
        
        # 验证性能目标 - 小数据集应该在150ms内完成
        self.assertLess(elapsed_ms, 150, f"小数据集渲染超时: {elapsed_ms}ms > 150ms")
        
        # 测试大数据集渲染
        start_time = time.time()
        elapsed_ms_large = OptimizedTableRenderer.render_paginated_dataset(
            table_widget, self.large_dataset, self.headers
        )
        
        print(f"  ✓ 大数据集渲染({len(self.large_dataset)}行): {elapsed_ms_large:.1f}ms")
        
        # 验证大数据集性能 - 应该在合理时间内完成
        avg_ms_per_row = elapsed_ms_large / len(self.large_dataset)
        self.assertLess(avg_ms_per_row, 10, f"大数据集每行渲染过慢: {avg_ms_per_row:.2f}ms/行")
        
        # 验证表格内容正确性
        self.assertEqual(table_widget.rowCount(), len(self.large_dataset))
        self.assertEqual(table_widget.columnCount(), len(self.headers))
    
    @unittest.skipIf(not PYQT_AVAILABLE, "PyQt5 not available")
    def test_auto_strategy_selection(self):
        """测试自动策略选择"""
        print("\n[测试] 自动策略选择")
        
        table_widget = QTableWidget()
        
        # 测试自动策略 - 小数据集
        result_small = OptimizedTableRenderer.render_with_auto_strategy(
            table_widget, self.small_dataset, self.headers
        )
        
        self.assertEqual(result_small['strategy'], 'small_dataset')
        self.assertEqual(result_small['data_size'], len(self.small_dataset))
        print(f"  ✓ 小数据集自动选择: {result_small['strategy']}, "
              f"耗时{result_small['elapsed_ms']:.1f}ms, "
              f"评级{result_small['performance_rating']}")
        
        # 测试自动策略 - 大数据集
        result_large = OptimizedTableRenderer.render_with_auto_strategy(
            table_widget, self.large_dataset, self.headers
        )
        
        self.assertEqual(result_large['strategy'], 'paginated_dataset')
        self.assertEqual(result_large['data_size'], len(self.large_dataset))
        print(f"  ✓ 大数据集自动选择: {result_large['strategy']}, "
              f"耗时{result_large['elapsed_ms']:.1f}ms, "
              f"评级{result_large['performance_rating']}")
    
    def test_request_deduplication(self):
        """测试请求去重功能"""
        print("\n[测试] 请求去重功能")
        
        # 模拟相同的分页请求
        request_key = "table_name:test_table|page:1|size:20"
        
        # 第一次请求 - 应该允许
        should_process_1 = self.dedup_manager.should_process_request(
            request_key, ttl_seconds=5
        )
        self.assertTrue(should_process_1)
        print(f"  ✓ 首次请求: 允许处理")
        
        # 立即重复请求 - 应该被去重
        should_process_2 = self.dedup_manager.should_process_request(
            request_key, ttl_seconds=5
        )
        self.assertFalse(should_process_2)
        print(f"  ✓ 重复请求: 成功去重")
        
        # 不同请求 - 应该允许
        different_request = "table_name:test_table|page:2|size:20"
        should_process_3 = self.dedup_manager.should_process_request(
            different_request, ttl_seconds=5
        )
        self.assertTrue(should_process_3)
        print(f"  ✓ 不同请求: 允许处理")
        
        # 验证缓存统计
        stats = self.dedup_manager.get_cache_statistics()
        self.assertGreater(stats['total_requests'], 0)
        self.assertGreater(stats['cache_hits'], 0)
        print(f"  ✓ 缓存统计: 总请求{stats['total_requests']}, 命中{stats['cache_hits']}")
    
    def test_performance_metrics_collection(self):
        """测试性能度量收集"""
        print("\n[测试] 性能度量收集")
        
        # 模拟记录性能度量
        from datetime import datetime
        
        test_metrics = PerformanceMetrics(
            operation_type='render',
            data_size=100,
            render_time_ms=150.5,
            strategy_used='small_dataset',
            user_wait_time_ms=150.5,
            timestamp=datetime.now(),
            table_name='test_table',
            additional_info={'test': True}
        )
        
        # 记录度量
        self.metrics_collector.record_operation(test_metrics)
        
        # 获取性能报告
        report = self.metrics_collector.get_performance_report(time_range_hours=1)
        
        self.assertIn('total_operations', report)
        self.assertGreater(report['total_operations'], 0)
        print(f"  ✓ 性能报告生成: {report['total_operations']}个操作")
        
        # 获取详细分析
        analysis = self.metrics_collector.get_detailed_analysis()
        self.assertIn('analysis_by_data_size', analysis)
        print(f"  ✓ 详细分析: {analysis.get('total_metrics', 0)}条度量记录")
    
    def test_loading_indicator_management(self):
        """测试加载指示器管理"""
        print("\n[测试] 加载指示器管理")
        
        # 创建模拟父组件
        mock_parent = Mock()
        mock_parent.rect.return_value = Mock()
        mock_parent.rect.return_value.width.return_value = 800
        mock_parent.rect.return_value.height.return_value = 600
        
        # 显示加载状态
        self.loading_manager.show_loading(
            mock_parent, 
            message="测试加载中...", 
            indicator_type='default'
        )
        
        # 获取统计信息
        stats = self.loading_manager.get_loading_statistics()
        self.assertEqual(stats['active_loadings_count'], 1)
        print(f"  ✓ 活动加载数量: {stats['active_loadings_count']}")
        
        # 隐藏加载状态
        self.loading_manager.hide_loading(mock_parent)
        
        # 验证加载状态已清除
        stats_after = self.loading_manager.get_loading_statistics()
        self.assertEqual(stats_after['active_loadings_count'], 0)
        print(f"  ✓ 加载状态清除完成")
    
    def test_performance_improvement_target(self):
        """测试性能提升目标（85%提升）"""
        print("\n[测试] 性能提升目标验证")
        
        # 模拟旧版本渲染时间（基于日志分析的数据）
        # 原始渲染：1396行 × 24列 = 33504个setItem操作，约3000ms
        old_rendering_time_ms = 3000  # 旧版本渲染时间
        
        if PYQT_AVAILABLE:
            # 测试新版本渲染性能
            table_widget = QTableWidget()
            
            # 创建类似规模的测试数据（1396行）
            large_test_data = self._generate_test_data(1396)
            large_headers = ['姓名', '部门', '岗位', '基本工资', '绩效工资', '津贴补贴', 
                           '加班费', '奖金', '社保扣除', '公积金扣除', '个税', '实发工资',
                           '银行卡号', '身份证号', '入职日期', '工龄', '职级', '考勤天数',
                           '请假天数', '迟到次数', '早退次数', '备注', '审核状态', '发放状态']
            
            # 新版本渲染时间
            new_rendering_time_ms = OptimizedTableRenderer.render_with_auto_strategy(
                table_widget, large_test_data, large_headers
            )['elapsed_ms']
            
            # 计算性能提升
            improvement_percentage = ((old_rendering_time_ms - new_rendering_time_ms) 
                                    / old_rendering_time_ms) * 100
            
            print(f"  旧版本渲染时间: {old_rendering_time_ms}ms")
            print(f"  新版本渲染时间: {new_rendering_time_ms:.1f}ms") 
            print(f"  性能提升: {improvement_percentage:.1f}%")
            
            # 验证是否达到85%性能提升目标
            self.assertGreaterEqual(improvement_percentage, 85, 
                                   f"性能提升未达到目标: {improvement_percentage:.1f}% < 85%")
            
            print(f"  ✅ 性能提升目标达成: {improvement_percentage:.1f}% >= 85%")
        else:
            print("  ⚠️ PyQt5不可用，跳过实际渲染性能测试")
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        print("\n[测试] 端到端工作流程")
        
        # 模拟完整的数据处理流程
        test_table_name = "salary_202407"
        
        # 1. 智能分页策略决策
        decision = self.strategy_manager.should_use_pagination(
            total_records=len(self.medium_dataset),
            page_size=20
        )
        print(f"  1. 分页策略决策: {decision.strategy}")
        
        # 2. 请求去重检查
        request_key = f"table_name:{test_table_name}|operation:refresh"
        should_process = self.dedup_manager.should_process_request(request_key)
        print(f"  2. 请求去重检查: {'允许处理' if should_process else '重复请求'}")
        
        if should_process and PYQT_AVAILABLE:
            # 3. 加载指示器显示
            mock_parent = Mock()
            self.loading_manager.show_loading(mock_parent, "数据渲染中...")
            print(f"  3. 显示加载指示器")
            
            # 4. 执行渲染
            table_widget = QTableWidget()
            render_result = OptimizedTableRenderer.render_with_auto_strategy(
                table_widget, self.medium_dataset, self.headers
            )
            print(f"  4. 数据渲染完成: {render_result['strategy']}, "
                  f"{render_result['elapsed_ms']:.1f}ms")
            
            # 5. 隐藏加载指示器
            self.loading_manager.hide_loading(mock_parent)
            print(f"  5. 隐藏加载指示器")
            
            # 6. 验证结果
            self.assertEqual(table_widget.rowCount(), len(self.medium_dataset))
            self.assertEqual(table_widget.columnCount(), len(self.headers))
            print(f"  6. 结果验证: {table_widget.rowCount()}行 × {table_widget.columnCount()}列")
            
        print("  ✅ 端到端工作流程测试完成")
    
    def tearDown(self):
        """每个测试方法的清理"""
        # 清理请求去重缓存
        self.dedup_manager.clear_expired_cache()
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        print("\n[测试完成] 性能优化集成测试完成")
        
        if PYQT_AVAILABLE and cls.app:
            # 不退出应用，允许其他测试继续使用
            pass


def run_performance_optimization_test():
    """运行性能优化测试"""
    print("=" * 60)
    print("性能优化集成测试")
    print("验证目标：85%性能提升，智能分页策略，请求去重，加载指示器")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestPerformanceOptimizationIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) 
                   / result.testsRun * 100) if result.testsRun > 0 else 0
    
    print(f"\n整体成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 性能优化实施成功！所有关键功能正常工作。")
    elif success_rate >= 75:
        print("⚠️ 性能优化基本成功，但有部分问题需要关注。")
    else:
        print("❌ 性能优化存在重大问题，需要进一步修复。")
    
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_performance_optimization_test()
    sys.exit(0 if success else 1)