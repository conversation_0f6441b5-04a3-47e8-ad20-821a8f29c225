"""
右键菜单系统模块

提供智能的上下文相关菜单系统，支持动态菜单生成、权限控制、现代化样式和快捷键集成。

主要组件:
- ContextMenuManager: 右键菜单管理器
- SmartContextMenu: 智能上下文菜单
- MenuItemBuilder: 菜单项构建器
- MenuStyleManager: 菜单样式管理器
"""

import json
import os
from typing import Dict, List, Optional, Callable, Any, Union
from PyQt5.QtWidgets import (
    QWidget, QMenu, QAction, QActionGroup, QSeparator,
    QApplication, QTableWidget, QTreeWidget, QListWidget,
    QHeaderView, QAbstractItemView, QMessageBox
)
from PyQt5.QtCore import (
    Qt, QObject, pyqtSignal, QPoint, QModelIndex, QTimer
)
from PyQt5.QtGui import (
    QIcon, QFont, QPixmap, QPainter, QColor, QKeySequence,
    QCursor
)

from src.utils.log_config import get_module_logger
from src.gui.toast_system import ToastManager

logger = get_module_logger(__name__)

class ContextMenuManager(QObject):
    """右键菜单管理器"""
    
    # 信号定义
    menu_action_triggered = pyqtSignal(str, dict)  # 菜单动作触发信号
    menu_about_to_show = pyqtSignal(str, QMenu)  # 菜单即将显示信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.menus = {}  # 菜单字典
        self.menu_builders = {}  # 菜单构建器字典
        self.permissions = {}  # 权限字典
        self.toast_manager = ToastManager()
        
        # 样式管理器
        self.style_manager = MenuStyleManager()
        
        # 初始化默认菜单配置
        self.init_default_menus()
        
    def init_default_menus(self):
        """初始化默认菜单配置"""
        try:
            # 表格右键菜单
            self.register_menu_config('table', {
                'items': [
                    {
                        'id': 'table_add_row',
                        'text': '添加行',
                        'icon': 'add',
                        'shortcut': 'Ctrl+Shift+N',
                        'action': 'add_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit'
                    },
                    {
                        'id': 'table_insert_row',
                        'text': '插入行',
                        'icon': 'insert',
                        'shortcut': 'Ctrl+I',
                        'action': 'insert_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'table_copy_row',
                        'text': '复制行',
                        'icon': 'copy',
                        'shortcut': 'Ctrl+C',
                        'action': 'copy_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {
                        'id': 'table_cut_row',
                        'text': '剪切行',
                        'icon': 'cut',
                        'shortcut': 'Ctrl+X',
                        'action': 'cut_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit'
                    },
                    {
                        'id': 'table_paste_row',
                        'text': '粘贴行',
                        'icon': 'paste',
                        'shortcut': 'Ctrl+V',
                        'action': 'paste_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'table_delete_row',
                        'text': '删除行',
                        'icon': 'delete',
                        'shortcut': 'Delete',
                        'action': 'delete_row',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit',
                        'confirm': True,
                        'confirm_text': '确定要删除选中的行吗？'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'table_edit_cell',
                        'text': '编辑单元格',
                        'icon': 'edit',
                        'shortcut': 'F2',
                        'action': 'edit_cell',
                        'enabled': True,
                        'visible': True,
                        'permission': 'edit'
                    },
                    {
                        'id': 'table_select_all',
                        'text': '全选',
                        'icon': 'select_all',
                        'shortcut': 'Ctrl+A',
                        'action': 'select_all',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'table_export_selected',
                        'text': '导出选中行',
                        'icon': 'export',
                        'shortcut': 'Ctrl+E',
                        'action': 'export_selected',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {
                        'id': 'table_properties',
                        'text': '属性',
                        'icon': 'properties',
                        'action': 'show_properties',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    }
                ]
            })
            
            # 数据导入右键菜单
            self.register_menu_config('data_import', {
                'items': [
                    {
                        'id': 'import_excel',
                        'text': '导入Excel文件',
                        'icon': 'excel',
                        'action': 'import_excel',
                        'enabled': True,
                        'visible': True,
                        'permission': 'import'
                    },
                    {
                        'id': 'import_csv',
                        'text': '导入CSV文件',
                        'icon': 'csv',
                        'action': 'import_csv',
                        'enabled': True,
                        'visible': True,
                        'permission': 'import'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'import_template',
                        'text': '下载导入模板',
                        'icon': 'template',
                        'action': 'download_template',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {
                        'id': 'import_history',
                        'text': '导入历史',
                        'icon': 'history',
                        'action': 'show_import_history',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    }
                ]
            })
            
            # 报告生成右键菜单
            self.register_menu_config('report', {
                'items': [
                    {
                        'id': 'generate_report',
                        'text': '生成报告',
                        'icon': 'report',
                        'shortcut': 'Ctrl+R',
                        'action': 'generate_report',
                        'enabled': True,
                        'visible': True,
                        'permission': 'report'
                    },
                    {
                        'id': 'preview_report',
                        'text': '预览报告',
                        'icon': 'preview',
                        'action': 'preview_report',
                        'enabled': True,
                        'visible': True,
                        'permission': 'report'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'report_template',
                        'text': '报告模板',
                        'icon': 'template',
                        'submenu': [
                            {
                                'id': 'template_monthly',
                                'text': '月度报告模板',
                                'action': 'use_monthly_template',
                                'enabled': True,
                                'visible': True,
                                'permission': 'report'
                            },
                            {
                                'id': 'template_quarterly',
                                'text': '季度报告模板',
                                'action': 'use_quarterly_template',
                                'enabled': True,
                                'visible': True,
                                'permission': 'report'
                            },
                            {
                                'id': 'template_custom',
                                'text': '自定义模板',
                                'action': 'use_custom_template',
                                'enabled': True,
                                'visible': True,
                                'permission': 'report'
                            }
                        ]
                    },
                    {
                        'id': 'report_history',
                        'text': '报告历史',
                        'icon': 'history',
                        'action': 'show_report_history',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    }
                ]
            })
            
            # 系统右键菜单
            self.register_menu_config('system', {
                'items': [
                    {
                        'id': 'refresh',
                        'text': '刷新',
                        'icon': 'refresh',
                        'shortcut': 'F5',
                        'action': 'refresh',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {'type': 'separator'},
                    {
                        'id': 'settings',
                        'text': '设置',
                        'icon': 'settings',
                        'shortcut': 'Ctrl+,',
                        'action': 'show_settings',
                        'enabled': True,
                        'visible': True,
                        'permission': 'admin'
                    },
                    {
                        'id': 'help',
                        'text': '帮助',
                        'icon': 'help',
                        'shortcut': 'F1',
                        'action': 'show_help',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    },
                    {
                        'id': 'about',
                        'text': '关于',
                        'icon': 'info',
                        'action': 'show_about',
                        'enabled': True,
                        'visible': True,
                        'permission': 'read'
                    }
                ]
            })
            
            logger.info("默认菜单配置初始化完成")
            
        except Exception as e:
            logger.error(f"初始化默认菜单配置失败: {e}")
    
    def register_menu_config(self, menu_id: str, config: Dict):
        """注册菜单配置"""
        try:
            self.menus[menu_id] = config
            logger.debug(f"注册菜单配置: {menu_id}")
            
        except Exception as e:
            logger.error(f"注册菜单配置失败 {menu_id}: {e}")
    
    def create_context_menu(self, menu_id: str, context: Dict = None) -> Optional[QMenu]:
        """创建上下文菜单"""
        try:
            if menu_id not in self.menus:
                logger.warning(f"未找到菜单配置: {menu_id}")
                return None
            
            config = self.menus[menu_id]
            menu = SmartContextMenu(self.parent_widget)
            
            # 设置菜单样式
            self.style_manager.apply_style(menu)
            
            # 构建菜单项
            self.build_menu_items(menu, config.get('items', []), context)
            
            # 发送即将显示信号
            self.menu_about_to_show.emit(menu_id, menu)
            
            return menu
            
        except Exception as e:
            logger.error(f"创建上下文菜单失败 {menu_id}: {e}")
            return None
    
    def build_menu_items(self, menu: QMenu, items: List[Dict], context: Dict = None):
        """构建菜单项"""
        try:
            for item_config in items:
                if item_config.get('type') == 'separator':
                    menu.addSeparator()
                    continue
                
                # 检查权限
                if not self.check_permission(item_config.get('permission')):
                    continue
                
                # 检查可见性
                if not item_config.get('visible', True):
                    continue
                
                # 创建动作
                action = self.create_menu_action(item_config, context)
                if action:
                    menu.addAction(action)
                    
                    # 处理子菜单
                    if 'submenu' in item_config:
                        submenu = QMenu(item_config['text'], menu)
                        self.style_manager.apply_style(submenu)
                        self.build_menu_items(submenu, item_config['submenu'], context)
                        action.setMenu(submenu)
                        
        except Exception as e:
            logger.error(f"构建菜单项失败: {e}")
    
    def create_menu_action(self, item_config: Dict, context: Dict = None) -> Optional[QAction]:
        """创建菜单动作"""
        try:
            action = QAction(item_config['text'], self.parent_widget)
            
            # 设置图标
            icon_name = item_config.get('icon')
            if icon_name:
                icon = self.get_menu_icon(icon_name)
                if icon:
                    action.setIcon(icon)
            
            # 设置快捷键
            shortcut = item_config.get('shortcut')
            if shortcut:
                action.setShortcut(QKeySequence(shortcut))
            
            # 设置启用状态
            action.setEnabled(item_config.get('enabled', True))
            
            # 设置工具提示
            tooltip = item_config.get('tooltip')
            if tooltip:
                action.setToolTip(tooltip)
            elif shortcut:
                action.setToolTip(f"{item_config['text']} ({shortcut})")
            
            # 连接信号
            action_id = item_config.get('action')
            if action_id:
                action.triggered.connect(
                    lambda checked, aid=action_id, cfg=item_config: 
                    self.handle_menu_action(aid, cfg, context)
                )
            
            return action
            
        except Exception as e:
            logger.error(f"创建菜单动作失败: {e}")
            return None
    
    def handle_menu_action(self, action_id: str, config: Dict, context: Dict = None):
        """处理菜单动作"""
        try:
            # 检查确认对话框
            if config.get('confirm', False):
                confirm_text = config.get('confirm_text', '确定要执行此操作吗？')
                reply = QMessageBox.question(
                    self.parent_widget, '确认操作', confirm_text,
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 发送信号
            action_data = {
                'action': action_id,
                'config': config,
                'context': context or {}
            }
            self.menu_action_triggered.emit(action_id, action_data)
            
            # 显示操作反馈
            if self.toast_manager:
                self.toast_manager.show_info(f"执行: {config.get('text', action_id)}")
            
            logger.debug(f"处理菜单动作: {action_id}")
            
        except Exception as e:
            logger.error(f"处理菜单动作失败 {action_id}: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"操作失败: {e}")
    
    def get_menu_icon(self, icon_name: str) -> Optional[QIcon]:
        """获取菜单图标"""
        try:
            # 图标映射
            icon_map = {
                'add': '➕',
                'insert': '📝',
                'copy': '📋',
                'cut': '✂️',
                'paste': '📋',
                'delete': '🗑️',
                'edit': '✏️',
                'select_all': '🔲',
                'export': '📤',
                'properties': '⚙️',
                'excel': '📊',
                'csv': '📄',
                'template': '📋',
                'history': '📚',
                'report': '📊',
                'preview': '👁️',
                'refresh': '🔄',
                'settings': '⚙️',
                'help': '❓',
                'info': 'ℹ️'
            }
            
            # 创建文本图标
            if icon_name in icon_map:
                return self.create_text_icon(icon_map[icon_name])
            
            return None
            
        except Exception as e:
            logger.error(f"获取菜单图标失败 {icon_name}: {e}")
            return None
    
    def create_text_icon(self, text: str, size: int = 16) -> QIcon:
        """创建文本图标"""
        try:
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            font = QFont()
            font.setPointSize(size - 4)
            painter.setFont(font)
            
            painter.setPen(QColor(100, 100, 100))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
            
            painter.end()
            
            return QIcon(pixmap)
            
        except Exception as e:
            logger.error(f"创建文本图标失败: {e}")
            return QIcon()
    
    def check_permission(self, permission: str) -> bool:
        """检查权限"""
        try:
            if not permission:
                return True
            
            # 这里可以实现真实的权限检查逻辑
            # 目前返回True，允许所有操作
            return True
            
        except Exception as e:
            logger.error(f"检查权限失败 {permission}: {e}")
            return False
    
    def set_permissions(self, permissions: Dict[str, bool]):
        """设置权限"""
        try:
            self.permissions = permissions
            logger.info(f"设置权限: {len(permissions)} 个权限项")
            
        except Exception as e:
            logger.error(f"设置权限失败: {e}")
    
    def show_context_menu(self, menu_id: str, position: QPoint, context: Dict = None):
        """显示上下文菜单"""
        try:
            menu = self.create_context_menu(menu_id, context)
            if menu:
                menu.exec_(position)
                
        except Exception as e:
            logger.error(f"显示上下文菜单失败 {menu_id}: {e}")


class SmartContextMenu(QMenu):
    """智能上下文菜单"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setToolTipsVisible(True)
        
        # 设置菜单属性
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)
        
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        
        # 调整菜单位置，确保在屏幕内
        self.adjust_position()
    
    def adjust_position(self):
        """调整菜单位置"""
        try:
            screen = QApplication.desktop().screenGeometry()
            menu_rect = self.geometry()
            
            # 检查右边界
            if menu_rect.right() > screen.right():
                menu_rect.moveRight(screen.right() - 10)
            
            # 检查底边界
            if menu_rect.bottom() > screen.bottom():
                menu_rect.moveBottom(screen.bottom() - 10)
            
            # 检查左边界
            if menu_rect.left() < screen.left():
                menu_rect.moveLeft(screen.left() + 10)
            
            # 检查顶边界
            if menu_rect.top() < screen.top():
                menu_rect.moveTop(screen.top() + 10)
            
            self.move(menu_rect.topLeft())
            
        except Exception as e:
            logger.error(f"调整菜单位置失败: {e}")


class MenuStyleManager:
    """菜单样式管理器"""
    
    def __init__(self):
        self.style_sheet = self.create_menu_style()
    
    def create_menu_style(self) -> str:
        """创建菜单样式"""
        return """
        QMenu {
            background-color: white;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            padding: 4px;
        }
        
        QMenu::item {
            background-color: transparent;
            padding: 8px 16px 8px 8px;
            margin: 1px;
            border-radius: 4px;
            min-width: 120px;
        }
        
        QMenu::item:selected {
            background-color: #E3F2FD;
            color: #1976D2;
        }
        
        QMenu::item:disabled {
            color: #BDBDBD;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #E0E0E0;
            margin: 4px 8px;
        }
        
        QMenu::indicator {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        
        QMenu::right-arrow {
            width: 8px;
            height: 8px;
            margin-right: 8px;
        }
        """
    
    def apply_style(self, menu: QMenu):
        """应用样式到菜单"""
        try:
            menu.setStyleSheet(self.style_sheet)
            
        except Exception as e:
            logger.error(f"应用菜单样式失败: {e}")


class MenuIntegrationHelper:
    """菜单集成助手"""
    
    def __init__(self, context_manager: ContextMenuManager):
        self.context_manager = context_manager
        self.widget_menus = {}  # 控件菜单映射
        
    def integrate_table_widget(self, table_widget: QTableWidget, menu_id: str = 'table'):
        """集成表格控件"""
        try:
            table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
            table_widget.customContextMenuRequested.connect(
                lambda pos: self.show_table_menu(table_widget, pos, menu_id)
            )
            
            self.widget_menus[table_widget] = menu_id
            logger.debug(f"集成表格控件菜单: {menu_id}")
            
        except Exception as e:
            logger.error(f"集成表格控件失败: {e}")
    
    def show_table_menu(self, table_widget: QTableWidget, position: QPoint, menu_id: str):
        """显示表格菜单"""
        try:
            # 获取上下文信息
            context = self.get_table_context(table_widget, position)
            
            # 显示菜单
            global_pos = table_widget.mapToGlobal(position)
            self.context_manager.show_context_menu(menu_id, global_pos, context)
            
        except Exception as e:
            logger.error(f"显示表格菜单失败: {e}")
    
    def get_table_context(self, table_widget: QTableWidget, position: QPoint) -> Dict:
        """获取表格上下文"""
        try:
            context = {
                'widget_type': 'table',
                'widget': table_widget,
                'position': position
            }
            
            # 获取当前项
            item = table_widget.itemAt(position)
            if item:
                context['current_item'] = item
                context['current_row'] = item.row()
                context['current_column'] = item.column()
            
            # 获取选中项
            selected_items = table_widget.selectedItems()
            context['selected_items'] = selected_items
            context['selected_count'] = len(selected_items)
            
            # 获取选中行
            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())
            context['selected_rows'] = list(selected_rows)
            context['selected_row_count'] = len(selected_rows)
            
            return context
            
        except Exception as e:
            logger.error(f"获取表格上下文失败: {e}")
            return {}
    
    def integrate_widget(self, widget: QWidget, menu_id: str):
        """集成通用控件"""
        try:
            widget.setContextMenuPolicy(Qt.CustomContextMenu)
            widget.customContextMenuRequested.connect(
                lambda pos: self.show_widget_menu(widget, pos, menu_id)
            )
            
            self.widget_menus[widget] = menu_id
            logger.debug(f"集成控件菜单: {menu_id}")
            
        except Exception as e:
            logger.error(f"集成控件失败: {e}")
    
    def show_widget_menu(self, widget: QWidget, position: QPoint, menu_id: str):
        """显示控件菜单"""
        try:
            # 获取上下文信息
            context = {
                'widget_type': 'generic',
                'widget': widget,
                'position': position
            }
            
            # 显示菜单
            global_pos = widget.mapToGlobal(position)
            self.context_manager.show_context_menu(menu_id, global_pos, context)
            
        except Exception as e:
            logger.error(f"显示控件菜单失败: {e}")


class MenuActionHandler:
    """菜单动作处理器"""
    
    def __init__(self):
        self.action_handlers = {}
        self.toast_manager = ToastManager()
        
        # 注册默认处理器
        self.register_default_handlers()
    
    def register_default_handlers(self):
        """注册默认处理器"""
        try:
            # 表格操作处理器
            self.register_handler('add_row', self.handle_add_row)
            self.register_handler('insert_row', self.handle_insert_row)
            self.register_handler('delete_row', self.handle_delete_row)
            self.register_handler('copy_row', self.handle_copy_row)
            self.register_handler('cut_row', self.handle_cut_row)
            self.register_handler('paste_row', self.handle_paste_row)
            self.register_handler('edit_cell', self.handle_edit_cell)
            self.register_handler('select_all', self.handle_select_all)
            self.register_handler('export_selected', self.handle_export_selected)
            self.register_handler('show_properties', self.handle_show_properties)
            
            # 系统操作处理器
            self.register_handler('refresh', self.handle_refresh)
            self.register_handler('show_settings', self.handle_show_settings)
            self.register_handler('show_help', self.handle_show_help)
            self.register_handler('show_about', self.handle_show_about)
            
            logger.info("默认菜单动作处理器注册完成")
            
        except Exception as e:
            logger.error(f"注册默认处理器失败: {e}")
    
    def register_handler(self, action_id: str, handler: Callable):
        """注册动作处理器"""
        try:
            self.action_handlers[action_id] = handler
            logger.debug(f"注册动作处理器: {action_id}")
            
        except Exception as e:
            logger.error(f"注册动作处理器失败 {action_id}: {e}")
    
    def handle_action(self, action_id: str, action_data: Dict):
        """处理动作"""
        try:
            if action_id in self.action_handlers:
                handler = self.action_handlers[action_id]
                handler(action_data)
            else:
                logger.warning(f"未找到动作处理器: {action_id}")
                if self.toast_manager:
                    self.toast_manager.show_warning(f"未实现的操作: {action_id}")
                    
        except Exception as e:
            logger.error(f"处理动作失败 {action_id}: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"操作失败: {e}")
    
    # 默认处理器实现
    def handle_add_row(self, action_data: Dict):
        """处理添加行"""
        try:
            context = action_data.get('context', {})
            widget = context.get('widget')
            
            if isinstance(widget, QTableWidget):
                row_count = widget.rowCount()
                widget.insertRow(row_count)
                
                if self.toast_manager:
                    self.toast_manager.show_success("已添加新行")
                    
        except Exception as e:
            logger.error(f"处理添加行失败: {e}")
    
    def handle_insert_row(self, action_data: Dict):
        """处理插入行"""
        try:
            context = action_data.get('context', {})
            widget = context.get('widget')
            current_row = context.get('current_row', 0)
            
            if isinstance(widget, QTableWidget):
                widget.insertRow(current_row)
                
                if self.toast_manager:
                    self.toast_manager.show_success(f"已在第{current_row + 1}行插入新行")
                    
        except Exception as e:
            logger.error(f"处理插入行失败: {e}")
    
    def handle_delete_row(self, action_data: Dict):
        """处理删除行"""
        try:
            context = action_data.get('context', {})
            widget = context.get('widget')
            selected_rows = context.get('selected_rows', [])
            
            if isinstance(widget, QTableWidget) and selected_rows:
                # 从后往前删除，避免索引变化
                for row in sorted(selected_rows, reverse=True):
                    widget.removeRow(row)
                
                if self.toast_manager:
                    self.toast_manager.show_success(f"已删除 {len(selected_rows)} 行")
                    
        except Exception as e:
            logger.error(f"处理删除行失败: {e}")
    
    def handle_copy_row(self, action_data: Dict):
        """处理复制行"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("复制功能待实现")
                
        except Exception as e:
            logger.error(f"处理复制行失败: {e}")
    
    def handle_cut_row(self, action_data: Dict):
        """处理剪切行"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("剪切功能待实现")
                
        except Exception as e:
            logger.error(f"处理剪切行失败: {e}")
    
    def handle_paste_row(self, action_data: Dict):
        """处理粘贴行"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("粘贴功能待实现")
                
        except Exception as e:
            logger.error(f"处理粘贴行失败: {e}")
    
    def handle_edit_cell(self, action_data: Dict):
        """处理编辑单元格"""
        try:
            context = action_data.get('context', {})
            widget = context.get('widget')
            current_item = context.get('current_item')
            
            if isinstance(widget, QTableWidget) and current_item:
                widget.editItem(current_item)
                
        except Exception as e:
            logger.error(f"处理编辑单元格失败: {e}")
    
    def handle_select_all(self, action_data: Dict):
        """处理全选"""
        try:
            context = action_data.get('context', {})
            widget = context.get('widget')
            
            if isinstance(widget, QTableWidget):
                widget.selectAll()
                
                if self.toast_manager:
                    self.toast_manager.show_info("已全选")
                    
        except Exception as e:
            logger.error(f"处理全选失败: {e}")
    
    def handle_export_selected(self, action_data: Dict):
        """处理导出选中"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("导出功能待实现")
                
        except Exception as e:
            logger.error(f"处理导出选中失败: {e}")
    
    def handle_show_properties(self, action_data: Dict):
        """处理显示属性"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("属性对话框待实现")
                
        except Exception as e:
            logger.error(f"处理显示属性失败: {e}")
    
    def handle_refresh(self, action_data: Dict):
        """处理刷新"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("已刷新")
                
        except Exception as e:
            logger.error(f"处理刷新失败: {e}")
    
    def handle_show_settings(self, action_data: Dict):
        """处理显示设置"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("设置对话框待实现")
                
        except Exception as e:
            logger.error(f"处理显示设置失败: {e}")
    
    def handle_show_help(self, action_data: Dict):
        """处理显示帮助"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("帮助对话框待实现")
                
        except Exception as e:
            logger.error(f"处理显示帮助失败: {e}")
    
    def handle_show_about(self, action_data: Dict):
        """处理显示关于"""
        try:
            if self.toast_manager:
                self.toast_manager.show_info("关于对话框待实现")
                
        except Exception as e:
            logger.error(f"处理显示关于失败: {e}") 