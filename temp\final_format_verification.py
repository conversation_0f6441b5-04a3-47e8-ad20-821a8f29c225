#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终格式验证脚本 - 验证用户报告的三个问题是否已解决

测试三个具体问题：
1. 人员代码显示为"19990089.0"而不是"19990089"
2. "增发一次性生活补贴"和"补发"字段显示"-"而不是"0.00"
3. 月份字段未提取后两位数字
"""

import sys
import os
import pandas as pd
import numpy as np
sys.path.append('.')

def test_final_format_verification():
    print("=== 最终格式验证测试 ===")
    print("验证用户报告的三个格式问题是否已解决\n")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        # Initialize components
        config_manager = ConfigManager()
        db_manager = DynamicTableManager("data/salary_system.db")
        factory = get_architecture_factory(db_manager, config_manager)
        
        if not factory.initialize_architecture():
            print("❌ FAILED: Architecture initialization")
            return False
        
        # Get unified format manager
        unified_format_manager = factory.get_unified_format_manager()
        print("✅ SUCCESS: Got unified format manager")
        
        # Get format renderer
        format_renderer = unified_format_manager.format_renderer
        
        print("\n=== 问题1: 人员代码格式测试 ===")
        # Test employee ID formatting (should remove .0 suffix)
        test_employee_ids = [19990089.0, 19990090.0, "19990091.0", "19990092"]
        for employee_id in test_employee_ids:
            result = format_renderer.render_value(employee_id, 'string', 'employee_id')
            print(f"Employee ID {employee_id} -> '{result}'")
            
            # Check if .0 is removed
            if ".0" in str(result):
                print(f"⚠️  WARNING: Employee ID still contains .0: {result}")
            else:
                print(f"✅ OK: Employee ID formatted correctly: {result}")
        
        print("\n=== 问题2: 空值显示测试 (离休人员表) ===")
        # Test null/empty value formatting for retired employees table
        test_values = [None, np.nan, "", 0, 0.0, "-"]
        
        for value in test_values:
            result = format_renderer.render_value(value, 'float', '增发一次性生活补贴', 'retired_employees')
            print(f"增发一次性生活补贴 {repr(value)} -> '{result}'")
            
            if result == "-":
                print(f"❌ FAILED: 空值仍显示为 '-' 而不是 '0.00': {result}")
            elif result == "0.00":
                print(f"✅ OK: 空值正确显示为 '0.00': {result}")
            else:
                print(f"⚠️  WARNING: 意外的空值显示: {result}")
        
        print("\n=== 问题3: 月份字段提取测试 ===")
        # Test month string extraction (last two digits)
        test_months = ["202507", "202512", "202501", "25", "7", "202403"]
        
        for month in test_months:
            result = format_renderer.render_value(month, 'month_string', '月份')
            print(f"月份 '{month}' -> '{result}'")
            
            # Expected result should be last two digits
            expected = month[-2:] if len(month) >= 2 else month.zfill(2)
            if result == expected:
                print(f"✅ OK: 月份提取正确: {month} -> {result}")
            else:
                print(f"⚠️  WARNING: 月份提取可能不正确: {month} -> {result} (期望: {expected})")
        
        print("\n=== 配置验证 ===")
        # Verify configuration
        format_config = unified_format_manager.format_config
        
        # Check retired employees float config
        retired_float_config = format_config.get_format_rules('float', 'retired_employees')
        print(f"离休人员浮点型配置: {retired_float_config}")
        
        if retired_float_config and retired_float_config.get('null_display') == '0.00':
            print("✅ OK: 离休人员表配置null_display为'0.00'")
        else:
            print(f"❌ FAILED: 离休人员表配置null_display不正确: {retired_float_config}")
        
        print("\n=== 综合测试结果 ===")
        print("测试完成。请检查上述输出确认三个问题是否已解决：")
        print("1. 人员代码不再显示.0后缀")
        print("2. 离休人员表空值显示为'0.00'而不是'-'")
        print("3. 月份字段正确提取后两位数字")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final_format_verification()