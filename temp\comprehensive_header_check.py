#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查各种表格类型的表头顺序

检查所有可能的表格类型中"人员类别"和"人员类别代码"的顺序
包括缓存清理和实际测试
"""

import sys
import os
sys.path.append('.')

def check_all_table_types():
    """检查所有表格类型的表头顺序"""
    
    print("=== 全面表头顺序检查 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.format_config import FormatConfig
        
        # 创建注册器
        registry = FieldRegistry('state/data/field_mappings.json')
        
        # 检查所有表格类型
        table_types = [
            'active_employees',
            'a_grade_employees', 
            'retired_employees',
            'pension_employees',
            'part_time_employees',
            'contract_employees'
        ]
        
        print(f"\n🔍 检查 {len(table_types)} 种表格类型的字段顺序:")
        
        for table_type in table_types:
            print(f"\n📋 表格类型: {table_type}")
            
            try:
                # 获取字段顺序
                display_fields = registry.get_display_fields(table_type)
                
                if display_fields:
                    # 查找人员类别相关字段
                    type_fields = []
                    for i, field in enumerate(display_fields):
                        if '人员类别' in str(field) or 'employee_type' in str(field):
                            type_fields.append((i, field))
                    
                    if type_fields:
                        print(f"   人员类别相关字段:")
                        for pos, field in type_fields:
                            print(f"     位置 {pos+1}: {field}")
                        
                        # 检查顺序
                        type_pos = None
                        code_pos = None
                        
                        for pos, field in type_fields:
                            if field == '人员类别' or field == 'employee_type':
                                type_pos = pos
                            elif field == '人员类别代码' or field == 'employee_type_code':
                                code_pos = pos
                        
                        if type_pos is not None and code_pos is not None:
                            if type_pos < code_pos:
                                print(f"   ✅ 顺序正确: 人员类别({type_pos+1}) -> 人员类别代码({code_pos+1})")
                            else:
                                print(f"   ❌ 顺序错误: 人员类别({type_pos+1}) -> 人员类别代码({code_pos+1})")
                        else:
                            print(f"   ⚠️  字段不完整: type_pos={type_pos}, code_pos={code_pos}")
                    else:
                        print(f"   ℹ️  无人员类别字段")
                else:
                    print(f"   ⚠️  无字段配置")
                    
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
        
        # 检查format_renderer中的硬编码配置
        print(f"\n🎯 检查FormatRenderer硬编码配置:")
        
        try:
            config = FormatConfig()
            renderer = FormatRenderer(config, registry)
            
            for table_type in ['active_employees', 'a_grade_employees']:
                fallback_fields = renderer._get_fallback_display_fields(table_type)
                
                print(f"\n📋 {table_type} 硬编码字段:")
                type_pos = None
                code_pos = None
                
                for i, field in enumerate(fallback_fields):
                    if field == '人员类别':
                        type_pos = i
                        print(f"   位置 {i+1}: {field}")
                    elif field == '人员类别代码':
                        code_pos = i
                        print(f"   位置 {i+1}: {field}")
                
                if type_pos is not None and code_pos is not None:
                    if type_pos < code_pos:
                        print(f"   ✅ 硬编码顺序正确: 人员类别({type_pos+1}) -> 人员类别代码({code_pos+1})")
                    else:
                        print(f"   ❌ 硬编码顺序错误: 人员类别({type_pos+1}) -> 人员类别代码({code_pos+1})")
                        
        except Exception as e:
            print(f"   ❌ 硬编码检查失败: {e}")
            
        # 清理缓存建议
        print(f"\n🧹 缓存清理建议:")
        print(f"   1. 删除 state/sort_states.json")
        print(f"   2. 删除 state/column_widths.json") 
        print(f"   3. 重启应用程序")
        print(f"   4. 重新导入数据测试")
        
    except Exception as e:
        print(f"❌ 全面检查失败: {e}")
        import traceback
        traceback.print_exc()

def clear_cache_files():
    """清理缓存文件"""
    cache_files = [
        'state/sort_states.json',
        'state/column_widths.json',
        'state/user_preferences.json'
    ]
    
    print(f"\n🧹 清理缓存文件:")
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
                print(f"   ✅ 已删除: {cache_file}")
            except Exception as e:
                print(f"   ❌ 删除失败: {cache_file}, 错误: {e}")
        else:
            print(f"   ℹ️  不存在: {cache_file}")

if __name__ == "__main__":
    check_all_table_types()
    
    # 询问是否清理缓存
    print(f"\n❓ 是否清理缓存文件? (y/n): ", end='')
    try:
        choice = input().lower().strip()
        if choice == 'y' or choice == 'yes':
            clear_cache_files()
            print(f"\n✅ 缓存清理完成！请重启应用程序测试。")
        else:
            print(f"\n🚫 已跳过缓存清理。")
    except:
        print(f"\n🚫 跳过缓存清理操作。")