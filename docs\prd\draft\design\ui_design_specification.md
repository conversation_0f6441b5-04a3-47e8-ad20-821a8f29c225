# 月度工资异动处理系统 - UI设计规范 (v9.1)

## 更新说明 (v9.1)
本版本基于 `main_interface_prototype.html` v1.3.1 进行行展开功能简化更新，重点优化数据展示和用户体验。主要更新内容：
- **简化行展开功能**：展开后只显示2行（原始数据行 + 差异值行），去除复杂的三段式布局
- **表格行式设计**：采用表格行形式展示展开内容，保持界面一致性
- **差异值颜色标识**：支持正值（绿色）、负值（红色）、零值（灰色）的视觉区分
- **行类型区分**：原始数据行（灰色系）和差异值行（橙黄色系）的明确视觉区分
- **展开状态管理**：完善分页、查询时的展开状态自动清理机制
- **视觉设计优化**：简化CSS样式，提升渲染性能和用户体验
- **操作提示更新**：更新用户提示文字，明确展开功能的作用
- **响应式适配**：确保展开功能在不同屏幕尺寸下的良好表现

## 1. 设计理念

### 1.1 设计原则
- **财务专业化**：针对财务人员工作特点，注重数据准确性和操作效率
- **现代化扁平**：采用现代扁平设计风格，简洁明了，降低认知负担
- **响应式适配**：支持1024px到1920px+的多种屏幕尺寸，确保在不同分辨率下的最佳体验
- **简洁空间管理**：单一数据面板设计，最大化数据展示空间
- **操作流程化**：按照业务逻辑设计操作流程，减少学习成本
- **界面极简性**：主界面专注核心功能，查询功能就近放置，提升操作效率
- **实时交互反馈**：所有用户操作提供即时视觉反馈，确保操作感知清晰

### 1.2 目标用户特征
- **主要用户**：财务部门工作人员
- **使用频率**：月度定期使用，每次处理时间较长
- **专业要求**：需要高精度数据处理，零错误容忍度
- **技术水平**：熟悉Office软件，对专业财务系统有一定了解
- **界面偏好**：偏好信息密度高、操作精确的专业界面

## 2. 视觉设计规范

### 2.1 色彩系统

#### 主色调
- **主蓝色**：#4285F4 (Google Blue) - 主要按钮、重要提示
- **成功绿**：#34A853 (Google Green) - 成功状态、确认操作
- **警告橙**：#FBBC04 (Google Yellow) - 警告信息、待处理状态
- **错误红**：#EA4335 (Google Red) - 错误状态、危险操作

#### 中性色
- **深灰色**：#333333 - 主要文本
- **中灰色**：#666666 - 次要文本、标签
- **浅灰色**：#999999 - 辅助信息、禁用状态
- **边框色**：#E0E0E0 - 分割线、边框
- **背景色**：#FAFAFA - 面板背景
- **纯白色**：#FFFFFF - 主要背景、卡片

#### 状态色彩映射
```css
/* 状态徽章颜色 */
.status-success { background: #E8F5E8; color: #2E7D32; }  /* 匹配成功 */
.status-warning { background: #FFF3E0; color: #F57C00; }  /* 待确认 */
.status-error   { background: #FFEBEE; color: #D32F2F; }  /* 匹配失败 */
.status-info    { background: #E3F2FD; color: #1976D2; }  /* 信息提示 */

/* 差异值颜色 */
.diff-value.positive { color: #34A853; }  /* 正差异（增加） */
.diff-value.negative { color: #E53935; }  /* 负差异（减少） */
```

### 2.2 字体系统

#### 字体族
- **西文字体**：Segoe UI (Windows系统字体)
- **中文字体**：Microsoft YaHei (微软雅黑)
- **等宽字体**：Consolas (代码、数字显示)

#### 字体规格
| 用途 | 字号 | 字重 | 行高 | 适用场景 |
|------|------|------|------|----------|
| 主标题 | 20px | 400 | 1.2 | 页面标题 |
| 区块标题 | 16px | 500 | 1.3 | 功能区域标题 |
| 正文 | 14px | 400 | 1.4 | 主要内容文本 |
| 辅助文本 | 12px | 400 | 1.3 | 说明信息、状态文本 |
| 按钮文字 | 14px | 500 | 1.0 | 按钮内文字 |

### 2.3 间距系统

#### 基础间距单位
- **基础单位**：8px
- **紧密间距**：8px (元素内部)
- **常规间距**：16px (相关元素间)
- **松散间距**：24px (不同区块间)
- **区域间距**：32px (主要区域间)

#### 组件内边距
- **按钮内边距**：10px 20px (常规), 8px 16px (紧凑)
- **输入框内边距**：8px 35px 8px 12px (右侧预留清除图标空间)
- **卡片内边距**：20px
- **面板内边距**：20px

## 3. 组件设计规范

### 3.1 按钮系统

#### 主要按钮 (Primary Button)
```css
.primary-button {
    background: #4285F4;
    color: white;
    border: 1px solid #4285F4;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
}

.primary-button:hover {
    background: #3367D6;
    border-color: #3367D6;
}
```
- **使用场景**：主要操作（异动处理、确认操作）
- **一个界面最多1个主要按钮**

#### 次要按钮 (Secondary Button)
```css
.secondary-button {
    background: #fff;
    color: #4285F4;
    border: 1px solid #4285F4;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
}

.secondary-button:hover {
    background: #f8f9ff;
}
```
- **使用场景**：辅助操作（数据验证、设置配置）

#### 导出按钮 (Export Button)
```css
.export-button {
    background: #34A853;
    color: white;
    border: 1px solid #34A853;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
}

.export-button:hover {
    background: #2E7D32;
    border-color: #2E7D32;
}
```
- **使用场景**：导出审批表、导出请示（OA流程）、导出请示（部务会）操作

### 3.2 查询输入控件 (v6.0 更新)

#### 查询输入框容器
```css
.query-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.query-input {
    width: 300px;
    min-width: 200px;
    padding: 8px 35px 8px 12px; /* 右侧预留清除图标空间 */
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.query-input:focus {
    outline: none;
    border-color: #4285F4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}
```

#### 清除图标 (v6.0 新增)
```css
.clear-icon {
    position: absolute;
    right: 10px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: #999;
    font-size: 14px;
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
    z-index: 1;
}

.clear-icon:hover {
    color: #333;
    background: #f0f0f0;
}

/* 当输入框有内容时显示清除图标 */
.query-input:not(:placeholder-shown) + .clear-icon {
    display: flex;
}

/* 兼容性：对于不支持:placeholder-shown的浏览器 */
.query-input-container.has-content .clear-icon {
    display: flex;
}
```

#### 查询按钮
```css
.query-button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #4285F4;
    background: #4285F4;
    color: white;
    white-space: nowrap;
    margin-left: 8px;
}

.query-button:hover {
    background: #3367D6;
    border-color: #3367D6;
}
```

### 3.3 数据树形导航组件 (v7.0 新增)

#### 树形导航容器
```css
.left-panel {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    background: #fafafa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    padding: 20px;
    flex-shrink: 0;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 16px;
    background: #4285F4;
    margin-right: 8px;
    border-radius: 2px;
}

.tree-view {
    flex: 1;
    overflow-y: auto;
}
```

#### 树形节点层级样式 (v7.0 核心实现)
```css
/* 根据层级调整样式 - 4级缩进系统 */
.tree-node > .tree-item {
    font-weight: 500;
    margin-left: 15px;
    font-size: 14px;
}

.tree-node .tree-node > .tree-item {
    font-weight: 400;
    margin-left: 35px;
    font-size: 13px;
}

.tree-node .tree-node .tree-node > .tree-item {
    font-weight: 400;
    margin-left: 55px;
    font-size: 13px;
    color: #666;
}

.tree-node .tree-node .tree-node .tree-node > .tree-item {
    font-weight: 400;
    margin-left: 75px;
    font-size: 12px;
    color: #666;
}
```

#### 树形节点交互状态
```css
.tree-item {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s;
}

.tree-item:hover {
    background: #e9e9e9;
}

.tree-item.active {
    background: #4285F4;
    color: white !important;
}

/* 展开/收起箭头 */
.tree-toggle {
    transition: transform 0.2s ease-in-out;
    font-size: 12px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tree-node.collapsed .tree-toggle {
    transform: rotate(-90deg);
}

/* 子节点容器 */
.tree-node > .tree-children {
    display: block;
    padding-left: 0px;
}

.tree-node.collapsed > .tree-children {
    display: none;
}
```

#### 默认展开策略 (v7.0 智能逻辑)
**智能展开规则：**
- **第一级**：月度工资表默认展开，异动人员表默认收起
- **第二级**：2025年默认展开，其他年份收起
- **第三级**：5月默认展开，其他月份收起
- **第四级**：全部在职人员默认选中(isDefault: true)

```javascript
// 默认展开实现逻辑
const expandedClass = hasChildren ? (
    depth === 0 ? (node.name === '月度工资表' ? '' : 'collapsed') :
    depth === 1 && node.name === '2025年' ? '' :
    depth === 2 && node.name === '5月' ? '' :
    'collapsed'
) : '';
```

### 3.4 数据交互区域 (v7.0 核心重构)

#### 数据交互区域容器
```css
.data-interaction-area {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 20px;
    gap: 20px;
    min-height: 0;
}

/* 详细信息面板隐藏时的样式 */
.data-interaction-area.details-hidden {
    gap: 0;
}

.data-interaction-area.details-hidden .data-list-panel {
    flex: 1;
    max-width: none;
    width: 100%;
}
```

#### 数据列表面板
```css
.data-list-panel {
    flex: 3;
    min-width: 400px;
    display: grid;
    grid-template-rows: 1fr auto;
    overflow: hidden;
    gap: 15px;
}

.table-container {
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    min-height: 0;
}

/* v9.0 优化表格布局 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 700px;
    table-layout: fixed; /* 固定布局确保列宽稳定 */
}

/* v9.0 固定列宽设置 */
.data-table th:nth-child(1), .data-table td:nth-child(1) {
    width: 120px;
    min-width: 120px;
} /* 姓名 */

.data-table th:nth-child(2), .data-table td:nth-child(2) {
    width: 120px;
    min-width: 120px;
} /* 工号 */

.data-table th:nth-child(3), .data-table td:nth-child(3) {
    width: 180px;
    min-width: 180px;
} /* 部门 */

.data-table th:nth-child(4), .data-table td:nth-child(4) {
    width: 140px;
    min-width: 140px;
} /* 基本工资 */

.data-table th:nth-child(5), .data-table td:nth-child(5) {
    width: 140px;
    min-width: 140px;
} /* 岗位津贴 */

.data-table th, .data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 500;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    color: #666;
    font-size: 14px;
}

.data-table tr {
    cursor: pointer;
}

.data-table tr:hover {
    background: #f8f9ff;
}

.data-table tr.selected {
    background: #e3f2fd;
}
```

### 3.5 可编辑字段组件 (v9.0 新增核心功能)

#### 可编辑字段基础样式
```css
.data-table .editable-field {
    position: relative;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px dashed transparent;
    background: linear-gradient(135deg, rgba(66, 133, 244, 0.03), rgba(66, 133, 244, 0.08));
    box-shadow: inset 0 1px 2px rgba(66, 133, 244, 0.1);
    box-sizing: border-box;
    min-height: 40px;
    vertical-align: middle;
}

.data-table .editable-field:hover {
    background: linear-gradient(135deg, rgba(66, 133, 244, 0.08), rgba(66, 133, 244, 0.15));
    border-color: rgba(66, 133, 244, 0.4);
    border-style: dashed;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15), inset 0 1px 2px rgba(66, 133, 244, 0.1);
    transform: translateY(-1px);
}
```

#### 编辑图标设计
```css
.data-table .editable-field::after {
    content: '✎';
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: #4285F4;
    opacity: 0;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    text-align: center;
    line-height: 16px;
    font-weight: bold;
}

.data-table .editable-field:hover::after {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}
```

#### 编辑状态样式
```css
.data-table .editing-field {
    padding: 0 !important;
    background: #fff !important;
    border: 2px solid #4285F4 !important;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2) !important;
    border-radius: 6px !important;
    position: relative;
    min-height: 40px;
    vertical-align: middle;
    box-sizing: border-box;
}

.data-table .editing-field input {
    width: 100%;
    height: 100%;
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    font-family: inherit;
    background: transparent;
    outline: none;
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    border-radius: 4px;
}
```

#### 表格编辑提示
```css
.table-edit-hint {
    font-size: 12px;
    color: #666;
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
}

.table-edit-hint .hint-icon {
    color: #4285F4;
    margin-right: 4px;
}
```

### 3.6 字段配置模态框 (v9.0 新增功能)

#### 模态框基础样式
```css
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 30px;
    width: 90%;
    max-width: 600px;
}
```

#### 字段配置项样式
```css
/* 字段配置项容器 */
.field-config-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.2s;
}

.field-config-item:hover {
    border-color: #4285F4;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}

.field-config-item.enabled {
    background: #f8fff8;
}

.field-config-item.disabled {
    background: #f8f8f8;
}

/* 字段配置复选框 */
.field-config-checkbox {
    margin-right: 12px;
    transform: scale(1.3);
    accent-color: #4285F4;
}

/* 字段信息区域 */
.field-config-info {
    flex: 1;
}

.field-config-label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    font-weight: 500;
    display: block;
}

.field-config-description {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

/* 字段类型标签 */
.field-type-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.field-type-badge.number {
    background: #e3f2fd;
    color: #1976d2;
}

.field-type-badge.text {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* 字段状态标识 */
.field-status {
    font-size: 12px;
    font-weight: 600;
}

.field-status.enabled {
    color: #34A853;
}

.field-status.disabled {
    color: #999;
}
```

### 3.7 简化行展开组件 (v9.1 核心新功能)

#### 展开按钮设计
```css
.expand-button {
    width: 20px;
    height: 20px;
    border: none;
    background: #4285F4;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.expand-button:hover {
    background: #3367D6;
    transform: scale(1.1);
}

.expand-button.expanded {
    background: #34A853; /* 展开时变绿色 */
}
```

#### 展开行基础样式
```css
/* 展开行通用样式 */
.expanded-row {
    background: #f8f9fa !important;
    border-left: 3px solid #4285F4;
}

.expanded-row:hover {
    background: #f0f0f0 !important;
}

.expanded-row td {
    padding: 8px 16px;
    font-size: 13px;
    border-bottom: 1px solid #e0e0e0;
}
```

#### 原始数据行样式
```css
.original-row {
    background: #f5f5f5 !important;
    border-left: 3px solid #666 !important;
}

.original-row:hover {
    background: #eeeeee !important;
}

/* 原始行标识 */
.original-row td:first-child {
    color: #666;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}
```

#### 差异值行样式
```css
.difference-row {
    background: #fff8e1 !important;
    border-left: 3px solid #FF9800 !important;
}

.difference-row:hover {
    background: #fff3c4 !important;
}

/* 差异行标识 */
.difference-row td:first-child {
    color: #FF9800;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}
```

#### 差异值颜色系统
```css
/* 正值 - 绿色系 */
.positive {
    color: #34A853 !important;
    font-weight: 600;
}

/* 负值 - 红色系 */
.negative {
    color: #EA4335 !important;
    font-weight: 600;
}

/* 零值 - 灰色系 */
.zero {
    color: #666 !important;
    font-weight: 500;
}

/* 无关字段 - 淡灰色 */
.irrelevant {
    color: #999 !important;
    font-weight: normal;
}
```

#### 展开行布局规范
```css
/* 表格列宽适配展开按钮 */
.data-table th:nth-child(1), .data-table td:nth-child(1) {
    width: 50px;
    min-width: 50px;
    text-align: center;
} /* 展开按钮列 */

.data-table th:nth-child(2), .data-table td:nth-child(2) {
    width: 120px;
    min-width: 120px;
} /* 姓名列 */

.data-table th:nth-child(3), .data-table td:nth-child(3) {
    width: 120px;
    min-width: 120px;
} /* 工号列 */

.data-table th:nth-child(4), .data-table td:nth-child(4) {
    width: 180px;
    min-width: 180px;
} /* 部门列 */

.data-table th:nth-child(5), .data-table td:nth-child(5) {
    width: 140px;
    min-width: 140px;
} /* 基本工资列 */

.data-table th:nth-child(6), .data-table td:nth-child(6) {
    width: 140px;
    min-width: 140px;
} /* 岗位津贴列 */
```

#### 详细信息面板 (v6.0 重构)
```css
.details-panel {
    flex: 2;
    min-width: 300px;
    max-width: 500px;
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* 隐藏状态样式 */
.details-panel.hidden {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
    overflow: hidden;
    border: none;
    padding: 0;
    margin: 0;
    flex: none !important;
}
```

#### 详细信息面板控制 (v6.0 新增核心功能)

##### 拖拽调整器
```css
.details-resizer {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #e0e0e0;
    cursor: col-resize;
    transition: background 0.2s;
    z-index: 10;
    opacity: 0.7;
}

.details-resizer:hover, .details-resizer.dragging {
    background: #4285F4;
    opacity: 1;
}

.details-resizer.hidden {
    display: none;
}
```

##### 显示/隐藏切换按钮
```css
.details-toggle {
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 48px;
    background: #4285F4;
    border: none;
    border-radius: 4px 0 0 4px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    z-index: 11;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.details-toggle:hover {
    background: #3367D6;
    left: -14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* 当详细信息面板隐藏时的特殊样式 */
.details-toggle.panel-hidden {
    left: auto !important;
    right: 20px !important;
    background: #4285F4 !important;
    border-radius: 4px 0 0 4px !important;
    position: fixed !important;
    z-index: 1000 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    box-shadow: -2px 0 8px rgba(0,0,0,0.3) !important;
}

.details-toggle.panel-hidden:hover {
    background: #3367D6 !important;
    right: 18px !important;
}
```

#### 详细信息内容布局
```css
.details-header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background: #f5f5f5;
}

.details-header h3 {
    font-size: 16px;
    font-weight: 500;
    word-wrap: break-word;
}

.details-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
}

.details-table th, .details-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.details-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 500;
    font-size: 14px;
    width: 120px;
}

.details-table td {
    color: #333;
    font-size: 14px;
}

/* 薪资行的双列布局 */
.details-table .salary-row td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.details-table .original-value {
    color: #666;
    font-size: 14px;
    font-weight: normal;
    flex-shrink: 0;
    min-width: 80px;
}

.details-table .current-value-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
}

.details-table .current-value {
    padding: 4px 8px;
    border-radius: 4px;
    transition: background 0.2s;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: 500;
}

.details-table .current-value:hover {
    background: #f0f8ff;
    border-color: #4285F4;
}

/* 差异值显示 */
.diff-value {
    font-size: 12px;
    margin-left: 8px;
}

.diff-value.positive {
    color: #34A853;
}

.diff-value.negative {
    color: #E53935;
}
```

### 3.5 分页组件 (v6.0 完整实现)

#### 分页容器
```css
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}
```

#### 分页按钮
```css
.pagination-button {
    padding: 6px 10px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
    min-width: 36px;
    text-align: center;
}

.pagination-button:hover:not(.disabled) {
    background: #f5f5f5;
    border-color: #4285F4;
    color: #4285F4;
}

.pagination-button.active {
    background: #4285F4;
    color: white;
    border-color: #4285F4;
}

.pagination-button.disabled {
    color: #ccc;
    cursor: not-allowed;
    background: #f9f9f9;
    border-color: #eee;
}
```

#### 页面大小选择器
```css
.page-size-select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    background: #fff;
    cursor: pointer;
    margin-left: 10px;
}
```

### 3.6 响应式布局规范 (v7.0 完整实现)

#### 响应式断点系统
```css
/* Full HD (1920px+) - 完整布局 */
@media screen and (min-width: 1920px) {
    .left-panel { width: 320px; }
    .query-input { width: 300px; }
}

/* HD (1600px-1919px) - 压缩间距 */
@media screen and (max-width: 1600px) {
    .query-input { width: 280px; min-width: 200px; }
    .control-panel { gap: 12px; }
}

/* 标准 (1400px-1599px) - 调整面板大小 */
@media screen and (max-width: 1400px) {
    .left-panel { width: 280px; min-width: 250px; }
    .data-list-panel { min-width: 350px; }
    .details-panel { min-width: 250px; max-width: 400px; }
    .query-input { width: 250px; min-width: 180px; }
    .control-panel { gap: 10px; padding: 15px 25px; }
    .control-actions-right { gap: 12px; }
}

/* 紧凑 (1200px-1399px) - 紧凑模式 */
@media screen and (max-width: 1200px) {
    .left-panel { width: 250px; min-width: 220px; padding: 15px; }
    .control-panel { padding: 15px 20px; }
    .data-interaction-area { padding: 15px; gap: 15px; }
    .data-list-panel { min-width: 300px; }
    .details-panel { min-width: 200px; max-width: 350px; }
    .query-input { width: 220px; min-width: 160px; }
    
    .primary-button, .secondary-button, .export-button, .query-button {
        padding: 8px 16px;
        font-size: 13px;
    }
    .control-actions-right { gap: 10px; }
}

/* 小屏 (1024px-1199px) - 垂直排列 */
@media screen and (max-width: 1024px) {
    .main-container { min-width: 1024px; }
    .left-panel { width: 220px; }
    .control-panel {
        min-height: auto;
        padding: 12px 15px;
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .query-section {
        width: 100%;
        justify-content: flex-start;
    }
    .query-input { width: 200px; min-width: 150px; }
    .control-actions-right {
        margin-left: 0;
        width: 100%;
        justify-content: flex-start;
    }
    
    .data-interaction-area { flex-direction: column; }
    .data-list-panel, .details-panel { flex: none; min-width: auto; max-width: none; }
    .details-panel { height: 300px; }
}

/* 最小支持 (900px以下) - 极紧凑模式 */
@media screen and (max-width: 900px) {
    .control-panel { padding: 10px 12px; }
    
    .primary-button, .secondary-button, .export-button, .query-button {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .query-input {
        width: 180px;
        min-width: 120px;
        font-size: 13px;
        padding: 6px 10px;
    }
    
    .control-actions-right { flex-wrap: wrap; }
}
```

#### 主布局容器 (v7.0 实现)
```css
.main-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    min-width: 1024px;
    min-height: 768px;
}

.header {
    height: 60px;
    background: linear-gradient(135deg, #4285F4, #34A853);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.workspace {
    flex: 1;
    display: flex;
    min-height: 0;
}

.footer {
    height: 60px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 30px;
    justify-content: space-between;
}
```

### 3.7 设置窗口组件 (v7.0 完整实现)

#### 设置窗口基础结构
```css
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.settings-window {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
```

#### 设置窗口头部
```css
.settings-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.settings-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.close-button:hover {
    background: #e0e0e0;
    color: #333;
}
```

#### 配置项开关组件 (v7.0 核心组件)
```css
.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #f0f0f0;
}

.config-item:last-child {
    border-bottom: none;
}

.config-label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.config-description {
    font-size: 14px;
    color: #666;
}

/* 开关组件 - 基于HTML原型实现 */
.config-switch {
    width: 50px;
    height: 26px;
    background: #ddd;
    border-radius: 13px;
    position: relative;
    cursor: pointer;
    transition: background 0.3s;
}

.config-switch.active {
    background: #4285F4;
}

.config-switch::after {
    content: '';
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.config-switch.active::after {
    transform: translateX(24px);
}
```

#### 设置窗口底部操作区
```css
.settings-footer {
    padding: 20px 30px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f8f9fa;
}
```

## 4. 布局规范 (v7.0 基于HTML原型完整实现)

### 4.1 主窗口布局结构

#### 4.1.1 整体布局架构
```
┌─────────────────────────────────────────────────────────────────────┐
│  Header (60px) - [月度工资异动处理系统 v1.0] [菜单栏] [设置▼] [🚪]   │
├────┬────────────────────────────────────────────────────────────────┤
│    │              Control Panel (80px)                             │
│    │  [查询框×🔍][查询]   [异动处理] [导出审批表] [导出请示...] [..] │
│L   ├────────────────────────────────────────────────────────────────┤
│e   │              Tab Navigation (50px)                            │
│f   │  [异动汇总] [人员匹配] [计算结果] [处理日志]    [🔍查询输入框]  │
│t   ├─────────────────────────────────────────────────────────────────┤
│    │                    Data List Panel                            │
│P   │  ┌─────────────────────────────────────────────────────────┐ │
│a   │  │ 💡 双击"基本工资"或"岗位津贴"字段可进行编辑        │ │
│n   │  │                    Data Table                         │ │
│e   │  │  姓名  工号  部门  基本工资✎ 岗位津贴✎              │ │
│l   │  │  张三  001  财务  [5000]   [1000]                  │ │
│    │  │  李四  002  人事  [5500]   [1200]                  │ │
│(   │  │  王五  003  技术  [6000]   [1500]                  │ │
│3   │  │  ...                                             │ │
│3   │  └─────────────────────────────────────────────────┘ │
│2   │  ┌─────────────────────────────────────────────────┐ │
│0   │  │                  Pagination                     │ │
│p   │  │ 显示1-10条，共159条 [上页][1][2][3][下页]        │ │
│x   │  └─────────────────────────────────────────────────┘ │
│)   │  注：[5000]表示可编辑字段，✎表示编辑图标            │
├───────────────────────────────────────────────────────────────────┤
│                    Footer (60px)                                   │
│  系统就绪 | 已加载: 2025年5月全部在职人员 | 共159条记录           │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.1.2 头部菜单栏设计 (v7.0 实现)
```css
.header {
    height: 60px;
    background: linear-gradient(135deg, #4285F4, #34A853);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 20px;
    font-weight: 400;
}

.menu-bar {
    margin-left: auto;
    display: flex;
    gap: 20px;
    align-items: center;
}

.menu-item {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
    position: relative;
}

.menu-item:hover {
    background: rgba(255,255,255,0.1);
}

.exit-icon {
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
    font-size: 18px;
    font-weight: bold;
}

.exit-icon:hover {
    background: rgba(255,255,255,0.1);
}
```

### 4.2 操作控制面板设计 (v7.0 响应式实现)

#### 4.2.1 控制面板布局
```css
.control-panel {
    min-height: 80px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 15px 30px;
    gap: 15px;
    flex-wrap: wrap;
    align-content: center;
    position: relative;
}

.query-section {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.control-actions-right {
    margin-left: auto;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}
```

#### 4.2.2 按钮组织结构 (v7.0 优化)
```html
<!-- 操作控制区域结构 -->
<div class="control-panel">
    <!-- 查询区域 -->
    <div class="query-section">
        <div class="query-input-container">
            <input type="text" class="query-input" placeholder="输入工号或姓名查询...">
            <div class="clear-icon">×</div>
        </div>
        <button class="query-button">查询</button>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="control-actions-right">
        <button class="primary-button">异动处理</button>
        <button class="export-button">导出审批表</button>
        <button class="export-button">导出请示 (OA)</button>
        <button class="export-button">导出请示 (部务会)</button>
    </div>
</div>
```

### 4.3 标签导航与查询集成 (v7.0 核心创新)

#### 4.3.1 标签栏结构
```css
.tab-navigation {
    padding: 0 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    height: 50px;
    background: #fff;
}

.tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.tab-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
    white-space: nowrap;
}

.tab-item.active, .tab-item:hover {
    color: #4285F4;
    border-bottom-color: #4285F4;
}
```

### 4.4 数据交互区域布局 (v8.0 单面板系统)

#### 4.4.1 单面板容器
```css
.data-interaction-area {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 20px;
    min-height: 0;
}
```

#### 4.4.2 数据列表面板
```css
.data-list-panel {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-rows: 1fr auto;
    overflow: hidden;
    gap: 15px;
}

.table-container {
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    min-height: 0;
}
```

### 4.5 状态栏设计 (v7.0 信息展示优化)

#### 4.5.1 状态栏布局
```css
.footer {
    height: 60px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 30px;
    justify-content: space-between;
}

.status-info {
    display: flex;
    gap: 20px;
    align-items: center;
    font-size: 14px;
    color: #666;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #34A853;
}

.status-dot.processing {
    background: #FBBC05;
}
```

#### 4.5.2 状态信息层次
**状态栏信息组织：**
- **系统状态**：`系统就绪` / `处理中...` / `出现错误`
- **文件状态**：`已加载: 2025年5月全部在职人员`
- **数据统计**：`共159条记录` / `查询到3条记录` / `匹配成功: 156人`
- **操作提示**：`双击编辑` / `拖拽调整面板大小`

## 5. 交互设计规范 (v8.0 基于单面板布局)

### 5.1 查询功能增强交互 (v8.0 完整实现)

#### 查询交互流程
1. **输入检测**：实时检测输入内容，显示/隐藏清除图标
2. **防抖处理**：输入时300ms防抖，减少不必要的查询
3. **快捷操作**：支持回车键快速查询
4. **清除功能**：一键清除输入内容，恢复完整数据列表

#### 查询输入交互
```css
/* 查询输入框焦点状态 */
.query-input:focus {
    outline: none;
    border-color: #4285F4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* 清除图标交互 */
.clear-icon:hover {
    color: #333;
    background: #f0f0f0;
}
```

### 5.2 数据表格交互 (v8.1 新增双击编辑功能)

#### 5.2.1 行选择交互
```css
.data-table tr {
    cursor: pointer;
    transition: background 0.2s;
}

.data-table tr:hover {
    background: #f8f9ff;
}

.data-table tr.selected {
    background: #e3f2fd;
    border-left: 3px solid #4285F4;
}
```

#### 5.2.2 表格内双击编辑功能 (v8.1 核心新功能)

**可编辑字段视觉设计**
```css
/* 可编辑字段基础样式 */
.data-table .editable-field {
    position: relative;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.2s;
    border: 1px solid transparent;
    background: rgba(66, 133, 244, 0.05);
}

/* 悬停状态 */
.data-table .editable-field:hover {
    background: rgba(66, 133, 244, 0.1);
    border-color: #4285F4;
    box-shadow: 0 0 0 1px rgba(66, 133, 244, 0.2);
}

/* 编辑图标 */
.data-table .editable-field::after {
    content: '✎';
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: #4285F4;
    opacity: 0;
    transition: opacity 0.2s;
}

.data-table .editable-field:hover::after {
    opacity: 1;
}
```

**编辑模式样式**
```css
/* 编辑状态样式 */
.data-table .editing-field {
    padding: 0;
    background: #fff;
    border: 2px solid #4285F4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.data-table .editing-field input {
    width: 100%;
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    font-family: inherit;
    background: transparent;
    outline: none;
}
```

**编辑交互流程**
1. **双击触发**：双击可编辑字段进入编辑模式
2. **自动聚焦**：输入框自动获得焦点并选中当前值
3. **键盘操作**：
   - 回车键：保存修改
   - ESC键：取消编辑
4. **点击保存**：点击其他区域自动保存
5. **数据验证**：
   - 数字字段：验证非负数
   - 文本字段：验证非空
6. **反馈提示**：保存成功显示绿色通知

**JavaScript实现核心逻辑**
```javascript
// 表格字段编辑功能
window.enableTableEditMode = (element) => {
    if (appState.editingField.element) return;

    const personId = parseInt(element.dataset.id);
    const field = element.dataset.field;
    const person = appState.allData.find(p => p.id === personId);
    const currentValue = person[field];

    // 保存原始状态
    const originalContent = element.innerHTML;
    const originalClass = element.className;

    // 切换到编辑状态
    element.className = 'editing-field';

    // 创建输入框
    let inputType = 'number';
    let inputStep = '0.01';
    let inputValue = currentValue.toFixed(2);

    element.innerHTML = `<input type="${inputType}" step="${inputStep}" value="${inputValue}" onblur="saveTableFieldEdit(this)" onkeydown="handleTableEditKeydown(event, this)">`;

    const input = element.querySelector('input');
    input.focus();
    input.select();

    appState.editingField = { element, personId, field, originalContent, originalClass };
};

// 保存编辑
window.saveTableFieldEdit = (inputElement) => {
    const { personId, field, originalClass } = appState.editingField;
    const person = appState.allData.find(p => p.id === personId);

    const numValue = parseFloat(inputElement.value);
    if (!isNaN(numValue) && numValue >= 0) {
        person[field] = numValue;
        appState.editingField = { element: null, personId: null, field: null, originalContent: null, originalClass: null };
        renderTable();
        showSaveNotification('修改已保存');
    } else {
        alert('请输入有效的数字（不能为负数）');
        inputElement.focus();
        inputElement.select();
    }
};
```

#### 5.2.3 编辑提示信息
```html
<!-- 表格上方的操作提示 -->
<div class="table-edit-hint">
    <span class="hint-icon">💡</span>
    双击"基本工资"或"岗位津贴"字段可进行编辑，按回车保存，按ESC取消
</div>
```

```css
.table-edit-hint {
    font-size: 12px;
    color: #666;
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
}

.table-edit-hint .hint-icon {
    color: #4285F4;
    margin-right: 4px;
}
```

### 5.3 动画效果规范

#### 过渡动画时长
- **快速交互**：0.15s (按钮点击、状态变化)
- **常规过渡**：0.2s (悬停效果、颜色变化)
- **面板切换**：0.3s (面板显示/隐藏、页面切换)

#### 缓动函数
- **ease-out**：适用于进入动画
- **ease-in**：适用于退出动画
- **ease**：适用于常规状态变化

---

**更新日期**：2025年1月
**版本号**：v8.1 - 单面板布局 + 表格内编辑版
**技术栈**：HTML5 + CSS3 + Modern JavaScript

## 8. 开发实现指导 (v8.0 基于单面板布局)

### 8.1 关键技术实现

#### 8.1.1 响应式布局实现
```css
/* 使用CSS Grid + Flexbox实现响应式布局 */
.main-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    min-width: 1024px;
    min-height: 768px;
}

.workspace {
    flex: 1;
    display: flex;
    min-height: 0;
}

.data-interaction-area {
    flex: 1;
    display: flex;
    gap: 20px;
    overflow: hidden;
    padding: 20px;
    min-height: 0;
}
```

#### 8.1.2 面板拖拽调整实现
```javascript
// 核心拖拽逻辑 - 完整实现
function initDetailsResizer() {
    const resizer = document.getElementById('details-resizer');
    const detailsPanel = document.getElementById('details-panel');
    const dataListPanel = document.querySelector('.data-list-panel');
    const dataInteractionArea = document.querySelector('.data-interaction-area');
    
    let isResizing = false;
    let startX = 0;
    let containerWidth = 0;
    let startDetailsWidth = 0;
    
    resizer.addEventListener('mousedown', (e) => {
        isResizing = true;
        startX = e.clientX;
        containerWidth = dataInteractionArea.offsetWidth;
        startDetailsWidth = detailsPanel.offsetWidth;
        
        resizer.classList.add('dragging');
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
        
        e.preventDefault();
        e.stopPropagation();
    });
    
    document.addEventListener('mousemove', (e) => {
        if (!isResizing) return;
        
        const deltaX = startX - e.clientX;
        const minDetailsWidth = 250;
        const maxDetailsWidth = Math.min(600, containerWidth * 0.6);
        const minListWidth = 400;
        
        let newDetailsWidth = startDetailsWidth + deltaX;
        newDetailsWidth = Math.max(minDetailsWidth, Math.min(maxDetailsWidth, newDetailsWidth));
        
        const newListWidth = containerWidth - newDetailsWidth - 20;
        
        if (newListWidth >= minListWidth) {
            detailsPanel.style.flex = 'none';
            detailsPanel.style.width = newDetailsWidth + 'px';
            detailsPanel.style.minWidth = newDetailsWidth + 'px';
            detailsPanel.style.maxWidth = newDetailsWidth + 'px';
        }
        
        e.preventDefault();
    });
    
    document.addEventListener('mouseup', (e) => {
        if (!isResizing) return;
        
        isResizing = false;
        resizer.classList.remove('dragging');
        document.body.style.cursor = 'default';
        document.body.style.userSelect = 'auto';
    });
}
```

#### 8.1.3 查询功能完整实现
```javascript
// 查询功能完整实现
function performSearch() {
    const query = document.getElementById('query-input').value.toLowerCase().trim();
    
    if (query === '') {
        appState.displayedData = [...appState.allData];
    } else {
        appState.displayedData = appState.allData.filter(p => 
            p.name.toLowerCase().includes(query) || 
            p.employeeId.toLowerCase().includes(query)
        );
    }
    
    appState.currentPage = 1;
    appState.selectedPersonId = null;
    renderTable();
    renderDetailsPanel();
    
    // 更新状态栏信息
    const totalRecords = appState.displayedData.length;
    const statusText = query === '' ? 
        `共 ${totalRecords} 条记录` : 
        `查询到 ${totalRecords} 条记录`;
    document.getElementById('status-stats').textContent = statusText;
}

// 防抖查询处理
function handleQueryInput() {
    updateClearIconVisibility();
    
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        performSearch();
    }, 300);
}
```

### 8.2 状态管理系统

#### 8.2.1 应用状态定义
```javascript
// 应用状态集中管理 - 基于HTML原型实现
const appState = {
    // 选中状态
    selectedPersonId: null,
    
    // 编辑状态
    editingField: { 
        element: null, 
        personId: null, 
        field: null,
        originalContent: null
    },
    
    // 分页状态
    currentPage: 1,
    pageSize: 10,
    
    // 数据状态
    allData: [],
    displayedData: [],
    
    // 面板设置
    panelSettings: {
        detailsWidth: 400,
        isDetailsHidden: false
    },
    
    // 树形导航数据
    treeData: [
        { 
            name: '月度工资表', 
            children: [
                { 
                    name: '2025年', 
                    children: [
                        { 
                            name: '5月', 
                            children: [ 
                                { name: '全部在职人员', isDefault: true },
                                { name: '离休人员' }, 
                                { name: '退休人员' } 
                            ] 
                        }
                    ]
                }
            ]
        }
    ]
};
```

#### 8.2.2 数据生成函数
```javascript
// 模拟数据生成 - 基于HTML原型
function generateMockData(count = 159) {
    const data = [];
    const names = ['张三', '李四', '王五', '赵六', '钱七'];
    const departments = ['财务处', '人事处', '行政处', '技术部', '市场部'];
    
    for (let i = 1; i <= count; i++) {
        const originalBasic = Math.round((Math.random() * 5000 + 5000) / 100) * 100;
        const originalAllowance = Math.round((Math.random() * 2000 + 1000) / 50) * 50;
        
        data.push({
            id: i,
            name: names[Math.floor(Math.random() * names.length)] + i,
            employeeId: `202400${String(i).padStart(3, '0')}`,
            department: departments[Math.floor(Math.random() * departments.length)],
            position: '专员',
            basicSalary: originalBasic,
            allowance: originalAllowance,
            original_basicSalary: originalBasic,
            original_allowance: originalAllowance,
        });
    }
    return data;
}
```

### 8.3 渲染函数实现

#### 8.3.1 表格渲染
```javascript
// 表格渲染函数 - 基于HTML原型
function renderTable() {
    const tableContainer = document.getElementById('table-container');
    const { currentPage, pageSize, displayedData } = appState;
    
    if (displayedData.length === 0) {
        const placeholderDiv = document.createElement('div');
        placeholderDiv.className = 'details-placeholder';
        placeholderDiv.style.cssText = 'padding: 40px; text-align: center;';
        
        const mainMessage = document.createElement('p');
        mainMessage.style.cssText = 'color: #666; font-size: 16px; margin-bottom: 10px;';
        mainMessage.textContent = '没有可显示的数据。';
        placeholderDiv.appendChild(mainMessage);
        
        tableContainer.innerHTML = '';
        tableContainer.appendChild(placeholderDiv);
        renderPagination();
        return;
    }

    const pageData = displayedData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    let tableHtml = `<table class="data-table">
        <thead><tr><th>姓名</th><th>工号</th><th>部门</th><th>基本工资</th><th>岗位津贴</th></tr></thead>
        <tbody>`;
    
    pageData.forEach(p => {
        const isSelected = p.id === appState.selectedPersonId;
        tableHtml += `<tr data-id="${p.id}" class="${isSelected ? 'selected' : ''}" onclick="selectRow(this)">
            <td>${p.name}</td><td>${p.employeeId}</td><td>${p.department}</td><td>${p.basicSalary.toFixed(2)}</td><td>${p.allowance.toFixed(2)}</td>
        </tr>`;
    });

    tableHtml += `</tbody></table>`;
    tableContainer.innerHTML = tableHtml;
    renderPagination();
}
```

#### 8.3.2 详细信息面板渲染
```javascript
// 详细信息面板渲染 - 基于HTML原型
function renderDetailsPanel() {
    const detailsPanel = document.getElementById('details-panel');
    const person = appState.allData.find(p => p.id === appState.selectedPersonId);
    
    if (!person) {
        detailsPanel.innerHTML = `
            <div class="details-resizer" id="details-resizer" title="拖拽调整宽度，双击重置"></div>
            <button class="details-toggle" id="details-toggle" onclick="toggleDetailsPanel()" title="隐藏详细信息">▶</button>
            <div class="details-placeholder">
                <p style="padding: 20px; color: #999; text-align: center;">从左侧列表中选择一项以查看详细信息。</p>
            </div>`;
        return;
    }
    
    function createDiffHtml(current, original) {
        const diff = current - original;
        if (Math.abs(diff) < 0.01) return '';
        const sign = diff > 0 ? '+' : '';
        const className = diff > 0 ? 'positive' : '';
        return `<span class="diff-value ${className}">(${sign}${diff.toFixed(2)})</span>`;
    }

    const basicSalaryDiff = createDiffHtml(person.basicSalary, person.original_basicSalary);
    const allowanceDiff = createDiffHtml(person.allowance, person.original_allowance);

    detailsPanel.innerHTML = `
        <div class="details-resizer" id="details-resizer" title="拖拽调整宽度，双击重置"></div>
        <button class="details-toggle" id="details-toggle" onclick="toggleDetailsPanel()" title="隐藏详细信息">▶</button>
        <div class="details-header"><h3>${person.name} (${person.employeeId}) - 详细信息</h3></div>
        <div class="details-content">
            <div class="details-single-table">
                <table class="details-table">
                    <tr class="salary-row">
                        <th>基本工资</th>
                        <td>
                            <span class="original-value">${person.original_basicSalary.toFixed(2)}</span>
                            <div class="current-value-container">
                                <span class="current-value editable" data-field="basicSalary" data-id="${person.id}" ondblclick="enableEditMode(this)">
                                    ${person.basicSalary.toFixed(2)}
                                </span>
                                ${basicSalaryDiff}
                            </div>
                        </td>
                    </tr>
                    <tr class="salary-row">
                        <th>岗位津贴</th>
                        <td>
                            <span class="original-value">${person.original_allowance.toFixed(2)}</span>
                            <div class="current-value-container">
                                <span class="current-value editable" data-field="allowance" data-id="${person.id}" ondblclick="enableEditMode(this)">
                                    ${person.allowance.toFixed(2)}
                                </span>
                                ${allowanceDiff}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th>部门</th>
                        <td>${person.department}</td>
                    </tr>
                    <tr>
                        <th>职位</th>
                        <td>${person.position}</td>
                    </tr>
                </table>
            </div>
        </div>
    `;
    
    // 重新初始化拖拽功能
    setTimeout(() => {
        initDetailsResizer();
    }, 10);
}
```

### 8.4 初始化函数

#### 8.4.1 应用初始化
```javascript
// 应用初始化 - 完整实现
function init() {
    // 生成模拟数据
    appState.allData = generateMockData();
    appState.displayedData = [...appState.allData];
    
    // 渲染界面
    renderTreeView();
    renderTable();
    renderDetailsPanel();
    
    // 更新状态栏
    document.getElementById('status-stats').textContent = `共 ${appState.allData.length} 条记录`;
    
    // 初始化查询输入框状态
    updateClearIconVisibility();
    
    // 初始化事件监听器
    const queryInput = document.getElementById('query-input');
    if (queryInput) {
        queryInput.addEventListener('input', handleQueryInput);
        queryInput.addEventListener('keydown', handleQueryKeydown);
    }

    // 初始化拖拽调整大小功能
    setTimeout(() => {
        initDetailsResizer();
        console.log('详细信息面板控制功能已初始化');
    }, 100);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    init();
});
```

### 8.5 组件化开发建议

#### 8.5.1 模块划分
- **数据管理模块**：`appState`、`generateMockData`、数据CRUD操作
- **渲染模块**：`renderTable`、`renderDetailsPanel`、`renderPagination`
- **交互模块**：事件处理函数、拖拽控制、编辑模式
- **工具模块**：防抖函数、格式化函数、验证函数

#### 8.5.2 性能优化策略
- **事件委托**：减少事件监听器数量，提升性能
- **防抖处理**：查询输入300ms防抖，避免频繁请求
- **虚拟滚动**：大数据量时使用虚拟滚动优化渲染
- **状态缓存**：面板设置状态持久化到localStorage

#### 8.5.3 扩展性考虑
- **插件系统**：为导出功能、数据验证等提供插件接口
- **主题系统**：支持自定义色彩主题和字体大小
- **国际化支持**：为多语言环境预留接口
- **数据源适配**：支持多种数据源格式（Excel、CSV、API）

## 9. 总结与实施建议 (v7.0)

### 9.1 设计成果总结

#### 9.1.1 核心特性实现
本UI设计规范基于`main_interface_prototype.html`的实际功能实现，确保了以下核心特性：

1. **智能面板控制**：详细信息面板支持拖拽调整、显示/隐藏切换
2. **增强查询功能**：集成清除图标、防抖搜索、状态管理
3. **响应式布局**：支持1024px到1920px+的完整屏幕适配
4. **数据树形导航**：4级智能展开系统，优化用户体验
5. **编辑功能集成**：双击编辑、原值对比、差值显示
6. **专业化交互**：针对财务专业需求优化的操作流程

#### 9.1.2 技术架构优势
- **纯前端实现**：无需复杂后端，降低部署难度
- **模块化设计**：组件间低耦合，易于维护和扩展
- **状态集中管理**：appState统一管理所有应用状态
- **性能优化**：防抖查询、事件委托、懒加载渲染

### 9.2 项目实施路线图

#### 9.2.1 短期实施（1-2周）
1. **基础功能完善**
   - 完善编辑模式功能，增加数据验证
   - 优化拖拽调整的边界检测
   - 增强查询功能的模糊匹配能力

2. **用户体验优化**
   - 添加操作提示和确认对话框
   - 完善键盘快捷键支持
   - 增加操作撤销/重做功能

#### 9.2.2 中期改进（3-4周）
1. **数据处理增强**
   - 集成Excel文件读取功能
   - 添加数据导出多格式支持
   - 实现数据验证和错误提示

2. **功能扩展**
   - 添加批量编辑功能
   - 实现数据备份和恢复
   - 增加计算公式配置

#### 9.2.3 长期优化（1-2月）
1. **系统集成**
   - 与OA系统对接
   - 数据库集成支持
   - 权限管理系统

2. **智能化功能**
   - 异动检测算法
   - 数据分析图表
   - 自动化报表生成

### 9.3 部署实施指导

#### 9.3.1 环境要求
```markdown
## 服务器环境
- **操作系统**：Windows Server 2016+ / Linux
- **Web服务器**：IIS 8.0+ / Nginx 1.18+ / Apache 2.4+
- **浏览器支持**：Chrome 90+, Firefox 88+, Edge 90+
- **分辨率支持**：1024x768 至 1920x1080+

## 文件结构
```
salary-changes-system/
├── index.html          # 主页面
├── css/
│   ├── main.css        # 主样式文件
│   └── responsive.css  # 响应式样式
├── js/
│   ├── app.js          # 主应用逻辑
│   ├── components/     # 组件模块
│   └── utils/          # 工具函数
├── assets/
│   ├── icons/          # 图标资源
│   └── templates/      # 模板文件
└── docs/              # 文档
```

#### 9.3.2 配置部署
```bash
# 1. 部署到Web服务器
cp -r salary-changes-system/ /var/www/html/

# 2. 配置Nginx（示例）
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/salary-changes-system;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript text/html;
}

# 3. 重启服务
systemctl restart nginx
```

### 9.4 后续优化建议

#### 9.4.1 性能优化
1. **资源优化**
   - CSS/JS文件压缩
   - 图片懒加载
   - CDN资源加速

2. **渲染优化**
   - 虚拟滚动实现
   - 分批渲染大数据
   - 缓存计算结果

#### 9.4.2 用户体验提升
1. **交互增强**
   - 添加快捷键支持
   - 优化触控设备体验
   - 增加操作引导

2. **视觉优化**
   - 暗色主题支持
   - 字体大小调节
   - 色彩对比度优化

### 9.5 质量保证

#### 9.5.1 测试策略
```markdown
## 测试检查清单

### 功能测试
- [ ] 数据导入导出功能正常
- [ ] 查询功能覆盖所有字段
- [ ] 编辑功能数据验证正确
- [ ] 面板拖拽调整流畅
- [ ] 响应式布局各断点正常

### 兼容性测试
- [ ] Chrome浏览器测试通过
- [ ] Firefox浏览器测试通过
- [ ] Edge浏览器测试通过
- [ ] 1024px分辨率测试通过
- [ ] 1920px分辨率测试通过

### 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] 1000条数据渲染 < 1秒
- [ ] 查询响应时间 < 500ms
- [ ] 面板拖拽帧率 > 30fps
```

#### 9.5.2 代码质量
- **代码审查**：每个功能模块需要同行审查
- **文档更新**：代码变更同步更新技术文档
- **版本控制**：使用Git管理代码版本
- **持续集成**：自动化测试和部署流程

---

**文档版本**：v9.1
**最后更新**：2024年12月
**维护状态**：持续维护，定期更新
**技术支持**：基于HTML原型v1.3.1的完整实现指导
**主要更新**：简化行展开功能、差异值颜色标识、展开状态管理优化、表格行式布局设计