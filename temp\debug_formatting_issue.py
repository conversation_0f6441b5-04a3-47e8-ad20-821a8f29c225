#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试格式化问题的测试脚本
验证配置加载和格式化逻辑是否正常工作
"""

import sys
import os
import pandas as pd

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def debug_formatting_issue():
    """调试格式化问题"""
    print("[DEBUG] 开始调试格式化问题...")
    
    try:
        # 1. 检查字段注册系统配置
        print("\n[DEBUG 1] 检查字段注册系统配置:")
        from src.modules.format_management.field_registry import FieldRegistry
        
        field_registry = FieldRegistry("state/data/field_mappings.json")
        
        # 测试具体表名
        table_name = "salary_data_2025_07_active_employees"
        field_types_specific = field_registry.get_table_field_types(table_name)
        print(f"具体表名 '{table_name}' 字段类型总数: {len(field_types_specific)}")
        
        # 检查关键字段
        critical_fields = ['basic_performance_2025', 'performance_bonus_2025', 'car_allowance', 'pension_insurance']
        for field in critical_fields:
            field_type = field_types_specific.get(field, 'NOT_FOUND')
            print(f"  {field}: {field_type}")
        
        # 测试通用表名
        generic_name = "active_employees"
        field_types_generic = field_registry.get_table_field_types(generic_name)
        print(f"\n通用表名 '{generic_name}' 字段类型总数: {len(field_types_generic)}")
        
        for field in critical_fields:
            field_type = field_types_generic.get(field, 'NOT_FOUND')
            print(f"  {field}: {field_type}")
        
        # 2. 检查表名映射逻辑
        print("\n[DEBUG 2] 检查表名映射逻辑:")
        from src.services.table_data_service import TableDataService
        
        # 创建临时实例来测试表名映射
        import tempfile
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.core.application_state_service import ApplicationStateService
        
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db_manager = DynamicTableManager(temp_db.name)
        state_service = ApplicationStateService()
        
        table_service = TableDataService(db_manager, state_service)
        
        extracted_type = table_service._extract_table_type_from_name(table_name)
        print(f"表名映射结果: '{table_name}' -> '{extracted_type}'")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        
        # 3. 测试格式化渲染器
        print("\n[DEBUG 3] 测试格式化渲染器:")
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        format_config = FormatConfig("src/modules/format_management/format_config.json") 
        renderer = FormatRenderer(format_config, field_registry)
        
        # 创建测试数据
        test_data = {
            'basic_performance_2025': [1234, 5678.0, 0, None],
            'performance_bonus_2025': [2000, 1500.5, 0.0, None], 
            'car_allowance': [100.5, 150, "-", None],
            'pension_insurance': [200.75, 250.5, 0.0, None]
        }
        
        df = pd.DataFrame(test_data)
        print(f"原始测试数据:\n{df}")
        
        # 使用具体表名进行格式化
        try:
            formatted_df_specific = renderer.render_dataframe(df, table_name)
            print(f"\n使用具体表名格式化结果:\n{formatted_df_specific}")
        except Exception as e:
            print(f"使用具体表名格式化失败: {e}")
        
        # 使用通用表名进行格式化
        try:
            formatted_df_generic = renderer.render_dataframe(df, generic_name) 
            print(f"\n使用通用表名格式化结果:\n{formatted_df_generic}")
        except Exception as e:
            print(f"使用通用表名格式化失败: {e}")
        
        # 4. 测试单个值格式化
        print("\n[DEBUG 4] 测试单个值格式化:")
        
        # 测试浮点数格式化
        test_values = [1234, 1234.0, 0, 0.0, None, "-", "150.5"]
        format_cfg = {"decimal_places": 2}
        
        for value in test_values:
            try:
                formatted_value = renderer._render_float_value(value, format_cfg, "basic_performance_2025")
                print(f"  {repr(value)} -> '{formatted_value}'")
            except Exception as e:
                print(f"  {repr(value)} -> ERROR: {e}")
        
        # 特别测试车补字段
        print("\n车补字段特殊测试:")
        car_values = [100.5, 150, "-", None, ""]
        for value in car_values:
            try:
                formatted_value = renderer._render_float_value(value, format_cfg, "car_allowance")
                print(f"  车补 {repr(value)} -> '{formatted_value}'")
            except Exception as e:
                print(f"  车补 {repr(value)} -> ERROR: {e}")
        
        print("\n[DEBUG] 调试完成!")
        return True
        
    except Exception as e:
        print(f"[DEBUG] 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_formatting_issue()