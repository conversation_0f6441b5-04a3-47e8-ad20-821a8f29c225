#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页和表头修复验证测试脚本
验证P0-P1优先级修复的关键问题

测试内容：
1. P0-1: HeaderUpdateManager.force_update_all()参数修复验证
2. P0-2: UnifiedStateManager状态保存数据类型修复验证  
3. P1-1: QBasicTimer多线程问题修复验证
4. P1-2: 分页UI同步机制验证

执行方式：
python temp/pagination_header_fix_validation.py
"""

import sys
import os
import re
from pathlib import Path

def validate_p0_1_header_update_fix():
    """验证P0-1: HeaderUpdateManager.force_update_all()参数修复"""
    print("\n🔧 P0-1验证: HeaderUpdateManager.force_update_all()参数修复")
    
    try:
        # 检查prototype_main_window.py中的修复
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes_found = 0
        
        # 1. 检查参数计算
        if "headers = list(mapped_data.columns)" in content:
            print("✅ 找到表头参数计算: headers = list(mapped_data.columns)")
            fixes_found += 1
        
        # 2. 检查起始记录计算 
        if "start_record = (page - 1) * 50 + 1" in content:
            print("✅ 找到起始记录计算: start_record = (page - 1) * 50 + 1")
            fixes_found += 1
            
        # 3. 检查记录数计算
        if "count = len(mapped_data)" in content:
            print("✅ 找到记录数计算: count = len(mapped_data)")
            fixes_found += 1
            
        # 4. 检查正确的方法调用
        if "force_update_all(headers=headers, start_record=start_record, count=count)" in content:
            print("✅ 找到正确的方法调用: force_update_all(headers=headers, start_record=start_record, count=count)")
            fixes_found += 1
        
        # 5. 检查P0-1修复标识
        if "🔧 [P0-1修复]" in content:
            print("✅ 找到P0-1修复标识")
            fixes_found += 1
            
        if fixes_found >= 4:
            print(f"✅ P0-1修复验证通过: {fixes_found}/5 项检查通过")
            return True
        else:
            print(f"❌ P0-1修复验证失败: 仅{fixes_found}/5 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P0-1验证异常: {e}")
        return False

def validate_p0_2_state_manager_fix():
    """验证P0-2: UnifiedStateManager状态保存数据类型修复"""
    print("\n🔧 P0-2验证: UnifiedStateManager状态保存数据类型修复")
    
    try:
        # 检查两个关键文件的修复
        
        # 1. 检查prototype_main_window.py中的状态数据收集修复
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
            
        with open(main_window_file, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        fixes_found = 0
        
        # 检查状态数据结构修复
        if "'column_widths': {}" in main_content:
            print("✅ 找到列宽字典结构: 'column_widths': {}")
            fixes_found += 1
            
        if "'sort_columns': []" in main_content:
            print("✅ 找到排序列表结构: 'sort_columns': []")
            fixes_found += 1
            
        if "success = self.state_manager.save_table_state_by_name(table_name, current_state)" in main_content:
            print("✅ 找到正确的状态保存调用: 传递current_state而非table_widget")
            fixes_found += 1
            
        # 2. 检查unified_state_manager.py中的TableState属性修复
        state_manager_file = Path("src/core/unified_state_manager.py")
        if not state_manager_file.exists():
            print("❌ UnifiedStateManager文件不存在")
            return False
            
        with open(state_manager_file, 'r', encoding='utf-8') as f:
            state_content = f.read()
            
        if "column_widths: Dict[str, int] = field(default_factory=dict)" in state_content:
            print("✅ 找到TableState.column_widths属性定义")
            fixes_found += 1
            
        if "selected_fields: List[str] = field(default_factory=list)" in state_content:
            print("✅ 找到TableState.selected_fields属性定义")
            fixes_found += 1
            
        # 检查序列化修复
        if '"column_widths": table_state.column_widths' in state_content:
            print("✅ 找到序列化方法中的column_widths")
            fixes_found += 1
            
        if fixes_found >= 5:
            print(f"✅ P0-2修复验证通过: {fixes_found}/6 项检查通过")
            return True
        else:
            print(f"❌ P0-2修复验证失败: 仅{fixes_found}/6 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P0-2验证异常: {e}")
        return False

def validate_p1_1_thread_safe_timer_fix():
    """验证P1-1: QBasicTimer多线程问题修复"""
    print("\n🔧 P1-1验证: QBasicTimer多线程问题修复")
    
    try:
        fixes_found = 0
        
        # 1. 检查error_handler_manager.py修复
        error_handler_file = Path("src/core/error_handler_manager.py")
        if error_handler_file.exists():
            with open(error_handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "from src.utils.thread_safe_timer import ThreadSafeTimer" in content:
                print("✅ 找到ThreadSafeTimer导入: error_handler_manager.py")
                fixes_found += 1
                
            if "ThreadSafeTimer.safe_single_shot(0" in content:
                print("✅ 找到线程安全定时器调用: error_handler_manager.py")
                fixes_found += 1
        
        # 2. 检查enhanced_navigation_panel.py修复
        nav_panel_file = Path("src/gui/prototype/widgets/enhanced_navigation_panel.py")
        if nav_panel_file.exists():
            with open(nav_panel_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "ThreadSafeTimer.safe_single_shot(800" in content:
                print("✅ 找到线程安全定时器调用: enhanced_navigation_panel.py")
                fixes_found += 1
        
        # 3. 检查ThreadSafeTimer工具类是否存在
        timer_util_file = Path("src/utils/thread_safe_timer.py")
        if timer_util_file.exists():
            with open(timer_util_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "def is_main_thread()" in content:
                print("✅ 找到主线程检查方法: is_main_thread()")
                fixes_found += 1
                
            if "def safe_single_shot(" in content:
                print("✅ 找到线程安全单次定时器方法: safe_single_shot()")
                fixes_found += 1
        
        if fixes_found >= 3:
            print(f"✅ P1-1修复验证通过: {fixes_found}/5 项检查通过")
            return True
        else:
            print(f"❌ P1-1修复验证失败: 仅{fixes_found}/5 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P1-1验证异常: {e}")
        return False

def validate_p1_2_ui_sync_mechanism():
    """验证P1-2: 分页UI同步机制优化"""
    print("\n🔧 P1-2验证: 分页UI同步机制优化")
    
    try:
        # 检查prototype_main_window.py中的UI同步验证机制
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("❌ PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_found = 0
        
        # 1. 检查UI同步验证方法
        if "def _verify_pagination_ui_sync(" in content:
            print("✅ 找到分页UI同步验证方法: _verify_pagination_ui_sync()")
            fixes_found += 1
            
        # 2. 检查分页状态验证
        if "actual_page = self.main_workspace.pagination_widget.state.current_page" in content:
            print("✅ 找到分页状态验证逻辑")
            fixes_found += 1
            
        # 3. 检查表头显示验证
        if "if first_header.isdigit():" in content:
            print("✅ 找到表头数字显示检测逻辑")
            fixes_found += 1
            
        # 4. 检查同步验证调用
        if "self._verify_pagination_ui_sync(page, current_table_name, mapped_data)" in content:
            print("✅ 找到UI同步验证调用")
            fixes_found += 1
            
        # 5. 检查P1-2修复标识
        if "🔧 [P1-2修复]" in content:
            print("✅ 找到P1-2修复标识")
            fixes_found += 1
            
        if fixes_found >= 4:
            print(f"✅ P1-2修复验证通过: {fixes_found}/5 项检查通过")
            return True
        else:
            print(f"❌ P1-2修复验证失败: 仅{fixes_found}/5 项检查通过")
            return False
            
    except Exception as e:
        print(f"❌ P1-2验证异常: {e}")
        return False

def validate_field_mapping_config():
    """验证字段映射配置完整性"""
    print("\n🔧 辅助验证: 字段映射配置完整性")
    
    try:
        # 检查字段映射配置文件
        mapping_file = Path("state/data/field_mappings.json")
        if not mapping_file.exists():
            print("❌ 字段映射配置文件不存在")
            return False
        
        import json
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        
        # 检查目标表的映射
        target_table = "salary_data_2025_08_active_employees"
        if "table_mappings" in mappings and target_table in mappings["table_mappings"]:
            field_mappings = mappings["table_mappings"][target_table].get("field_mappings", {})
            
            # 检查关键字段映射
            key_mappings = {
                "employee_id": "工号",
                "employee_name": "姓名", 
                "department": "部门名称"
            }
            
            found_mappings = 0
            for english, chinese in key_mappings.items():
                if english in field_mappings and field_mappings[english] == chinese:
                    print(f"✅ 字段映射正确: {english} -> {chinese}")
                    found_mappings += 1
                else:
                    print(f"❌ 字段映射错误或缺失: {english}")
            
            if found_mappings >= 2:
                print(f"✅ 字段映射配置验证通过: {found_mappings}/{len(key_mappings)} 个关键映射正确")
                return True
            else:
                print(f"❌ 字段映射配置验证失败: 仅{found_mappings}/{len(key_mappings)} 个关键映射正确")
                return False
        else:
            print(f"❌ 目标表 {target_table} 的映射配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 字段映射验证异常: {e}")
        return False

def main():
    """主验证函数"""
    print("🔧 分页和表头显示修复验证测试")
    print("=" * 60)
    print("验证P0-P1优先级修复的关键问题解决情况")
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    tests = [
        ("P0-1: HeaderUpdateManager参数修复", validate_p0_1_header_update_fix),
        ("P0-2: UnifiedStateManager数据类型修复", validate_p0_2_state_manager_fix),
        ("P1-1: QBasicTimer多线程问题修复", validate_p1_1_thread_safe_timer_fix),
        ("P1-2: 分页UI同步机制优化", validate_p1_2_ui_sync_mechanism),
        ("辅助: 字段映射配置完整性", validate_field_mapping_config),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n>>> 执行验证: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} - 验证通过")
            else:
                print(f"❌ {test_name} - 验证失败")
        except Exception as e:
            print(f"💥 {test_name} - 验证异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 验证结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} - {test_name}")
    
    print(f"\n📊 总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有关键修复验证通过！")
        print("✅ 分页功能应该能够正常工作")
        print("✅ 表头应该正确显示中文（工号、姓名、部门名称等）")
        print("✅ 不再出现QBasicTimer多线程警告") 
        print("✅ UI状态保存和恢复应该正常工作")
        print("\n🚀 请重新启动系统测试分页和表头显示效果！")
        return True
    else:
        print(f"\n⚠️ {total - passed} 项验证失败，可能仍存在问题")
        print("📋 建议检查失败项目的具体修复内容")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)