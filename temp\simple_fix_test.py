#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化修复验证测试脚本
"""

import sys
import os
sys.path.append('.')

def test_fix():
    print("Testing ArchitectureFactory fix...")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        print("SUCCESS: Module imports OK")
        
        config_manager = ConfigManager()
        db_manager = DynamicTableManager("data/salary_system.db")
        factory = get_architecture_factory(db_manager, config_manager)
        
        print("SUCCESS: Components initialized")
        
        if factory.initialize_architecture():
            print("SUCCESS: Architecture initialized")
        else:
            print("FAILED: Architecture initialization")
            return False
        
        # Test the key method
        unified_format_manager = factory.get_unified_format_manager()
        print("SUCCESS: get_unified_format_manager() works!")
        print(f"SUCCESS: Created {type(unified_format_manager)}")
        
        # Test basic functionality
        system_status = unified_format_manager.get_system_status()
        print(f"SUCCESS: System status retrieved: initialized={system_status.get('initialized', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fix()
    if success:
        print("\n*** FIX VERIFICATION: SUCCESS ***")
        print("The get_unified_format_manager method is working correctly!")
    else:
        print("\n*** FIX VERIFICATION: FAILED ***")
    
    sys.exit(0 if success else 1)