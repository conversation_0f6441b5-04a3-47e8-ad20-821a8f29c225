# -*- coding: utf-8 -*-
"""
分页行号调试脚本

用于调试实际系统中分页行号显示问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger


def debug_pagination_row_numbers():
    """调试分页行号显示问题"""
    logger = setup_logger("PaginationDebugger")
    
    # 查找相关日志记录
    log_file = project_root / "logs" / "salary_system.log"
    
    if not log_file.exists():
        logger.error(f"日志文件不存在: {log_file}")
        return
        
    logger.info("开始分析分页行号相关的日志记录...")
    
    # 分析日志中的分页相关信息
    pagination_events = []
    row_number_events = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 搜索分页状态设置相关的日志
            if "set_pagination_state" in line or "分页状态" in line:
                pagination_events.append((line_num, line))
                
            # 搜索行号设置相关的日志
            if ("行号" in line and ("设置" in line or "更新" in line)) or "_setup_row_numbers" in line:
                row_number_events.append((line_num, line))
                
            # 搜索分页操作相关的日志
            if "页码" in line or "current_page" in line or "page_changed" in line:
                if "分页" in line or "pagination" in line:
                    pagination_events.append((line_num, line))
    
        # 输出分析结果
        logger.info(f"找到 {len(pagination_events)} 条分页相关事件:")
        for line_num, line in pagination_events[-10:]:  # 显示最近10条
            print(f"  {line_num}: {line}")
            
        logger.info(f"找到 {len(row_number_events)} 条行号相关事件:")
        for line_num, line in row_number_events[-10:]:  # 显示最近10条
            print(f"  {line_num}: {line}")
            
        # 分析可能的问题
        logger.info("分析可能的问题:")
        
        if len(pagination_events) == 0:
            logger.warning("❌ 没有找到分页状态设置的日志，可能分页状态没有正确设置")
        elif len(row_number_events) == 0:
            logger.warning("❌ 没有找到行号设置的日志，可能行号设置方法没有被调用")
        else:
            logger.info("✅ 找到了分页和行号相关的日志记录")
            
        # 检查最近的分页操作
        recent_pagination = [e for e in pagination_events if "current_page" in e[1] or "页码" in e[1]]
        if recent_pagination:
            logger.info("最近的分页操作:")
            for line_num, line in recent_pagination[-5:]:
                print(f"  {line_num}: {line}")
                
    except Exception as e:
        logger.error(f"分析日志文件失败: {e}")


def create_pagination_debug_patch():
    """创建分页调试补丁"""
    logger = setup_logger("PaginationPatcher")
    
    patch_code = '''
# 在VirtualizedExpandableTable的set_pagination_state方法中添加详细调试
def set_pagination_state_debug(self, pagination_state: dict):
    """增强版分页状态设置，带详细调试"""
    try:
        self.logger.info(f"🔧 [分页调试] 接收到分页状态: {pagination_state}")
        
        # 检查必要参数
        required_keys = ['current_page', 'page_size', 'total_records', 'start_record', 'end_record']
        missing_keys = [key for key in required_keys if key not in pagination_state]
        if missing_keys:
            self.logger.error(f"🔧 [分页调试] 缺少必要参数: {missing_keys}")
            return
            
        # 验证参数合理性
        current_page = pagination_state.get('current_page', 1)
        start_record = pagination_state.get('start_record', 1)
        end_record = pagination_state.get('end_record', 0)
        
        if current_page < 1:
            self.logger.error(f"🔧 [分页调试] 页码无效: {current_page}")
            return
            
        if start_record < 1:
            self.logger.error(f"🔧 [分页调试] 起始记录号无效: {start_record}")
            return
            
        if end_record < start_record:
            self.logger.error(f"🔧 [分页调试] 记录号范围无效: {start_record}-{end_record}")
            return
            
        self.pagination_state = pagination_state
        self._pagination_mode = True
        
        # 获取当前表格行数
        current_row_count = self.rowCount()
        expected_row_count = end_record - start_record + 1
        
        self.logger.info(f"🔧 [分页调试] 表格行数: {current_row_count}, 期望行数: {expected_row_count}")
        
        if current_row_count != expected_row_count:
            self.logger.warning(f"🔧 [分页调试] 行数不匹配，可能影响行号设置")
            
        # 更新行号前记录当前状态
        old_labels = []
        for i in range(min(5, current_row_count)):
            header_item = self.verticalHeaderItem(i)
            old_labels.append(header_item.text() if header_item else str(i+1))
            
        self.logger.info(f"🔧 [分页调试] 更新前行号: {old_labels}")
        
        # 更新行号
        self._update_pagination_row_labels_only()
        
        # 验证更新后的行号
        new_labels = []
        for i in range(min(5, current_row_count)):
            header_item = self.verticalHeaderItem(i)
            new_labels.append(header_item.text() if header_item else str(i+1))
            
        self.logger.info(f"🔧 [分页调试] 更新后行号: {new_labels}")
        
        # 验证期望结果
        expected_labels = [str(start_record + i) for i in range(min(5, current_row_count))]
        self.logger.info(f"🔧 [分页调试] 期望行号: {expected_labels}")
        
        if new_labels == expected_labels:
            self.logger.info(f"🔧 [分页调试] ✅ 行号更新成功")
        else:
            self.logger.error(f"🔧 [分页调试] ❌ 行号更新失败")
            
    except Exception as e:
        self.logger.error(f"🔧 [分页调试] 设置分页状态失败: {e}")
        import traceback
        traceback.print_exc()
'''
    
    # 保存调试补丁代码
    patch_file = project_root / "temp" / "pagination_debug_patch.py"
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_code)
        
    logger.info(f"调试补丁已创建: {patch_file}")
    
    # 提供使用说明
    usage_instructions = f"""
使用说明：
1. 将以下代码添加到 VirtualizedExpandableTable 类中：
   {patch_file}
   
2. 在需要调试的地方，临时替换 set_pagination_state 方法：
   # 在类的 __init__ 方法中添加
   self.set_pagination_state = lambda state: self.set_pagination_state_debug(state)
   
3. 运行系统并进行分页操作，查看详细的调试日志

4. 或者直接应用以下快速修复：
"""
    
    print(usage_instructions)
    return patch_file


def suggest_quick_fixes():
    """建议快速修复方案"""
    logger = setup_logger("QuickFixes")
    
    fixes = [
        {
            "名称": "强制行号更新修复",
            "描述": "在数据设置后强制更新行号",
            "文件": "src/gui/prototype/widgets/virtualized_expandable_table.py", 
            "方法": "set_data",
            "修复": "在set_data方法末尾添加强制行号更新"
        },
        {
            "名称": "分页状态延迟设置修复",
            "描述": "延迟设置分页状态，确保表格数据已经设置完成",
            "文件": "src/gui/prototype/prototype_main_window.py",
            "方法": "_set_paginated_data",
            "修复": "使用QTimer延迟50ms设置分页状态"
        },
        {
            "名称": "分页状态验证修复",
            "描述": "在设置分页状态前验证参数",
            "文件": "src/gui/prototype/widgets/virtualized_expandable_table.py",
            "方法": "set_pagination_state", 
            "修复": "添加参数验证和错误处理"
        }
    ]
    
    logger.info("建议的快速修复方案:")
    for i, fix in enumerate(fixes, 1):
        print(f"{i}. {fix['名称']}")
        print(f"   描述: {fix['描述']}")
        print(f"   文件: {fix['文件']}")
        print(f"   方法: {fix['方法']}")
        print(f"   修复: {fix['修复']}")
        print()


def main():
    """主函数"""
    print("🔧 分页行号调试工具")
    print("=" * 50)
    
    # 1. 分析日志
    print("1. 分析日志记录...")
    debug_pagination_row_numbers()
    print()
    
    # 2. 创建调试补丁
    print("2. 创建调试补丁...")
    patch_file = create_pagination_debug_patch()
    print()
    
    # 3. 建议修复方案
    print("3. 建议修复方案...")
    suggest_quick_fixes()
    print()
    
    # 4. 检查实际问题
    print("4. 检查可能的问题原因:")
    print("   - 分页状态计算错误")
    print("   - set_pagination_state调用时机不对")
    print("   - 表格数据设置与分页状态设置不同步")
    print("   - 行号更新方法没有被正确调用")
    print("   - 分页模式标志设置错误")
    print()
    
    print("请根据日志分析结果和建议的修复方案进行问题定位和修复。")


if __name__ == '__main__':
    main() 