"""
月度工资异动处理系统 - 可展开表格组件

本模块实现Phase 3的行展开/折叠功能，提供树形展示和详情面板展开。

主要功能:
1. 树形展示支持
2. 详情面板展开
3. 嵌套数据显示
4. 动画效果
5. 层级管理
6. 展开状态保存
"""

import sys
from typing import List, Dict, Any, Optional, Union, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, QSplitter, QFrame, QLabel,
    QPushButton, QTextEdit, QGroupBox, QScrollArea, QTabWidget,
    QHeaderView, QAbstractItemView, QApplication, QSizePolicy
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QTimer, QAbstractAnimation, QParallelAnimationGroup
)
from PyQt5.QtGui import QFont, QColor, QBrush, QIcon, QPalette

from src.utils.log_config import setup_logger
from src.gui.modern_table_editor import EditableTableWidget


class ExpandableTreeItem(QTreeWidgetItem):
    """可展开的树形项"""
    
    def __init__(self, parent=None, item_type: str = "normal"):
        """初始化树形项"""
        super().__init__(parent)
        self.item_type = item_type  # normal, group, detail
        self.detail_data = {}  # 详细数据
        self.is_expanded_custom = False
        self.child_count = 0
        
    def set_detail_data(self, data: Dict[str, Any]):
        """设置详细数据"""
        self.detail_data = data
        
    def get_detail_data(self) -> Dict[str, Any]:
        """获取详细数据"""
        return self.detail_data
        
    def set_item_type(self, item_type: str):
        """设置项目类型"""
        self.item_type = item_type
        self._update_style()
        
    def _update_style(self):
        """更新样式"""
        if self.item_type == "group":
            # 分组项样式
            font = QFont()
            font.setBold(True)
            font.setPointSize(12)
            self.setFont(0, font)
            self.setBackground(0, QBrush(QColor(240, 248, 255)))
            
        elif self.item_type == "detail":
            # 详情项样式
            font = QFont()
            font.setPointSize(10)
            self.setFont(0, font)
            self.setForeground(0, QBrush(QColor(108, 117, 125)))


class DetailPanel(QWidget):
    """详情面板组件"""
    
    # 信号定义
    panel_closed = pyqtSignal()
    data_modified = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """初始化详情面板"""
        super().__init__(parent)
        self.current_data = {}
        self._init_ui()
        self._init_animations()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题栏
        title_layout = QHBoxLayout()
        
        self.title_label = QLabel("详细信息")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        title_layout.addWidget(self.title_label)
        
        title_layout.addStretch()
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(24, 24)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.close_btn.clicked.connect(self.panel_closed.emit)
        title_layout.addWidget(self.close_btn)
        
        layout.addLayout(title_layout)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet("background-color: #dee2e6;")
        layout.addWidget(line)
        
        # 内容区域
        self.content_area = QTabWidget()
        self.content_area.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
        """)
        
        # 基本信息标签
        self._create_basic_info_tab()
        
        # 扩展信息标签
        self._create_extended_info_tab()
        
        # 历史记录标签
        self._create_history_tab()
        
        layout.addWidget(self.content_area)
        
        # 设置面板样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
    def _create_basic_info_tab(self):
        """创建基本信息标签"""
        basic_widget = QScrollArea()
        basic_content = QWidget()
        basic_layout = QVBoxLayout(basic_content)
        
        # 基本字段显示区域
        self.basic_fields_layout = QVBoxLayout()
        basic_layout.addLayout(self.basic_fields_layout)
        basic_layout.addStretch()
        
        basic_widget.setWidget(basic_content)
        basic_widget.setWidgetResizable(True)
        
        self.content_area.addTab(basic_widget, "基本信息")
        
    def _create_extended_info_tab(self):
        """创建扩展信息标签"""
        extended_widget = QScrollArea()
        extended_content = QWidget()
        extended_layout = QVBoxLayout(extended_content)
        
        # 扩展字段显示区域
        self.extended_fields_layout = QVBoxLayout()
        extended_layout.addLayout(self.extended_fields_layout)
        extended_layout.addStretch()
        
        extended_widget.setWidget(extended_content)
        extended_widget.setWidgetResizable(True)
        
        self.content_area.addTab(extended_widget, "扩展信息")
        
    def _create_history_tab(self):
        """创建历史记录标签"""
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)
        
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        self.history_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        history_layout.addWidget(self.history_text)
        
        self.content_area.addTab(history_widget, "历史记录")
        
    def _init_animations(self):
        """初始化动画效果"""
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(300)
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        
    def show_data(self, data: Dict[str, Any], title: str = "详细信息"):
        """显示数据"""
        self.current_data = data
        self.title_label.setText(title)
        
        # 清空现有内容
        self._clear_layouts()
        
        # 分类显示数据
        basic_fields = ["姓名", "工号", "部门", "职位", "基本工资", "状态"]
        extended_fields = []
        
        for key, value in data.items():
            if key in basic_fields:
                self._add_field_to_layout(self.basic_fields_layout, key, value)
            else:
                extended_fields.append((key, value))
        
        # 添加扩展字段
        for key, value in extended_fields:
            self._add_field_to_layout(self.extended_fields_layout, key, value)
        
        # 生成历史记录
        self._generate_history_text(data)
        
        # 显示动画
        self.show()
        self._animate_show()
        
    def _clear_layouts(self):
        """清空布局"""
        self._clear_layout(self.basic_fields_layout)
        self._clear_layout(self.extended_fields_layout)
        
    def _clear_layout(self, layout):
        """清空指定布局"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def _add_field_to_layout(self, layout, key: str, value: Any):
        """添加字段到布局"""
        field_frame = QFrame()
        field_frame.setFrameStyle(QFrame.StyledPanel)
        field_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin: 2px;
            }
        """)
        
        field_layout = QHBoxLayout(field_frame)
        field_layout.setContentsMargins(8, 6, 8, 6)
        
        # 字段名
        key_label = QLabel(f"{key}:")
        key_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #495057;
                min-width: 80px;
            }
        """)
        field_layout.addWidget(key_label)
        
        # 字段值
        value_label = QLabel(str(value))
        value_label.setStyleSheet("""
            QLabel {
                color: #212529;
                padding-left: 8px;
            }
        """)
        value_label.setWordWrap(True)
        field_layout.addWidget(value_label, 1)
        
        layout.addWidget(field_frame)
        
    def _generate_history_text(self, data: Dict[str, Any]):
        """生成历史记录文本"""
        history_text = "数据记录历史:\n"
        history_text += "=" * 40 + "\n"
        history_text += f"记录时间: {data.get('更新时间', '未知')}\n"
        history_text += f"操作类型: {data.get('操作类型', '查看')}\n"
        history_text += f"操作用户: {data.get('操作用户', '系统')}\n"
        history_text += "\n详细数据:\n"
        history_text += "-" * 40 + "\n"
        
        for key, value in data.items():
            history_text += f"{key}: {value}\n"
            
        self.history_text.setPlainText(history_text)
        
    def _animate_show(self):
        """显示动画"""
        # 淡入效果
        self.setWindowOpacity(0.0)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()


class ExpandableTableWidget(QWidget):
    """
    可展开表格组件
    
    支持树形展示、行展开和详情面板的高级表格组件。
    """
    
    # 信号定义
    item_expanded = pyqtSignal(object, bool)    # 项目展开/折叠信号
    detail_requested = pyqtSignal(dict)         # 详情请求信号
    data_structure_changed = pyqtSignal()       # 数据结构变化信号
    
    def __init__(self, parent=None):
        """初始化可展开表格组件"""
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 数据管理
        self.original_data = []
        self.grouped_data = {}
        self.expanded_items = set()
        self.detail_panel_visible = False
        
        # 组件
        self.tree_widget = None
        self.detail_panel = None
        self.splitter = None
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        self.logger.info("可展开表格组件初始化完成")
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：树形表格
        self._create_tree_widget()
        
        # 右侧：详情面板
        self._create_detail_panel()
        
        # 设置分割器比例
        self.splitter.setSizes([700, 300])
        self.splitter.setStretchFactor(0, 1)
        self.splitter.setStretchFactor(1, 0)
        
        # 默认隐藏详情面板
        self.detail_panel.hide()
        
        layout.addWidget(self.splitter)
        
    def _create_tree_widget(self):
        """创建树形控件"""
        tree_frame = QFrame()
        tree_frame.setFrameStyle(QFrame.StyledPanel)
        tree_layout = QVBoxLayout(tree_frame)
        tree_layout.setContentsMargins(8, 8, 8, 8)
        
        # 标题
        title_label = QLabel("数据树形视图")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        tree_layout.addWidget(title_label)
        
        # 树形控件
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["名称", "值", "类型", "状态"])
        
        # 设置树形控件样式
        self.tree_widget.setStyleSheet("""
            QTreeWidget {
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #0078d4;
                font-size: 14px;
            }
            
            QTreeWidget::item {
                padding: 6px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QTreeWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            
            QTreeWidget::item:hover {
                background-color: #f0f6ff;
            }
            
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 13px;
                color: #495057;
            }
        """)
        
        # 设置树形控件属性
        self.tree_widget.setAlternatingRowColors(True)
        self.tree_widget.setRootIsDecorated(True)
        self.tree_widget.setExpandsOnDoubleClick(True)
        self.tree_widget.setAnimated(True)
        
        # 设置列宽
        header = self.tree_widget.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        tree_layout.addWidget(self.tree_widget)
        
        self.splitter.addWidget(tree_frame)
        
    def _create_detail_panel(self):
        """创建详情面板"""
        self.detail_panel = DetailPanel()
        self.splitter.addWidget(self.detail_panel)
        
    def _connect_signals(self):
        """连接信号"""
        # 树形控件信号
        self.tree_widget.itemExpanded.connect(self._on_item_expanded)
        self.tree_widget.itemCollapsed.connect(self._on_item_collapsed)
        self.tree_widget.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.tree_widget.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 详情面板信号
        self.detail_panel.panel_closed.connect(self._hide_detail_panel)
        self.detail_panel.data_modified.connect(self._on_detail_data_modified)
        
    def set_data(self, data: List[Dict[str, Any]], group_by: str = None):
        """设置数据"""
        try:
            self.original_data = data.copy()
            
            if group_by:
                self._set_grouped_data(data, group_by)
            else:
                self._set_flat_data(data)
                
            self.logger.info(f"设置表格数据成功: {len(data)} 行")
            self.data_structure_changed.emit()
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
            
    def _set_flat_data(self, data: List[Dict[str, Any]]):
        """设置平铺数据"""
        self.tree_widget.clear()
        
        for i, record in enumerate(data):
            item = ExpandableTreeItem()
            item.set_detail_data(record)
            
            # 设置基本信息
            name = record.get("姓名", f"记录{i+1}")
            value = record.get("基本工资", "")
            item_type = record.get("类型", "普通")
            status = record.get("状态", "正常")
            
            item.setText(0, name)
            item.setText(1, str(value))
            item.setText(2, item_type)
            item.setText(3, status)
            
            # 添加子项（详细字段）
            self._add_detail_children(item, record)
            
            self.tree_widget.addTopLevelItem(item)
            
    def _set_grouped_data(self, data: List[Dict[str, Any]], group_by: str):
        """设置分组数据"""
        self.tree_widget.clear()
        self.grouped_data = {}
        
        # 按指定字段分组
        for record in data:
            group_key = record.get(group_by, "未分组")
            if group_key not in self.grouped_data:
                self.grouped_data[group_key] = []
            self.grouped_data[group_key].append(record)
            
        # 创建分组项
        for group_key, group_records in self.grouped_data.items():
            group_item = ExpandableTreeItem()
            group_item.set_item_type("group")
            group_item.setText(0, f"{group_key} ({len(group_records)})")
            group_item.setText(1, "")
            group_item.setText(2, "分组")
            group_item.setText(3, "")
            
            # 添加分组下的记录
            for i, record in enumerate(group_records):
                record_item = ExpandableTreeItem(group_item)
                record_item.set_detail_data(record)
                
                name = record.get("姓名", f"记录{i+1}")
                value = record.get("基本工资", "")
                item_type = record.get("类型", "普通")
                status = record.get("状态", "正常")
                
                record_item.setText(0, name)
                record_item.setText(1, str(value))
                record_item.setText(2, item_type)
                record_item.setText(3, status)
                
                # 添加详细字段子项
                self._add_detail_children(record_item, record)
                
            self.tree_widget.addTopLevelItem(group_item)
            
    def _add_detail_children(self, parent_item: ExpandableTreeItem, record: Dict[str, Any]):
        """添加详细字段子项"""
        detail_fields = ["绩效工资", "奖金", "补贴", "扣款", "实发工资", "部门", "职位"]
        
        for field in detail_fields:
            if field in record:
                detail_item = ExpandableTreeItem(parent_item)
                detail_item.set_item_type("detail")
                detail_item.setText(0, field)
                detail_item.setText(1, str(record[field]))
                detail_item.setText(2, "详细")
                detail_item.setText(3, "")
                
    def _on_item_expanded(self, item: QTreeWidgetItem):
        """处理项目展开"""
        if isinstance(item, ExpandableTreeItem):
            item.is_expanded_custom = True
            self.expanded_items.add(id(item))
            self.item_expanded.emit(item, True)
            self.logger.debug(f"项目展开: {item.text(0)}")
            
    def _on_item_collapsed(self, item: QTreeWidgetItem):
        """处理项目折叠"""
        if isinstance(item, ExpandableTreeItem):
            item.is_expanded_custom = False
            self.expanded_items.discard(id(item))
            self.item_expanded.emit(item, False)
            self.logger.debug(f"项目折叠: {item.text(0)}")
            
    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """处理双击事件"""
        if isinstance(item, ExpandableTreeItem):
            detail_data = item.get_detail_data()
            if detail_data:
                self._show_detail_panel(detail_data, item.text(0))
                
    def _on_selection_changed(self):
        """处理选择变化"""
        selected_items = self.tree_widget.selectedItems()
        if selected_items:
            item = selected_items[0]
            self.logger.debug(f"选择项目: {item.text(0)}")
            
    def _show_detail_panel(self, data: Dict[str, Any], title: str):
        """显示详情面板"""
        if not self.detail_panel_visible:
            self.detail_panel.show()
            self.detail_panel_visible = True
            
            # 调整分割器比例
            self.splitter.setSizes([500, 400])
            
        self.detail_panel.show_data(data, title)
        self.detail_requested.emit(data)
        
    def _hide_detail_panel(self):
        """隐藏详情面板"""
        self.detail_panel.hide()
        self.detail_panel_visible = False
        
        # 恢复分割器比例
        self.splitter.setSizes([700, 0])
        
    def _on_detail_data_modified(self, modified_data: Dict[str, Any]):
        """处理详情数据修改"""
        self.logger.info(f"详情数据已修改: {modified_data}")
        # TODO: 实现数据修改的同步更新
        
    def expand_all(self):
        """展开所有项目"""
        self.tree_widget.expandAll()
        
    def collapse_all(self):
        """折叠所有项目"""
        self.tree_widget.collapseAll()
        
    def expand_to_level(self, level: int):
        """展开到指定层级"""
        self.tree_widget.expandToDepth(level)
        
    def get_expanded_items(self) -> List[str]:
        """获取展开的项目列表"""
        expanded_names = []
        for item_id in self.expanded_items:
            # 根据ID查找对应的项目名称
            # 这里简化处理，实际应该维护ID到名称的映射
            pass
        return expanded_names
        
    def restore_expanded_state(self, expanded_items: List[str]):
        """恢复展开状态"""
        # TODO: 实现展开状态恢复
        pass
        
    def filter_data(self, filter_text: str):
        """过滤数据"""
        if not filter_text:
            # 显示所有项目
            for i in range(self.tree_widget.topLevelItemCount()):
                item = self.tree_widget.topLevelItem(i)
                item.setHidden(False)
                self._show_all_children(item)
        else:
            # 过滤显示
            filter_text = filter_text.lower()
            for i in range(self.tree_widget.topLevelItemCount()):
                item = self.tree_widget.topLevelItem(i)
                should_show = self._item_matches_filter(item, filter_text)
                item.setHidden(not should_show)
                
    def _show_all_children(self, item: QTreeWidgetItem):
        """显示所有子项"""
        for i in range(item.childCount()):
            child = item.child(i)
            child.setHidden(False)
            self._show_all_children(child)
            
    def _item_matches_filter(self, item: QTreeWidgetItem, filter_text: str) -> bool:
        """检查项目是否匹配过滤条件"""
        # 检查当前项目
        for col in range(item.columnCount()):
            if filter_text in item.text(col).lower():
                return True
                
        # 检查子项目
        for i in range(item.childCount()):
            child = item.child(i)
            if self._item_matches_filter(child, filter_text):
                return True
                
        return False
        
    def get_selected_data(self) -> Optional[Dict[str, Any]]:
        """获取选中项目的数据"""
        selected_items = self.tree_widget.selectedItems()
        if selected_items and isinstance(selected_items[0], ExpandableTreeItem):
            return selected_items[0].get_detail_data()
        return None


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试数据
    test_data = [
        {
            "姓名": "张三", "工号": "001", "部门": "技术部", "职位": "工程师",
            "基本工资": "5000.00", "绩效工资": "2000.00", "奖金": "1000.00",
            "补贴": "500.00", "扣款": "100.00", "实发工资": "8400.00",
            "状态": "正常", "类型": "技术", "更新时间": "2025-01-22"
        },
        {
            "姓名": "李四", "工号": "002", "部门": "销售部", "职位": "销售经理",
            "基本工资": "5500.00", "绩效工资": "2200.00", "奖金": "1500.00",
            "补贴": "600.00", "扣款": "50.00", "实发工资": "9750.00",
            "状态": "异动", "类型": "管理", "更新时间": "2025-01-22"
        },
        {
            "姓名": "王五", "工号": "003", "部门": "技术部", "职位": "高级工程师",
            "基本工资": "6000.00", "绩效工资": "2500.00", "奖金": "2000.00",
            "补贴": "700.00", "扣款": "200.00", "实发工资": "11000.00",
            "状态": "正常", "类型": "技术", "更新时间": "2025-01-22"
        },
    ]
    
    # 创建可展开表格
    expandable_table = ExpandableTableWidget()
    expandable_table.setWindowTitle("可展开表格测试")
    expandable_table.resize(1000, 600)
    
    # 设置分组数据
    expandable_table.set_data(test_data, "部门")
    
    expandable_table.show()
    
    sys.exit(app.exec_()) 