# 全部在职人员工资表字段格式化需求分析与解决方案

## 📋 需求概述

本文档梳理了"全部在职人员工资表"在主界面右侧列表展示区域的字段格式化需求，包括字段类型转换、显示隐藏、空值处理、序号功能和排序功能等九个核心需求点。

## 🎯 具体需求清单

### 1. 字段类型转换需求

#### 1.1 字符串类型字段
需要转换为字符串类型的字段：
- 工号 (`employee_id`)
- 姓名 (`employee_name`)
- 部门名称 (`department`)
- 人员类别代码 (`employee_type_code`)
- 人员类别 (`employee_type`)

#### 1.2 整型字段
需要转换为整型的字段：
- 2025年岗位工资 (`position_salary_2025`)
- 2025年薪级工资 (`grade_salary_2025`)
- 津贴 (`allowance`)
- 结余津贴 (`balance_allowance`)
- 2025年基础性绩效 (`basic_performance_2025`)
- 卫生费 (`health_fee`)
- 交通补贴 (`transport_allowance`)
- 物业补贴 (`property_allowance`)
- 通讯补贴 (`communication_allowance`)
- 2025年奖励性绩效预发 (`performance_bonus_2025`)
- 2025公积金 (`provident_fund_2025`)

#### 1.3 浮点型字段（保留两位小数）
需要转换为浮点型的字段：
- 住房补贴 (`housing_allowance`)
- 车补 (`car_allowance`)
- 补发 (`supplement`)
- 借支 (`advance`)
- 应发工资 (`total_salary`)
- 代扣代存养老保险 (`pension_insurance`)

#### 1.4 特殊字段处理
- **月份字段**：提取后两位月份部分，转换为字符串
- **年份字段**：转换为字符串

### 2. 字段隐藏需求
以下字段不在主界面右侧列表展示区域显示：
- 创建时间 (`created_at`)
- 更新时间 (`updated_at`)
- 自增主键 (`id`)
- 序号 (`sequence_number`)

### 3. 空值处理需求
字段值为以下内容时，在展示区域统一显示为空白：
- None
- nan
- 0
- 0.0
- 空字符串 ("")
- 空格 (" ")

### 4. 界面功能需求
- **开启列表自身序号**：在表格左侧显示行号
- **开启排序功能**：每个字段支持升序、降序、取消排序，支持多字段排序

## 🏗️ 项目架构分析

### 当前系统架构
基于深入分析，系统采用4层MVC架构：

1. **表现层（GUI）**：PyQt5界面组件
   - 主窗口：`PrototypeMainWindow`
   - 表格组件：`VirtualizedExpandableTable`
   - 分页组件：`PaginationWidget`

2. **应用层**：`ApplicationService`统一协调
   - 状态管理：`ApplicationStateService`
   - 控制器：`ApplicationController`

3. **业务层**：模块化处理
   - 数据导入：`data_import`模块
   - 数据存储：`data_storage`模块
   - 系统配置：`system_config`模块

4. **数据层**：SQLite数据库 + 动态表管理
   - 数据库管理：`DatabaseManager`
   - 动态表管理：`DynamicTableManager`

### 关键组件分析

#### 1. 数据处理流程
```mermaid
graph LR
    A[数据库查询] --> B[PaginationWorker.run]
    B --> C[字段映射处理]
    C --> D[VirtualizedExpandableTable]
    D --> E[界面展示]
```

#### 2. 字段映射机制
系统已具备完善的字段映射机制：
- **类型转换**：`DataValidator`模块提供数据类型验证和转换
- **字段隐藏**：`TableFieldPreferenceDialog`支持字段显示配置
- **空值处理**：在多个层面支持空值检测和处理
- **排序功能**：`VirtualizedExpandableTable`支持列排序和拖拽排序

#### 3. 专用表模板
`SpecializedTableTemplates`中已定义"全部在职人员工资表"模板：
```python
def _get_active_employee_template(self) -> List[FieldDefinition]:
    """全部在职人员工资表模板（28个字段）"""
    return [
        FieldDefinition("employee_id", "TEXT", False, None, False, "工号"),
        FieldDefinition("employee_name", "TEXT", False, None, False, "姓名"),
        # ... 其他字段定义
    ]
```

## 🛠️ 详细解决方案

### 方案1：数据格式化模块（核心方案）

#### 1.1 创建专用格式化类
**文件位置**：`src/modules/data_storage/salary_data_formatter.py`

```python
class SalaryDataFormatter:
    """工资数据格式化器"""
    
    @staticmethod
    def format_active_employee_data(df: pd.DataFrame) -> pd.DataFrame:
        """格式化全部在职人员工资表数据"""
        
        # 1. 字符串类型字段转换
        string_fields = ['employee_id', 'employee_name', 'department', 
                        'employee_type_code', 'employee_type']
        for field in string_fields:
            if field in df.columns:
                df[field] = df[field].astype(str).fillna('')
        
        # 2. 整型字段转换
        integer_fields = ['position_salary_2025', 'grade_salary_2025', 'allowance',
                         'balance_allowance', 'basic_performance_2025', 'health_fee',
                         'transport_allowance', 'property_allowance', 'communication_allowance',
                         'performance_bonus_2025', 'provident_fund_2025']
        for field in integer_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce').fillna(0).astype(int)
        
        # 3. 浮点型字段转换（保留两位小数）
        float_fields = ['housing_allowance', 'car_allowance', 'supplement',
                       'advance', 'total_salary', 'pension_insurance']
        for field in float_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce').fillna(0.0).round(2)
        
        # 4. 特殊字段处理
        df = SalaryDataFormatter._format_month_field(df)
        df = SalaryDataFormatter._format_year_field(df)
        
        return df
    
    @staticmethod
    def _format_month_field(df: pd.DataFrame) -> pd.DataFrame:
        """月份字段：提取后两位，转换为字符串"""
        if 'month' in df.columns:
            df['month'] = df['month'].astype(str).str[-2:].fillna('')
        return df
    
    @staticmethod
    def _format_year_field(df: pd.DataFrame) -> pd.DataFrame:
        """年份字段：转换为字符串"""
        if 'year' in df.columns:
            df['year'] = df['year'].astype(str).fillna('')
        return df
    
    @staticmethod
    def format_empty_values(value: Any) -> str:
        """统一处理空值显示"""
        if pd.isna(value) or value in [None, 'None', 'nan', 0, 0.0, '', ' ']:
            return ''
        return str(value)
```

#### 1.2 集成到数据加载流程
**修改文件**：`src/gui/prototype/prototype_main_window.py`

在`PaginationWorker.run()`方法中集成格式化逻辑：

```python
class PaginationWorker(QRunnable):
    def run(self):
        try:
            # ... 现有的数据加载逻辑 ...
            
            # 加载分页数据
            df, total_records = self.table_manager.get_dataframe_paginated(
                self.table_name, self.page, self.page_size
            )
            
            # 新增：数据格式化处理
            if self.table_name == 'active_employees':
                from src.modules.data_storage.salary_data_formatter import SalaryDataFormatter
                df = SalaryDataFormatter.format_active_employee_data(df)
            
            # ... 后续处理逻辑 ...
            
        except Exception as e:
            # 错误处理逻辑
            pass
```

### 方案2：字段隐藏配置

#### 2.1 扩展字段偏好系统
**修改文件**：`src/gui/table_field_preference_dialog.py`

```python
# 预定义隐藏字段配置
HIDDEN_FIELDS_BY_TABLE = {
    'active_employees': ['created_at', 'updated_at', 'id', 'sequence_number']
}

class TableFieldPreferenceDialog:
    def get_visible_fields(self, table_name: str) -> List[str]:
        """获取应该显示的字段列表"""
        all_fields = self.get_all_table_fields(table_name)
        hidden_fields = HIDDEN_FIELDS_BY_TABLE.get(table_name, [])
        return [f for f in all_fields if f not in hidden_fields]
    
    def apply_field_visibility(self, table_name: str, df: pd.DataFrame) -> pd.DataFrame:
        """应用字段可见性配置"""
        visible_fields = self.get_visible_fields(table_name)
        available_fields = [f for f in visible_fields if f in df.columns]
        return df[available_fields]
```

### 方案3：序号功能实现

#### 3.1 表格序号列添加
**修改文件**：`src/gui/prototype/widgets/virtualized_expandable_table.py`

```python
class VirtualizedExpandableTable(QTableWidget):
    def setup_table_with_row_numbers(self):
        """设置带行号的表格"""
        # 在第一列插入序号列
        self.insertColumn(0)
        self.setHorizontalHeaderItem(0, QTableWidgetItem("序号"))
        
        # 设置序号列宽度
        self.setColumnWidth(0, 60)
        
        # 填充行号
        for row in range(self.rowCount()):
            item = QTableWidgetItem(str(row + 1))
            item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)  # 设为只读
            item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, item)
    
    def update_row_numbers(self):
        """更新行号（在排序后调用）"""
        for row in range(self.rowCount()):
            if self.item(row, 0):
                self.item(row, 0).setText(str(row + 1))
```

### 方案4：多字段排序功能

#### 4.1 多列排序管理器
**新建文件**：`src/gui/multi_column_sort_manager.py`

```python
class MultiColumnSortManager:
    """多列排序管理器"""
    
    def __init__(self, table_widget):
        self.table = table_widget
        self.sort_columns = []  # [(column_index, order), ...]
        self.setup_sort_indicators()
    
    def add_sort_column(self, column: int, order: Qt.SortOrder):
        """添加排序列"""
        # 移除已存在的同列排序
        self.sort_columns = [(c, o) for c, o in self.sort_columns if c != column]
        # 添加新的排序列
        self.sort_columns.append((column, order))
        # 限制最多3个排序列
        if len(self.sort_columns) > 3:
            self.sort_columns = self.sort_columns[-3:]
    
    def clear_sort(self):
        """清除所有排序"""
        self.sort_columns = []
    
    def apply_multi_sort(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用多列排序"""
        if not self.sort_columns:
            return df
        
        # 构建排序参数
        columns = [df.columns[col] for col, _ in self.sort_columns]
        ascending = [order == Qt.AscendingOrder for _, order in self.sort_columns]
        
        return df.sort_values(by=columns, ascending=ascending, na_position='last')
    
    def setup_sort_indicators(self):
        """设置排序指示器"""
        header = self.table.horizontalHeader()
        header.setSortIndicatorShown(True)
        header.sortIndicatorChanged.connect(self.on_sort_indicator_changed)
    
    def on_sort_indicator_changed(self, logical_index: int, order: Qt.SortOrder):
        """响应排序指示器变化"""
        self.add_sort_column(logical_index, order)
        # 触发数据重新排序
        self.table.refresh_data_with_sort()
```

### 方案5：空值处理增强

#### 5.1 表格显示层空值处理
**修改文件**：`src/gui/prototype/widgets/virtualized_expandable_table.py`

```python
class VirtualizedExpandableTable(QTableWidget):
    def setItem(self, row: int, column: int, item: QTableWidgetItem):
        """重写setItem方法，统一处理空值显示"""
        if item and item.text():
            # 使用格式化器处理空值
            from src.modules.data_storage.salary_data_formatter import SalaryDataFormatter
            formatted_text = SalaryDataFormatter.format_empty_values(item.text())
            item.setText(formatted_text)
        super().setItem(row, column, item)
    
    def populate_table_data(self, df: pd.DataFrame):
        """填充表格数据，统一处理空值"""
        self.setRowCount(len(df))
        self.setColumnCount(len(df.columns))
        
        # 设置表头
        self.setHorizontalHeaderLabels(df.columns.tolist())
        
        # 填充数据，应用空值格式化
        for row in range(len(df)):
            for col in range(len(df.columns)):
                value = df.iloc[row, col]
                formatted_value = SalaryDataFormatter.format_empty_values(value)
                
                item = QTableWidgetItem(formatted_value)
                self.setItem(row, col, item)
```

## 📝 实施计划

### Phase 1：核心功能实现（优先级：高）
**预计时间**：2-3个工作日

1. **创建数据格式化模块**
   - 实现`SalaryDataFormatter`类
   - 集成到数据加载流程

2. **字段隐藏功能**
   - 扩展`TableFieldPreferenceDialog`
   - 配置"全部在职人员工资表"隐藏字段

3. **空值处理优化**
   - 在表格显示层统一处理空值
   - 确保空值显示为空白

### Phase 2：增强功能实现（优先级：中）
**预计时间**：2个工作日

1. **序号功能实现**
   - 在表格左侧添加序号列
   - 实现排序后序号自动更新

2. **多列排序功能**
   - 实现`MultiColumnSortManager`
   - 支持最多3列同时排序

### Phase 3：测试与优化（优先级：中）
**预计时间**：1个工作日

1. **功能测试验证**
   - 测试各种数据类型转换
   - 验证字段隐藏和序号功能

2. **性能优化**
   - 确保大数据量下的性能表现
   - 优化排序和格式化效率

## 🔧 技术实施细节

### 修改文件清单

1. **新建文件**：
   - `src/modules/data_storage/salary_data_formatter.py`
   - `src/gui/multi_column_sort_manager.py`

2. **修改文件**：
   - `src/gui/prototype/prototype_main_window.py`
   - `src/gui/prototype/widgets/virtualized_expandable_table.py`
   - `src/gui/table_field_preference_dialog.py`

3. **配置文件**：
   - 更新字段隐藏配置
   - 添加表级格式化规则

### 核心技术要点

1. **数据类型转换**：使用`pandas`的类型转换方法，处理异常值
2. **字段映射**：利用现有的字段映射机制，扩展格式化功能
3. **界面更新**：确保数据格式化后界面能正确显示
4. **性能考虑**：在分页加载时进行格式化，避免影响性能

### 兼容性保证

1. **向后兼容**：不影响其他工资表类型的正常功能
2. **配置独立**：每种表类型的格式化规则独立配置
3. **错误处理**：格式化失败时不影响数据的正常显示

## 📊 预期效果

实施完成后，"全部在职人员工资表"将具备以下特性：

1. **数据类型标准化**：所有字段按照指定类型正确显示
2. **界面简洁明了**：隐藏技术字段，只显示业务相关字段
3. **空值处理优雅**：空值统一显示为空白，提升视觉效果
4. **操作便捷高效**：支持序号显示和多字段排序
5. **性能稳定可靠**：在大数据量下仍能流畅运行

## 🎯 总结

本解决方案充分利用了项目现有的架构优势，采用最小侵入性的方式实现所有需求。通过模块化的设计，确保了功能的可维护性和可扩展性。整体方案具有以下优势：

- **架构友好**：完全符合现有的MVC架构设计
- **性能优越**：在数据加载阶段进行格式化，不影响界面响应
- **扩展灵活**：易于扩展到其他工资表类型
- **维护便利**：格式化逻辑集中管理，便于后续维护

预计总体实施时间为5-6个工作日，实施后将显著提升"全部在职人员工资表"的用户体验和数据展示效果。