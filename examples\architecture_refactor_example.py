"""
架构重构集成示例

展示如何在现有系统中集成新的架构重构组件，
实现从旧架构到新架构的平滑迁移。

使用场景：
1. 现有主窗口集成新架构
2. 排序功能使用新的事件驱动模式
3. 分页功能使用统一状态管理
4. 字段映射使用统一数据请求管道

迁移步骤：
1. 初始化架构工厂
2. 替换现有的数据加载逻辑
3. 集成事件驱动的交互
4. 验证功能正常性
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.architecture_factory import get_architecture_factory
from src.core.event_bus import SortRequestEvent, PageRequestEvent, DataUpdatedEvent
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config import ConfigManager
from src.utils.log_config import setup_logger


class ArchitectureIntegrationExample:
    """架构重构集成示例"""
    
    def __init__(self):
        self.logger = setup_logger("ArchitectureExample")
        
        # 初始化现有组件（模拟现有系统）
        self.config_manager = ConfigManager()
        self.db_manager = DynamicTableManager(config_manager=self.config_manager)
        
        # 初始化新架构
        self.architecture_factory = None
        self.table_data_service = None
        
        self.logger.info("架构集成示例初始化完成")
    
    def initialize_new_architecture(self) -> bool:
        """初始化新架构"""
        try:
            self.logger.info("开始初始化新架构...")
            
            # 1. 获取架构工厂
            self.architecture_factory = get_architecture_factory(
                self.db_manager, 
                self.config_manager
            )
            
            # 2. 初始化整个架构系统
            if not self.architecture_factory.initialize_architecture():
                self.logger.error("架构初始化失败")
                return False
            
            # 3. 获取核心服务
            self.table_data_service = self.architecture_factory.get_table_data_service()
            
            # 4. 设置事件监听器
            self._setup_event_listeners()
            
            self.logger.info("✅ 新架构初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化新架构失败: {e}")
            return False
    
    def _setup_event_listeners(self):
        """设置事件监听器"""
        try:
            event_bus = self.architecture_factory.get_event_bus()
            
            # 监听数据更新事件
            event_bus.subscribe(
                "data_updated",
                self._on_data_updated,
                async_handler=False
            )
            
            self.logger.debug("事件监听器设置完成")
            
        except Exception as e:
            self.logger.error(f"设置事件监听器失败: {e}")
    
    def _on_data_updated(self, event: DataUpdatedEvent):
        """数据更新事件处理器"""
        try:
            self.logger.info(f"接收到数据更新事件: {event.table_name}")
            
            if event.data is not None:
                rows, cols = event.data.shape
                self.logger.info(f"数据内容: {rows}行 x {cols}列")
                
                # 显示前几行数据（示例）
                if rows > 0:
                    self.logger.debug(f"数据预览:\n{event.data.head().to_string()}")
            
            # 更新UI（在实际集成中，这里会更新表格组件）
            self._update_ui_with_new_data(event)
            
        except Exception as e:
            self.logger.error(f"处理数据更新事件失败: {e}")
    
    def _update_ui_with_new_data(self, event: DataUpdatedEvent):
        """使用新数据更新UI（模拟）"""
        try:
            metadata = event.metadata
            self.logger.info(f"更新UI: 请求类型={metadata.get('request_type')}, "
                           f"总记录={metadata.get('total_records')}, "
                           f"当前页={metadata.get('current_page')}")
            
            # 在实际集成中，这里会调用表格组件的更新方法
            # 例如：self.main_workspace.data_table.set_data(event.data)
            
        except Exception as e:
            self.logger.error(f"更新UI失败: {e}")
    
    def demonstrate_new_sorting(self, table_name: str):
        """演示新的排序功能"""
        try:
            self.logger.info(f"演示新排序功能: {table_name}")
            
            # 使用新的事件驱动排序
            event_bus = self.architecture_factory.get_event_bus()
            
            # 创建排序请求事件
            sort_event = SortRequestEvent(
                table_name=table_name,
                sort_columns=[
                    {
                        "column_name": "employee_name",
                        "order": "ascending",
                        "priority": 0
                    }
                ],
                current_page=1
            )
            
            # 发布排序事件
            event_bus.publish(sort_event)
            self.logger.info("✅ 排序事件已发布")
            
        except Exception as e:
            self.logger.error(f"演示排序功能失败: {e}")
    
    def demonstrate_new_pagination(self, table_name: str):
        """演示新的分页功能"""
        try:
            self.logger.info(f"演示新分页功能: {table_name}")
            
            # 使用新的事件驱动分页
            event_bus = self.architecture_factory.get_event_bus()
            
            # 创建分页请求事件
            page_event = PageRequestEvent(
                table_name=table_name,
                page=2,
                page_size=25,
                preserve_sort=True
            )
            
            # 发布分页事件
            event_bus.publish(page_event)
            self.logger.info("✅ 分页事件已发布")
            
        except Exception as e:
            self.logger.error(f"演示分页功能失败: {e}")
    
    def demonstrate_unified_data_loading(self, table_name: str):
        """演示统一数据加载"""
        try:
            self.logger.info(f"演示统一数据加载: {table_name}")
            
            # 使用新的统一数据服务
            response = self.table_data_service.load_table_data(
                table_name=table_name,
                page=1,
                page_size=50,
                sort_columns=[
                    {
                        "column_name": "employee_id",
                        "order": "ascending",
                        "priority": 0
                    }
                ]
            )
            
            if response.success:
                self.logger.info(f"✅ 数据加载成功: {response.loaded_fields}字段, "
                               f"{len(response.data)}行, 耗时{response.processing_time_ms:.1f}ms")
            else:
                self.logger.error(f"❌ 数据加载失败: {response.error}")
            
        except Exception as e:
            self.logger.error(f"演示统一数据加载失败: {e}")
    
    def demonstrate_state_management(self, table_name: str):
        """演示状态管理"""
        try:
            self.logger.info(f"演示状态管理: {table_name}")
            
            # 获取当前状态
            state = self.table_data_service.get_table_state(table_name)
            self.logger.info(f"当前状态: 页码={state.get('current_page')}, "
                           f"页大小={state.get('page_size')}, "
                           f"总记录={state.get('total_records')}")
            
            # 更新状态
            state_manager = self.architecture_factory.get_unified_state_manager()
            state_manager.update_table_state(
                table_name,
                {"current_page": 3, "page_size": 30},
                change_type=None
            )
            
            # 获取更新后的状态
            updated_state = self.table_data_service.get_table_state(table_name)
            self.logger.info(f"✅ 状态更新成功: 页码={updated_state.get('current_page')}, "
                           f"页大小={updated_state.get('page_size')}")
            
        except Exception as e:
            self.logger.error(f"演示状态管理失败: {e}")
    
    def show_architecture_statistics(self):
        """显示架构统计信息"""
        try:
            self.logger.info("获取架构统计信息...")
            
            stats = self.architecture_factory.get_architecture_statistics()
            
            self.logger.info("📊 架构统计信息:")
            self.logger.info(f"  工厂状态: {stats.get('factory_status', {})}")
            self.logger.info(f"  事件总线: 已发布={stats.get('event_bus_stats', {}).get('published_events', 0)}事件")
            self.logger.info(f"  状态管理: 管理={stats.get('state_manager_stats', {}).get('total_tables', 0)}个表")
            self.logger.info(f"  数据请求: 成功率={stats.get('data_request_stats', {}).get('success_rate', 0):.1%}")
            
        except Exception as e:
            self.logger.error(f"获取架构统计失败: {e}")
    
    def run_complete_example(self):
        """运行完整示例"""
        try:
            self.logger.info("🚀 开始运行完整架构重构示例...")
            
            # 1. 初始化新架构
            if not self.initialize_new_architecture():
                return False
            
            # 2. 选择一个示例表（需要确保数据库中有数据）
            table_name = "salary_data_2025_05_active_employees"
            
            # 检查表是否存在
            if not self.db_manager.table_exists(table_name):
                self.logger.warning(f"示例表 {table_name} 不存在，使用模拟演示")
                table_name = "demo_table"
            
            # 3. 演示各个功能
            self.demonstrate_unified_data_loading(table_name)
            self.demonstrate_new_sorting(table_name)
            self.demonstrate_new_pagination(table_name)
            self.demonstrate_state_management(table_name)
            
            # 4. 显示统计信息
            self.show_architecture_statistics()
            
            self.logger.info("🎉 完整架构重构示例运行完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"运行完整示例失败: {e}")
            return False
        finally:
            # 清理资源
            if self.architecture_factory:
                self.architecture_factory.shutdown()


def main():
    """主函数"""
    print("=" * 60)
    print("🏗️  架构重构集成示例")
    print("=" * 60)
    
    example = ArchitectureIntegrationExample()
    success = example.run_complete_example()
    
    print("=" * 60)
    if success:
        print("✅ 示例运行成功！")
        print("\n📝 关键收益:")
        print("  • 统一的数据处理管道")
        print("  • 事件驱动的松耦合架构")  
        print("  • 自动化状态管理")
        print("  • 改进的错误处理")
        print("  • 更好的性能监控")
    else:
        print("❌ 示例运行失败！")
    print("=" * 60)


if __name__ == "__main__":
    main() 