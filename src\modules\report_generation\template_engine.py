"""
模板引擎核心类 (Template Engine)

提供统一的模板管理、数据绑定和格式控制功能。
支持Word和Excel模板的动态数据填充，保持原始格式。

主要功能：
- 模板文件管理和验证
- 数据占位符解析和替换
- 格式保持和样式控制
- 模板变量映射和验证

Author: Development Team
Date: 2025-06-13
"""

import os
import re
import json
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging

from src.utils.log_config import get_module_logger


class TemplateEngine:
    """模板引擎核心类"""
    
    def __init__(self, template_base_path: Optional[str] = None):
        """
        初始化模板引擎
        
        Args:
            template_base_path: 模板文件基础路径，默认使用项目根目录下的template文件夹
        """
        self.logger = get_module_logger(__name__)
        
        # 设置模板基础路径
        if template_base_path is None:
            # 获取项目根目录
            current_dir = Path(__file__)
            project_root = current_dir.parent.parent.parent.parent
            self.template_base_path = project_root / "template"
        else:
            self.template_base_path = Path(template_base_path)
            
        self.logger.info(f"模板引擎初始化，模板路径: {self.template_base_path}")
        
        # 验证模板目录存在性
        if not self.template_base_path.exists():
            raise FileNotFoundError(f"模板目录不存在: {self.template_base_path}")
        
        # 模板映射字典
        self.template_mapping = {
            'salary_approval': {
                'name': '工资审批表',
                'type': 'excel',
                'path': '工资审批表/2025年5月工资呈报表 -最终版.xlsx',
                'description': '月度工资审批表模板'
            },
            'department_meeting': {
                'name': '部务会请示',
                'type': 'word',
                'path': '关于发放教职工薪酬的请示-部务会/审议2025年5月教职工薪酬发放事宜-最终版-2025.5.13.docx',
                'description': '部务会薪酬发放请示文档'
            },
            'oa_process': {
                'name': 'OA流程请示',
                'type': 'word', 
                'path': '关于发放教职工薪酬的请示-OA流程/审议2025年5月教职工薪酬发放事宜-最终版 - OA流程 - 最终版.docx',
                'description': 'OA流程薪酬发放请示文档'
            }
        }
        
        self.logger.info(f"已注册 {len(self.template_mapping)} 个模板类型")
        
    def get_template_path(self, template_key: str) -> Path:
        """
        获取模板文件的完整路径
        
        Args:
            template_key: 模板键名
            
        Returns:
            Path: 模板文件的完整路径
            
        Raises:
            KeyError: 模板键名不存在
            FileNotFoundError: 模板文件不存在
        """
        if template_key not in self.template_mapping:
            raise KeyError(f"未知的模板键名: {template_key}")
            
        template_info = self.template_mapping[template_key]
        template_path = self.template_base_path / template_info['path']
        
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
        self.logger.debug(f"获取模板路径: {template_key} -> {template_path}")
        return template_path
        
    def get_template_info(self, template_key: str) -> Dict[str, Any]:
        """
        获取模板信息
        
        Args:
            template_key: 模板键名
            
        Returns:
            Dict: 模板信息字典
        """
        if template_key not in self.template_mapping:
            raise KeyError(f"未知的模板键名: {template_key}")
            
        return self.template_mapping[template_key].copy()
        
    def list_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有可用的模板
        
        Returns:
            Dict: 所有模板的信息字典
        """
        return self.template_mapping.copy()
        
    def extract_placeholders(self, text: str) -> List[str]:
        """
        从文本中提取占位符
        
        支持的占位符格式：
        - {{variable_name}} - 标准占位符
        - {variable_name} - 简单占位符
        - [variable_name] - 方括号占位符
        
        Args:
            text: 包含占位符的文本
            
        Returns:
            List[str]: 占位符列表
        """
        placeholders = []
        
        # 匹配 {{variable_name}} 格式
        pattern1 = r'\{\{(\w+)\}\}'
        matches1 = re.findall(pattern1, text)
        placeholders.extend(matches1)
        
        # 匹配 {variable_name} 格式
        pattern2 = r'\{(\w+)\}'
        matches2 = re.findall(pattern2, text)
        placeholders.extend(matches2)
        
        # 匹配 [variable_name] 格式
        pattern3 = r'\[(\w+)\]'
        matches3 = re.findall(pattern3, text)
        placeholders.extend(matches3)
        
        # 去重并返回
        unique_placeholders = list(set(placeholders))
        self.logger.debug(f"提取到占位符: {unique_placeholders}")
        return unique_placeholders
        
    def replace_placeholders(self, text: str, data: Dict[str, Any]) -> str:
        """
        替换文本中的占位符
        
        Args:
            text: 包含占位符的文本
            data: 替换数据字典
            
        Returns:
            str: 替换后的文本
        """
        result_text = text
        
        # 替换 {{variable_name}} 格式
        def replace_double_brace(match):
            var_name = match.group(1)
            return str(data.get(var_name, match.group(0)))
        result_text = re.sub(r'\{\{(\w+)\}\}', replace_double_brace, result_text)
        
        # 替换 {variable_name} 格式
        def replace_single_brace(match):
            var_name = match.group(1)
            return str(data.get(var_name, match.group(0)))
        result_text = re.sub(r'\{(\w+)\}', replace_single_brace, result_text)
        
        # 替换 [variable_name] 格式
        def replace_bracket(match):
            var_name = match.group(1)
            return str(data.get(var_name, match.group(0)))
        result_text = re.sub(r'\[(\w+)\]', replace_bracket, result_text)
        
        return result_text
        
    def validate_template_data(self, template_key: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证模板数据的完整性
        
        Args:
            template_key: 模板键名
            data: 要验证的数据
            
        Returns:
            Dict: 验证结果，包含 'valid', 'missing_fields', 'extra_fields' 等
        """
        # 这里可以根据不同模板的具体需求来定义必需字段
        template_requirements = {
            'salary_approval': {
                'required_fields': ['month', 'year', 'total_amount', 'employee_count'],
                'optional_fields': ['department', 'notes']
            },
            'department_meeting': {
                'required_fields': ['month', 'year', 'total_amount', 'reason'],
                'optional_fields': ['meeting_date', 'participants']
            },
            'oa_process': {
                'required_fields': ['month', 'year', 'total_amount', 'process_id'],
                'optional_fields': ['submitter', 'approval_level']
            }
        }
        
        if template_key not in template_requirements:
            self.logger.warning(f"模板 {template_key} 没有定义验证规则")
            return {'valid': True, 'missing_fields': [], 'extra_fields': []}
            
        requirements = template_requirements[template_key]
        required_fields = requirements['required_fields']
        
        # 检查缺失字段
        missing_fields = [field for field in required_fields if field not in data]
        
        # 检查额外字段
        all_allowed_fields = required_fields + requirements.get('optional_fields', [])
        extra_fields = [field for field in data.keys() if field not in all_allowed_fields]
        
        validation_result = {
            'valid': len(missing_fields) == 0,
            'missing_fields': missing_fields,
            'extra_fields': extra_fields,
            'required_fields': required_fields,
            'provided_fields': list(data.keys())
        }
        
        self.logger.debug(f"模板数据验证结果: {validation_result}")
        return validation_result
        
    def format_currency(self, amount: Union[int, float, str]) -> str:
        """
        格式化货币金额
        
        Args:
            amount: 金额数值
            
        Returns:
            str: 格式化后的金额字符串
        """
        try:
            if isinstance(amount, str):
                amount = float(amount)
            return f"{amount:,.2f}"
        except (ValueError, TypeError):
            self.logger.warning(f"无法格式化金额: {amount}")
            return str(amount)
            
    def format_date(self, date_str: str, format_type: str = 'chinese') -> str:
        """
        格式化日期
        
        Args:
            date_str: 日期字符串
            format_type: 格式类型 ('chinese', 'standard', 'short')
            
        Returns:
            str: 格式化后的日期字符串
        """
        # 这里可以根据需要实现不同的日期格式化逻辑
        if format_type == 'chinese':
            # 例如：2025年5月 -> 二零二五年五月
            return date_str  # 简化实现
        return date_str 