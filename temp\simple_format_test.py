# -*- coding: utf-8 -*-
"""
简化的A岗职工格式化测试
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_formatting():
    """基础格式化测试"""
    print("开始测试A岗职工格式化功能")
    
    try:
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        
        # 初始化格式管理器
        format_manager = get_unified_format_manager()
        
        # 创建简单测试数据
        test_data = {
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四'],
            'position_salary_2025': [None, 3500.5],
            'car_allowance': ['-', 300.0],
            'month': ['202505', '05'],
            'year': [2025, '2025'],
            'created_at': ['2025-07-31', '2025-07-31'],
            'id': [1, 2]
        }
        
        df = pd.DataFrame(test_data)
        print(f"原始数据: {len(df)}行, {len(df.columns)}列")
        
        # 测试格式化
        formatted_headers, formatted_df = format_manager.format_table_complete(
            df, 
            table_type='salary_data_2025_07_a_grade_employees',
            data_source='test'
        )
        
        print("格式化成功!")
        print(f"格式化后: {len(formatted_df)}行, {len(formatted_df.columns)}列")
        print("表头:", formatted_headers)
        
        # 检查关键字段
        if '2025年岗位工资' in formatted_df.columns:
            values = formatted_df['2025年岗位工资'].tolist()
            print("岗位工资字段:", values)
            
        if '车补' in formatted_df.columns:
            values = formatted_df['车补'].tolist()
            print("车补字段:", values)
            
        if '月份' in formatted_df.columns:
            values = formatted_df['月份'].tolist()
            print("月份字段:", values)
            
        # 检查隐藏字段
        hidden_fields = ['创建时间', '自增主键']
        for field in hidden_fields:
            if field in formatted_headers:
                print(f"错误: 隐藏字段 {field} 仍在显示")
            else:
                print(f"正确: 隐藏字段 {field} 已隐藏")
        
        print("测试完成!")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_basic_formatting()