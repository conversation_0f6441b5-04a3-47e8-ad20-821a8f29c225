"""
月度工资异动处理系统 - 报告管理模块

本模块提供报告生成的统一管理功能，支持：
- 多种报告类型的生成
- 批量报告处理
- 进度跟踪和回调
- 异步报告生成

依赖库：
- pathlib: 路径处理
- threading: 多线程支持
- typing: 类型注解

创建时间: 2025-06-16 (CREATIVE阶段 Day 2)
更新时间: 2025-01-22 (REFLECT Day 2 - Optional参数修复)
"""

import os
import shutil
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path
import logging
from datetime import datetime
import json
import threading
from concurrent.futures import ThreadPoolExecutor

from src.utils.log_config import get_module_logger
from .template_engine import TemplateEngine
from .word_generator import WordGenerator
from .excel_generator import ExcelGenerator
from .preview_manager import PreviewManager


class ReportGenerationTask:
    """报告生成任务类"""
    
    def __init__(self, task_id: str, template_key: str, data: Dict[str, Any], 
                 output_path: str, task_type: str):
        self.task_id = task_id
        self.template_key = template_key
        self.data = data
        self.output_path = output_path
        self.task_type = task_type
        self.status = 'pending'  # pending, running, completed, failed
        self.created_time = datetime.now()
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.error_message: Optional[str] = None
        self.progress = 0
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'template_key': self.template_key,
            'output_path': self.output_path,
            'task_type': self.task_type,
            'status': self.status,
            'created_time': self.created_time.isoformat(),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'progress': self.progress
        }


class ReportManager:
    """报告管理器"""
    
    def __init__(self, output_base_path: Optional[str] = None, max_workers: int = 4):
        """
        初始化报告管理器
        
        Args:
            output_base_path: 输出文件基础路径，默认使用项目根目录下的output文件夹
            max_workers: 最大并发生成任务数
        """
        self.logger = get_module_logger(__name__)
        
        # 设置输出基础路径
        if output_base_path is None:
            current_dir = Path(__file__)
            project_root = current_dir.parent.parent.parent.parent
            self.output_base_path = project_root / "output"
        else:
            self.output_base_path = Path(output_base_path)
            
        # 确保输出目录存在
        self.output_base_path.mkdir(exist_ok=True)
        
        # 初始化生成器
        self.template_engine = TemplateEngine()
        self.word_generator = WordGenerator(self.template_engine)
        self.excel_generator = ExcelGenerator(self.template_engine)
        self.preview_manager = PreviewManager()
        
        # 任务管理
        self.tasks: Dict[str, ReportGenerationTask] = {}
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._task_lock = threading.Lock()
        
        # 历史记录文件
        self.history_file = self.output_base_path / "generation_history.json"
        
        self.logger.info(f"报告管理器初始化完成，输出路径: {self.output_base_path}")
        
    def generate_report(self, 
                       template_key: str, 
                       data: Dict[str, Any], 
                       output_filename: Optional[str] = None,
                       async_mode: bool = False,
                       progress_callback: Optional[Callable[[str, int], None]] = None) -> Optional[Union[bool, str]]:
        """
        生成单个报告
        
        Args:
            template_key: 模板键名
            data: 填充数据字典
            output_filename: 输出文件名，如果为None则自动生成
            async_mode: 是否异步模式
            progress_callback: 进度回调函数 (task_id, progress)
            
        Returns:
            Optional[Union[bool, str]]: 同步模式返回bool，异步模式返回task_id
        """
        try:
            # 获取模板信息
            template_info = self.template_engine.get_template_info(template_key)
            
            # 生成输出文件路径
            if output_filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_extension = '.docx' if template_info['type'] == 'word' else '.xlsx'
                output_filename = f"{template_info['name']}_{timestamp}{file_extension}"
                
            output_path = self.output_base_path / output_filename
            
            if async_mode:
                # 异步模式：提交任务并返回task_id
                return self._submit_async_task(template_key, data, str(output_path), 
                                             template_info['type'], progress_callback)
            else:
                # 同步模式：直接生成
                return self._generate_sync(template_key, data, str(output_path), 
                                         template_info['type'])
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}", exc_info=True)
            return None
            
    def _generate_sync(self, template_key: str, data: Dict[str, Any], 
                      output_path: str, report_type: str) -> bool:
        """
        同步生成报告
        
        Args:
            template_key: 模板键名
            data: 数据字典
            output_path: 输出路径
            report_type: 报告类型
            
        Returns:
            bool: 是否生成成功
        """
        try:
            if report_type == 'word':
                success = self.word_generator.generate_document(template_key, data, output_path)
            elif report_type == 'excel':
                success = self.excel_generator.generate_report(template_key, data, output_path)
            else:
                raise ValueError(f"不支持的报告类型: {report_type}")
                
            if success:
                # 记录生成历史
                self._save_generation_history(template_key, output_path, data, True)
                self.logger.info(f"报告生成成功: {output_path}")
            else:
                self.logger.error(f"报告生成失败: {output_path}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"同步生成报告失败: {str(e)}")
            return False
            
    def _submit_async_task(self, template_key: str, data: Dict[str, Any], 
                          output_path: str, report_type: str,
                          progress_callback: Optional[Callable[[str, int], None]] = None) -> str:
        """
        提交异步生成任务
        
        Args:
            template_key: 模板键名
            data: 数据字典
            output_path: 输出路径
            report_type: 报告类型
            progress_callback: 进度回调函数
            
        Returns:
            str: 任务ID
        """
        # 生成任务ID
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.tasks)}"
        
        # 创建任务对象
        task = ReportGenerationTask(task_id, template_key, data, output_path, report_type)
        
        with self._task_lock:
            self.tasks[task_id] = task
            
        # 提交任务
        future = self.executor.submit(self._execute_async_task, task, progress_callback)
        
        self.logger.info(f"异步任务已提交: {task_id}")
        return task_id
        
    def _execute_async_task(self, task: ReportGenerationTask, 
                           progress_callback: Optional[Callable[[str, int], None]] = None) -> None:
        """
        执行异步任务
        
        Args:
            task: 任务对象
            progress_callback: 进度回调函数
        """
        try:
            # 更新任务状态
            task.status = 'running'
            task.start_time = datetime.now()
            task.progress = 10
            
            if progress_callback:
                progress_callback(task.task_id, task.progress)
                
            # 执行生成
            task.progress = 30
            if progress_callback:
                progress_callback(task.task_id, task.progress)
                
            if task.task_type == 'word':
                success = self.word_generator.generate_document(
                    task.template_key, task.data, task.output_path
                )
            elif task.task_type == 'excel':
                success = self.excel_generator.generate_report(
                    task.template_key, task.data, task.output_path
                )
            else:
                raise ValueError(f"不支持的报告类型: {task.task_type}")
                
            task.progress = 80
            if progress_callback:
                progress_callback(task.task_id, task.progress)
                
            # 更新任务状态
            if success:
                task.status = 'completed'
                task.progress = 100
                # 记录生成历史
                self._save_generation_history(
                    task.template_key, task.output_path, task.data, True
                )
                self.logger.info(f"异步任务完成: {task.task_id}")
            else:
                task.status = 'failed'
                task.error_message = "生成失败"
                self.logger.error(f"异步任务失败: {task.task_id}")
                
        except Exception as e:
            task.status = 'failed'
            task.error_message = str(e)
            self.logger.error(f"异步任务执行异常: {task.task_id}, {str(e)}")
            
        finally:
            task.end_time = datetime.now()
            if progress_callback:
                progress_callback(task.task_id, task.progress)
                
    def batch_generate(self, 
                      generation_requests: List[Dict[str, Any]], 
                      async_mode: bool = True,
                      progress_callback: Optional[Callable[[str, int], None]] = None) -> List[Optional[Union[bool, str]]]:
        """
        批量生成报告
        
        Args:
            generation_requests: 生成请求列表，每个请求包含 template_key, data, output_filename
            async_mode: 是否异步模式
            progress_callback: 进度回调函数
            
        Returns:
            List: 生成结果列表（同步模式返回bool，异步模式返回task_id）
        """
        results = []
        
        for i, request in enumerate(generation_requests):
            try:
                template_key = request['template_key']
                data = request['data']
                output_filename = request.get('output_filename')
                
                result = self.generate_report(
                    template_key, data, output_filename, async_mode, progress_callback
                )
                results.append(result)
                
                # 批量进度回调
                if progress_callback and not async_mode:
                    batch_progress = int((i + 1) / len(generation_requests) * 100)
                    progress_callback(f"batch_{datetime.now().strftime('%H%M%S')}", batch_progress)
                    
            except Exception as e:
                self.logger.error(f"批量生成第{i+1}个报告失败: {str(e)}")
                results.append(None if not async_mode else None)
                
        self.logger.info(f"批量生成完成，总数: {len(generation_requests)}, 成功: {sum(1 for r in results if r)}")
        return results
        
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务状态信息
        """
        with self._task_lock:
            task = self.tasks.get(task_id)
            return task.to_dict() if task else None
            
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务状态
        
        Returns:
            List[Dict]: 所有任务状态列表
        """
        with self._task_lock:
            return [task.to_dict() for task in self.tasks.values()]
            
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务（注意：正在执行的任务无法立即取消）
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否取消成功
        """
        with self._task_lock:
            task = self.tasks.get(task_id)
            if task and task.status == 'pending':
                task.status = 'cancelled'
                self.logger.info(f"任务已取消: {task_id}")
                return True
            return False
            
    def cleanup_completed_tasks(self, keep_recent: int = 100) -> int:
        """
        清理已完成的任务
        
        Args:
            keep_recent: 保留最近的任务数量
            
        Returns:
            int: 清理的任务数量
        """
        with self._task_lock:
            completed_tasks = [
                task for task in self.tasks.values() 
                if task.status in ['completed', 'failed', 'cancelled']
            ]
            
            if len(completed_tasks) <= keep_recent:
                return 0
                
            # 按时间排序，保留最新的
            completed_tasks.sort(key=lambda t: t.end_time or t.created_time, reverse=True)
            tasks_to_remove = completed_tasks[keep_recent:]
            
            removed_count = 0
            for task in tasks_to_remove:
                if task.task_id in self.tasks:
                    del self.tasks[task.task_id]
                    removed_count += 1
                    
            self.logger.info(f"清理了 {removed_count} 个已完成的任务")
            return removed_count
            
    def _save_generation_history(self, template_key: str, output_path: str, 
                                data: Dict[str, Any], success: bool) -> None:
        """
        保存生成历史记录
        
        Args:
            template_key: 模板键名
            output_path: 输出路径
            data: 数据字典
            success: 是否成功
        """
        try:
            history_entry = {
                'timestamp': datetime.now().isoformat(),
                'template_key': template_key,
                'output_path': output_path,
                'success': success,
                'data_keys': list(data.keys())  # 只保存数据键名，不保存实际数据
            }
            
            # 读取现有历史
            history = []
            if self.history_file.exists():
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except:
                    history = []
                    
            # 添加新记录
            history.append(history_entry)
            
            # 只保留最近1000条记录
            if len(history) > 1000:
                history = history[-1000:]
                
            # 保存历史
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存生成历史失败: {str(e)}")
            
    def get_generation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取生成历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 历史记录列表
        """
        try:
            if not self.history_file.exists():
                return []
                
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
                
            # 返回最近的记录
            return history[-limit:] if len(history) > limit else history
            
        except Exception as e:
            self.logger.error(f"读取生成历史失败: {str(e)}")
            return []
            
    def cleanup_output_files(self, older_than_days: int = 30) -> int:
        """
        清理过期的输出文件
        
        Args:
            older_than_days: 清理多少天前的文件
            
        Returns:
            int: 清理的文件数量
        """
        try:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(days=older_than_days)
            
            removed_count = 0
            for file_path in self.output_base_path.iterdir():
                if file_path.is_file() and file_path.suffix in ['.docx', '.xlsx']:
                    # 检查文件修改时间
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            removed_count += 1
                            self.logger.debug(f"删除过期文件: {file_path}")
                        except Exception as e:
                            self.logger.warning(f"删除文件失败: {file_path}, {str(e)}")
                            
            self.logger.info(f"清理了 {removed_count} 个过期输出文件")
            return removed_count
            
        except Exception as e:
            self.logger.error(f"清理输出文件失败: {str(e)}")
            return 0
            
    def shutdown(self) -> None:
        """
        关闭报告管理器，等待所有任务完成
        """
        self.logger.info("正在关闭报告管理器...")
        self.executor.shutdown(wait=True)
        self.logger.info("报告管理器已关闭") 