# 月度工资异动处理系统 - 数据库集成设计补充方案

## 1. 问题分析

原设计方案主要基于Excel文件处理，存在以下局限性：

### 1.1 文件驱动模式的问题
- **数据孤立性**：每月数据独立存储，缺乏关联性
- **历史追溯困难**：无法方便地查询历史异动记录
- **数据一致性风险**：多个文件可能存在数据不一致
- **并发访问限制**：Excel文件不支持多用户同时操作
- **查询能力有限**：无法进行复杂的数据分析和统计

### 1.2 业务需求的变化
- **审计要求**：需要完整的历史数据追踪
- **统计分析**：需要跨月度的数据分析能力
- **决策支持**：需要生成各种统计报表
- **数据安全**：需要更好的数据保护和权限控制

## 2. 数据库集成方案

### 2.1 数据库选型

考虑到项目是单机版Windows应用，推荐**SQLite**作为主要数据库解决方案：

**优势分析：**
- **轻量级部署**：单文件存储，无需额外数据库服务器
- **零配置安装**：Python内置支持，降低部署复杂度
- **事务完整性**：支持ACID事务，保证数据一致性
- **SQL标准支持**：支持复杂查询和数据分析
- **性能适中**：对于单机版应用性能完全满足需求
- **易于备份**：整个数据库就是一个文件，备份简单

**与其他方案对比：**
```python
DATABASE_COMPARISON = {
    "SQLite": {
        "优势": ["无需安装", "单文件存储", "轻量级", "Python原生支持"],
        "劣势": ["并发写入有限", "不支持网络访问"],
        "适用场景": "单用户桌面应用",
        "推荐指数": "★★★★★"
    },
    "SQL Server LocalDB": {
        "优势": ["功能完整", "与Windows集成好", "支持复杂查询"],
        "劣势": ["需要额外安装", "占用资源较多"],
        "适用场景": "企业环境，需要高级功能",
        "推荐指数": "★★★"
    },
    "MySQL/PostgreSQL": {
        "优势": ["功能强大", "支持网络访问", "并发性能好"],
        "劣势": ["需要服务器配置", "部署复杂"],
        "适用场景": "多用户网络应用",
        "推荐指数": "★★"
    }
}
```

### 2.2 核心数据表设计

基于工资异动处理的业务需求，设计以下核心数据表：

```sql
-- 1. 员工基础信息表
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL,    -- 工号
    id_card VARCHAR(18) UNIQUE,                 -- 身份证号
    name VARCHAR(50) NOT NULL,                  -- 姓名
    department VARCHAR(100),                    -- 部门
    position VARCHAR(100),                      -- 职位
    employee_type VARCHAR(50),                  -- 人员类别
    status VARCHAR(20) DEFAULT 'active',       -- 状态：active/inactive
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 工资数据表（月度快照）
CREATE TABLE salary_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) NOT NULL,          -- 工号
    salary_month VARCHAR(7) NOT NULL,          -- 工资月份（YYYY-MM）
    basic_salary DECIMAL(10,2) DEFAULT 0,     -- 岗位工资
    grade_salary DECIMAL(10,2) DEFAULT 0,     -- 薪级工资
    allowance DECIMAL(10,2) DEFAULT 0,        -- 津贴
    performance_basic DECIMAL(10,2) DEFAULT 0, -- 基础性绩效
    performance_reward DECIMAL(10,2) DEFAULT 0, -- 奖励性绩效
    subsidy_transport DECIMAL(10,2) DEFAULT 0, -- 交通补贴
    subsidy_housing DECIMAL(10,2) DEFAULT 0,   -- 住房补贴
    subsidy_property DECIMAL(10,2) DEFAULT 0,  -- 物业补贴
    subsidy_communication DECIMAL(10,2) DEFAULT 0, -- 通讯补贴
    subsidy_car DECIMAL(10,2) DEFAULT 0,       -- 车补
    subsidy_health DECIMAL(10,2) DEFAULT 0,    -- 卫生费
    bonus DECIMAL(10,2) DEFAULT 0,             -- 补发
    advance DECIMAL(10,2) DEFAULT 0,           -- 借支
    gross_salary DECIMAL(10,2) DEFAULT 0,      -- 应发工资
    fund_housing DECIMAL(10,2) DEFAULT 0,      -- 公积金
    insurance_pension DECIMAL(10,2) DEFAULT 0, -- 养老保险
    net_salary DECIMAL(10,2) DEFAULT 0,        -- 实发工资
    data_source VARCHAR(100),                  -- 数据来源
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
    UNIQUE(employee_id, salary_month)
);

-- 3. 异动记录表
CREATE TABLE salary_changes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) NOT NULL,          -- 工号
    change_month VARCHAR(7) NOT NULL,          -- 异动月份
    change_type VARCHAR(50) NOT NULL,          -- 异动类型
    change_reason TEXT,                        -- 异动原因
    effective_date DATE,                       -- 生效日期
    affected_fields TEXT,                      -- 影响字段（JSON格式）
    original_values TEXT,                      -- 原始值（JSON格式）
    new_values TEXT,                           -- 新值（JSON格式）
    change_amount DECIMAL(10,2),               -- 变动金额
    operator VARCHAR(50),                      -- 操作人
    approval_status VARCHAR(20) DEFAULT 'pending', -- 审批状态
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_time DATETIME,
    remarks TEXT,
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
);

-- 4. 异动规则配置表
CREATE TABLE change_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name VARCHAR(100) UNIQUE NOT NULL,    -- 规则名称
    change_type VARCHAR(50) NOT NULL,          -- 异动类型
    operation_type VARCHAR(20) NOT NULL,       -- 操作类型：add/subtract/replace/clear
    affected_fields TEXT NOT NULL,             -- 影响字段（JSON格式）
    calculation_formula TEXT,                  -- 计算公式
    rule_config TEXT,                          -- 规则配置（JSON格式）
    is_active BOOLEAN DEFAULT 1,              -- 是否启用
    priority INTEGER DEFAULT 0,               -- 优先级
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 5. 处理历史表
CREATE TABLE processing_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL,     -- 批次ID
    process_month VARCHAR(7) NOT NULL,        -- 处理月份
    process_type VARCHAR(50) NOT NULL,        -- 处理类型
    input_files TEXT,                         -- 输入文件（JSON格式）
    output_files TEXT,                        -- 输出文件（JSON格式）
    total_employees INTEGER,                  -- 处理员工数
    success_count INTEGER,                    -- 成功数量
    error_count INTEGER,                      -- 错误数量
    total_amount DECIMAL(15,2),               -- 总金额变动
    process_status VARCHAR(20),               -- 处理状态
    start_time DATETIME,
    end_time DATETIME,
    operator VARCHAR(50),
    remarks TEXT
);

-- 6. 系统配置表
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string/number/boolean/json
    description TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引提升查询性能
CREATE INDEX idx_employees_id ON employees(employee_id);
CREATE INDEX idx_employees_idcard ON employees(id_card);
CREATE INDEX idx_salary_employee_month ON salary_data(employee_id, salary_month);
CREATE INDEX idx_changes_employee_month ON salary_changes(employee_id, change_month);
CREATE INDEX idx_changes_type ON salary_changes(change_type);
CREATE INDEX idx_processing_month ON processing_history(process_month);
```

### 2.3 数据库操作层设计

采用DAO（Data Access Object）模式：

```python
# 数据库管理器基类
class DatabaseManager:
    """数据库管理器，提供统一的数据库操作接口"""
    
    def __init__(self, db_path: str = "data/salary_system.db"):
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """建立数据库连接"""
        import sqlite3
        self.connection = sqlite3.connect(self.db_path)
        self.connection.row_factory = sqlite3.Row
        return self.connection
        
    def execute_query(self, query: str, params: tuple = ()):
        """执行查询操作"""
        cursor = self.connection.cursor()
        cursor.execute(query, params)
        return cursor.fetchall()
        
    def execute_transaction(self, operations: list):
        """执行事务操作"""
        try:
            cursor = self.connection.cursor()
            for operation in operations:
                cursor.execute(operation['sql'], operation.get('params', ()))
            self.connection.commit()
            return True
        except Exception as e:
            self.connection.rollback()
            raise e

# 员工数据访问对象
class EmployeeDAO:
    """员工数据访问对象"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        
    def find_employee(self, query: str) -> dict:
        """多条件查询员工（优先级：工号 > 身份证 > 姓名）"""
        sql_queries = [
            "SELECT * FROM employees WHERE employee_id = ?",
            "SELECT * FROM employees WHERE id_card = ?", 
            "SELECT * FROM employees WHERE name LIKE ?"
        ]
        
        for i, sql in enumerate(sql_queries):
            params = (query,) if i < 2 else (f"%{query}%",)
            result = self.db.execute_query(sql, params)
            if result:
                return dict(result[0])
        return None
```

### 2.4 数据分析模块

```python
class SalaryAnalytics:
    """工资数据分析模块"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        
    def generate_monthly_summary(self, month: str):
        """生成月度工资汇总统计"""
        sql = """
        SELECT 
            e.employee_type,
            COUNT(*) as employee_count,
            SUM(sd.gross_salary) as total_gross,
            AVG(sd.gross_salary) as avg_gross,
            SUM(sd.net_salary) as total_net
        FROM salary_data sd
        JOIN employees e ON sd.employee_id = e.employee_id
        WHERE sd.salary_month = ? AND e.status = 'active'
        GROUP BY e.employee_type
        ORDER BY total_gross DESC
        """
        return self.db.execute_query(sql, (month,))
        
    def analyze_salary_trends(self, employee_id: str, months: int = 12):
        """分析个人工资变动趋势"""
        sql = """
        SELECT 
            salary_month,
            gross_salary,
            net_salary,
            (gross_salary - LAG(gross_salary) OVER (ORDER BY salary_month)) as month_change
        FROM salary_data
        WHERE employee_id = ?
        ORDER BY salary_month DESC
        LIMIT ?
        """
        return self.db.execute_query(sql, (employee_id, months))
        
    def get_department_statistics(self, month: str):
        """部门工资统计分析"""
        sql = """
        SELECT 
            e.department,
            COUNT(*) as employee_count,
            SUM(sd.gross_salary) as department_total,
            AVG(sd.gross_salary) as department_avg
        FROM salary_data sd
        JOIN employees e ON sd.employee_id = e.employee_id
        WHERE sd.salary_month = ?
        GROUP BY e.department
        ORDER BY department_total DESC
        """
        return self.db.execute_query(sql, (month,))
```

### 2.5 混合模式设计

设计文件驱动与数据库存储的混合模式，兼顾用户习惯和数据管理需求：

```python
class HybridDataManager:
    """混合数据管理器，支持Excel文件和数据库的协同工作"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.db_manager.connect()
        
        self.employee_dao = EmployeeDAO(self.db_manager)
        self.salary_dao = SalaryDAO(self.db_manager)
        self.change_dao = ChangeDAO(self.db_manager)
        self.analytics = SalaryAnalytics(self.db_manager)
        
    def process_monthly_workflow(self, excel_files: dict, month: str):
        """月度处理主工作流（混合模式）"""
        workflow_steps = [
            self._step1_import_excel_data,
            self._step2_perform_matching,
            self._step3_apply_changes,
            self._step4_generate_reports,
            self._step5_export_results
        ]
        
        context = {
            'excel_files': excel_files,
            'month': month,
            'results': {}
        }
        
        for step in workflow_steps:
            context = step(context)
            
        return context['results']
        
    def get_employee_detail(self, query: str):
        """获取员工详细信息（包含历史数据）"""
        employee = self.employee_dao.find_employee(query)
        if not employee:
            return None
            
        # 获取工资历史
        salary_history = self.salary_dao.get_employee_salary_history(employee['employee_id'])
        
        return {
            'basic_info': employee,
            'salary_history': salary_history,
            'trend_analysis': self.analytics.analyze_salary_trends(employee['employee_id'])
        }
```

## 3. 系统架构调整

### 3.1 技术选型更新

```python
UPDATED_TECH_STACK = {
    "开发语言": "Python 3.x",
    "GUI框架": "Tkinter",
    "数据处理": "pandas, openpyxl, xlrd, xlwt",
    "文档处理": "python-docx, xlsxwriter",
    "数据库": "SQLite",
    "ORM框架": "SQLAlchemy (可选)",
    "数据分析": "sqlite3 + pandas",
    "可视化": "matplotlib (可选)",
    "打包工具": "PyInstaller",
    "日志管理": "logging"
}
```

### 3.2 新增模块

```mermaid
graph TD
A[主控模块] --> B[数据管理模块]
A --> C[异动处理引擎]
A --> D[报表生成模块]
A --> E[文档生成模块]

B --> F[Excel处理器]
B --> G[数据库管理器]
B --> H[数据同步器]

G --> I[员工DAO]
G --> J[工资DAO]
G --> K[异动DAO]
G --> L[配置DAO]

C --> M[人员匹配器]
C --> N[异动规则引擎]
C --> O[计算验证器]

D --> P[异动汇总表生成]
D --> Q[数据分析引擎]
D --> R[统计报表生成]

E --> S[工资审批表生成]
E --> T[请示文档生成]
```

## 4. 实施策略

### 4.1 渐进式迁移

1. **第一阶段**：保持现有Excel处理逻辑，增加数据库存储
2. **第二阶段**：开发数据分析功能，提供历史查询能力
3. **第三阶段**：优化性能，完善混合模式工作流
4. **第四阶段**：增加高级分析和可视化功能

### 4.2 兼容性保证

- **数据格式兼容**：支持现有Excel文件格式
- **用户习惯保持**：保留熟悉的操作流程
- **渐进式切换**：用户可选择使用数据库功能

### 4.3 性能优化

- **索引策略**：为常用查询字段建立索引
- **批量操作**：使用事务处理大量数据
- **内存管理**：大数据集分批处理
- **缓存机制**：缓存常用查询结果

## 5. 优势总结

### 5.1 解决的问题

1. **数据持久化**：所有工资和异动数据永久保存
2. **历史追溯**：快速查询任意员工的历史记录
3. **统计分析**：支持复杂的跨月度数据分析
4. **数据一致性**：通过事务保证数据完整性
5. **审计能力**：完整的操作日志和变更记录

### 5.2 新增能力

1. **趋势分析**：员工工资变动趋势图表
2. **部门统计**：各部门工资分布和变化
3. **异动影响分析**：异动对总体工资的影响
4. **自动报表**：基于数据库的动态报表生成
5. **数据验证**：更强的数据完整性检查

### 5.3 用户体验提升

1. **快速查询**：秒级响应的员工信息查询
2. **智能匹配**：基于历史数据的智能人员匹配
3. **实时统计**：实时的数据统计和分析
4. **数据备份**：简单的数据库备份和恢复
5. **错误恢复**：基于事务的数据回滚能力

这个数据库集成方案完美补充了原有设计，提供了强大的数据管理和分析能力，同时保持了系统的易用性和兼容性。 