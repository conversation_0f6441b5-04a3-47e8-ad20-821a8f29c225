# 用户界面原型设计

## 1. 界面设计原则

### 1.1 设计理念
- **简洁明了**: 界面简洁，信息层次清晰
- **操作便捷**: 减少用户操作步骤，提供快捷方式
- **信息透明**: 处理过程可视化，状态反馈及时
- **容错友好**: 提供清晰的错误提示和恢复机制

### 1.2 界面设计规范
- **配色方案**: 采用简洁的蓝白配色，突出重要操作
- **字体规范**: 统一使用微软雅黑，确保可读性
- **控件规范**: 统一控件样式，保持界面一致性
- **响应式设计**: 适配不同分辨率屏幕

## 2. 主界面设计

### 2.1 主窗口布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 月度工资异动处理系统 v1.0                                    [_] [□] [×]      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 文件(F)  工具(T)  设置(S)  帮助(H)                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─── 文件选择区 ────────────────────────────┐ ┌─── 人员查询区 ──────────────┐ │
│ │ 📁 工资表文件:                              │ │ 🔍 人员查询:                 │ │
│ │ [data/工资表/2025年5月工资表.xls     ] [浏览] │ │ [请输入工号/身份证/姓名      ] │ │
│ │                                           │ │ [🔍查询] [清空]               │ │
│ │ 📁 异动人员文件:                           │ │                             │ │
│ │ [data/异动人员/5月异动人员.xlsx     ] [浏览] │ │ 查询结果:                    │ │
│ │                                           │ │ ┌─────────────────────────┐ │ │
│ │ 📁 输出目录:                              │ │ │ 工号: 12345678           │ │ │
│ │ [output/2025年5月/               ] [浏览] │ │ │ 姓名: 张三               │ │ │
│ │                                           │ │ │ 部门: 计算机学院          │ │ │
│ │ ☑ 自动备份原文件                           │ │ │ 当前工资: 8500.00       │ │ │
│ │ ☑ 生成处理报告                             │ │ └─────────────────────────┘ │ │
│ └───────────────────────────────────────────┘ └─────────────────────────────┘ │
│                                                                               │
│ ┌─── 异动处理配置 ──────────────────────────┐ ┌─── 处理进度 ────────────────┐ │
│ │ 处理月份: [2025-05 ▼]                     │ │ 当前状态: 就绪              │ │
│ │                                           │ │                             │ │
│ │ 异动规则:                                 │ │ 进度: [████████████] 100%   │ │
│ │ ☑ 起薪（恢复薪资待遇）                     │ │                             │ │
│ │ ☑ 停薪-退休                               │ │ 处理详情:                   │ │
│ │ ☑ 停薪-调离                               │ │ ┌─────────────────────────┐ │ │
│ │ ☑ 其他-考核扣款                           │ │ │ [时间] 操作日志...        │ │ │
│ │ ☑ 请假扣款                                │ │ │ 14:30:25 开始读取工资表   │ │ │
│ │ ☑ 脱产读博                                │ │ │ 14:30:26 读取完成,共1250条 │ │ │
│ │                                           │ │ │ 14:30:27 开始人员匹配...  │ │ │
│ │ [高级设置...]                              │ │ │ 14:30:35 匹配完成,成功98% │ │ │
│ └───────────────────────────────────────────┘ │ │ ...                     │ │ │
│                                                │ └─────────────────────────┘ │ │
│ ┌─── 操作按钮区 ────────────────────────────┐ └─────────────────────────────┘ │
│ │ [🚀 开始处理]  [⏸ 暂停]  [⏹ 停止]  [📋 预览结果]                         │ │
│ │                                                                         │ │
│ │ [📊 生成汇总表]  [📋 生成审批表]  [📄 生成请示文档]                        │ │
│ │                                                                         │ │
│ │ [📁 打开输出目录]  [📝 查看日志]  [❓ 帮助]                               │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态栏: 就绪 | 工资记录: 1250条 | 异动记录: 45条 | 最后更新: 2025-01-20 14:30:35 │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 界面区域说明

#### 2.2.1 文件选择区
- **工资表文件选择**: 支持.xls和.xlsx格式
- **异动人员文件选择**: 支持多sheet Excel文件
- **输出目录选择**: 可自定义输出位置
- **处理选项**: 备份和报告生成选项

#### 2.2.2 人员查询区
- **查询输入框**: 支持工号、身份证、姓名查询
- **查询结果显示**: 展示人员基本信息和当前工资
- **操作按钮**: 查询和清空功能

#### 2.2.3 异动处理配置区
- **处理月份选择**: 下拉框选择处理月份
- **异动规则配置**: 复选框选择启用的异动类型
- **高级设置**: 详细参数配置入口

#### 2.2.4 处理进度区
- **状态显示**: 当前处理状态
- **进度条**: 可视化处理进度
- **操作日志**: 实时显示处理过程

#### 2.2.5 操作按钮区
- **处理控制**: 开始、暂停、停止按钮
- **报表生成**: 各类报表和文档生成
- **辅助功能**: 文件夹打开、日志查看等

## 3. 子界面设计

### 3.1 文件选择对话框

```
┌─────────────────────────────────────────────────────┐
│ 选择工资表文件                                [×]   │
├─────────────────────────────────────────────────────┤
│ 查找范围: [data/工资表/            ] [浏览...]       │
│                                                     │
│ 文件列表:                                           │
│ ┌─────────────────────────────────────────────────┐ │
│ │ 📄 2025年4月份正式工工资表.xls    (1.2MB)      │ │
│ │ 📄 2025年5月份正式工工资表.xls    (1.3MB)      │ │
│ │ 📄 2025年5月份正式工工资表(修正).xls (1.3MB)   │ │
│ │ 📁 历史文件/                                  │ │
│ │ 📁 备份文件/                                  │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ 文件名: [2025年5月份正式工工资表.xls               ] │
│                                                     │
│ 文件类型: [Excel文件(*.xls;*.xlsx) ▼]              │
│                                                     │
│ ☑ 显示文件预览                                      │
│                                                     │
│ ┌─── 文件预览 ─────────────────────────────────────┐ │
│ │ Sheet: 全部在职人员工资表 (1250行 × 23列)        │ │
│ │ ┌─────────────────────────────────────────────┐ │ │
│ │ │ 序号 | 工号     | 姓名 | 部门     | ...     │ │ │
│ │ │ 1    | 12345678 | 张三 | 计算机学院 | ...   │ │ │
│ │ │ 2    | 12345679 | 李四 | 数学学院  | ...    │ │ │
│ │ │ ...  | ...      | ... | ...       | ...   │ │ │
│ │ └─────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│              [确定]  [取消]  [帮助]                 │
└─────────────────────────────────────────────────────┘
```

### 3.2 人员匹配确认对话框

```
┌─────────────────────────────────────────────────────────────────┐
│ 人员匹配确认                                              [×]   │
├─────────────────────────────────────────────────────────────────┤
│ 发现以下人员需要手动确认匹配:                                   │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 异动人员: 张三 (身份证: 320***************8)                │ │
│ │                                                             │ │
│ │ 可能匹配的工资表人员:                                       │ │
│ │ ○ 张三   (工号: 12345678, 部门: 计算机学院)                │ │
│ │ ○ 张三丰 (工号: 12345679, 部门: 数学学院)                  │ │
│ │ ○ 张小三 (工号: 12345680, 部门: 外语学院)                  │ │
│ │ ○ 不匹配 (标记为未找到)                                     │ │
│ │                                                             │ │
│ │ 匹配置信度: ★★★☆☆ (75%)                                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 异动人员: 李四 (工号: 12345999)                             │ │
│ │                                                             │ │
│ │ ❌ 在工资表中未找到该工号                                   │ │
│ │                                                             │ │
│ │ 可能的原因:                                                 │ │
│ │ • 工号输入错误                                              │ │
│ │ • 该员工不在当月工资表中                                    │ │
│ │ • 该员工已离职                                              │ │
│ │                                                             │ │
│ │ 处理方式:                                                   │ │
│ │ ○ 手动输入正确工号: [____________]                          │ │
│ │ ○ 跳过此人员                                                │ │
│ │ ○ 标记为异常记录                                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 进度: 2/5 待确认                                               │ │
│                                                                 │
│        [< 上一个]  [下一个 >]  [全部确认]  [取消]              │
└─────────────────────────────────────────────────────────────────┘
```

### 3.3 处理结果预览窗口

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 异动处理结果预览                                                      [×]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 标签: [汇总统计] [详细列表] [异常记录] [金额变动]                            │
│                                                                             │
│ ═══ 汇总统计 ═══════════════════════════════════════════════════════════════ │
│                                                                             │
│ 📊 处理统计:                                                                │
│ ├─ 总计处理人员: 45人                                                      │
│ ├─ 成功处理: 42人 (93.3%)                                                  │
│ ├─ 异常记录: 3人 (6.7%)                                                    │
│ └─ 跳过处理: 0人                                                           │
│                                                                             │
│ 💰 金额统计:                                                                │
│ ├─ 工资总变动: -85,600.00元                                                │
│ ├─ 增加金额: +12,800.00元 (6人)                                            │
│ ├─ 减少金额: -98,400.00元 (36人)                                           │
│ └─ 平均变动: -2,035.71元/人                                                │
│                                                                             │
│ 📋 异动类型分布:                                                            │
│ ┌─────────────────────────────────────────────────────────────────────┐     │
│ │ 异动类型        │ 人数 │ 变动金额      │ 占比     │ 平均金额        │     │
│ ├─────────────────────────────────────────────────────────────────────┤     │
│ │ 停薪-退休       │ 15   │ -75,000.00   │ 35.7%    │ -5,000.00      │     │
│ │ 其他-考核扣款    │ 12   │ -30,000.00   │ 28.6%    │ -2,500.00      │     │
│ │ 请假扣款        │ 8    │ -8,400.00    │ 19.0%    │ -1,050.00      │     │
│ │ 起薪           │ 6    │ +12,800.00   │ 14.3%    │ +2,133.33      │     │
│ │ 脱产读博        │ 4    │ -4,000.00    │ 9.5%     │ -1,000.00      │     │
│ │ 停薪-调离       │ 2    │ -10,000.00   │ 4.8%     │ -5,000.00      │     │
│ └─────────────────────────────────────────────────────────────────────┘     │
│                                                                             │
│                    [导出详细报告]  [保存结果]  [关闭]                      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.4 高级设置对话框

```
┌─────────────────────────────────────────────────────────────────┐
│ 高级设置                                                  [×]   │
├─────────────────────────────────────────────────────────────────┤
│ 标签: [匹配设置] [计算规则] [文件格式] [系统设置]                │
│                                                                 │
│ ═══ 匹配设置 ═══════════════════════════════════════════════════ │
│                                                                 │
│ 匹配优先级:                                                     │
│ ☑ 工号匹配 (最高优先级)                                         │
│ ☑ 身份证号匹配                                                  │
│ ☑ 姓名匹配                                                      │
│                                                                 │
│ 模糊匹配设置:                                                   │
│ ☑ 启用姓名模糊匹配                                              │
│ 相似度阈值: [85    ]% (推荐值: 80-90%)                         │
│                                                                 │
│ ☑ 启用身份证号容错匹配                                          │
│ □ 忽略身份证号中的空格和连字符                                  │
│                                                                 │
│ 工号格式验证:                                                   │
│ ☑ 验证工号为8位数字                                             │
│ ☑ 工号范围检查: [10000000] 到 [99999999]                      │
│                                                                 │
│ 匹配结果处理:                                                   │
│ ○ 自动接受高置信度匹配 (≥95%)                                  │
│ ● 所有匹配结果都需要确认                                        │
│ ○ 仅对低置信度匹配进行确认 (<80%)                              │
│                                                                 │
│ ☑ 保存匹配结果到数据库                                          │
│ ☑ 生成匹配报告                                                  │
│                                                                 │
│              [恢复默认]  [确定]  [取消]  [应用]                 │
└─────────────────────────────────────────────────────────────────┘
```

## 4. 交互流程设计

### 4.1 主流程交互图

```mermaid
graph TD
    A[启动应用] --> B[加载界面]
    B --> C[选择文件]
    C --> D[验证文件]
    D --> E{文件有效?}
    E -->|否| F[显示错误信息]
    F --> C
    E -->|是| G[预览数据]
    G --> H[配置处理参数]
    H --> I[开始处理]
    I --> J[人员匹配]
    J --> K{需要手动确认?}
    K -->|是| L[显示匹配确认对话框]
    L --> M[用户确认匹配]
    M --> N[执行异动计算]
    K -->|否| N
    N --> O[显示处理进度]
    O --> P[生成结果预览]
    P --> Q[用户确认结果]
    Q --> R{结果满意?}
    R -->|否| S[调整参数]
    S --> H
    R -->|是| T[生成报表文档]
    T --> U[保存结果]
    U --> V[显示完成信息]
```

### 4.2 人员匹配交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant GUI as 界面
    participant M as 匹配器
    participant DB as 数据库
    
    U->>GUI: 点击"开始处理"
    GUI->>M: 开始人员匹配
    M->>DB: 查询员工数据
    DB-->>M: 返回员工列表
    
    loop 对每个异动人员
        M->>M: 执行匹配算法
        alt 精确匹配成功
            M->>GUI: 更新进度 (自动匹配)
        else 模糊匹配
            M->>GUI: 显示匹配确认对话框
            GUI->>U: 请求用户确认
            U->>GUI: 选择匹配结果
            GUI->>M: 返回用户选择
        else 匹配失败
            M->>GUI: 标记为异常记录
            GUI->>U: 显示异常提示
        end
    end
    
    M->>GUI: 匹配完成
    GUI->>U: 显示匹配结果摘要
```

### 4.3 数据验证交互流程

```mermaid
flowchart TD
    A[用户选择文件] --> B[文件格式检查]
    B --> C{格式正确?}
    C -->|否| D[显示格式错误提示]
    D --> A
    C -->|是| E[读取文件内容]
    E --> F[数据结构验证]
    F --> G{结构正确?}
    G -->|否| H[显示结构错误详情]
    H --> A
    G -->|是| I[数据内容验证]
    I --> J[检查必填字段]
    J --> K[检查数据类型]
    K --> L[检查数据范围]
    L --> M{验证通过?}
    M -->|否| N[显示验证错误列表]
    N --> O[用户选择处理方式]
    O --> P{继续处理?}
    P -->|否| A
    P -->|是| Q[标记问题数据]
    M -->|是| Q
    Q --> R[显示数据预览]
    R --> S[用户确认数据]
```

## 5. 界面响应式设计

### 5.1 分辨率适配

| 分辨率类型 | 最小尺寸 | 推荐尺寸 | 布局调整 |
|------------|----------|----------|----------|
| **标准屏幕** | 1024×768 | 1280×1024 | 标准布局 |
| **宽屏** | 1366×768 | 1920×1080 | 横向扩展 |
| **高分辨率** | 1920×1080 | 2560×1440 | 字体放大 |
| **超宽屏** | 2560×1080 | 3440×1440 | 分栏显示 |

### 5.2 字体和控件自适应

```python
# 自适应字体配置
FONT_CONFIG = {
    "small_screen": {
        "base_font": ("微软雅黑", 9),
        "title_font": ("微软雅黑", 11, "bold"),
        "button_font": ("微软雅黑", 9)
    },
    "standard_screen": {
        "base_font": ("微软雅黑", 10),
        "title_font": ("微软雅黑", 12, "bold"),
        "button_font": ("微软雅黑", 10)
    },
    "large_screen": {
        "base_font": ("微软雅黑", 12),
        "title_font": ("微软雅黑", 14, "bold"),
        "button_font": ("微软雅黑", 11)
    }
}

# 控件尺寸自适应
WIDGET_CONFIG = {
    "button_height": {"min": 25, "standard": 30, "large": 35},
    "entry_height": {"min": 22, "standard": 25, "large": 30},
    "listbox_height": {"min": 120, "standard": 150, "large": 180}
}
```

## 6. 主题和样式设计

### 6.1 颜色方案

```python
# 主题颜色配置
THEME_COLORS = {
    "primary": "#0078D4",      # 主色调（微软蓝）
    "secondary": "#106EBE",    # 辅助色
    "success": "#107C10",      # 成功色（绿色）
    "warning": "#FF8C00",      # 警告色（橙色）
    "danger": "#D13438",       # 危险色（红色）
    "info": "#00BCF2",         # 信息色（浅蓝）
    
    "background": "#FFFFFF",   # 背景色
    "surface": "#F5F5F5",      # 表面色
    "border": "#D1D1D1",       # 边框色
    "text_primary": "#323232", # 主文本色
    "text_secondary": "#717171" # 次要文本色
}

# 控件样式配置
WIDGET_STYLES = {
    "button_primary": {
        "bg": THEME_COLORS["primary"],
        "fg": "white",
        "relief": "flat",
        "borderwidth": 0,
        "padx": 20,
        "pady": 8
    },
    "button_secondary": {
        "bg": THEME_COLORS["surface"],
        "fg": THEME_COLORS["text_primary"],
        "relief": "solid",
        "borderwidth": 1,
        "padx": 15,
        "pady": 6
    },
    "entry_standard": {
        "bg": "white",
        "fg": THEME_COLORS["text_primary"],
        "relief": "solid",
        "borderwidth": 1,
        "highlightthickness": 2,
        "highlightcolor": THEME_COLORS["primary"]
    }
}
```

### 6.2 图标和资源

```python
# 图标资源配置
ICONS = {
    "file_open": "📁",
    "search": "🔍", 
    "start": "🚀",
    "pause": "⏸",
    "stop": "⏹",
    "preview": "📋",
    "report": "📊",
    "document": "📄",
    "folder": "📁",
    "log": "📝",
    "help": "❓",
    "success": "✅",
    "warning": "⚠️",
    "error": "❌",
    "info": "ℹ️"
}

# 状态图标
STATUS_ICONS = {
    "ready": "🟢",
    "processing": "🟡", 
    "completed": "✅",
    "error": "🔴",
    "warning": "🟠"
}
```

## 7. 用户体验优化

### 7.1 操作便捷性

#### 7.1.1 快捷键支持
- **Ctrl+O**: 打开工资表文件
- **Ctrl+I**: 打开异动人员文件
- **F5**: 开始处理
- **Ctrl+R**: 生成报表
- **F1**: 帮助
- **Ctrl+Q**: 退出程序

#### 7.1.2 拖拽支持
- 支持将Excel文件直接拖拽到相应的文件选择框
- 拖拽文件夹到输出目录选择框

#### 7.1.3 自动保存
- 自动保存用户设置和首选项
- 记住最后使用的文件路径
- 保存处理历史记录

### 7.2 错误处理和提示

#### 7.2.1 友好的错误信息
```python
ERROR_MESSAGES = {
    "file_not_found": {
        "title": "文件未找到",
        "message": "指定的文件不存在，请检查文件路径是否正确。",
        "suggestion": "点击'浏览'按钮重新选择文件。"
    },
    "file_format_error": {
        "title": "文件格式错误", 
        "message": "文件格式不正确或文件已损坏。",
        "suggestion": "请确保文件是有效的Excel格式(.xls或.xlsx)。"
    },
    "data_validation_error": {
        "title": "数据验证失败",
        "message": "文件中存在无效的数据格式。",
        "suggestion": "请检查以下字段的数据格式：{field_list}"
    }
}
```

#### 7.2.2 进度反馈机制
- 实时进度条显示
- 详细的操作日志
- 预计完成时间显示
- 可取消的长时间操作

### 7.3 帮助和指导

#### 7.3.1 内置帮助系统
- 操作步骤向导
- 常见问题解答
- 字段说明文档
- 视频教程链接

#### 7.3.2 工具提示
- 重要按钮和字段的工具提示
- 鼠标悬停显示详细说明
- 状态指示器解释

## 8. 可访问性设计

### 8.1 键盘导航
- 支持Tab键在控件间切换
- 支持Enter键确认操作
- 支持Esc键取消操作
- 支持方向键在列表中导航

### 8.2 视觉辅助
- 高对比度模式支持
- 字体大小可调节
- 颜色盲友好的配色
- 重要信息的多重提示（颜色+图标+文字）

### 8.3 屏幕阅读器支持
- 控件标签和描述
- 状态信息的文字描述
- 进度信息的语音反馈

这个用户界面原型设计提供了完整的界面规范和交互流程，确保系统具备良好的用户体验和操作便捷性。 