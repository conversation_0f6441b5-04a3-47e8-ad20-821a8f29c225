# 4类工资表专用结构长期方案

## 📋 总体目标

将当前的"一刀切"通用表结构改造为"量身定制"的专用表结构，真正实现4类工资表的差异化存储和管理。

## 🎯 实施策略

### 策略选择：渐进式改造
- **优点**：风险可控，可以逐步验证效果
- **实施方式**：先在一个表类型上试点，成功后推广到其他表类型
- **回滚机制**：保留现有通用结构作为备份

## 📊 第一阶段：专用表结构模板设计

### 1.1 4类工资表字段分析

#### 离休人员工资表（16个字段）
```python
RETIRED_EMPLOYEE_FIELDS = [
    ("id", "INTEGER", False, None, True, "自增主键"),
    ("sequence_number", "INTEGER", True, None, False, "序号"),
    ("employee_id", "TEXT", False, None, False, "人员代码"),
    ("employee_name", "TEXT", False, None, False, "姓名"),
    ("department", "TEXT", True, None, False, "部门名称"),
    ("basic_retirement_salary", "REAL", True, "0", False, "基本离休费"),
    ("balance_allowance", "REAL", True, "0", False, "结余津贴"),
    ("living_allowance", "REAL", True, "0", False, "生活补贴"),
    ("housing_allowance", "REAL", True, "0", False, "住房补贴"),
    ("property_allowance", "REAL", True, "0", False, "物业补贴"),
    ("retirement_allowance", "REAL", True, "0", False, "离休补贴"),
    ("nursing_fee", "REAL", True, "0", False, "护理费"),
    ("one_time_living_allowance", "REAL", True, "0", False, "增发一次性生活补贴"),
    ("supplement", "REAL", True, "0", False, "补发"),
    ("total", "REAL", True, "0", False, "合计"),
    ("advance", "REAL", True, "0", False, "借支"),
    ("remarks", "TEXT", True, None, False, "备注"),
    ("month", "TEXT", False, None, False, "月份"),
    ("year", "INTEGER", False, None, False, "年份"),
    ("created_at", "TEXT", False, None, False, "创建时间"),
    ("updated_at", "TEXT", False, None, False, "更新时间")
]
```

#### 退休人员工资表（27个字段）
```python
PENSION_EMPLOYEE_FIELDS = [
    ("id", "INTEGER", False, None, True, "自增主键"),
    ("sequence_number", "INTEGER", True, None, False, "序号"),
    ("employee_id", "TEXT", False, None, False, "人员代码"),
    ("employee_name", "TEXT", False, None, False, "姓名"),
    ("department", "TEXT", True, None, False, "部门名称"),
    ("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
    ("basic_retirement_salary", "REAL", True, "0", False, "基本退休费"),
    ("allowance", "REAL", True, "0", False, "津贴"),
    ("balance_allowance", "REAL", True, "0", False, "结余津贴"),
    ("retirement_living_allowance", "REAL", True, "0", False, "离退休生活补贴"),
    ("nursing_fee", "REAL", True, "0", False, "护理费"),
    ("property_allowance", "REAL", True, "0", False, "物业补贴"),
    ("housing_allowance", "REAL", True, "0", False, "住房补贴"),
    ("salary_advance", "REAL", True, "0", False, "增资预付"),
    ("adjustment_2016", "REAL", True, "0", False, "2016待遇调整"),
    ("adjustment_2017", "REAL", True, "0", False, "2017待遇调整"),
    ("adjustment_2018", "REAL", True, "0", False, "2018待遇调整"),
    ("adjustment_2019", "REAL", True, "0", False, "2019待遇调整"),
    ("adjustment_2020", "REAL", True, "0", False, "2020待遇调整"),
    ("adjustment_2021", "REAL", True, "0", False, "2021待遇调整"),
    ("adjustment_2022", "REAL", True, "0", False, "2022待遇调整"),
    ("adjustment_2023", "REAL", True, "0", False, "2023待遇调整"),
    ("supplement", "REAL", True, "0", False, "补发"),
    ("advance", "REAL", True, "0", False, "借支"),
    ("total_salary", "REAL", True, "0", False, "应发工资"),
    ("provident_fund", "REAL", True, "0", False, "公积"),
    ("insurance_deduction", "REAL", True, "0", False, "保险扣款"),
    ("remarks", "TEXT", True, None, False, "备注"),
    ("month", "TEXT", False, None, False, "月份"),
    ("year", "INTEGER", False, None, False, "年份"),
    ("created_at", "TEXT", False, None, False, "创建时间"),
    ("updated_at", "TEXT", False, None, False, "更新时间")
]
```

#### 全部在职人员工资表（23个字段）
```python
ACTIVE_EMPLOYEE_FIELDS = [
    ("id", "INTEGER", False, None, True, "自增主键"),
    ("sequence_number", "INTEGER", True, None, False, "序号"),
    ("employee_id", "TEXT", False, None, False, "工号"),
    ("employee_name", "TEXT", False, None, False, "姓名"),
    ("department", "TEXT", True, None, False, "部门名称"),
    ("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
    ("employee_type", "TEXT", True, None, False, "人员类别"),
    ("position_salary_2025", "REAL", True, "0", False, "2025年岗位工资"),
    ("grade_salary_2025", "REAL", True, "0", False, "2025年薪级工资"),
    ("allowance", "REAL", True, "0", False, "津贴"),
    ("balance_allowance", "REAL", True, "0", False, "结余津贴"),
    ("basic_performance_2025", "REAL", True, "0", False, "2025年基础性绩效"),
    ("health_fee", "REAL", True, "0", False, "卫生费"),
    ("transport_allowance", "REAL", True, "0", False, "交通补贴"),
    ("property_allowance", "REAL", True, "0", False, "物业补贴"),
    ("housing_allowance", "REAL", True, "0", False, "住房补贴"),
    ("car_allowance", "REAL", True, "0", False, "车补"),
    ("communication_allowance", "REAL", True, "0", False, "通讯补贴"),
    ("performance_bonus_2025", "REAL", True, "0", False, "2025年奖励性绩效预发"),
    ("supplement", "REAL", True, "0", False, "补发"),
    ("advance", "REAL", True, "0", False, "借支"),
    ("total_salary", "REAL", True, "0", False, "应发工资"),
    ("provident_fund_2025", "REAL", True, "0", False, "2025公积金"),
    ("pension_insurance", "REAL", True, "0", False, "代扣代存养老保险"),
    ("month", "TEXT", False, None, False, "月份"),
    ("year", "INTEGER", False, None, False, "年份"),
    ("created_at", "TEXT", False, None, False, "创建时间"),
    ("updated_at", "TEXT", False, None, False, "更新时间")
]
```

#### A岗职工工资表（21个字段）
```python
A_GRADE_EMPLOYEE_FIELDS = [
    ("id", "INTEGER", False, None, True, "自增主键"),
    ("sequence_number", "INTEGER", True, None, False, "序号"),
    ("employee_id", "TEXT", False, None, False, "工号"),
    ("employee_name", "TEXT", False, None, False, "姓名"),
    ("department", "TEXT", True, None, False, "部门名称"),
    ("employee_type", "TEXT", True, None, False, "人员类别"),
    ("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
    ("position_salary_2025", "REAL", True, "0", False, "2025年岗位工资"),
    ("seniority_salary_2025", "REAL", True, "0", False, "2025年校龄工资"),
    ("allowance", "REAL", True, "0", False, "津贴"),
    ("balance_allowance", "REAL", True, "0", False, "结余津贴"),
    ("basic_performance_2025", "REAL", True, "0", False, "2025年基础性绩效"),
    ("health_fee", "REAL", True, "0", False, "卫生费"),
    ("living_allowance_2025", "REAL", True, "0", False, "2025年生活补贴"),
    ("car_allowance", "REAL", True, "0", False, "车补"),
    ("performance_bonus_2025", "REAL", True, "0", False, "2025年奖励性绩效预发"),
    ("supplement", "REAL", True, "0", False, "补发"),
    ("advance", "REAL", True, "0", False, "借支"),
    ("total_salary", "REAL", True, "0", False, "应发工资"),
    ("provident_fund_2025", "REAL", True, "0", False, "2025公积金"),
    ("insurance_deduction", "REAL", True, "0", False, "保险扣款"),
    ("pension_insurance", "REAL", True, "0", False, "代扣代存养老保险"),
    ("month", "TEXT", False, None, False, "月份"),
    ("year", "INTEGER", False, None, False, "年份"),
    ("created_at", "TEXT", False, None, False, "创建时间"),
    ("updated_at", "TEXT", False, None, False, "更新时间")
]
```

### 1.2 模板映射关系

| 表类型 | 模板键名 | 字段数量 | 特色字段示例 |
|--------|---------|---------|-------------|
| 离休人员 | `retired_employees` | 21 | 基本离休费、护理费、离休补贴 |
| 退休人员 | `pension_employees` | 32 | 基本退休费、历年调整、增资预付 |
| 在职人员 | `active_employees` | 28 | 岗位工资、薪级工资、基础性绩效 |
| A岗职工 | `a_grade_employees` | 26 | 校龄工资、生活补贴、岗位工资 |

## 🔧 第二阶段：修改数据导入逻辑

### 2.1 表类型识别增强

```python
def _detect_table_type_enhanced(self, sheet_name: str, headers: List[str]) -> str:
    """增强的表类型识别"""
    header_set = set(headers)
    
    # 离休人员特征字段
    retired_features = {"基本离休费", "离休补贴", "护理费"}
    if retired_features.intersection(header_set):
        return "retired_employees"
    
    # 退休人员特征字段  
    pension_features = {"基本退休费", "2016待遇调整", "增资预付"}
    if pension_features.intersection(header_set):
        return "pension_employees"
    
    # A岗职工特征字段
    a_grade_features = {"校龄工资", "2025年校龄工资"}
    if a_grade_features.intersection(header_set):
        return "a_grade_employees"
    
    # 在职人员特征字段
    active_features = {"岗位工资", "薪级工资", "基础性绩效"}
    if active_features.intersection(header_set):
        return "active_employees"
    
    return "salary_data"  # 默认通用模板
```

### 2.2 动态表创建逻辑修改

```python
def create_specialized_table(self, table_type: str, year: int, month: int) -> bool:
    """创建专用表结构"""
    template_mapping = {
        "retired_employees": RETIRED_EMPLOYEE_FIELDS,
        "pension_employees": PENSION_EMPLOYEE_FIELDS,
        "active_employees": ACTIVE_EMPLOYEE_FIELDS,
        "a_grade_employees": A_GRADE_EMPLOYEE_FIELDS
    }
    
    if table_type not in template_mapping:
        return self.create_salary_table(month, year)  # 回退到通用模板
    
    # 使用专用模板创建表
    field_definitions = template_mapping[table_type]
    # ... 创建表的具体实现
```

## 📈 第三阶段：数据迁移策略

### 3.1 迁移方案选择

**方案A：全量重新导入**
- 优点：数据完整，结构清晰
- 缺点：需要重新导入所有Excel文件

**方案B：增量迁移**
- 优点：保留现有数据，风险较小
- 缺点：新旧结构并存，复杂度较高

**推荐：方案A（全量重新导入）**

### 3.2 迁移步骤

1. **备份现有数据**
2. **创建专用表结构**
3. **重新导入Excel文件**
4. **验证数据完整性**
5. **更新字段映射配置**
6. **测试系统功能**

## 🎯 实施时间线

| 阶段 | 任务 | 预计时间 | 风险等级 |
|------|------|---------|---------|
| 第一阶段 | 专用模板设计与实现 | 2-3天 | 低 |
| 第二阶段 | 导入逻辑修改 | 2-3天 | 中 |
| 第三阶段 | 数据迁移与测试 | 1-2天 | 中 |
| 验收阶段 | 功能验证与优化 | 1天 | 低 |

## 🔄 回滚机制

1. **保留现有通用结构**作为备份
2. **分阶段实施**，每个阶段都可以回滚
3. **数据备份**，确保数据安全

## 📊 成功指标

1. **数据完整性**：所有Excel字段都能正确存储
2. **显示效果**：主界面正确显示所有专用字段
3. **性能表现**：导入和查询性能不下降
4. **用户体验**：界面友好，操作流畅
