"""
数据验证器

功能说明:
- 数据类型验证和转换
- 字段完整性检查
- 业务规则验证  
- 错误数据识别和分类
- 数据修复建议生成
- 数据质量报告
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger

class ValidationLevel(Enum):
    """验证级别"""
    WARNING = "warning"    # 警告级别
    ERROR = "error"       # 错误级别
    CRITICAL = "critical" # 严重错误级别

class FieldType(Enum):
    """字段类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    DATE = "date"
    BOOLEAN = "boolean"
    ID_CARD = "id_card"
    EMPLOYEE_ID = "employee_id"
    PHONE = "phone"
    EMAIL = "email"

@dataclass
class ValidationRule:
    """验证规则"""
    field_name: str
    field_type: FieldType
    required: bool = True
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    pattern: Optional[str] = None
    allowed_values: Optional[List[Any]] = None
    custom_validator: Optional[Callable] = None
    error_message: Optional[str] = None

@dataclass
class ValidationResult:
    """验证结果"""
    row_index: int
    field_name: str
    field_value: Any
    validation_level: ValidationLevel
    error_code: str
    error_message: str
    suggested_fix: Optional[str] = None

class DataValidator:
    """数据验证器
    
    提供全面的数据验证功能，包括类型检查、格式验证、业务规则验证等
    """
    
    def __init__(self, validation_rules: Optional[List[ValidationRule]] = None):
        """初始化数据验证器
        
        Args:
            validation_rules: 验证规则列表
        """
        self.logger = setup_logger(__name__)
        self.validation_rules = validation_rules or []
        self.validation_results = []
        
        # 预定义的验证模式
        self.patterns = {
            'id_card_15': r'^\d{15}$',
            'id_card_18': r'^\d{17}[\dXx]$',
            'employee_id': r'^[A-Za-z0-9]{4,20}$',
            'phone': r'^1[3-9]\d{9}$',
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'date_formats': [
                '%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d',
                '%d-%m-%Y', '%d/%m/%Y', '%d.%m.%Y',
                '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S'
            ]
        }
        
    def add_validation_rule(self, rule: ValidationRule) -> None:
        """添加验证规则
        
        Args:
            rule: 验证规则
        """
        self.validation_rules.append(rule)
        self.logger.debug(f"添加验证规则: {rule.field_name} ({rule.field_type.value})")
        
    def validate_dataframe(self, df: pd.DataFrame, 
                          rules: Optional[List[ValidationRule]] = None) -> Dict[str, Any]:
        """验证整个DataFrame
        
        Args:
            df: 待验证的DataFrame
            rules: 验证规则列表，为None时使用默认规则
            
        Returns:
            验证结果字典，包含错误统计、详细结果、修复建议等
        """
        if df.empty:
            return {
                'total_rows': 0,
                'total_errors': 0,
                'validation_passed': True,
                'results': [],
                'summary': {}
            }
            
        # 使用指定规则或默认规则
        validation_rules = rules or self.validation_rules
        if not validation_rules:
            validation_rules = self._generate_default_rules(df)
            
        self.validation_results = []
        
        self.logger.info(f"开始验证数据: {len(df)}行 x {len(df.columns)}列")
        
        # 逐行验证
        for index, row in df.iterrows():
            self._validate_row(index, row, validation_rules)
            
        # 生成验证报告
        validation_report = self._generate_validation_report(df)
        
        self.logger.info(f"数据验证完成: {validation_report['total_errors']}个错误")
        return validation_report
        
    def _validate_row(self, row_index: int, row: pd.Series, 
                     rules: List[ValidationRule]) -> None:
        """验证单行数据
        
        Args:
            row_index: 行索引
            row: 行数据
            rules: 验证规则列表
        """
        for rule in rules:
            if rule.field_name not in row.index:
                # 字段不存在
                if rule.required:
                    self.validation_results.append(ValidationResult(
                        row_index=row_index,
                        field_name=rule.field_name,
                        field_value=None,
                        validation_level=ValidationLevel.ERROR,
                        error_code="MISSING_FIELD",
                        error_message=f"必填字段 '{rule.field_name}' 不存在",
                        suggested_fix=f"添加字段 '{rule.field_name}'"
                    ))
                continue
                
            field_value = row[rule.field_name]
            self._validate_field(row_index, rule.field_name, field_value, rule)
            
    def _validate_field(self, row_index: int, field_name: str, 
                       field_value: Any, rule: ValidationRule) -> None:
        """验证单个字段（重构后的主函数）
        
        Args:
            row_index: 行索引
            field_name: 字段名
            field_value: 字段值
            rule: 验证规则
        """
        # 检查必填字段
        if self._is_required_field_empty(field_value, rule):
            self._add_validation_error(row_index, field_name, field_value, 
                                     ValidationLevel.ERROR, "MISSING_VALUE",
                                     f"必填字段 '{field_name}' 值为空", "填入有效值")
            return
            
        # 如果值为空且非必填，跳过其他验证
        if self._is_optional_field_empty(field_value, rule):
            return
            
        # 转换为字符串进行验证
        str_value = str(field_value).strip()
        
        # 执行各种验证
        self._validate_field_type_check(row_index, field_name, field_value, str_value, rule)
        self._validate_field_length(row_index, field_name, field_value, str_value, rule)
        self._validate_field_numeric_range(row_index, field_name, field_value, str_value, rule)
        self._validate_field_pattern(row_index, field_name, field_value, str_value, rule)
        self._validate_field_allowed_values(row_index, field_name, field_value, str_value, rule)
        self._validate_field_custom(row_index, field_name, field_value, rule)
    
    def _is_required_field_empty(self, field_value: Any, rule: ValidationRule) -> bool:
        """检查必填字段是否为空"""
        if not rule.required:
            return False
        # 检查多种空值情况：None, NaN, 空字符串
        return pd.isna(field_value) or (isinstance(field_value, str) and field_value.strip() == '')
    
    def _is_optional_field_empty(self, field_value: Any, rule: ValidationRule) -> bool:
        """检查可选字段是否为空"""
        if rule.required:
            return False
        # 检查多种空值情况：None, NaN, 空字符串
        return pd.isna(field_value) or (isinstance(field_value, str) and field_value.strip() == '')
    
    def _add_validation_error(self, row_index: int, field_name: str, field_value: Any,
                            level: ValidationLevel, error_code: str, 
                            error_message: str, suggested_fix: str) -> None:
        """添加验证错误记录"""
        self.validation_results.append(ValidationResult(
            row_index=row_index,
            field_name=field_name,
            field_value=field_value,
            validation_level=level,
            error_code=error_code,
            error_message=error_message,
            suggested_fix=suggested_fix
        ))
    
    def _validate_field_type_check(self, row_index: int, field_name: str, 
                                 field_value: Any, str_value: str, rule: ValidationRule) -> None:
        """验证字段类型"""
        type_valid, type_error = self._validate_field_type(str_value, rule.field_type)
        if not type_valid:
            level = ValidationLevel.ERROR if rule.field_type in [
                FieldType.ID_CARD, FieldType.EMPLOYEE_ID
            ] else ValidationLevel.WARNING
            
            self._add_validation_error(row_index, field_name, field_value, level,
                                     "TYPE_MISMATCH", type_error,
                                     self._get_type_fix_suggestion(rule.field_type))
    
    def _validate_field_length(self, row_index: int, field_name: str, 
                             field_value: Any, str_value: str, rule: ValidationRule) -> None:
        """验证字段长度"""
        if rule.min_length is not None and len(str_value) < rule.min_length:
            self._add_validation_error(row_index, field_name, field_value,
                                     ValidationLevel.WARNING, "LENGTH_TOO_SHORT",
                                     f"字段长度({len(str_value)})小于最小长度({rule.min_length})",
                                     f"确保字段长度至少为{rule.min_length}个字符")
            
        if rule.max_length is not None and len(str_value) > rule.max_length:
            self._add_validation_error(row_index, field_name, field_value,
                                     ValidationLevel.WARNING, "LENGTH_TOO_LONG",
                                     f"字段长度({len(str_value)})超过最大长度({rule.max_length})",
                                     f"截断字段内容到{rule.max_length}个字符")
    
    def _validate_field_numeric_range(self, row_index: int, field_name: str, 
                                    field_value: Any, str_value: str, rule: ValidationRule) -> None:
        """验证数值范围"""
        if rule.field_type not in [FieldType.INTEGER, FieldType.FLOAT]:
            return
            
        try:
            numeric_value = float(str_value)
            
            if rule.min_value is not None and numeric_value < rule.min_value:
                self._add_validation_error(row_index, field_name, field_value,
                                         ValidationLevel.WARNING, "VALUE_TOO_SMALL",
                                         f"数值({numeric_value})小于最小值({rule.min_value})",
                                         f"调整数值至{rule.min_value}以上")
                
            if rule.max_value is not None and numeric_value > rule.max_value:
                self._add_validation_error(row_index, field_name, field_value,
                                         ValidationLevel.WARNING, "VALUE_TOO_LARGE",
                                         f"数值({numeric_value})超过最大值({rule.max_value})",
                                         f"调整数值至{rule.max_value}以下")
        except ValueError:
            pass  # 类型验证已经处理
    
    def _validate_field_pattern(self, row_index: int, field_name: str, 
                              field_value: Any, str_value: str, rule: ValidationRule) -> None:
        """验证字段模式"""
        if rule.pattern and not re.match(rule.pattern, str_value):
            self._add_validation_error(row_index, field_name, field_value,
                                     ValidationLevel.WARNING, "PATTERN_MISMATCH",
                                     f"字段格式不匹配规定模式",
                                     "请检查字段格式是否正确")
    
    def _validate_field_allowed_values(self, row_index: int, field_name: str, 
                                     field_value: Any, str_value: str, rule: ValidationRule) -> None:
        """验证字段允许值"""
        if rule.allowed_values and str_value not in rule.allowed_values:
            self._add_validation_error(row_index, field_name, field_value,
                                     ValidationLevel.WARNING, "INVALID_VALUE",
                                     f"字段值不在允许范围内: {rule.allowed_values}",
                                     f"选择以下值之一: {', '.join(map(str, rule.allowed_values))}")
    
    def _validate_field_custom(self, row_index: int, field_name: str, 
                             field_value: Any, rule: ValidationRule) -> None:
        """执行自定义验证"""
        if not rule.custom_validator:
            return
            
        try:
            is_valid, error_msg = rule.custom_validator(field_value)
            if not is_valid:
                self._add_validation_error(row_index, field_name, field_value,
                                         ValidationLevel.WARNING, "CUSTOM_VALIDATION_FAILED",
                                         error_msg or "自定义验证失败",
                                         "请检查字段值是否符合业务规则")
        except Exception as e:
            self.logger.error(f"自定义验证函数执行失败: {e}")
                
    def _validate_field_type(self, value: str, field_type: FieldType) -> Tuple[bool, str]:
        """验证字段类型（重构后的主函数）
        
        Args:
            value: 字段值
            field_type: 期望的字段类型
            
        Returns:
            (是否有效, 错误消息)
        """
        if not value:
            return True, ""  # 空值由必填验证处理
            
        # 使用策略模式：字段类型到验证方法的映射
        validators = {
            FieldType.STRING: self._validate_string_type,
            FieldType.INTEGER: self._validate_integer_type,
            FieldType.FLOAT: self._validate_float_type,
            FieldType.DATE: self._validate_date_type,
            FieldType.BOOLEAN: self._validate_boolean_type,
            FieldType.ID_CARD: self._validate_id_card_type,
            FieldType.EMPLOYEE_ID: self._validate_employee_id_type,
            FieldType.PHONE: self._validate_phone_type,
            FieldType.EMAIL: self._validate_email_type
        }
        
        validator = validators.get(field_type)
        if validator:
            return validator(value)
        else:
            return True, ""  # 未知类型默认通过
    
    def _validate_string_type(self, value: str) -> Tuple[bool, str]:
        """验证字符串类型"""
        return True, ""
    
    def _validate_integer_type(self, value: str) -> Tuple[bool, str]:
        """验证整数类型"""
        try:
            int(value)
            return True, ""
        except ValueError:
            return False, f"整数格式不正确: {value}"
    
    def _validate_float_type(self, value: str) -> Tuple[bool, str]:
        """验证浮点数类型"""
        try:
            float(value)
            return True, ""
        except ValueError:
            return False, f"数字格式不正确: {value}"
    
    def _validate_date_type(self, value: str) -> Tuple[bool, str]:
        """验证日期类型"""
        for date_format in self.patterns['date_formats']:
            try:
                datetime.strptime(value, date_format)
                return True, ""
            except ValueError:
                continue
        return False, f"日期格式不正确: {value}"
    
    def _validate_boolean_type(self, value: str) -> Tuple[bool, str]:
        """验证布尔类型"""
        if value.lower() in ['true', 'false', '1', '0', 'yes', 'no', '是', '否']:
            return True, ""
        return False, f"布尔值格式不正确: {value}"
    
    def _validate_id_card_type(self, value: str) -> Tuple[bool, str]:
        """验证身份证号类型"""
        if re.match(self.patterns['id_card_15'], value) or \
           re.match(self.patterns['id_card_18'], value):
            return True, ""
        return False, f"身份证号格式不正确: {value}"
    
    def _validate_employee_id_type(self, value: str) -> Tuple[bool, str]:
        """验证工号类型"""
        if re.match(self.patterns['employee_id'], value):
            return True, ""
        return False, f"工号格式不正确: {value}"
    
    def _validate_phone_type(self, value: str) -> Tuple[bool, str]:
        """验证手机号类型"""
        if re.match(self.patterns['phone'], value):
            return True, ""
        return False, f"手机号格式不正确: {value}"
    
    def _validate_email_type(self, value: str) -> Tuple[bool, str]:
        """验证邮箱类型"""
        if re.match(self.patterns['email'], value):
            return True, ""
        return False, f"邮箱格式不正确: {value}"
        
    def _get_type_fix_suggestion(self, field_type: FieldType) -> str:
        """获取类型修复建议
        
        Args:
            field_type: 字段类型
            
        Returns:
            修复建议
        """
        suggestions = {
            FieldType.INTEGER: "请输入整数",
            FieldType.FLOAT: "请输入数字",
            FieldType.DATE: "请使用正确的日期格式，如: 2023-12-31",
            FieldType.BOOLEAN: "请输入: True/False 或 1/0",
            FieldType.ID_CARD: "请输入15位或18位身份证号",
            FieldType.EMPLOYEE_ID: "请输入4-20位字母数字组合",
            FieldType.PHONE: "请输入11位手机号",
            FieldType.EMAIL: "请输入正确的邮箱地址"
        }
        return suggestions.get(field_type, "请检查字段格式")
        
    def _generate_default_rules(self, df: pd.DataFrame) -> List[ValidationRule]:
        """根据DataFrame生成默认验证规则（重构后的主函数）
        
        Args:
            df: DataFrame数据
            
        Returns:
            默认验证规则列表
        """
        rules = []
        
        for column in df.columns:
            rule = self._create_rule_for_column(column)
            rules.append(rule)
                
        self.logger.info(f"生成默认验证规则: {len(rules)}个字段")
        return rules
    
    def _create_rule_for_column(self, column: str) -> ValidationRule:
        """为指定列创建验证规则
        
        Args:
            column: 列名
            
        Returns:
            验证规则
        """
        column_str = str(column).lower()
        
        # 字段类型推断配置
        field_configs = self._get_field_type_configs()
        
        # 按优先级顺序检查字段类型
        for config in field_configs:
            if self._column_matches_pattern(column_str, config['patterns']):
                return ValidationRule(
                    field_name=column,
                    field_type=config['field_type'],
                    required=config['required'],
                    **config.get('extra_params', {})
                )
        
        # 默认为字符串类型
        return ValidationRule(
            field_name=column,
            field_type=FieldType.STRING,
            required=False
        )
    
    def _get_field_type_configs(self) -> List[Dict[str, Any]]:
        """获取字段类型推断配置
        
        Returns:
            字段类型配置列表（按优先级排序）
        """
        return [
            {
                'patterns': ['身份证', 'id_card'],
                'field_type': FieldType.ID_CARD,
                'required': True,
                'extra_params': {}
            },
            {
                'patterns': ['工号', 'employee'],
                'field_type': FieldType.EMPLOYEE_ID,
                'required': True,
                'extra_params': {}
            },
            {
                'patterns': ['手机', 'phone'],
                'field_type': FieldType.PHONE,
                'required': False,
                'extra_params': {}
            },
            {
                'patterns': ['邮箱', 'email'],
                'field_type': FieldType.EMAIL,
                'required': False,
                'extra_params': {}
            },
            {
                'patterns': ['日期', 'date', '时间'],
                'field_type': FieldType.DATE,
                'required': False,
                'extra_params': {}
            },
            {
                'patterns': ['金额', '工资', '薪'],
                'field_type': FieldType.FLOAT,
                'required': False,
                'extra_params': {'min_value': 0}
            }
        ]
    
    def _column_matches_pattern(self, column_str: str, patterns: List[str]) -> bool:
        """检查列名是否匹配任何模式
        
        Args:
            column_str: 列名（小写）
            patterns: 匹配模式列表
            
        Returns:
            是否匹配
        """
        return any(pattern in column_str for pattern in patterns)
        
    def _generate_validation_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成验证报告
        
        Args:
            df: 原始DataFrame
            
        Returns:
            验证报告字典
        """
        total_rows = len(df)
        total_errors = len(self.validation_results)
        
        # 按级别统计错误
        error_counts = {
            ValidationLevel.WARNING: 0,
            ValidationLevel.ERROR: 0,
            ValidationLevel.CRITICAL: 0
        }
        
        for result in self.validation_results:
            error_counts[result.validation_level] += 1
            
        # 按字段统计错误
        field_errors = {}
        for result in self.validation_results:
            if result.field_name not in field_errors:
                field_errors[result.field_name] = 0
            field_errors[result.field_name] += 1
            
        # 按错误类型统计
        error_type_counts = {}
        for result in self.validation_results:
            if result.error_code not in error_type_counts:
                error_type_counts[result.error_code] = 0
            error_type_counts[result.error_code] += 1
            
        validation_passed = error_counts[ValidationLevel.ERROR] == 0 and \
                          error_counts[ValidationLevel.CRITICAL] == 0
                          
        return {
            'total_rows': total_rows,
            'total_errors': total_errors,
            'validation_passed': validation_passed,
            'error_counts': {level.value: count for level, count in error_counts.items()},
            'field_errors': field_errors,
            'error_type_counts': error_type_counts,
            'results': [
                {
                    'row_index': result.row_index,
                    'field_name': result.field_name,
                    'field_value': result.field_value,
                    'validation_level': result.validation_level.value,
                    'error_code': result.error_code,
                    'error_message': result.error_message,
                    'suggested_fix': result.suggested_fix
                }
                for result in self.validation_results
            ],
            'summary': {
                'error_rate': total_errors / total_rows if total_rows > 0 else 0,
                'most_problematic_fields': sorted(field_errors.items(), 
                                                key=lambda x: x[1], reverse=True)[:5],
                'most_common_errors': sorted(error_type_counts.items(),
                                           key=lambda x: x[1], reverse=True)[:5]
            }
        }
        
    def get_validation_results(self) -> List[ValidationResult]:
        """获取验证结果
        
        Returns:
            验证结果列表
        """
        return self.validation_results.copy()
        
    def clear_results(self) -> None:
        """清空验证结果"""
        self.validation_results = []
        
    def export_error_report(self, output_path: str, include_suggestions: bool = True) -> bool:
        """导出错误报告
        
        Args:
            output_path: 输出文件路径
            include_suggestions: 是否包含修复建议
            
        Returns:
            是否导出成功
        """
        try:
            if not self.validation_results:
                self.logger.warning("没有验证结果可导出")
                return False
                
            # 构建报告DataFrame
            report_data = []
            for result in self.validation_results:
                row_data = {
                    '行号': result.row_index + 1,  # 转换为1开始的行号
                    '字段名': result.field_name,
                    '字段值': result.field_value,
                    '错误级别': result.validation_level.value,
                    '错误代码': result.error_code,
                    '错误信息': result.error_message
                }
                
                if include_suggestions and result.suggested_fix:
                    row_data['修复建议'] = result.suggested_fix
                    
                report_data.append(row_data)
                
            report_df = pd.DataFrame(report_data)
            
            # 导出Excel文件
            report_df.to_excel(output_path, index=False, engine='openpyxl')
            
            from src.utils.log_config import log_file_operation
            log_file_operation(output_path, 'write')
            
            self.logger.info(f"错误报告导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"错误报告导出失败: {e}")
            return False 