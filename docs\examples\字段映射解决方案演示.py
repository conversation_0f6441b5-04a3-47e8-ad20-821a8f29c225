#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页字段映射丢失问题解决方案演示

本演示脚本展示了修复后的字段映射功能：
1. 智能字段映射生成
2. 分页时字段映射的一致性
3. 用户编辑字段映射的保存和应用

创建时间: 2025-01-27
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator


def demo_intelligent_field_mapping():
    """演示智能字段映射生成"""
    print("🎯 演示一：智能字段映射生成")
    print("=" * 50)
    
    # 模拟Excel导入的表头
    excel_headers = ["工号", "姓名", "部门名称", "基本工资", "绩效工资", "应发工资", "五险一金"]
    
    # 模拟数据库字段名
    db_fields = ["employee_id", "employee_name", "department", "basic_salary", "performance_bonus", "total_salary", "social_insurance"]
    
    print(f"📋 Excel原始表头: {excel_headers}")
    print(f"🗄️  数据库字段名: {db_fields}")
    
    # 生成智能字段映射
    generator = AutoFieldMappingGenerator()
    mapping = generator.create_initial_field_mapping(excel_headers, db_fields)
    
    print(f"\n🧠 智能生成的字段映射:")
    for db_field, display_name in mapping.items():
        print(f"   {db_field} → {display_name}")
    
    print("\n✅ 智能字段映射生成完成！")
    return mapping


def demo_pagination_consistency():
    """演示分页时字段映射的一致性"""
    print("\n🎯 演示二：分页字段映射一致性")
    print("=" * 50)
    
    # 创建临时配置管理器
    import tempfile
    with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
        temp_config_path = f.name
    
    try:
        config_sync = ConfigSyncManager(config_path=temp_config_path)
        
        # 设置字段映射配置
        field_mapping = {
            "employee_id": "工号",
            "employee_name": "员工姓名",
            "department": "所属部门",
            "basic_salary": "基本工资",
            "total_salary": "应发合计"
        }
        
        mapping_data = {
            "field_mappings": field_mapping,
            "original_excel_headers": {
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称",
                "basic_salary": "基本工资",
                "total_salary": "应发工资"
            },
            "metadata": {
                "source": "demo",
                "auto_generated": True,
                "user_modified": True,
                "created_at": "2025-01-27T10:00:00"
            }
        }
        
        table_name = "salary_data_2025_01_demo"
        config_sync.save_complete_mapping(table_name, mapping_data)
        
        print(f"💾 已保存字段映射配置到表: {table_name}")
        
        # 模拟第一页数据（数据库返回的原始字段名）
        page1_data = pd.DataFrame({
            "employee_id": ["001", "002", "003"],
            "employee_name": ["张三", "李四", "王五"],
            "department": ["技术部", "财务部", "人事部"],
            "basic_salary": [8000, 7000, 6000],
            "total_salary": [10000, 8500, 7500]
        })
        
        # 模拟第二页数据
        page2_data = pd.DataFrame({
            "employee_id": ["004", "005", "006"],
            "employee_name": ["赵六", "钱七", "孙八"],
            "department": ["市场部", "运营部", "技术部"],
            "basic_salary": [7500, 8200, 9000],
            "total_salary": [9000, 9800, 11000]
        })
        
        print(f"\n📄 第一页原始数据列名: {list(page1_data.columns)}")
        print(f"📄 第二页原始数据列名: {list(page2_data.columns)}")
        
        # 应用修复后的字段映射逻辑
        def apply_field_mapping_to_dataframe(df, table_name):
            """修复后的字段映射应用逻辑"""
            field_mapping = config_sync.load_mapping(table_name)
            
            if not field_mapping:
                return df
            
            # 创建列名重命名映射：数据库字段名 → 用户显示名
            column_rename_map = {}
            for db_field, display_name in field_mapping.items():
                if db_field in df.columns and display_name:
                    column_rename_map[db_field] = display_name
            
            # 应用重命名
            if column_rename_map:
                return df.rename(columns=column_rename_map)
            
            return df
        
        # 应用字段映射到两页数据
        page1_mapped = apply_field_mapping_to_dataframe(page1_data, table_name)
        page2_mapped = apply_field_mapping_to_dataframe(page2_data, table_name)
        
        print(f"\n🎨 第一页映射后列名: {list(page1_mapped.columns)}")
        print(f"🎨 第二页映射后列名: {list(page2_mapped.columns)}")
        
        # 验证一致性
        if list(page1_mapped.columns) == list(page2_mapped.columns):
            print("\n✅ 分页字段映射一致性验证通过！")
            print("   两页数据的表头完全一致，用户看到的是统一的中文表头")
        else:
            print("\n❌ 分页字段映射一致性验证失败！")
        
        # 显示映射后的数据示例
        print(f"\n📊 第一页映射后数据预览:")
        print(page1_mapped.head(2).to_string(index=False))
        
        print(f"\n📊 第二页映射后数据预览:")
        print(page2_mapped.head(2).to_string(index=False))
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass


def demo_user_edit_field_mapping():
    """演示用户编辑字段映射"""
    print("\n🎯 演示三：用户编辑字段映射")
    print("=" * 50)
    
    # 创建临时配置管理器
    import tempfile
    with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
        temp_config_path = f.name
    
    try:
        config_sync = ConfigSyncManager(config_path=temp_config_path)
        
        # 初始字段映射
        initial_mapping = {
            "employee_id": "工号",
            "employee_name": "姓名",
            "department": "部门"
        }
        
        mapping_data = {
            "field_mappings": initial_mapping,
            "metadata": {"source": "demo"}
        }
        
        table_name = "demo_table"
        config_sync.save_complete_mapping(table_name, mapping_data)
        
        print(f"📝 初始字段映射:")
        for db_field, display_name in initial_mapping.items():
            print(f"   {db_field} → {display_name}")
        
        # 模拟用户双击编辑表头
        print(f"\n👆 用户双击编辑 'employee_name' 字段...")
        print(f"   当前显示名: '姓名'")
        print(f"   用户输入新显示名: '员工姓名'")
        
        # 更新字段映射
        success = config_sync.update_single_field_mapping(
            table_name, "employee_name", "员工姓名"
        )
        
        if success:
            print(f"✅ 字段映射更新成功！")
            
            # 获取更新后的映射
            updated_mapping = config_sync.load_mapping(table_name)
            print(f"\n📝 更新后的字段映射:")
            for db_field, display_name in updated_mapping.items():
                print(f"   {db_field} → {display_name}")
            
            # 验证更新
            if updated_mapping["employee_name"] == "员工姓名":
                print(f"\n✅ 用户编辑验证通过！")
                print(f"   字段 'employee_name' 的显示名已更新为 '员工姓名'")
            else:
                print(f"\n❌ 用户编辑验证失败！")
        else:
            print(f"❌ 字段映射更新失败！")
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass


def main():
    """主演示函数"""
    print("🚀 分页字段映射丢失问题解决方案演示")
    print("=" * 60)
    print("本演示将展示修复后的字段映射功能如何解决分页时中文表头丢失的问题")
    print()
    
    try:
        # 演示一：智能字段映射生成
        demo_intelligent_field_mapping()
        
        # 演示二：分页一致性
        demo_pagination_consistency()
        
        # 演示三：用户编辑
        demo_user_edit_field_mapping()
        
        print("\n🎉 所有演示完成！")
        print("=" * 60)
        print("总结：")
        print("✅ 智能字段映射生成 - 自动建立合理的字段映射关系")
        print("✅ 分页字段映射一致性 - 确保分页前后表头显示完全一致")
        print("✅ 用户编辑字段映射 - 支持用户自定义表头显示名称")
        print("\n🔧 问题已解决：分页时中文表头不再丢失！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
