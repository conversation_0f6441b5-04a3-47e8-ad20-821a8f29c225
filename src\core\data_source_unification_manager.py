#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源统一管理器

解决混合路径数据一致性问题的核心组件。
负责管理数据导入会话，确保导航路径与分页路径使用一致的数据源、字段映射和排序状态。

主要功能:
1. 数据导入会话管理
2. 路径间数据一致性保证
3. 状态传递机制
4. 数据源上下文管理

创建时间: 2025-07-07
作者: 月度工资异动处理系统开发团队
"""

import time
import uuid
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

from src.utils.log_config import setup_logger


class DataPath(Enum):
    """数据展示路径枚举"""
    NAVIGATION = "navigation"  # 导航路径（数据导航切换）
    PAGINATION = "pagination"  # 分页路径（分页组件跳转）


class SessionStatus(Enum):
    """会话状态枚举"""
    ACTIVE = "active"      # 活跃状态
    EXPIRED = "expired"    # 已过期
    ARCHIVED = "archived"  # 已归档


@dataclass
class ImportSessionData:
    """数据导入会话数据"""
    session_id: str
    table_name: str
    table_type: str
    data_source: Dict[str, Any]
    field_mapping: Dict[str, str]
    total_records: int
    created_at: float
    status: SessionStatus = SessionStatus.ACTIVE
    last_accessed: float = field(default_factory=time.time)
    
    # 路径状态信息
    navigation_context: Dict[str, Any] = field(default_factory=dict)
    pagination_context: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self, max_age_hours: float = 24.0) -> bool:
        """检查会话是否过期"""
        return (time.time() - self.created_at) > (max_age_hours * 3600)
    
    def update_access_time(self):
        """更新最后访问时间"""
        self.last_accessed = time.time()


@dataclass
class PathContext:
    """路径上下文数据"""
    path_type: DataPath
    session_id: str
    table_name: str
    current_page: int = 1
    page_size: int = 50
    sort_state: Dict[str, Any] = field(default_factory=dict)
    field_mapping: Dict[str, str] = field(default_factory=dict)
    filter_state: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'path_type': self.path_type.value,
            'session_id': self.session_id,
            'table_name': self.table_name,
            'current_page': self.current_page,
            'page_size': self.page_size,
            'sort_state': self.sort_state,
            'field_mapping': self.field_mapping,
            'filter_state': self.filter_state
        }


class DataSourceUnificationManager:
    """
    数据源统一管理器
    
    解决混合路径数据一致性问题的核心组件。
    确保数据导入后，导航路径与分页路径使用一致的数据源和状态。
    """
    
    def __init__(self, max_sessions: int = 100, session_max_age_hours: float = 24.0):
        """
        初始化数据源统一管理器
        
        Args:
            max_sessions: 最大会话数量
            session_max_age_hours: 会话最大存活时间（小时）
        """
        self.logger = setup_logger(__name__)
        
        # 会话管理
        self.import_sessions: Dict[str, ImportSessionData] = {}
        self.path_contexts: Dict[str, PathContext] = {}
        
        # 配置参数
        self.max_sessions = max_sessions
        self.session_max_age_hours = session_max_age_hours
        
        # 当前活跃会话跟踪
        self.current_session_id: Optional[str] = None
        self.active_table_sessions: Dict[str, str] = {}  # table_name -> session_id
        
        # 统计信息
        self.total_sessions_created = 0
        self.total_path_transitions = 0
        self.consistency_violations = 0
        
        self.logger.info("数据源统一管理器初始化完成")
    
    def register_data_import_session(
        self, 
        table_name: str, 
        table_type: str,
        import_result: Dict[str, Any]
    ) -> str:
        """
        注册数据导入会话，确保后续路径使用一致数据源
        
        Args:
            table_name: 表名
            table_type: 表类型（如：A岗职工表、离休人员表等）
            import_result: 导入结果数据
            
        Returns:
            str: 会话ID
        """
        try:
            # 生成唯一会话ID
            session_id = f"{table_type}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # 清理过期会话
            self._cleanup_expired_sessions()
            
            # 创建会话数据
            session_data = ImportSessionData(
                session_id=session_id,
                table_name=table_name,
                table_type=table_type,
                data_source=import_result.get('data_source', {}),
                field_mapping=import_result.get('field_mapping', {}),
                total_records=import_result.get('total_records', 0),
                created_at=time.time()
            )
            
            # 存储会话
            self.import_sessions[session_id] = session_data
            
            # 更新活跃会话跟踪
            self.current_session_id = session_id
            self.active_table_sessions[table_name] = session_id
            
            # 更新统计
            self.total_sessions_created += 1
            
            self.logger.info(f"注册数据导入会话: {session_id}, 表: {table_name}, 记录数: {session_data.total_records}")
            
            return session_id
            
        except Exception as e:
            self.logger.error(f"注册数据导入会话失败: {e}")
            raise
    
    def ensure_path_consistency(
        self, 
        source_path: DataPath, 
        target_path: DataPath, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        确保路径切换时的数据一致性
        
        Args:
            source_path: 源路径类型
            target_path: 目标路径类型
            context: 当前上下文信息
            
        Returns:
            Dict: 传递给目标路径的一致性上下文
        """
        try:
            self.total_path_transitions += 1
            
            if source_path == DataPath.NAVIGATION and target_path == DataPath.PAGINATION:
                return self._handle_nav_to_pagination_transition(context)
            elif source_path == DataPath.PAGINATION and target_path == DataPath.NAVIGATION:
                return self._handle_pagination_to_nav_transition(context)
            else:
                self.logger.warning(f"未处理的路径转换: {source_path} -> {target_path}")
                return context
                
        except Exception as e:
            self.logger.error(f"确保路径一致性失败: {e}")
            self.consistency_violations += 1
            return context
    
    def _handle_nav_to_pagination_transition(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理从导航路径到分页路径的转换
        
        Args:
            context: 导航路径上下文
            
        Returns:
            Dict: 分页路径需要的上下文
        """
        try:
            table_name = context.get('table_name')
            if not table_name:
                self.logger.warning("导航到分页转换缺少table_name")
                return context
            
            # 获取当前表的活跃会话
            session_id = self.active_table_sessions.get(table_name)
            if not session_id or session_id not in self.import_sessions:
                self.logger.warning(f"表 {table_name} 没有找到活跃会话")
                return context
            
            session_data = self.import_sessions[session_id]
            session_data.update_access_time()
            
            # 构建一致性上下文
            consistent_context = {
                'session_id': session_id,
                'table_name': table_name,
                'table_type': session_data.table_type,
                'field_mapping': session_data.field_mapping.copy(),
                'data_source': session_data.data_source.copy(),
                'total_records': session_data.total_records,
                
                # 从导航路径传递的状态
                'sort_state': context.get('sort_state', {}),
                'current_page': context.get('current_page', 1),
                'page_size': context.get('page_size', 50),
                'filter_state': context.get('filter_state', {}),
                
                # 路径转换信息
                'source_path': DataPath.NAVIGATION.value,
                'target_path': DataPath.PAGINATION.value,
                'transition_time': time.time()
            }
            
            # 保存导航路径上下文到会话
            session_data.navigation_context = context.copy()
            
            self.logger.info(f"导航->分页转换完成: 表={table_name}, 会话={session_id}")
            
            return consistent_context
            
        except Exception as e:
            self.logger.error(f"处理导航到分页转换失败: {e}")
            return context
    
    def _handle_pagination_to_nav_transition(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理从分页路径到导航路径的转换
        
        Args:
            context: 分页路径上下文
            
        Returns:
            Dict: 导航路径需要的上下文
        """
        try:
            session_id = context.get('session_id')
            if not session_id or session_id not in self.import_sessions:
                self.logger.warning("分页到导航转换缺少有效session_id")
                return context
            
            session_data = self.import_sessions[session_id]
            session_data.update_access_time()
            
            # 保存分页路径上下文到会话
            session_data.pagination_context = context.copy()
            
            # 构建导航路径上下文
            nav_context = {
                'session_id': session_id,
                'table_name': session_data.table_name,
                'table_type': session_data.table_type,
                'field_mapping': session_data.field_mapping.copy(),
                'data_source': session_data.data_source.copy(),
                
                # 从分页路径传递的状态
                'sort_state': context.get('sort_state', {}),
                'filter_state': context.get('filter_state', {}),
                
                # 导航路径特定信息
                'current_page': 1,  # 导航通常显示第一页
                'source_path': DataPath.PAGINATION.value,
                'target_path': DataPath.NAVIGATION.value,
                'transition_time': time.time()
            }
            
            self.logger.info(f"分页->导航转换完成: 表={session_data.table_name}, 会话={session_id}")
            
            return nav_context
            
        except Exception as e:
            self.logger.error(f"处理分页到导航转换失败: {e}")
            return context
    
    def get_session_data(self, session_id: str) -> Optional[ImportSessionData]:
        """
        获取会话数据
        
        Args:
            session_id: 会话ID
            
        Returns:
            ImportSessionData: 会话数据，如果不存在返回None
        """
        session_data = self.import_sessions.get(session_id)
        if session_data:
            session_data.update_access_time()
        return session_data
    
    def get_active_session_for_table(self, table_name: str) -> Optional[ImportSessionData]:
        """
        获取指定表的活跃会话
        
        Args:
            table_name: 表名
            
        Returns:
            ImportSessionData: 活跃会话数据，如果不存在返回None
        """
        session_id = self.active_table_sessions.get(table_name)
        if session_id:
            return self.get_session_data(session_id)
        return None
    
    def invalidate_session(self, session_id: str) -> bool:
        """
        失效指定会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功失效
        """
        try:
            if session_id in self.import_sessions:
                session_data = self.import_sessions[session_id]
                session_data.status = SessionStatus.EXPIRED
                
                # 从活跃会话跟踪中移除
                table_name = session_data.table_name
                if self.active_table_sessions.get(table_name) == session_id:
                    del self.active_table_sessions[table_name]
                
                # 如果是当前会话，清空当前会话ID
                if self.current_session_id == session_id:
                    self.current_session_id = None
                
                self.logger.info(f"会话失效: {session_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"失效会话失败: {e}")
            return False
    
    def _cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            current_time = time.time()
            expired_sessions = []
            
            for session_id, session_data in self.import_sessions.items():
                if session_data.is_expired(self.session_max_age_hours):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                session_data = self.import_sessions[session_id]
                session_data.status = SessionStatus.EXPIRED
                
                # 从活跃跟踪中移除
                table_name = session_data.table_name
                if self.active_table_sessions.get(table_name) == session_id:
                    del self.active_table_sessions[table_name]
            
            if expired_sessions:
                self.logger.info(f"清理过期会话: {len(expired_sessions)} 个")
            
            # 限制会话数量
            if len(self.import_sessions) > self.max_sessions:
                # 按创建时间排序，删除最老的会话
                sorted_sessions = sorted(
                    self.import_sessions.items(),
                    key=lambda x: x[1].created_at
                )
                
                sessions_to_remove = len(self.import_sessions) - self.max_sessions
                for i in range(sessions_to_remove):
                    session_id = sorted_sessions[i][0]
                    del self.import_sessions[session_id]
                
                self.logger.info(f"清理旧会话: {sessions_to_remove} 个")
                
        except Exception as e:
            self.logger.error(f"清理过期会话失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict: 统计信息
        """
        active_sessions = sum(
            1 for session in self.import_sessions.values() 
            if session.status == SessionStatus.ACTIVE
        )
        
        return {
            'total_sessions': len(self.import_sessions),
            'active_sessions': active_sessions,
            'total_sessions_created': self.total_sessions_created,
            'total_path_transitions': self.total_path_transitions,
            'consistency_violations': self.consistency_violations,
            'current_session_id': self.current_session_id,
            'active_table_count': len(self.active_table_sessions)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_sessions_created = 0
        self.total_path_transitions = 0
        self.consistency_violations = 0
        self.logger.info("统计信息已重置")


# 全局单例实例
_global_data_source_manager = None

def get_global_data_source_manager() -> DataSourceUnificationManager:
    """
    获取全局数据源统一管理器实例
    
    Returns:
        DataSourceUnificationManager: 全局实例
    """
    global _global_data_source_manager
    if _global_data_source_manager is None:
        _global_data_source_manager = DataSourceUnificationManager()
    return _global_data_source_manager


if __name__ == "__main__":
    # 测试代码
    print("测试数据源统一管理器...")
    
    manager = DataSourceUnificationManager()
    
    # 测试会话注册
    import_result = {
        'data_source': {'type': 'excel', 'file_path': '/test/data.xlsx'},
        'field_mapping': {'name': '姓名', 'salary': '工资'},
        'total_records': 100
    }
    
    session_id = manager.register_data_import_session(
        table_name="test_table",
        table_type="A岗职工表", 
        import_result=import_result
    )
    
    print(f"创建会话: {session_id}")
    
    # 测试路径转换
    nav_context = {
        'table_name': 'test_table',
        'sort_state': {'column': 'salary', 'order': 'desc'},
        'current_page': 1
    }
    
    pagination_context = manager.ensure_path_consistency(
        DataPath.NAVIGATION, 
        DataPath.PAGINATION, 
        nav_context
    )
    
    print(f"路径转换结果: {pagination_context}")
    print(f"统计信息: {manager.get_statistics()}")
    
    print("数据源统一管理器测试完成!")