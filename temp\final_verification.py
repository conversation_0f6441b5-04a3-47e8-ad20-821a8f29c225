#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证修复效果的脚本
"""

import sys
import os
import pandas as pd

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def final_verification():
    """最终验证修复效果"""
    print("[FINAL] 开始最终验证修复效果...")
    
    try:
        # 1. 使用UnifiedFormatManager测试完整的格式化流程
        print("\n[FINAL 1] 完整格式化流程测试:")
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        
        unified_formatter = get_unified_format_manager()
        
        # 创建测试数据 - 模拟实际系统中的问题数据
        test_data = {
            'basic_performance_2025': [1234, 5678.0, 0, None],
            'performance_bonus_2025': [2000, 1500.5, 0.0, None], 
            'car_allowance': [100.5, 150, "-", None],
            'pension_insurance': [200.75, 250.5, 0.0, None],
            'employee_type_code': ["1", "02", "3", "10"]
        }
        
        df = pd.DataFrame(test_data)
        print(f"原始数据:\n{df}")
        
        # 使用具体表名进行格式化（这是实际系统中的调用方式）
        table_name = "salary_data_2025_07_active_employees"
        formatted_df = unified_formatter.format_table_data(df, table_name)
        
        print(f"\n格式化结果 (使用具体表名 '{table_name}'):\n{formatted_df}")
        
        # 2. 验证关键格式化效果
        print("\n[FINAL 2] 关键格式化效果验证:")
        
        all_correct = True
        
        # 验证浮点数格式（应该显示两位小数）
        basic_perf_val = formatted_df.iloc[0]['basic_performance_2025']
        if basic_perf_val == "1234.00":
            print("[OK] basic_performance_2025 格式正确: 1234 -> '1234.00'")
        else:
            print(f"[FAIL] basic_performance_2025 格式错误: 1234 -> '{basic_perf_val}'")  
            all_correct = False
        
        # 验证车补"-"字符处理
        car_val = formatted_df.iloc[2]['car_allowance']
        if car_val == "0.00":
            print("[OK] car_allowance '-' 处理正确: '-' -> '0.00'")
        else:
            print(f"[FAIL] car_allowance '-' 处理错误: '-' -> '{car_val}'")
            all_correct = False
        
        # 验证养老保险格式
        pension_val = formatted_df.iloc[0]['pension_insurance']
        if pension_val == "200.75":
            print("[OK] pension_insurance 格式正确: 200.75 -> '200.75'")
        else:
            print(f"[FAIL] pension_insurance 格式错误: 200.75 -> '{pension_val}'")
            all_correct = False
        
        # 3. 测试表名映射一致性
        print("\n[FINAL 3] 表名映射一致性测试:")
        
        # 测试TableDataService映射
        from src.services.table_data_service import TableDataService
        import tempfile
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager 
        from src.core.application_state_service import ApplicationStateService
        
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db_manager = DynamicTableManager(temp_db.name)
        state_service = ApplicationStateService()
        table_service = TableDataService(db_manager, state_service)
        
        service_mapping = table_service._extract_table_type_from_name(table_name)
        formatter_mapping = unified_formatter._normalize_table_type(table_name)
        
        if service_mapping == formatter_mapping:
            print(f"[OK] 表名映射一致: '{table_name}' -> '{service_mapping}'")
        else:
            print(f"[FAIL] 表名映射不一致: service='{service_mapping}', formatter='{formatter_mapping}'")
            all_correct = False
        
        # 清理临时文件
        os.unlink(temp_db.name)
        
        # 4. 最终评估
        print("\n[FINAL 4] 最终评估:")
        
        if all_correct:
            print("SUCCESS: 所有格式化问题已修复!")
            print("  - 浮点数显示两位小数: OK")
            print("  - 车补'-'字符转换: OK") 
            print("  - 养老保险格式化: OK")
            print("  - 表名映射一致性: OK")
            print("\n用户反馈的问题应该已经完全解决。")
            return True
        else:
            print("FAIL: 仍有格式化问题需要解决!")
            return False
        
    except Exception as e:
        print(f"[FINAL] 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    final_verification()