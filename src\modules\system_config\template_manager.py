"""
月度工资异动处理系统 - 模板管理器

本模块实现模板文件的管理功能：
- 模板文件路径管理
- 模板文件存在性验证
- 模板文件监控和更新检测
- 模板文件备份和恢复

支持的模板格式：Word (.docx), Excel (.xlsx)

功能函数：
- register_template(): 注册模板文件
- validate_template(): 验证模板文件
- get_template_path(): 获取模板文件路径
- list_templates(): 列出所有可用模板
- backup_template(): 备份模板文件
- watch_template_changes(): 监控模板文件变化

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import hashlib

# 导入项目内部模块
from src.utils.log_config import setup_logger
from .config_manager import ConfigManager

# 初始化日志
logger = setup_logger("system_config.template_manager")


class TemplateManager:
    """模板管理器，负责管理系统模板文件"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化模板管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("system_config.template_manager")
        
        # 配置管理器
        self.config_manager = config_manager or ConfigManager()
        
        # 获取模板目录配置
        self.template_dir = self._get_template_directory()
        self.backup_dir = self.template_dir / "backups"
        
        # 模板注册表
        self._template_registry: Dict[str, Dict[str, Any]] = {}
        
        # 支持的模板格式
        self.supported_formats = {
            '.docx': 'Word文档模板',
            '.xlsx': 'Excel表格模板',
            '.doc': 'Word文档模板(旧版)',
            '.xls': 'Excel表格模板(旧版)'
        }
        
        # 初始化目录
        self._initialize_directories()
        
        # 扫描现有模板
        self._scan_existing_templates()
        
        self.logger.info(f"模板管理器初始化完成，模板目录: {self.template_dir}")
    
    def _get_template_directory(self) -> Path:
        """
        获取模板目录路径
        
        Returns:
            Path: 模板目录路径
        """
        try:
            # 从配置中获取模板目录
            template_dir = self.config_manager.get_config_value(
                "reports.default_template_dir", 
                "template"
            )
            
            # 如果是相对路径，转换为绝对路径
            if not Path(template_dir).is_absolute():
                # 获取项目根目录
                current_file = Path(__file__)
                project_root = current_file.parent.parent.parent.parent
                template_dir = project_root / template_dir
            
            return Path(template_dir)
            
        except Exception as e:
            self.logger.error(f"获取模板目录配置失败: {str(e)}")
            # 使用默认路径
            current_file = Path(__file__)
            project_root = current_file.parent.parent.parent.parent
            return project_root / "template"
    
    def _initialize_directories(self) -> None:
        """初始化模板相关目录"""
        try:
            # 创建模板目录
            self.template_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建备份目录
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.debug("模板目录初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化模板目录失败: {str(e)}")
    
    def _scan_existing_templates(self) -> None:
        """扫描现有模板文件并注册"""
        try:
            if not self.template_dir.exists():
                return
            
            template_count = 0
            for file_path in self.template_dir.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    template_name = self._generate_template_name(file_path)
                    self._register_template_internal(template_name, file_path)
                    template_count += 1
            
            self.logger.info(f"扫描完成，发现 {template_count} 个模板文件")
            
        except Exception as e:
            self.logger.error(f"扫描模板文件失败: {str(e)}")
    
    def _generate_template_name(self, file_path: Path) -> str:
        """
        根据文件路径生成模板名称
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 模板名称
        """
        # 获取相对于模板目录的路径
        try:
            relative_path = file_path.relative_to(self.template_dir)
            # 移除文件扩展名
            return str(relative_path.with_suffix(''))
        except ValueError:
            # 如果不在模板目录内，使用文件名
            return file_path.stem
    
    def register_template(
        self, 
        template_name: str, 
        template_path: Union[str, Path],
        template_type: Optional[str] = None,
        description: Optional[str] = None
    ) -> bool:
        """
        注册模板文件
        
        Args:
            template_name: 模板名称
            template_path: 模板文件路径
            template_type: 模板类型 (auto, word, excel)
            description: 模板描述
            
        Returns:
            bool: 是否注册成功
        """
        try:
            template_path = Path(template_path)
            
            # 验证模板文件
            if not self.validate_template_file(template_path):
                return False
            
            # 自动检测模板类型
            if template_type is None:
                template_type = self._detect_template_type(template_path)
            
            # 注册模板
            success = self._register_template_internal(
                template_name, 
                template_path, 
                template_type, 
                description
            )
            
            if success:
                self.logger.info(f"模板注册成功: {template_name} -> {template_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"注册模板失败: {str(e)}")
            return False
    
    def _register_template_internal(
        self, 
        template_name: str, 
        template_path: Path,
        template_type: Optional[str] = None,
        description: Optional[str] = None
    ) -> bool:
        """
        内部模板注册方法
        
        Args:
            template_name: 模板名称
            template_path: 模板文件路径
            template_type: 模板类型
            description: 模板描述
            
        Returns:
            bool: 是否注册成功
        """
        try:
            # 计算文件哈希值
            file_hash = self._calculate_file_hash(template_path)
            
            # 注册模板信息
            self._template_registry[template_name] = {
                'path': template_path,
                'type': template_type or self._detect_template_type(template_path),
                'description': description or f"{template_name} 模板",
                'hash': file_hash,
                'size': template_path.stat().st_size,
                'modified_time': datetime.fromtimestamp(template_path.stat().st_mtime),
                'registered_time': datetime.now()
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"内部模板注册失败: {str(e)}")
            return False
    
    def validate_template_file(self, template_path: Union[str, Path]) -> bool:
        """
        验证模板文件的有效性
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            bool: 模板文件是否有效
        """
        try:
            template_path = Path(template_path)
            
            # 检查文件是否存在
            if not template_path.exists():
                self.logger.error(f"模板文件不存在: {template_path}")
                return False
            
            # 检查是否为文件
            if not template_path.is_file():
                self.logger.error(f"路径不是文件: {template_path}")
                return False
            
            # 检查文件格式
            if template_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"不支持的模板格式: {template_path.suffix}")
                return False
            
            # 检查文件大小
            file_size = template_path.stat().st_size
            if file_size == 0:
                self.logger.error(f"模板文件为空: {template_path}")
                return False
            
            # 检查文件读取权限
            if not os.access(template_path, os.R_OK):
                self.logger.error(f"模板文件无读取权限: {template_path}")
                return False
            
            self.logger.debug(f"模板文件验证通过: {template_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"验证模板文件时发生错误: {str(e)}")
            return False
    
    def _detect_template_type(self, template_path: Path) -> str:
        """
        检测模板类型
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            str: 模板类型
        """
        suffix = template_path.suffix.lower()
        
        if suffix in ['.docx', '.doc']:
            return 'word'
        elif suffix in ['.xlsx', '.xls']:
            return 'excel'
        else:
            return 'unknown'
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件MD5哈希值
        """
        try:
            hasher = hashlib.md5()
            with open(file_path, 'rb') as f:
                # 分块读取避免内存问题
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        
        except Exception as e:
            self.logger.error(f"计算文件哈希值失败: {str(e)}")
            return ""
    
    def get_template_path(self, template_name: str) -> Optional[Path]:
        """
        获取模板文件路径
        
        Args:
            template_name: 模板名称
            
        Returns:
            Optional[Path]: 模板文件路径，如果不存在返回None
        """
        template_info = self._template_registry.get(template_name)
        if template_info:
            template_path = template_info['path']
            
            # 验证文件是否仍然存在
            if template_path.exists():
                return template_path
            else:
                self.logger.warning(f"模板文件已不存在: {template_path}")
                # 从注册表中移除无效模板
                del self._template_registry[template_name]
                return None
        
        self.logger.warning(f"未找到模板: {template_name}")
        return None
    
    def list_templates(self, template_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出所有可用模板
        
        Args:
            template_type: 过滤模板类型 (word, excel, 或 None 表示所有)
            
        Returns:
            List[Dict[str, Any]]: 模板信息列表
        """
        templates = []
        
        for name, info in self._template_registry.items():
            # 类型过滤
            if template_type and info['type'] != template_type:
                continue
            
            # 验证文件是否存在
            if not info['path'].exists():
                continue
            
            templates.append({
                'name': name,
                'path': str(info['path']),
                'type': info['type'],
                'description': info['description'],
                'size': info['size'],
                'modified_time': info['modified_time'],
                'registered_time': info['registered_time']
            })
        
        return templates
    
    def backup_template(self, template_name: str) -> bool:
        """
        备份模板文件
        
        Args:
            template_name: 模板名称
            
        Returns:
            bool: 是否备份成功
        """
        try:
            template_path = self.get_template_path(template_name)
            if not template_path:
                return False
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{template_name}_{timestamp}{template_path.suffix}"
            backup_path = self.backup_dir / backup_name
            
            # 确保备份目录存在
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(template_path, backup_path)
            
            self.logger.info(f"模板备份成功: {template_name} -> {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份模板失败: {str(e)}")
            return False
    
    def check_template_changes(self) -> Dict[str, Dict[str, Any]]:
        """
        检查模板文件是否有变化
        
        Returns:
            Dict[str, Dict[str, Any]]: 变化的模板信息
        """
        changed_templates = {}
        
        for name, info in self._template_registry.items():
            template_path = info['path']
            
            if not template_path.exists():
                changed_templates[name] = {
                    'change_type': 'deleted',
                    'old_info': info
                }
                continue
            
            # 检查修改时间
            current_mtime = datetime.fromtimestamp(template_path.stat().st_mtime)
            if current_mtime != info['modified_time']:
                # 重新计算哈希值
                current_hash = self._calculate_file_hash(template_path)
                if current_hash != info['hash']:
                    changed_templates[name] = {
                        'change_type': 'modified',
                        'old_info': info,
                        'new_hash': current_hash,
                        'new_modified_time': current_mtime
                    }
        
        if changed_templates:
            self.logger.info(f"检测到 {len(changed_templates)} 个模板发生变化")
        
        return changed_templates
    
    def update_template_info(self, template_name: str) -> bool:
        """
        更新模板信息（当检测到变化时）
        
        Args:
            template_name: 模板名称
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if template_name not in self._template_registry:
                return False
            
            template_path = self._template_registry[template_name]['path']
            
            if not template_path.exists():
                # 模板文件已删除，从注册表中移除
                del self._template_registry[template_name]
                self.logger.info(f"模板已删除，从注册表中移除: {template_name}")
                return True
            
            # 更新模板信息
            self._template_registry[template_name].update({
                'hash': self._calculate_file_hash(template_path),
                'size': template_path.stat().st_size,
                'modified_time': datetime.fromtimestamp(template_path.stat().st_mtime)
            })
            
            self.logger.info(f"模板信息已更新: {template_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新模板信息失败: {str(e)}")
            return False
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板详细信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            Optional[Dict[str, Any]]: 模板信息，如果不存在返回None
        """
        if template_name not in self._template_registry:
            return None
        
        info = self._template_registry[template_name].copy()
        info['path'] = str(info['path'])  # 转换Path为字符串
        return info

    def validate_template_structure(self, template_data: Dict[str, Any]) -> Any:
        """验证模板结构"""
        pass
    
    def extract_template_fields(self, template_path: str) -> Any:
        """提取模板字段"""
        pass
    
    def merge_template_data(self, base_data: Dict[str, Any], override_data: Dict[str, Any]) -> Any:
        """合并模板数据"""
        pass 