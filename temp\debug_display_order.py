#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试A岗职工display_order问题的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_registry_display_fields():
    """测试field_registry的get_display_fields方法"""
    print("=== 测试FieldRegistry的get_display_fields方法 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化field_registry
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 测试A岗职工的显示字段
        table_type = 'a_grade_employees'
        display_fields = field_registry.get_display_fields(table_type)
        
        print(f"表类型: {table_type}")
        print(f"显示字段数量: {len(display_fields)}")
        print(f"显示字段列表: {display_fields}")
        
        # 测试具体表名
        table_name = 'salary_data_2025_08_a_grade_employees'
        display_fields_by_name = field_registry.get_display_fields(table_name)
        
        print(f"\n表名: {table_name}")
        print(f"显示字段数量: {len(display_fields_by_name)}")
        print(f"显示字段列表: {display_fields_by_name}")
        
        # 检查配置中的display_order
        field_mappings = field_registry._field_mappings
        table_mappings = field_mappings.get("table_mappings", {})
        
        if table_name in table_mappings:
            config = table_mappings[table_name]
            display_order = config.get("display_order", [])
            print(f"\n配置中的display_order: {display_order}")
            print(f"display_order长度: {len(display_order)}")
        else:
            print(f"\n未找到表配置: {table_name}")
            
        return len(display_fields) > 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_config_lookup():
    """测试表配置查找"""
    print("\n=== 测试表配置查找 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        table_mappings = field_registry._field_mappings.get("table_mappings", {})
        table_type = 'a_grade_employees'
        table_name = 'salary_data_2025_08_a_grade_employees'
        
        # 测试_find_table_config方法
        config_by_type = field_registry._find_table_config(table_type, table_mappings)
        config_by_name = field_registry._find_table_config(table_name, table_mappings)
        
        print(f"通过类型查找配置 ({table_type}): {'找到' if config_by_type else '未找到'}")
        print(f"通过表名查找配置 ({table_name}): {'找到' if config_by_name else '未找到'}")
        
        if config_by_name:
            display_order = config_by_name.get("display_order", [])
            print(f"找到的display_order: {display_order[:10]}...")  # 只显示前10个
            
        return config_by_name is not None
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始调试A岗职工display_order问题")
    print("=" * 60)
    
    # 测试1: FieldRegistry的display_fields
    test1_passed = test_field_registry_display_fields()
    
    # 测试2: 表配置查找
    test2_passed = test_table_config_lookup()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  FieldRegistry显示字段测试: {'通过' if test1_passed else '失败'}")
    print(f"  表配置查找测试: {'通过' if test2_passed else '失败'}")
    
    if test1_passed and test2_passed:
        print("\n所有测试通过！配置查找正常")
    else:
        print("\n部分测试失败，需要进一步检查")