# 方案：重构导航面板，实现元数据驱动的数据加载

## 1. 核心问题分析

当前方案的主要问题在于：
*   **逻辑耦合：** `enhanced_navigation_panel.py` 强耦合了 `salary_data_` 这种表命名规则。如果未来命名规则改变，或者引入其他类型的表，导航逻辑就需要修改。
*   **信息源不统一：** 系统中有 `table_metadata` 这样的"目录"，但导航却不使用它，而是自己去"猜"目录内容，导致了数据和视图的不一致。
*   **扩展性差：** 无法方便地支持对导航项进行排序、隐藏、设置别名等高级功能，因为这些信息无处存放。解析表名能承载的信息非常有限。

## 2. 解决方案概述

我们将重构数据加载流程，从"**扫描并解析表名**"模式切换到"**查询并渲染元数据**"模式。

整个流程的核心将变为：**`EnhancedNavigationPanel` 请求 `DynamicTableManager` 提供用于导航的结构化元数据，`DynamicTableManager` 从 `table_metadata` 表中查询、处理并返回这些数据，最后 `EnhancedNavigationPanel` 根据返回的结构化数据直接构建出树形导航菜单。**

## 3. 详细实施步骤

### 步骤一：增强数据层 (`src/modules/data_storage/dynamic_table_manager.py`)

这是改造的基础。我们需要在 `DynamicTableManager` 中创建一个新方法，专门为导航面板提供其所需的数据结构。

1.  **创建新方法 `get_navigation_tree_data()`:**
    *   **功能：** 查询 `table_metadata` 表，获取所有类型为 `salary_data` 的记录。
    *   **SQL查询（示意）：**
        ```sql
        SELECT year, month, display_name, table_name, category
        FROM table_metadata
        WHERE type = 'salary_data'
        ORDER BY year DESC, month DESC;
        ```
    *   **数据处理：** 将查询出的扁平化列表，处理成一个嵌套的字典结构，方便前端（GUI）直接渲染。
    *   **返回值结构（示例）：**
        ```python
        # {year: {month: [ {display_name: '退休人员', table_name: '...'}, ... ]}}
        Dict[int, Dict[int, List[Dict[str, str]]]]
        ```
        例如:
        ```json
        {
          "2025": {
            "6": [
              { "display_name": "全部在职人员", "table_name": "salary_data_2025_06_active_employees", "icon": "👥" },
              { "display_name": "A岗职工", "table_name": "salary_data_2025_06_a_grade_employees", "icon": "🏢" }
            ],
            "5": [
              { "display_name": "全部在职人员", "table_name": "salary_data_2025_05_active_employees", "icon": "👥" },
              { "display_name": "退休人员", "table_name": "salary_data_2025_05_pension_employees", "icon": "🏠" }
            ]
          }
        }
        ```
    *   **注意：** 这里的 `display_name` 和 `icon` 等都应该从 `table_metadata` 中读取，如果表中没有，可以暂时硬编码，但长远看应该在元数据表中增加这些字段。

### 步骤二：改造UI层 (`src/gui/prototype/widgets/enhanced_navigation_panel.py`)

现在UI层不再需要复杂的解析逻辑，只需渲染数据即可。

1.  **修改 `_load_dynamic_salary_data()` 方法：**
    *   删除对 `_get_salary_tables()` 和 `_parse_table_name()` 的调用。这些方法将不再被需要。
    *   调用新的数据层方法：`navigation_data = self.dynamic_table_manager.get_navigation_tree_data()`。
    *   直接遍历 `navigation_data` 这个已经结构化的字典来构建树形菜单。
        ```python
        // 伪代码
        for year, months_data in navigation_data.items():
            year_path = self.tree_widget.add_navigation_item(parent_path, f"{year}年", "...")
            for month, items in months_data.items():
                month_path = self.tree_widget.add_navigation_item(year_path, f"{month}月", "...")
                for item_info in items:
                    self.tree_widget.add_navigation_item(month_path, item_info['display_name'], item_info['icon'])
        ```
    *   这样一来，UI层的代码会变得极其简洁和清晰，只负责渲染，不负责数据解析。

### 步骤三：确保数据一致性 (`src/gui/dialogs.py`)

这是确保方案长期有效的关键。当有新数据表被创建时，必须同步更新 `table_metadata` 表。

1.  **修改 `DataImportDialog` 的导入逻辑：**
    *   在数据导入成功，并且 `DynamicTableManager` 成功创建了一个新的数据表（例如 `salary_data_2025_07_active_employees`）之后。
    *   必须立即调用 `DynamicTableManager` 的一个方法（例如 `add_table_metadata()`），将新表的元信息（年份、月份、显示名称、表名等）写入到 `table_metadata` 表中。
    *   这样就形成了一个闭环：**创建表 -> 记录元数据 -> 导航读取元数据 -> 显示导航**。

## 4. 方案影响与优势

*   **受影响的主要文件：**
    1.  `src/modules/data_storage/dynamic_table_manager.py` (核心数据逻辑)
    2.  `src/gui/prototype/widgets/enhanced_navigation_panel.py` (导航视图逻辑)
    3.  `src/gui/dialogs.py` (数据导入逻辑，确保元数据写入)

*   **方案优势：**
    *   **单一事实来源：** `table_metadata` 成为导航的唯一依据，保证了数据和视图的一致性。
    *   **解耦：** UI层与表命名规则完全解耦，未来修改表结构或命名方式，无需改动UI代码。
    *   **可扩展性强：** 未来可以轻松在 `table_metadata` 中增加新字段（如 `sort_order`, `is_visible`, `alias_name`）来实现排序、隐藏、别名等高级导航功能，而不需要改动核心加载逻辑。
    *   **可维护性高：** 代码职责更清晰。`DynamicTableManager` 负责数据，`EnhancedNavigationPanel` 负责展示。排查问题会更容易。

## 5. 流程图与时序图

### Mermaid 流程图 (Flowchart)
```mermaid
graph TD
    A[应用启动] --> B[EnhancedNavigationPanel 初始化];
    B --> C{调用\ndynamic_table_manager\n.get_navigation_tree_data()};
    C --> D[DynamicTableManager 连接数据库];
    D --> E["查询 table_metadata 表\n(type='salary_data')"];
    E --> F[处理查询结果\n构建嵌套字典结构];
    F --> G[返回结构化数据];
    C --> G;
    G --> H[EnhancedNavigationPanel 遍历数据];
    H --> I[循环创建\n年/月/表 导航节点];
    I --> J[导航树构建完成];
```

### Mermaid 时序图 (Sequence Diagram)
```mermaid
sequenceDiagram
    participant User as 用户
    participant MainWindow as 主窗口
    participant NavPanel as EnhancedNavigationPanel
    participant TableManager as DynamicTableManager
    participant DB as 数据库

    User->>MainWindow: 启动应用
    MainWindow->>NavPanel: 初始化
    NavPanel->>TableManager: get_navigation_tree_data()
    activate TableManager
    TableManager->>DB: SELECT * FROM table_metadata WHERE ...
    activate DB
    DB-->>TableManager: 返回元数据列表
    deactivate DB
    TableManager-->>NavPanel: 返回结构化导航数据
    deactivate TableManager
    activate NavPanel
    NavPanel->>NavPanel: 遍历数据并构建Tree
    deactivate NavPanel
    MainWindow-->>User: 显示主界面(含完整导航)
``` 