"""
统一表格组件 - 简化的分页排序实现
"""
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from typing import Dict, List, Any, Optional
import pandas as pd
from dataclasses import dataclass

from src.core.unified_data_manager import UnifiedDataManager, DataQuery, DataResult

class UnifiedTableWidget(QWidget):
    """
    统一表格组件 - 根本解决分页排序问题
    
    核心原则：
    1. 单一数据源：所有数据都通过UnifiedDataManager获取
    2. 状态最小化：只保存必要的查询状态
    3. 操作统一化：分页和排序通过同一个接口处理
    """
    
    # 信号定义
    data_loaded = pyqtSignal(DataResult)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager: Optional[UnifiedDataManager] = None
        self.current_query = DataQuery(table_name="")
        self.current_result: Optional[DataResult] = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 表格
        self.table_view = QTableView()
        self.table_view.setSortingEnabled(True)
        self.table_view.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        layout.addWidget(self.table_view)
        
        # 分页控件
        self.pagination_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("上一页")
        self.next_button = QPushButton("下一页")
        self.page_label = QLabel("第 1 页，共 1 页")
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["25", "50", "100", "200"])
        self.page_size_combo.setCurrentText("50")
        
        self.pagination_layout.addWidget(self.prev_button)
        self.pagination_layout.addWidget(self.page_label)
        self.pagination_layout.addWidget(self.next_button)
        self.pagination_layout.addStretch()
        self.pagination_layout.addWidget(QLabel("每页显示:"))
        self.pagination_layout.addWidget(self.page_size_combo)
        
        layout.addLayout(self.pagination_layout)
        self.setLayout(layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.prev_button.clicked.connect(self.prev_page)
        self.next_button.clicked.connect(self.next_page)
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
    
    def set_data_manager(self, data_manager: UnifiedDataManager):
        """设置数据管理器"""
        self.data_manager = data_manager
    
    def load_table(self, table_name: str, page: int = 1, sort_columns: List[Dict[str, Any]] = None):
        """
        加载表格数据 - 统一入口
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列
        """
        if not self.data_manager:
            self.error_occurred.emit("数据管理器未初始化")
            return
        
        # 更新查询参数
        self.current_query = DataQuery(
            table_name=table_name,
            page=page,
            page_size=int(self.page_size_combo.currentText()),
            sort_columns=sort_columns or []
        )
        
        # 执行查询
        self._execute_query()
    
    def _execute_query(self):
        """执行查询并更新UI"""
        try:
            # 统一查询
            result = self.data_manager.query_data(self.current_query)
            self.current_result = result
            
            # 更新表格
            self._update_table_view(result)
            
            # 更新分页控件
            self._update_pagination_controls(result)
            
            # 发出信号
            self.data_loaded.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(f"数据加载失败: {str(e)}")
    
    def _update_table_view(self, result: DataResult):
        """更新表格视图"""
        if result.data.empty:
            self.table_view.setModel(None)
            return
        
        # 创建模型
        model = PandasModel(result.data)
        self.table_view.setModel(model)
        
        # 设置表头排序指示器
        self._update_sort_indicators(result.sort_columns)
    
    def _update_pagination_controls(self, result: DataResult):
        """更新分页控件"""
        # 更新页码显示
        self.page_label.setText(f"第 {result.page} 页，共 {result.total_pages} 页 (总计 {result.total_count} 条)")
        
        # 更新按钮状态
        self.prev_button.setEnabled(result.page > 1)
        self.next_button.setEnabled(result.page < result.total_pages)
    
    def _update_sort_indicators(self, sort_columns: List[Dict[str, Any]]):
        """更新排序指示器"""
        if not sort_columns:
            return
        
        # 清除所有排序指示器
        header = self.table_view.horizontalHeader()
        for i in range(header.count()):
            header.setSortIndicatorShown(False)
        
        # 设置当前排序指示器
        for sort_col in sort_columns:
            column_name = sort_col.get('column_name', '')
            order = sort_col.get('order', 'ascending')
            
            # 找到列索引
            model = self.table_view.model()
            if model and hasattr(model, 'dataframe'):
                columns = model.dataframe.columns.tolist()
                if column_name in columns:
                    column_index = columns.index(column_name)
                    sort_order = Qt.AscendingOrder if order == 'ascending' else Qt.DescendingOrder
                    header.setSortIndicator(column_index, sort_order)
                    header.setSortIndicatorShown(True)
                    break  # 只显示第一个排序列的指示器
    
    def on_header_clicked(self, logical_index: int):
        """处理表头点击排序"""
        if not self.current_result:
            return
        
        # 获取列名
        model = self.table_view.model()
        if not model or not hasattr(model, 'dataframe'):
            return
        
        columns = model.dataframe.columns.tolist()
        if logical_index >= len(columns):
            return
        
        column_name = columns[logical_index]
        
        # 确定排序方向
        current_sort = self.current_query.sort_columns
        current_order = 'ascending'
        
        # 如果已经按此列排序，切换方向
        for sort_col in current_sort:
            if sort_col.get('column_name') == column_name:
                current_order = 'descending' if sort_col.get('order') == 'ascending' else 'ascending'
                break
        
        # 更新排序列（简化：只支持单列排序）
        new_sort_columns = [{'column_name': column_name, 'order': current_order}]
        
        # 重新加载数据（保持当前页码）
        self.load_table(
            table_name=self.current_query.table_name,
            page=self.current_query.page,  # 关键：保持当前页码
            sort_columns=new_sort_columns
        )
    
    def prev_page(self):
        """上一页"""
        if self.current_query.page > 1:
            self.load_table(
                table_name=self.current_query.table_name,
                page=self.current_query.page - 1,
                sort_columns=self.current_query.sort_columns
            )
    
    def next_page(self):
        """下一页"""
        if self.current_result and self.current_query.page < self.current_result.total_pages:
            self.load_table(
                table_name=self.current_query.table_name,
                page=self.current_query.page + 1,
                sort_columns=self.current_query.sort_columns
            )
    
    def on_page_size_changed(self, new_size: str):
        """页面大小改变"""
        if self.current_query.table_name:
            self.load_table(
                table_name=self.current_query.table_name,
                page=1,  # 重置到第一页
                sort_columns=self.current_query.sort_columns
            )


class PandasModel(QAbstractTableModel):
    """简化的Pandas模型"""
    
    def __init__(self, dataframe: pd.DataFrame):
        super().__init__()
        self.dataframe = dataframe
    
    def rowCount(self, parent=None):
        return len(self.dataframe)
    
    def columnCount(self, parent=None):
        return len(self.dataframe.columns)
    
    def data(self, index, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            value = self.dataframe.iloc[index.row(), index.column()]
            return str(value) if pd.notna(value) else ""
        return None
    
    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self.dataframe.columns[section])
            else:
                return str(section + 1)
        return None