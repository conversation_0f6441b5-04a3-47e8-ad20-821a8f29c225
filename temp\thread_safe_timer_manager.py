
"""
线程安全的定时器管理器

用于管理Qt定时器，确保线程安全性。
"""

from PyQt5.QtCore import QTimer, QThread, QObject, pyqtSignal
from typing import Dict, Callable, Optional
import weakref

class ThreadSafeTimerManager(QObject):
    """线程安全的定时器管理器"""
    
    def __init__(self):
        super().__init__()
        self._timers: Dict[str, QTimer] = {}
        self._callbacks: Dict[str, Callable] = {}
        
    def create_timer(self, timer_id: str, interval: int, callback: Callable, 
                    single_shot: bool = True) -> bool:
        """
        创建或更新定时器
        
        Args:
            timer_id: 定时器唯一标识
            interval: 延迟时间(毫秒)
            callback: 回调函数
            single_shot: 是否单次触发
        
        Returns:
            bool: 是否成功创建
        """
        try:
            # 检查是否在主线程
            if QThread.currentThread() != QThread.currentThread().parent():
                print(f"⚠️ [定时器警告] 尝试在非主线程创建定时器: {timer_id}")
                return False
            
            # 停止现有定时器
            if timer_id in self._timers:
                self.stop_timer(timer_id)
            
            # 创建新定时器
            timer = QTimer()
            timer.setSingleShot(single_shot)
            timer.timeout.connect(callback)
            
            self._timers[timer_id] = timer
            self._callbacks[timer_id] = callback
            
            # 启动定时器
            timer.start(interval)
            
            return True
            
        except Exception as e:
            print(f"❌ [定时器错误] 创建定时器失败 {timer_id}: {e}")
            return False
    
    def stop_timer(self, timer_id: str) -> bool:
        """
        停止指定定时器
        
        Args:
            timer_id: 定时器标识
            
        Returns:
            bool: 是否成功停止
        """
        try:
            if timer_id in self._timers:
                timer = self._timers[timer_id]
                if timer.isActive():
                    timer.stop()
                timer.deleteLater()
                
                del self._timers[timer_id]
                if timer_id in self._callbacks:
                    del self._callbacks[timer_id]
                
                return True
            return False
            
        except Exception as e:
            print(f"❌ [定时器错误] 停止定时器失败 {timer_id}: {e}")
            return False
    
    def stop_all_timers(self):
        """停止所有定时器"""
        timer_ids = list(self._timers.keys())
        for timer_id in timer_ids:
            self.stop_timer(timer_id)
    
    def is_active(self, timer_id: str) -> bool:
        """检查定时器是否活跃"""
        return (timer_id in self._timers and 
                self._timers[timer_id].isActive())

# 全局定时器管理器实例
_timer_manager = None

def get_timer_manager() -> ThreadSafeTimerManager:
    """获取全局定时器管理器实例"""
    global _timer_manager
    if _timer_manager is None:
        _timer_manager = ThreadSafeTimerManager()
    return _timer_manager

def safe_single_shot(delay: int, callback: Callable, timer_id: Optional[str] = None):
    """
    线程安全的单次定时器
    
    Args:
        delay: 延迟时间(毫秒)
        callback: 回调函数
        timer_id: 可选的定时器标识
    """
    if timer_id is None:
        timer_id = f"singleshot_{id(callback)}_{delay}"
    
    manager = get_timer_manager()
    return manager.create_timer(timer_id, delay, callback, single_shot=True)
