#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的表头顺序验证 - 避免Unicode问题
"""

import sys
import json
from pathlib import Path

sys.path.append('.')

def check_order():
    print("=== Header Order Check ===")
    
    config_path = Path("state/data/field_mappings.json")
    if not config_path.exists():
        print("Config file not found")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    table_mappings = config_data.get("table_mappings", {})
    
    target_tables = [
        "active_employees",
        "salary_data_2025_08_active_employees"
    ]
    
    all_passed = True
    
    for table_name in target_tables:
        if table_name not in table_mappings:
            continue
            
        table_config = table_mappings[table_name]
        if "field_mappings" not in table_config:
            continue
        
        field_mappings = table_config["field_mappings"]
        field_order = list(field_mappings.keys())
        
        if "employee_type" in field_order and "employee_type_code" in field_order:
            pos_type = field_order.index("employee_type")
            pos_code = field_order.index("employee_type_code")
            
            print(f"\nTable: {table_name}")
            print(f"  employee_type position: {pos_type + 1}")
            print(f"  employee_type_code position: {pos_code + 1}")
            
            if pos_type < pos_code:
                print(f"  Result: CORRECT order")
            else:
                print(f"  Result: WRONG order")
                all_passed = False
        else:
            print(f"\nTable: {table_name}")
            print(f"  Missing employee_type fields")
    
    print(f"\nOverall result: {'PASS' if all_passed else 'FAIL'}")
    return all_passed

if __name__ == "__main__":
    check_order()