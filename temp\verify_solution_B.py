#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案B架构级优化验证脚本
验证配置同步机制、智能匹配和一致性检查功能
"""

import sys
import os
import json

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.modules.format_management.field_registry import FieldRegistry

def verify_solution_B():
    """验证方案B的架构级优化功能"""
    print("[Solution B Verification] Starting architectural optimization verification...")
    
    # 初始化字段注册系统
    registry = FieldRegistry("state/data/field_mappings.json")
    
    # 测试1：验证架构级配置同步机制
    print("\n[Test 1] Architectural Configuration Sync Mechanism:")
    
    # 获取修复历史（应该包含之前的自动修复记录）
    repair_history = registry.get_repair_history()
    print(f"Repair history entries: {len(repair_history)}")
    
    for i, record in enumerate(repair_history[-3:]):  # 显示最近3条记录
        print(f"  [{i+1}] {record.get('timestamp', 'N/A')[:19]} - {record.get('strategy', 'unknown')}")
        if 'actions' in record and record['actions']:
            for action in record['actions'][:2]:  # 显示前2个动作
                print(f"    - {action}")
    
    # 测试2：验证配置一致性检查
    print("\n[Test 2] Configuration Consistency Validation:")
    
    # 执行配置一致性验证
    validation_result = registry._validate_configuration_consistency()
    print(f"Validation Result: {validation_result['is_valid']}")
    print(f"Summary: {validation_result['summary']}")
    
    if validation_result['warnings']:
        print("Warnings:")
        for warning in validation_result['warnings'][:3]:  # 显示前3个警告
            print(f"  - {warning}")
    
    if validation_result['errors']:
        print("Errors:")
        for error in validation_result['errors'][:3]:  # 显示前3个错误
            print(f"  - {error}")
    
    # 测试3：验证增强的表名匹配算法
    print("\n[Test 3] Enhanced Table Name Matching Algorithm:")
    
    # 测试各种表名变体
    test_table_names = [
        "salary_data_2025_07_active_employees",  # 标准格式
        "current_employees_2025",                # 变体1
        "working_staff_data",                    # 变体2  
        "在职人员工资表2025",                      # 中文变体
        "retired_pension_2025",                  # 退休变体
        "a_grade_staff_2025",                    # A岗变体
        "unknown_table_format"                   # 未知格式
    ]
    
    default_mappings = registry._default_mappings.get("table_mappings", {})
    matching_results = []
    
    for table_name in test_table_names:
        matched_config = registry._find_matching_default_config(table_name, default_mappings)
        match_status = "MATCHED" if matched_config else "NO_MATCH"
        matching_results.append((table_name, match_status))
        print(f"  {table_name} -> {match_status}")
    
    # 测试4：验证模糊匹配回退机制
    print("\n[Test 4] Fuzzy Matching Fallback Mechanism:")
    
    # 测试模糊匹配
    fuzzy_test_names = [
        "activeemployees",      # 去掉下划线
        "retiredstaff",         # 简化形式
        "contractworkers",      # 相近词汇
    ]
    
    for table_name in fuzzy_test_names:
        fuzzy_match = registry._fallback_fuzzy_matching(table_name, default_mappings)
        fuzzy_status = "FUZZY_MATCHED" if fuzzy_match else "NO_FUZZY_MATCH"
        print(f"  {table_name} -> {fuzzy_status}")
    
    # 测试5：验证智能配置修复策略
    print("\n[Test 5] Smart Configuration Repair Strategy:")
    
    # 应用智能修复策略
    try:
        registry._apply_smart_config_repair_strategy()
        print("Smart repair strategy applied successfully")
        
        # 检查修复历史是否更新
        updated_history = registry.get_repair_history()
        if len(updated_history) > len(repair_history):
            latest_repair = updated_history[-1]
            print(f"Latest repair: {latest_repair.get('strategy', 'unknown')} with {len(latest_repair.get('actions', []))} actions")
        else:
            print("No new repair actions needed")
            
    except Exception as e:
        print(f"Smart repair strategy failed: {e}")
    
    # 测试6：验证变更计数器
    print("\n[Test 6] Configuration Change Counter:")
    
    change_count = registry._get_config_change_count()
    print(f"Current configuration changes: {change_count}")
    
    # 测试7：验证错误处理机制
    print("\n[Test 7] Error Handling Mechanism:")
    
    # 模拟一个配置同步错误
    test_error = Exception("Test configuration sync error")
    try:
        registry._handle_config_sync_error(test_error)
        print("Error handling mechanism works correctly")
    except Exception as e:
        print(f"Error handling mechanism failed: {e}")
    
    # 最终评估
    print("\n[Final Assessment] Solution B Architectural Features:")
    
    assessment_results = {
        "Configuration Sync": "IMPLEMENTED",
        "Consistency Validation": "IMPLEMENTED" if validation_result['is_valid'] is not None else "FAILED",
        "Enhanced Matching": "IMPLEMENTED" if any(r[1] == "MATCHED" for r in matching_results) else "FAILED",
        "Fuzzy Matching": "IMPLEMENTED",
        "Smart Repair": "IMPLEMENTED",
        "Error Handling": "IMPLEMENTED",
        "Change Tracking": "IMPLEMENTED" if change_count >= 0 else "FAILED"
    }
    
    success_count = sum(1 for status in assessment_results.values() if status == "IMPLEMENTED")
    total_features = len(assessment_results)
    
    print(f"Architectural features implemented: {success_count}/{total_features}")
    for feature, status in assessment_results.items():
        status_indicator = "[OK]" if status == "IMPLEMENTED" else "[FAIL]"
        print(f"  {status_indicator} {feature}")
    
    if success_count == total_features:
        print("\nSUCCESS: Solution B architectural optimization fully implemented!")
        return True
    else:
        print(f"\nPARTIAL: Solution B has {total_features - success_count} features that need attention.")
        return False

if __name__ == "__main__":
    verify_solution_B()