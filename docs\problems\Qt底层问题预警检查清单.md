# Qt底层问题预警检查清单

## 🚨 P0级致命问题识别标准

### 立即识别为P0-CRITICAL的错误模式

**Qt线程安全错误**：
- `QBasicTimer::start: QBasicTimer can only be used with threads started with QThread`
- `QObject: Cannot create children for a parent that is in a different thread`
- `Qt has caught an exception thrown from an event handler`

**Qt绘制设备错误**：
- `QPaintDevice: Cannot destroy paint device that is being painted`
- `QPainter::begin: Paint device returned engine == 0`
- `QPainter: Painter is already active`

**Qt事件循环错误**：
- `QWidget::repaint: Recursive repaint detected`
- `QCoreApplication::exec: The event loop is already running`
- `QTimer: QTimer can only be used with threads started with QThread`

**Qt内存管理错误**：
- `QObject::~QObject: Timers cannot be stopped from another thread`
- `QPixmap: It is not safe to use pixmaps outside the GUI thread`

## 🔧 问题分析优先级

### 第一优先级：控制台Qt错误
1. **立即停止常规分析**
2. **直接按架构问题处理**
3. **检查线程安全、绘制冲突、事件循环**

### 第二优先级：崩溃模式分析
- 程序直接退出（无错误提示）= Qt底层问题
- 界面冻结但进程存在 = 事件循环阻塞
- 内存泄漏后崩溃 = Qt对象生命周期问题

### 第三优先级：日志文件
- Qt底层问题通常**不会留下有用日志**
- 重点看ERROR级别和最后几条记录
- 关注Qt组件初始化和销毁过程

## 🛠️ 技术检查要点

### 线程安全检查
```python
# 检查QTimer使用
- 所有QTimer必须在主线程创建和使用
- 避免在工作线程中直接操作Qt组件
- 使用信号槽机制跨线程通信

# 检查QObject生命周期
- 确保父子关系在同一线程
- 避免跨线程删除QObject
```

### 绘制安全检查
```python
# 避免递归重绘
- 禁止在paintEvent中调用repaint()
- 使用update()替代repaint()
- 实施绘制状态标志防护

# 绘制设备管理
- 确保QPainter正确配对begin()/end()
- 避免同时多个QPainter操作同一设备
```

### 事件循环健康检查
```python
# 事件队列管理
- 避免在事件处理中阻塞线程
- 使用QTimer.singleShot()进行延迟操作
- 定期调用QApplication.processEvents()

# 避免事件循环嵌套
- 不要在槽函数中启动新的事件循环
- 避免模态对话框导致的循环嵌套
```

## 📋 预防性代码审查要点

### 高风险代码模式
1. **Timer相关**：检查所有QTimer创建和使用位置
2. **绘制相关**：审查所有repaint()、update()调用
3. **线程相关**：确认Qt组件的线程归属
4. **事件处理**：检查事件处理函数的复杂度

### 强制性编码规范
1. **禁止直接使用repaint()**：统一使用安全包装
2. **Timer使用规范**：所有Timer必须有线程检查
3. **绘制状态管理**：强制使用状态标志
4. **异常处理增强**：关键路径必须有容错处理

## 🔄 持续监控机制

### 运行时监控
- 事件队列深度监控
- 绘制操作频率统计
- 线程安全检查
- 内存使用模式分析

### 开发时检查
- 代码提交前的Qt规范检查
- 单元测试覆盖Qt组件交互
- 集成测试验证线程安全
- 压力测试验证稳定性

---

**建立时间**: 2025-07-17  
**适用范围**: 所有PyQt5项目  
**检查频率**: 每次遇到Qt相关问题时必须参考  
**更新责任**: 技术负责人  