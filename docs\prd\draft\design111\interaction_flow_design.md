# 月度工资异动处理系统 - 交互流程设计 (v7.0)

## 1. 核心交互流程概览

### 1.1 主流程图
```mermaid
flowchart TD
    A[启动系统] --> B[数据导航界面]
    B --> C{选择数据文件}
    C -->|选择工资表| D[加载工资数据]
    C -->|选择异动表| E[加载异动数据]
    D --> F[数据预览与验证]
    E --> F
    F --> G[人员匹配处理]
    G --> H{匹配是否完整?}
    H -->|否| I[手动匹配界面]
    I --> J[确认匹配结果]
    J --> K[异动计算处理]
    H -->|是| K
    K --> L[分栏预览结果]
    L --> M{详细信息面板操作}
    M -->|拖拽调整| N[调整面板宽度]
    M -->|切换显示| O[隐藏/显示面板]
    M -->|查看详情| P[查看人员详细信息]
    N --> L
    O --> L
    P --> L
    L --> Q{是否需要调整?}
    Q -->|是| R[打开设置下拉菜单]
    R --> S[选择设置类别]
    S --> T[打开对应设置窗口]
    T --> U[调整处理参数]
    U --> V[保存并关闭设置窗口]
    V --> K
    Q -->|否| W[导出报表]
    W --> X[完成提示]
    X --> Y[查看结果文件]
```

### 1.2 数据交互区域核心流程（v7.0 新增）
```mermaid
flowchart TD
    A[数据加载完成] --> B[显示分栏布局]
    B --> C[数据列表面板 - 左侧]
    B --> D[详细信息面板 - 右侧]
    
    C --> E{用户交互}
    E -->|点击表格行| F[选中人员]
    E -->|查询操作| G[筛选数据]
    E -->|分页操作| H[切换页面]
    
    F --> I[更新详细信息面板]
    G --> J[更新表格内容]
    H --> K[更新表格显示]
    
    D --> L{面板控制操作}
    L -->|拖拽调整条| M[实时调整宽度]
    L -->|点击切换按钮| N[隐藏/显示面板]
    L -->|双击调整条| O[重置默认比例]
    
    M --> P[保持新的面板尺寸]
    N --> Q{面板状态}
    Q -->|隐藏| R[数据列表占满全宽]
    Q -->|显示| S[恢复分栏布局]
    O --> T[恢复3:2默认比例]
    
    I --> U[显示人员详细信息]
    J --> U
    K --> U
    P --> U
    R --> U
    S --> U
    T --> U
```

### 1.3 查询功能增强流程（v7.0 更新）
```mermaid
flowchart TD
    A[用户在查询框输入] --> B{检测输入状态}
    B -->|有内容| C[显示清除图标×]
    B -->|内容为空| D[隐藏清除图标]
    
    A --> E[300ms防抖处理]
    E --> F[实时筛选数据]
    F --> G[更新表格显示]
    G --> H[更新状态栏统计]
    
    A --> I{用户操作}
    I -->|回车键| J[立即执行查询]
    I -->|点击查询按钮| J
    I -->|点击清除图标×| K[清空输入框]
    
    J --> F
    K --> L[恢复完整数据显示]
    L --> M[隐藏清除图标]
    M --> N[更新状态栏为默认信息]
    
    C --> O[用户可点击清除]
    O --> K
```

### 1.4 响应式布局适配流程（v7.0 新增）
```mermaid
flowchart TD
    A[页面加载/窗口调整] --> B[检测屏幕宽度]
    B --> C{宽度断点判断}
    
    C -->|≥1920px| D[Full HD模式]
    C -->|1600px-1919px| E[HD模式]
    C -->|1400px-1599px| F[标准模式]
    C -->|1200px-1399px| G[紧凑模式]
    C -->|1024px-1199px| H[小屏模式]
    C -->|<1024px| I[最小支持模式]
    
    D --> J[完整布局展示]
    E --> K[压缩间距]
    F --> L[调整面板比例]
    G --> M[减少内边距]
    H --> N[垂直排列控制区]
    I --> O[启用水平滚动]
    
    J --> P[应用对应CSS样式]
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
```

## 2. 详细信息面板控制交互设计（v7.0 核心新增）

### 2.1 拖拽调整大小交互流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Resizer as 拖拽条
    participant Panel as 详细信息面板
    participant List as 数据列表面板
    participant Container as 容器区域

    User->>Resizer: 鼠标按下(mousedown)
    Resizer->>Resizer: 开始拖拽状态
    Resizer->>Container: 获取容器宽度
    Resizer->>Panel: 获取当前面板宽度
    
    loop 拖拽过程(mousemove)
        User->>Resizer: 移动鼠标
        Resizer->>Resizer: 计算新宽度
        Resizer->>Resizer: 应用宽度限制(250px-600px)
        Resizer->>Panel: 实时更新面板宽度
        Resizer->>List: 自动调整数据列表宽度
        Resizer->>User: 显示视觉反馈(蓝色拖拽条)
    end
    
    User->>Resizer: 鼠标释放(mouseup)
    Resizer->>Resizer: 结束拖拽状态
    Resizer->>Panel: 保存最终宽度
    Resizer->>User: 恢复默认视觉状态
```

#### 2.1.1 拖拽交互细节
**启动条件：**
- 鼠标在详细信息面板左边缘4px拖拽条上按下
- 光标自动变为`col-resize`调整光标
- 拖拽条变为蓝色并增加透明度，提供即时视觉反馈

**拖拽过程：**
- **实时调整**：鼠标移动时面板宽度实时跟随变化
- **边界限制**：面板宽度限制在250px-600px之间
- **空间保证**：确保数据列表面板最小保持400px宽度
- **性能优化**：使用`requestAnimationFrame`确保流畅的60fps体验

**结束处理：**
- 鼠标释放时保存新的面板宽度
- 拖拽条恢复默认外观
- 光标恢复正常状态

#### 2.1.2 双击重置功能
```mermaid
flowchart LR
    A[用户双击拖拽条] --> B[检测双击事件]
    B --> C[重置为默认比例]
    C --> D[数据列表:详细信息 = 3:2]
    D --> E[平滑过渡动画]
    E --> F[显示重置完成反馈]
```

### 2.2 显示/隐藏切换交互流程
```mermaid
stateDiagram-v2
    [*] --> 显示状态
    显示状态 --> 隐藏状态: 点击切换按钮▶
    隐藏状态 --> 显示状态: 点击切换按钮◀
    
    display: 详细信息面板可见
    display: 切换按钮显示▶箭头
    display: 按钮位置在面板左侧
    
    hidden: 详细信息面板隐藏
    hidden: 切换按钮显示◀箭头
    hidden: 按钮位置在屏幕右侧固定
    hidden: 数据列表占满全宽
```

#### 2.2.1 切换按钮交互设计
**按钮位置和外观：**
- **显示状态**：位于详细信息面板左侧边缘，显示"▶"箭头
- **隐藏状态**：固定在屏幕右侧20px处，显示"◀"箭头
- **悬停效果**：按钮颜色加深，位置略微外移，增加投影

**状态切换动画：**
```css
transition: all 0.3s ease-in-out;
```
- 面板宽度从当前值平滑过渡到0（隐藏）或恢复值（显示）
- 按钮位置平滑移动到新位置
- 箭头方向平滑旋转180度

#### 2.2.2 布局响应机制
```mermaid
flowchart TD
    A[切换操作触发] --> B{目标状态}
    B -->|隐藏面板| C[面板宽度→0]
    B -->|显示面板| D[恢复面板宽度]
    
    C --> E[数据列表扩展到全宽]
    C --> F[切换按钮移至右侧固定位置]
    C --> G[拖拽条隐藏]
    
    D --> H[数据列表恢复原比例]
    D --> I[切换按钮恢复面板位置]
    D --> J[拖拽条显示]
    
    E --> K[更新布局类名]
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K
```

### 2.3 详细信息展示优化（v7.0 更新）

#### 2.3.1 数据展示结构
```
详细信息面板结构：
┌─────────────────────────────────┐
│ 拖拽条│ 人员信息标题      [▶]  │
├─────────────────────────────────┤
│      │ 基本工资: [原值] → [新值]  │
│      │ 岗位津贴: [原值] → [新值]  │  
│      │ 部门: [部门名称]         │
│      │ 职位: [职位名称]         │
│      │ 工号: [工号]             │
└─────────────────────────────────┘
```

#### 2.3.2 薪资数据双列显示
**布局特点：**
- **左侧显示**：原始薪资数据（灰色，较小字体）
- **右侧显示**：当前薪资数据（可编辑，双击编辑）
- **差异标识**：显示变化量，绿色表示增加，红色表示减少
- **编辑模式**：双击右侧数值进入编辑状态

**交互流程：**
```mermaid
flowchart TD
    A[显示薪资字段] --> B[双击右侧数值]
    B --> C[进入编辑模式]
    C --> D[显示输入框]
    D --> E{用户操作}
    E -->|输入新值+回车| F[保存修改]
    E -->|按ESC键| G[取消编辑]
    E -->|点击其他区域| F
    F --> H[更新显示值]
    H --> I[重新计算差异]
    I --> J[更新表格对应行]
    G --> K[恢复原始值]
    K --> A
    J --> A
```

## 3. 响应式交互设计规范（v7.0 新增）

### 3.1 断点系统交互适配

#### 3.1.1 控制面板响应式布局
```mermaid
flowchart TD
    A[屏幕宽度检测] --> B{断点判断}
    
    B -->|≥1400px| C[水平单行布局]
    B -->|1200px-1399px| D[紧凑水平布局]
    B -->|1024px-1199px| E[垂直双行布局]
    B -->|<1024px| F[完全垂直布局]
    
    C --> G[查询区+按钮区水平排列]
    D --> H[减少间距和内边距]
    E --> I[查询区独占一行，按钮区独占一行]
    F --> J[所有元素垂直堆叠]
    
    G --> K[应用对应CSS媒体查询]
    H --> K
    I --> K
    J --> K
```

#### 3.1.2 数据交互区域适配
```mermaid
flowchart LR
    A[大屏设备] --> B[3:2分栏布局]
    C[中屏设备] --> D[调整最小宽度限制]
    E[小屏设备] --> F[垂直排列布局]
    
    B --> G[数据列表400px+，详细信息300px+]
    D --> H[数据列表350px+，详细信息250px+]
    F --> I[数据列表100%，详细信息固定高度300px]
```

### 3.2 触控设备交互优化

#### 3.2.1 触控操作适配
**拖拽条优化：**
- 最小触控区域扩展到12px宽度
- 增加触控视觉反馈
- 支持触摸手势拖拽

**按钮尺寸调整：**
- 小屏设备按钮最小高度44px
- 增加触控间距，避免误触
- 提供触控反馈动画

#### 3.2.2 移动端手势支持
```mermaid
flowchart TD
    A[移动设备检测] --> B[启用触控手势]
    B --> C[左滑手势]
    B --> D[右滑手势]
    B --> E[双击手势]
    
    C --> F[隐藏详细信息面板]
    D --> G[显示详细信息面板]
    E --> H[重置面板比例]
```

## 4. 查询功能增强交互设计（v7.0 更新）

### 4.1 智能查询输入框交互
```mermaid
flowchart TD
    A[用户聚焦查询框] --> B[输入框获得焦点]
    B --> C[显示蓝色边框和阴影]
    C --> D[用户开始输入]
    D --> E{输入内容检测}
    E -->|有内容| F[显示清除图标×]
    E -->|无内容| G[隐藏清除图标]
    
    F --> H[防抖处理300ms]
    H --> I[自动执行搜索]
    I --> J[更新表格显示]
    J --> K[更新状态栏统计]
    
    D --> L{键盘事件}
    L -->|回车键| M[立即执行搜索]
    L -->|ESC键| N[清空输入框]
    
    M --> I
    N --> O[恢复完整数据显示]
    O --> P[隐藏清除图标]
```

#### 4.1.1 清除功能交互细节
**清除图标设计：**
- **位置**：输入框右侧10px处，16x16px圆形按钮
- **显示逻辑**：仅在输入框有内容时显示
- **交互效果**：悬停时背景变浅灰色，点击时立即清空

**清除操作流程：**
```mermaid
sequenceDiagram
    participant User as 用户
    participant Input as 查询输入框
    participant Icon as 清除图标
    participant Table as 数据表格
    participant Status as 状态栏

    User->>Input: 输入查询内容
    Input->>Icon: 显示清除图标
    User->>Icon: 点击清除图标
    Icon->>Input: 清空输入框内容
    Icon->>Icon: 隐藏清除图标
    Input->>Table: 恢复显示所有数据
    Table->>Status: 更新状态栏为默认信息
    Input->>User: 重新获得焦点
```

### 4.2 实时搜索交互优化

#### 4.2.1 防抖搜索机制
```javascript
// 核心防抖逻辑
let searchTimeout;
function handleQueryInput() {
    updateClearIconVisibility();
    
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        performSearch();
    }, 300); // 300ms防抖延迟
}
```

#### 4.2.2 搜索结果展示
```mermaid
flowchart TD
    A[执行搜索] --> B{搜索结果}
    B -->|有结果| C[更新表格显示匹配数据]
    B -->|无结果| D[显示'无匹配记录'提示]
    
    C --> E[更新状态栏：显示 X-Y 条，共 Z 条]
    C --> F[重置分页到第一页]
    C --> G[保持当前选中状态]
    
    D --> H[显示友好的空状态界面]
    H --> I[提供搜索建议]
    I --> J[显示'调整查询条件'按钮]
```

### 4.3 搜索状态管理

#### 4.3.1 搜索上下文保持
```mermaid
stateDiagram-v2
    [*] --> 正常浏览状态
    正常浏览状态 --> 搜索状态: 输入查询条件
    搜索状态 --> 正常浏览状态: 清除查询
    
    正常浏览状态 : 显示所有数据
    正常浏览状态 : 状态栏显示总记录数
    正常浏览状态 : 分页正常工作
    
    搜索状态 : 显示过滤后数据
    搜索状态 : 状态栏显示搜索统计
    搜索状态 : 分页基于搜索结果
    搜索状态 : 保持搜索高亮
```

## 5. 数据表格交互设计（v7.0 更新）

### 5.1 行选择交互流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Row as 表格行
    participant DetailPanel as 详细信息面板
    participant AppState as 应用状态

    User->>Row: 点击表格行
    Row->>AppState: 检查当前选中状态
    alt 非编辑状态
        Row->>Row: 移除其他行选中状态
        Row->>Row: 添加当前行选中样式
        Row->>AppState: 更新selectedPersonId
        Row->>DetailPanel: 触发详细信息更新
        DetailPanel->>User: 显示人员详细信息
    else 编辑状态
        Row->>User: 阻止选择操作
        Note over Row,User: 编辑模式下禁止切换选择
    end
```

#### 5.1.1 行选择视觉反馈
**选中状态样式：**
```css
.data-table tr.selected {
    background: #e3f2fd;
    border-left: 3px solid #4285F4;
}
```

**悬停效果：**
```css
.data-table tr:hover {
    background: #f8f9ff;
    cursor: pointer;
}
```

### 5.2 分页交互设计

#### 5.2.1 分页控件交互流程
```mermaid
flowchart TD
    A[用户操作分页] --> B{操作类型}
    B -->|点击页码| C[跳转到指定页面]
    B -->|上一页/下一页| D[相对页面跳转]
    B -->|修改每页显示数| E[重新计算分页]
    
    C --> F[更新currentPage]
    D --> F
    E --> G[重置currentPage为1]
    
    F --> H[重新渲染表格]
    G --> H
    H --> I[更新分页控件状态]
    I --> J[更新状态栏信息]
```

#### 5.2.2 分页状态管理
```mermaid
stateDiagram-v2
    [*] --> 计算分页
    计算分页 --> 渲染页码: 数据变化
    渲染页码 --> 更新按钮状态: 页码生成完成
    更新按钮状态 --> 显示分页信息: 按钮状态确定
    显示分页信息 --> [*]: 分页渲染完成
    
    note right of 计算分页
        totalPages = Math.ceil(totalRecords / pageSize)
        pagesToShow = 计算显示页码范围
    end note
    
    note right of 更新按钮状态
        禁用首页/上一页(currentPage === 1)
        禁用尾页/下一页(currentPage === totalPages)
    end note
```

## 6. 数据树形导航交互设计（v7.0 更新）

### 6.1 树形节点交互流程
```mermaid
flowchart TD
    A[用户点击树节点] --> B{节点类型}
    B -->|父节点| C[切换展开/收起状态]
    B -->|叶子节点| D[选中节点]
    
    C --> E{当前状态}
    E -->|已展开| F[收起子节点]
    E -->|已收起| G[展开子节点]
    
    F --> H[隐藏子节点列表]
    G --> I[显示子节点列表]
    
    D --> J[更新选中状态]
    J --> K[更新状态栏显示路径]
    K --> L[触发数据加载]
    
    H --> M[更新箭头方向]
    I --> M
    M --> N[应用展开/收起动画]
```

#### 6.1.1 智能展开逻辑
```mermaid
flowchart TD
    A[选中叶子节点] --> B[获取节点路径]
    B --> C[检查父节点状态]
    C --> D{父节点是否收起}
    D -->|是| E[自动展开父节点路径]
    D -->|否| F[保持当前状态]
    
    E --> G[从根节点开始逐级展开]
    G --> H[确保选中节点可见]
    F --> H
```

### 6.2 默认展开策略

#### 6.2.1 初始化展开状态
```mermaid
flowchart TD
    A[树形数据加载] --> B[查找默认节点]
    B --> C[找到isDefault: true的节点]
    C --> D[计算展开路径]
    D --> E[展开路径上的所有父节点]
    E --> F[选中默认节点]
    F --> G[更新界面状态]
```

**默认展开规则：**
1. **第一级**：月度工资表默认展开，异动人员表默认收起
2. **第二级**：2025年默认展开，其他年份收起
3. **第三级**：5月默认展开，其他月份收起
4. **第四级**：全部在职人员默认选中

### 6.3 树形节点视觉层次

#### 6.3.1 层级样式设计
```css
/* 根据层级调整样式 */
.tree-node > .tree-item { 
    margin-left: 15px;
    font-weight: 500; 
}
.tree-node .tree-node > .tree-item { 
    margin-left: 35px; 
    font-weight: 400; 
    font-size: 13px;
}
.tree-node .tree-node .tree-node > .tree-item { 
    margin-left: 55px; 
    font-size: 13px;
    color: #666;
}
```

#### 6.3.2 展开收起动画
```css
.tree-toggle { 
    transition: transform 0.2s ease-in-out; 
}
.tree-node.collapsed .tree-toggle { 
    transform: rotate(-90deg); 
}
.tree-children {
    transition: height 0.3s ease-in-out;
    overflow: hidden;
}
```

## 7. 设置功能交互设计（保持原有 + v7.0 优化）

### 7.1 设置窗口响应式适配
```mermaid
flowchart TD
    A[打开设置窗口] --> B[检测屏幕尺寸]
    B --> C{屏幕大小}
    C -->|桌面端| D[固定600x500窗口]
    C -->|平板端| E[90%宽度，最大600px]
    C -->|手机端| F[全屏模式，顶部留空]
    
    D --> G[居中显示]
    E --> H[居中显示，限制高度80vh]
    F --> I[从底部滑入动画]
```

### 7.2 设置项交互优化

#### 7.2.1 开关组件增强
```mermaid
stateDiagram-v2
    [*] --> 关闭状态
    关闭状态 --> 开启状态: 点击开关/标签
    开启状态 --> 关闭状态: 点击开关/标签
    
    关闭状态 : 开关滑块在左侧
    关闭状态 : 背景灰色 #ddd
    关闭状态 : 相关功能禁用
    
    开启状态 : 开关滑块在右侧
    开启状态 : 背景蓝色 #4285F4
    开启状态 : 相关功能启用
```

#### 7.2.2 设置冲突检测
```mermaid
flowchart TD
    A[用户修改设置] --> B[实时验证设置组合]
    B --> C{检测到冲突?}
    C -->|是| D[显示警告标识]
    C -->|否| E[正常保存]
    
    D --> F[在设置项旁显示⚠️图标]
    F --> G[悬停显示冲突说明]
    G --> H[提供自动修复建议]
```

## 8. 性能优化交互设计（v7.0 新增）

### 8.1 大数据处理交互

#### 8.1.1 虚拟滚动交互
```mermaid
flowchart TD
    A[加载大量数据] --> B[检测数据量]
    B --> C{数据量 > 100条?}
    C -->|是| D[启用虚拟滚动]
    C -->|否| E[使用普通分页]
    
    D --> F[只渲染可视区域行]
    D --> G[动态计算滚动位置]
    D --> H[预加载上下缓冲区]
    
    F --> I[提供流畅滚动体验]
    G --> I
    H --> I
```

### 8.2 交互性能监控

#### 8.2.1 拖拽性能优化
```mermaid
sequenceDiagram
    participant User as 用户
    participant RAF as RequestAnimationFrame
    participant DOM as DOM更新
    participant GPU as GPU加速

    User->>RAF: 开始拖拽
    loop 拖拽过程
        RAF->>DOM: 计算新位置
        DOM->>GPU: 触发transform动画
        GPU->>User: 60fps流畅反馈
    end
    User->>RAF: 结束拖拽
    RAF->>DOM: 最终位置确认
```

---

**更新日期**：2024年12月  
**版本号**：v7.0 - 基于HTML原型的完整交互流程设计  
**主要更新**：详细信息面板控制、响应式交互、查询功能增强、数据树导航优化