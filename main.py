#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度工资异动处理系统 - 主应用入口

本文件是系统的主入口，负责启动应用程序和初始化各个模块。

功能:
1. 应用程序初始化
2. 异常处理和日志记录
3. 系统环境检查
4. 主窗口启动
"""

import sys
import os
import traceback
from pathlib import Path

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置应用程序信息
APP_NAME = "月度工资异动处理系统"
APP_VERSION = "2.0.0-refactored"
APP_AUTHOR = "月度工资异动处理系统开发团队"

def setup_environment():
    """设置应用程序环境"""
    try:
        # 创建必要的目录
        directories = [
            'logs',
            'data',
            'output',
            'temp',
            'backup'
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # 设置环境变量
        os.environ['APP_NAME'] = APP_NAME
        os.environ['APP_VERSION'] = APP_VERSION
        os.environ['APP_ROOT'] = str(project_root)
        
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查必要的包
        required_packages = [
            'PyQt5',
            'pandas',
            'openpyxl',
            'python-docx',
            'sqlite3'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                if package == 'sqlite3':
                    import sqlite3
                elif package == 'PyQt5':
                    import PyQt5
                elif package == 'pandas':
                    import pandas
                elif package == 'openpyxl':
                    import openpyxl
                elif package == 'python-docx':
                    import docx
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"错误: 缺少以下必要的包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        return True
        
    except Exception as e:
        print(f"依赖检查失败: {e}")
        return False

def setup_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志文件
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 🔧 [P0-修复] 使用loguru记录异常，统一日志格式
        try:
            from src.utils.log_config import setup_logger
            logger = setup_logger("global_exception_handler")
            logger.critical(f"🔧 [P0-修复] 全局未捕获异常: {exc_type.__name__}: {exc_value}")
            logger.critical(f"🔧 [P0-修复] 异常详情:\n{error_msg}")
        except Exception:
            # 如果loguru不可用，回退到直接文件写入
            log_file = project_root / 'logs' / 'error.log'
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    from datetime import datetime
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    f.write(f"\n[{timestamp}] 未捕获的异常:\n{error_msg}\n")
            except Exception:
                pass  # 如果无法写入日志，忽略错误
        
        # 显示错误对话框
        try:
            from PyQt5.QtWidgets import QMessageBox, QApplication
            
            if QApplication.instance():
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Critical)
                msg_box.setWindowTitle("系统错误")
                msg_box.setText("系统发生未处理的错误，程序将尝试继续运行")
                msg_box.setDetailedText(error_msg)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
        except Exception:
            # 如果GUI不可用，打印到控制台
            print(f"系统错误: {error_msg}")
    
    # 设置异常处理器
    sys.excepthook = handle_exception

def setup_qt_exception_handling(app):
    """设置PyQt专用异常处理机制"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger("qt_exception_handler")
        
        # 保存原始的异常处理器
        original_excepthook = sys.excepthook
        
        def qt_aware_excepthook(exc_type, exc_value, exc_traceback):
            """PyQt感知的异常处理器"""
            try:
                # 记录详细的异常信息
                logger.critical(f"🔧 [P0-修复] PyQt事件循环异常: {exc_type.__name__}: {exc_value}")
                
                # 如果是在Qt事件循环中，显示用户友好的错误对话框
                from PyQt5.QtWidgets import QMessageBox, QApplication
                from PyQt5.QtCore import QTimer
                
                if QApplication.instance() and not isinstance(exc_value, KeyboardInterrupt):
                    # 使用QTimer确保在主线程中显示对话框
                    def show_error_dialog():
                        try:
                            msg = QMessageBox()
                            msg.setIcon(QMessageBox.Warning)
                            msg.setWindowTitle("排序功能错误")
                            msg.setText("表格排序时发生错误，程序将继续运行")
                            msg.setInformativeText("请尝试重新排序或重启程序")
                            msg.setDetailedText(f"{exc_type.__name__}: {exc_value}")
                            msg.setStandardButtons(QMessageBox.Ok)
                            msg.exec_()
                        except Exception as dialog_error:
                            logger.error(f"显示错误对话框失败: {dialog_error}")
                    
                    # 🔧 [P0-CRITICAL] 安全显示错误对话框，避免QTimer线程问题
                    try:
                        from PyQt5.QtCore import QCoreApplication, QMetaObject, Qt
                        # 使用元对象系统的线程安全调用
                        QMetaObject.invokeMethod(
                            QCoreApplication.instance(),
                            "processEvents",
                            Qt.QueuedConnection
                        )
                        # 直接显示，不使用QTimer避免线程问题
                        show_error_dialog()
                    except Exception as safe_error:
                        logger.error(f"安全显示错误对话框失败: {safe_error}")
                        # 最后的后备方案：直接显示
                        try:
                            show_error_dialog()
                        except:
                            pass
                
            except Exception as handler_error:
                print(f"Qt异常处理器自身发生错误: {handler_error}")
            
            # 调用原始处理器，但不重新抛出异常（避免程序退出）
            try:
                original_excepthook(exc_type, exc_value, exc_traceback)
            except Exception:
                pass  # 防止异常处理器本身导致程序退出
        
        # 应用PyQt专用异常处理器
        sys.excepthook = qt_aware_excepthook
        
        logger.info("🔧 [P0-修复] PyQt专用异常处理机制已启用")
        
    except Exception as e:
        print(f"设置PyQt异常处理失败: {e}")

def create_application():
    """创建QApplication实例"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QFont
        
        # 🔧 [Qt类型修复] 全面解决Qt元对象类型注册问题
        try:
            from PyQt5.QtCore import Qt, qRegisterMetaType

            # 确保Qt枚举类型被加载到Python环境中
            _ = Qt.Orientation.Horizontal
            _ = Qt.Orientation.Vertical
            _ = Qt.SortOrder.AscendingOrder
            _ = Qt.SortOrder.DescendingOrder

            # 🔧 [Qt类型修复] 注册所有可能引起问题的Qt元类型
            registered_types = []
            try:
                # 注册基本枚举类型
                orientation_id = qRegisterMetaType("Qt::Orientation")
                sort_order_id = qRegisterMetaType("Qt::SortOrder")
                registered_types.extend([f"Orientation({orientation_id})", f"SortOrder({sort_order_id})"])
                
                # 注册容器类型
                try:
                    int_list_id = qRegisterMetaType("QList<int>")
                    int_vector_id = qRegisterMetaType("QVector<int>")
                    registered_types.extend([f"QList<int>({int_list_id})", f"QVector<int>({int_vector_id})"])
                except Exception:
                    pass  # 如果注册失败，继续其他类型
                
                # 注册表格相关类型
                try:
                    from PyQt5.QtCore import QModelIndex, QItemSelection
                    model_index_id = qRegisterMetaType("QModelIndex")
                    item_selection_id = qRegisterMetaType("QItemSelection")
                    registered_types.extend([f"QModelIndex({model_index_id})", f"QItemSelection({item_selection_id})"])
                except Exception:
                    pass
                
                print(f"[成功] Qt类型注册完成: {', '.join(registered_types)}")
                
            except Exception as reg_error:
                print(f"[警告] qRegisterMetaType部分失败: {reg_error}")
                print("[信息] 已改为DirectConnection连接方式避免类型问题")

        except Exception as type_error:
            print(f"[警告] Qt类型处理失败: {type_error}")
            print("[信息] 已改为DirectConnection连接方式避免类型问题")
        
        # 设置高DPI支持 - 必须在QApplication创建之前设置
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName(APP_AUTHOR)
        
        # 设置应用程序图标
        icon_path = project_root / 'resources' / 'icons' / 'app_icon.png'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 设置样式
        app.setStyle('Fusion')
        
        # 🔧 [P0-修复] 应用Material Design样式系统
        try:
            from src.gui.style_manager import StyleManager
            style_manager = StyleManager.get_instance()
            
            # 应用全局样式
            if style_manager.apply_global_style(app):
                print("[成功] Material Design全局样式应用成功")
            else:
                print("Material Design全局样式应用失败，使用默认样式")
                
            # 启用样式热重载（开发环境）
            style_manager.enable_hot_reload(True)
            
        except Exception as style_error:
            print(f"[警告] 样式系统初始化失败: {style_error}")
            print("[信息] 继续使用默认Fusion样式")
        
        # 🔧 [P0-修复] 设置PyQt专用异常处理
        setup_qt_exception_handling(app)
        
        return app
        
    except Exception as e:
        print(f"创建应用程序失败: {e}")
        return None

def create_main_window(config_manager, db_manager, dynamic_table_manager):
    """创建主窗口"""
    try:
        # 重构后，我们统一使用 PrototypeMainWindow
        main_window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        
        main_window.show()
        return main_window
        
    except Exception as e:
        print(f"创建主窗口失败: {e}")
        traceback.print_exc()
        return None

def setup_app_logging():
    """设置日志系统"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger(__name__)
        logger.info(f"{APP_NAME} v{APP_VERSION} 启动")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

def show_splash_screen(app):
    """显示启动画面"""
    try:
        from PyQt5.QtWidgets import QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QPixmap, QFont
        
        # 创建启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        
        # 添加启动信息
        splash.showMessage(
            f"{APP_NAME}\n版本 {APP_VERSION}\n正在启动...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
        
    except Exception as e:
        print(f"启动画面创建失败: {e}")
        return None

def main():
    """主函数"""
    try:
        from PyQt5.QtCore import Qt, QTimer
        
        print(f"正在启动 {APP_NAME} v{APP_VERSION}...")
        
        # 1. 设置环境
        if not setup_environment():
            sys.exit(1)
        
        # 2. 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        # 3. 设置全局异常处理器
        setup_exception_handler()
        
        # 4. 创建应用程序
        app = create_application()
        if not app:
            sys.exit(1)
            
        # 5. 设置日志
        logger = setup_app_logging()
        if not logger:
            sys.exit(1)

        # 6. 初始化核心管理器
        logger.info("初始化核心管理器...")
        try:
            config_manager = ConfigManager()
            db_manager = DatabaseManager(config_manager=config_manager)
            dynamic_table_manager = DynamicTableManager(db_manager=db_manager)
            logger.info("核心管理器初始化完成。")
        except Exception as e:
            logger.error(f"核心管理器初始化失败: {e}")
            traceback.print_exc()
            sys.exit(1)
            
        # 7. 显示启动画面 (可选)
        # splash = show_splash_screen(app)
        
        # 8. 创建主窗口
        main_window = create_main_window(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        if not main_window:
            sys.exit(1)
            
        # 关闭启动画面
        # if splash:
        #     splash.finish(main_window)
            
        logger.info("应用程序启动成功")
        print(f"{APP_NAME} 启动成功!")
        
        # 9. 运行应用程序
        exit_code = app.exec_()
        logger.info("应用程序正常退出")
        print("应用程序已退出")
        sys.exit(exit_code)

    except Exception as e:
        # 记录任何在主函数中发生的未捕获异常
        error_msg = traceback.format_exc()
        try:
            # 尝试写入日志
            log_file = project_root / 'logs' / 'error.log'
            with open(log_file, 'a', encoding='utf-8') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n[{timestamp}] 主函数发生严重错误:\n{error_msg}\n")
        except:
            pass # 忽略日志写入错误
        
        print(f"主函数发生严重错误: {e}")
        sys.exit(1)

def run_tests():
    """运行测试"""
    try:
        import pytest
        print("正在运行测试...")
        result = pytest.main(['-v', 'test/'])
        return result == 0
    except ImportError:
        print("pytest未安装，跳过测试")
        return True
    except Exception as e:
        print(f"测试运行失败: {e}")
        return False

def show_version():
    """显示版本信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

开发团队: {APP_AUTHOR}
Python版本: {sys.version}
系统平台: {sys.platform}

项目目录: {project_root}
    """)

def show_help():
    """显示帮助信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

用法:
    python main.py [选项]

选项:
    -h, --help      显示此帮助信息
    -v, --version   显示版本信息
    --test          运行测试
    --check         检查系统环境和依赖

示例:
    python main.py              # 正常启动程序
    python main.py --test       # 运行测试
    python main.py --check      # 检查环境
    """)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if '--test' in sys.argv:
            run_tests()
        elif '--version' in sys.argv:
            show_version()
        elif '--help' in sys.argv:
            show_help()
        else:
            main()
    else:
        main() 