#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试表名映射问题的脚本
"""

import sys
import os

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def debug_table_mapping():
    """调试表名映射问题"""
    print("[DEBUG] 开始调试表名映射问题...")
    
    try:
        # 1. 检查table_data_service中的映射逻辑
        print("\n[DEBUG 1] TableDataService映射逻辑:")
        from src.services.table_data_service import TableDataService
        import tempfile
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager 
        from src.core.application_state_service import ApplicationStateService
        
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db_manager = DynamicTableManager(temp_db.name)
        state_service = ApplicationStateService()
        table_service = TableDataService(db_manager, state_service)
        
        table_name = "salary_data_2025_07_active_employees"
        extracted_type = table_service._extract_table_type_from_name(table_name)
        print(f"TableDataService映射: '{table_name}' -> '{extracted_type}'")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        
        # 2. 检查UnifiedFormatManager的normalize逻辑
        print("\n[DEBUG 2] UnifiedFormatManager normalize逻辑:")
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        
        unified_formatter = get_unified_format_manager()
        
        # 测试不同的表名
        test_names = [
            "salary_data_2025_07_active_employees",
            "active_employees", 
        ]
        
        for name in test_names:
            normalized = unified_formatter._normalize_table_type(name)
            print(f"Normalize: '{name}' -> '{normalized}'")
        
        # 3. 检查字段注册系统对两种表名的处理
        print("\n[DEBUG 3] 字段注册系统对比:")
        from src.modules.format_management.field_registry import FieldRegistry
        
        field_registry = FieldRegistry("state/data/field_mappings.json")
        
        # 检查具体表名的字段类型
        specific_types = field_registry.get_table_field_types("salary_data_2025_07_active_employees")
        generic_types = field_registry.get_table_field_types("active_employees")
        
        print(f"具体表名字段类型数量: {len(specific_types)}")
        print(f"通用表名字段类型数量: {len(generic_types)}")
        
        # 检查关键字段的类型是否一致
        key_fields = ['basic_performance_2025', 'performance_bonus_2025', 'car_allowance']
        print("\n关键字段类型对比:")
        for field in key_fields:
            specific_type = specific_types.get(field, 'NOT_FOUND')
            generic_type = generic_types.get(field, 'NOT_FOUND')
            match = "OK" if specific_type == generic_type else "FAIL"
            print(f"  {field}: specific='{specific_type}', generic='{generic_type}' {match}")
        
        # 4. 检查隐藏字段配置
        print("\n[DEBUG 4] 隐藏字段配置对比:")
        specific_hidden = field_registry.get_hidden_fields("salary_data_2025_07_active_employees")
        generic_hidden = field_registry.get_hidden_fields("active_employees")
        
        print(f"具体表名隐藏字段: {specific_hidden}")
        print(f"通用表名隐藏字段: {generic_hidden}")
        
        # 5. 检查display_order配置
        print("\n[DEBUG 5] Display Order配置对比:")
        specific_display = field_registry.get_display_fields("salary_data_2025_07_active_employees")
        generic_display = field_registry.get_display_fields("active_employees")
        
        print(f"具体表名display字段数量: {len(specific_display) if specific_display else 0}")
        print(f"通用表名display字段数量: {len(generic_display) if generic_display else 0}")
        
        if specific_display and generic_display:
            print("Display字段差异:")
            specific_set = set(specific_display)
            generic_set = set(generic_display)
            only_specific = specific_set - generic_set
            only_generic = generic_set - specific_set
            
            if only_specific:
                print(f"  仅在具体表名中: {only_specific}")
            if only_generic:
                print(f"  仅在通用表名中: {only_generic}")
        
        print("\n[DEBUG] 表名映射调试完成!")
        return True
        
    except Exception as e:
        print(f"[DEBUG] 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_table_mapping()