#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页行号专项修复脚本
"""

import os
import re
import tempfile
import shutil
from pathlib import Path


def fix_pagination_logic_in_main_window():
    """修复主窗口中的分页逻辑错误"""
    
    file_path = "src/gui/prototype/prototype_main_window.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 修复 set_data 方法中的页码重置逻辑
    old_pagination_logic = """                # 🔧 [关键修复] 设置分页组件状态
            if hasattr(self, 'pagination_widget'):
                # 设置总记录数
                self.pagination_widget.set_total_records(total_records)
                
                # 🔧 [关键修复] 设置当前页但不触发信号
                if hasattr(self.pagination_widget, 'set_current_page_silent'):
                    self.pagination_widget.set_current_page_silent(current_page)
                elif not is_sort_operation:
                    # 非排序操作时才重置页码
                    self.logger.info(f"🔧 [CRITICAL修复] 非排序操作，重置页码到第{current_page}页")
                    # 暂时断开信号
                    self.pagination_widget.page_changed.disconnect()
                    self.pagination_widget.set_current_page(current_page)
                    # 重新连接信号
                    self.pagination_widget.page_changed.connect(self._on_page_changed_new_architecture)"""

    new_pagination_logic = """                # 🔧 [关键修复] 设置分页组件状态
            if hasattr(self, 'pagination_widget'):
                # 设置总记录数
                self.pagination_widget.set_total_records(total_records)
                
                # 🔧 [分页行号修复] 保持当前页码，不要重置！
                # 分页操作时应该保持用户的当前页面
                if hasattr(self.pagination_widget, 'set_current_page_silent'):
                    self.pagination_widget.set_current_page_silent(current_page)
                    self.logger.info(f"🔧 [分页行号修复] 静默设置页码: {current_page}")
                else:
                    # 使用防信号循环的方式设置页码
                    self.logger.info(f"🔧 [分页行号修复] 安全设置页码: {current_page}")
                    # 暂时断开信号
                    try:
                        self.pagination_widget.page_changed.disconnect()
                        self.pagination_widget.set_current_page(current_page)
                        # 重新连接信号
                        self.pagination_widget.page_changed.connect(self._on_page_changed_new_architecture)
                    except Exception as e:
                        self.logger.warning(f"🔧 [分页行号修复] 设置页码时信号处理异常: {e}")"""

    if old_pagination_logic in content:
        content = content.replace(old_pagination_logic, new_pagination_logic)
        print("✅ 已修复 set_data 方法中的页码重置逻辑")
    
    # 2. 增强分页状态同步逻辑
    enhanced_sync_logic = """
            # 🔧 [分页行号修复] 确保表格组件获得正确的分页状态
            if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                # 计算正确的分页状态
                pagination_state = {
                    'current_page': current_page,
                    'page_size': page_size,
                    'total_records': total_records,
                    'start_record': start_record,
                    'end_record': end_record
                }
                
                # 🔧 [关键修复] 立即设置分页状态到表格组件
                self.main_workspace.expandable_table.set_pagination_state(pagination_state)
                self.logger.info(f"🔧 [分页行号修复] 已设置表格分页状态: 第{current_page}页, 记录{start_record}-{end_record}")
                
                # 🔧 [关键修复] 数据设置后强制检查行号
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, lambda: self.main_workspace.expandable_table._post_data_set_row_number_check())
"""

    # 在 set_data 方法的最后添加这个逻辑
    pattern = r'(self\.logger\.info\(f"表格数据设置完成: {len\(df\)} 行"\))'
    replacement = r'\1' + enhanced_sync_logic
    
    if re.search(pattern, content):
        content = re.sub(pattern, replacement, content)
        print("✅ 已添加增强的分页状态同步逻辑")
    
    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 修复完成")
    return True


def enhance_pagination_state_handling():
    """增强分页状态处理"""
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在增强: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 添加调试和验证方法
    debug_methods = '''
    def debug_pagination_state(self):
        """🔧 [分页行号修复] 调试分页状态"""
        try:
            if hasattr(self, 'pagination_state') and self.pagination_state:
                state = self.pagination_state
                self.logger.info(f"🔧 [调试] 分页状态: 第{state.get('current_page', 'N/A')}页")
                self.logger.info(f"🔧 [调试] 记录范围: {state.get('start_record', 'N/A')}-{state.get('end_record', 'N/A')}")
                self.logger.info(f"🔧 [调试] 总记录数: {state.get('total_records', 'N/A')}")
                
                # 检查当前表格状态
                row_count = self.rowCount()
                self.logger.info(f"🔧 [调试] 表格行数: {row_count}")
                
                if row_count > 0:
                    # 检查前3行的行号
                    for i in range(min(3, row_count)):
                        header_item = self.verticalHeaderItem(i)
                        actual_label = header_item.text() if header_item else "N/A"
                        expected_label = str(state.get('start_record', 1) + i)
                        status = "✅" if actual_label == expected_label else "❌"
                        self.logger.info(f"🔧 [调试] 第{i}行: 期望{expected_label}, 实际{actual_label} {status}")
            else:
                self.logger.info("🔧 [调试] 无分页状态")
                
        except Exception as e:
            self.logger.error(f"🔧 [调试] 分页状态调试失败: {e}")
    
    def force_fix_row_numbers_now(self):
        """🔧 [分页行号修复] 立即强制修复行号"""
        try:
            self.logger.info("🔧 [立即修复] 开始强制修复行号...")
            
            # 调试当前状态
            self.debug_pagination_state()
            
            # 强制修复
            self._force_update_pagination_row_numbers()
            
            # 验证结果
            self._verify_pagination_row_numbers()
            
            self.logger.info("🔧 [立即修复] 强制修复完成")
            
        except Exception as e:
            self.logger.error(f"🔧 [立即修复] 强制修复失败: {e}")
'''

    # 在类的最后添加调试方法
    class_end_pattern = r'(class VirtualizedExpandableTable.*?)(\n\n\n|\nclass |\Z)'
    
    if re.search(class_end_pattern, content, re.DOTALL):
        content = re.sub(class_end_pattern, 
                        lambda m: m.group(1) + debug_methods + (m.group(2) if m.group(2) else ''),
                        content, flags=re.DOTALL)
        print("✅ 已添加分页状态调试方法")
    
    # 2. 增强 set_data 方法，在数据设置完成后立即修复行号
    enhanced_set_data_end = '''
            # 🔧 [分页行号修复] 数据设置完成后的行号修复
            try:
                if hasattr(self, 'pagination_state') and self.pagination_state:
                    self.logger.info("🔧 [分页行号修复] 数据设置完成，开始行号检查和修复")
                    
                    # 使用定时器确保在UI更新完成后执行
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(10, self._post_data_set_row_number_check)
                    QTimer.singleShot(50, self.force_fix_row_numbers_now)
                    
            except Exception as e:
                self.logger.error(f"🔧 [分页行号修复] 数据设置后处理失败: {e}")
'''

    # 在 set_data 方法的最后添加
    pattern = r'(self\.logger\.info\(f"表格数据设置完成: {len\(df\)} 行"\).*?self\.logger\.info\(f"表格数据设置完成: {data_rows} 行"\))'
    
    if not re.search(pattern, content, re.DOTALL):
        # 如果没找到上面的模式，尝试其他模式
        pattern = r'(self\.logger\.info\(f"表格数据设置完成.*?耗时.*?"\))'
        
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, r'\1' + enhanced_set_data_end, content, flags=re.DOTALL)
        print("✅ 已添加数据设置后的行号修复逻辑")
    
    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 增强完成")
    return True


def create_pagination_test_script():
    """创建分页测试脚本"""
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页行号测试脚本
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_pagination_row_numbers():
    """测试分页行号修复效果"""
    print("🧪 分页行号测试开始...")
    
    try:
        # 测试1: 检查分页组件
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        print("✅ 虚拟化表格组件: 已加载")
        
        # 检查新的调试方法是否存在
        if hasattr(VirtualizedExpandableTable, 'debug_pagination_state'):
            print("✅ 分页状态调试方法: 已存在")
        else:
            print("❌ 分页状态调试方法: 不存在")
            
        if hasattr(VirtualizedExpandableTable, 'force_fix_row_numbers_now'):
            print("✅ 强制行号修复方法: 已存在")
        else:
            print("❌ 强制行号修复方法: 不存在")
            
    except ImportError as e:
        print(f"❌ 组件加载失败: {e}")
    
    print("\\n🚀 测试建议:")
    print("1. 启动系统: python main.py")
    print("2. 导入数据（确保数据量大于50条）")
    print("3. 切换到第2页或更高页面")
    print("4. 检查行号是否从正确的数字开始（如第2页应从51开始）")
    print("5. 测试表头排序，检查排序后行号是否保持正确")
    print("6. 查看日志中的 '🔧 [分页行号修复]' 相关信息")


if __name__ == "__main__":
    test_pagination_row_numbers()
'''
    
    with open('temp/test_pagination_fix.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 分页测试脚本已创建: temp/test_pagination_fix.py")


def main():
    """执行分页行号修复"""
    print("🚀 开始分页行号专项修复...")
    
    try:
        # 修复主窗口的分页逻辑
        if fix_pagination_logic_in_main_window():
            print("✅ 主窗口分页逻辑修复完成")
        
        # 增强表格组件的分页处理
        if enhance_pagination_state_handling():
            print("✅ 表格组件分页处理增强完成")
        
        # 创建测试脚本
        create_pagination_test_script()
        
        print("\n🎉 分页行号修复完成！")
        print("\n📋 修复内容总结：")
        print("1. ✅ 修复主窗口页码重置逻辑错误")
        print("2. ✅ 增强分页状态同步机制") 
        print("3. ✅ 添加数据设置后行号自动修复")
        print("4. ✅ 添加分页状态调试工具")
        print("5. ✅ 添加强制行号修复机制")
        
        print("\n🧪 测试步骤：")
        print("1. 运行: python temp/test_pagination_fix.py (验证修复)")
        print("2. 运行: python main.py (启动系统)")
        print("3. 导入数据，确保有多页数据")
        print("4. 切换到第2页，检查行号是否从51开始")
        print("5. 测试排序功能，确认排序后行号仍然正确")
        
        print("\n⚠️ 重点检查：")
        print("- 行号应该反映实际的记录位置，不是从1开始")
        print("- 第2页第1行应该显示51（假设每页50条）")
        print("- 排序后行号应该保持分页逻辑正确")
        print("- 查看日志中 '🔧 [分页行号修复]' 标记的信息")
        
    except Exception as e:
        print(f"❌ 分页行号修复失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 