#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合性能监控系统 - Comprehensive Performance Monitor

集成环境感知日志系统和性能分析器，提供：
1. 分页操作性能深度分析
2. 格式化管理器性能对比（原版 vs 单例版）
3. 防抖策略效果评估
4. 实时性能指标监控
5. 性能瓶颈自动识别和优化建议

针对性解决：
- 5秒分页延迟问题的根因分析
- 200ms防抖 vs 智能防抖的效果对比
- 格式化管理器重复初始化的性能影响量化

Author: Claude Code
Created: 2025-07-26
Phase: 1.3 日志系统规范化完成
"""

import time
import threading
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from contextlib import contextmanager

# 可选依赖
try:
    import psutil
    _psutil_available = True
except ImportError:
    _psutil_available = False
    # 模拟psutil接口
    class MockPsutil:
        @staticmethod
        def cpu_percent(interval=None):
            return 0.0
        @staticmethod
        def virtual_memory():
            class MockMemory:
                percent = 0.0
                used = 0
            return MockMemory()
    psutil = MockPsutil()

try:
    from src.core.environment_aware_logger import get_env_logger
    _env_logger_available = True
except ImportError:
    _env_logger_available = False
    def get_env_logger(name):
        import logging
        return logging.getLogger(name)

try:
    from src.core.performance_analyzer import PerformanceAnalyzer, PerformanceContext
    _performance_analyzer_available = True
except ImportError:
    _performance_analyzer_available = False
    # 模拟性能分析器
    class MockPerformanceAnalyzer:
        def start_operation(self, name, params=None):
            return f"mock_{time.time()}"
        def end_operation(self, operation_id, error=None):
            pass
        def analyze_pagination_performance(self):
            return {"total_operations": 0, "avg_time_ms": 0, "recommendations": ["性能分析器不可用"]}
    PerformanceAnalyzer = MockPerformanceAnalyzer

try:
    from src.core.smart_debounce import SmartDebounceManager
    _smart_debounce_available = True
except ImportError:
    _smart_debounce_available = False


@dataclass
class PerformanceComparison:
    """性能对比结果"""
    scenario_name: str
    original_time_ms: float
    optimized_time_ms: float
    improvement_percent: float
    memory_saving_mb: float
    description: str
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class BottleneckAnalysis:
    """性能瓶颈分析"""
    operation_name: str
    duration_ms: float
    cpu_usage_percent: float
    memory_usage_mb: float
    bottleneck_type: str  # CPU, Memory, IO, Network
    severity: str  # low, medium, high, critical
    optimization_suggestions: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class ComprehensivePerformanceMonitor:
    """综合性能监控系统"""
    
    def __init__(self):
        self.logger = get_env_logger("ComprehensivePerformanceMonitor")
        self.performance_analyzer = PerformanceAnalyzer()
        self.comparisons: List[PerformanceComparison] = []
        self.bottlenecks: List[BottleneckAnalysis] = []
        self._monitoring_active = False
        self._monitor_thread = None
        
        # 系统监控指标
        self.system_metrics = {
            'cpu_usage': [],
            'memory_usage': [], 
            'disk_io': [],
            'network_io': []
        }
        
        self.logger.info("综合性能监控系统初始化完成")
        
    def start_monitoring(self, interval_seconds: float = 1.0):
        """启动实时性能监控"""
        if self._monitoring_active:
            self.logger.warning("性能监控已在运行中")
            return
            
        self._monitoring_active = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_system_performance,
            args=(interval_seconds,),
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info(f"启动实时性能监控，间隔: {interval_seconds}秒")
        
    def stop_monitoring(self):
        """停止性能监控"""
        self._monitoring_active = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=2.0)
        self.logger.info("性能监控已停止")
        
    def _monitor_system_performance(self, interval: float):
        """系统性能监控线程"""
        while self._monitoring_active:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=0.1)
                
                # 内存使用情况
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # 磁盘IO（如果可用）
                try:
                    disk_io = psutil.disk_io_counters()
                    disk_usage = disk_io.read_bytes + disk_io.write_bytes
                except:
                    disk_usage = 0
                
                # 网络IO（如果可用）
                try:
                    net_io = psutil.net_io_counters()
                    network_usage = net_io.bytes_sent + net_io.bytes_recv
                except:
                    network_usage = 0
                
                # 记录指标
                current_time = datetime.now()
                self.system_metrics['cpu_usage'].append((current_time, cpu_percent))
                self.system_metrics['memory_usage'].append((current_time, memory_percent))
                self.system_metrics['disk_io'].append((current_time, disk_usage))
                self.system_metrics['network_io'].append((current_time, network_usage))
                
                # 限制历史数据长度
                max_history = 300  # 5分钟历史数据
                for metric_list in self.system_metrics.values():
                    if len(metric_list) > max_history:
                        metric_list[:] = metric_list[-max_history:]
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"系统性能监控错误: {e}")
                time.sleep(interval)
    
    @contextmanager
    def monitor_operation(self, operation_name: str, params: Dict[str, Any] = None):
        """性能监控上下文管理器"""
        # 记录开始状态
        start_time = time.time()
        start_cpu = psutil.cpu_percent()
        start_memory = psutil.virtual_memory().percent
        
        operation_id = self.performance_analyzer.start_operation(operation_name, params or {})
        
        try:
            yield
        finally:
            # 记录结束状态
            end_time = time.time()
            end_cpu = psutil.cpu_percent()
            end_memory = psutil.virtual_memory().percent
            
            duration_ms = (end_time - start_time) * 1000
            cpu_usage = max(end_cpu - start_cpu, 0)
            memory_usage = end_memory - start_memory
            
            self.performance_analyzer.end_operation(operation_id)
            
            # 分析是否为瓶颈
            self._analyze_bottleneck(operation_name, duration_ms, cpu_usage, memory_usage)
            
    def _analyze_bottleneck(self, operation_name: str, duration_ms: float, 
                          cpu_usage: float, memory_usage: float):
        """分析操作是否构成性能瓶颈"""
        
        bottleneck_type = "None"
        severity = "low"
        suggestions = []
        
        # CPU瓶颈检测
        if cpu_usage > 80:
            bottleneck_type = "CPU"
            severity = "high" if cpu_usage > 95 else "medium"
            suggestions.append("考虑优化算法复杂度或使用多线程处理")
            
        # 内存瓶颈检测
        if abs(memory_usage) > 5:  # 内存变化超过5%
            bottleneck_type = "Memory"
            severity = "high" if abs(memory_usage) > 10 else "medium"
            suggestions.append("检查内存泄漏或优化数据结构")
            
        # 时间瓶颈检测
        if duration_ms > 1000:  # 操作超过1秒
            if bottleneck_type == "None":
                bottleneck_type = "IO"
            severity = "critical" if duration_ms > 5000 else "high"
            suggestions.append("优化数据库查询或减少IO操作")
            
        # 记录瓶颈分析
        if severity != "low" or duration_ms > 500:
            bottleneck = BottleneckAnalysis(
                operation_name=operation_name,
                duration_ms=duration_ms,
                cpu_usage_percent=cpu_usage,
                memory_usage_mb=memory_usage,
                bottleneck_type=bottleneck_type,
                severity=severity,
                optimization_suggestions=suggestions
            )
            self.bottlenecks.append(bottleneck)
            
            # 实时告警
            if severity in ["high", "critical"]:
                self.logger.warning(f"检测到性能瓶颈: {operation_name} - {duration_ms:.1f}ms, CPU: {cpu_usage:.1f}%, 内存: {memory_usage:.1f}%")
                
    def compare_performance(self, scenario_name: str, 
                          original_func: Callable, optimized_func: Callable,
                          test_args: tuple = (), test_kwargs: dict = None,
                          iterations: int = 5) -> PerformanceComparison:
        """性能对比测试"""
        
        test_kwargs = test_kwargs or {}
        original_times = []
        optimized_times = []
        
        self.logger.info(f"开始性能对比测试: {scenario_name}")
        
        # 测试原版性能
        for i in range(iterations):
            with self.monitor_operation(f"{scenario_name}_original_{i}"):
                start_memory = psutil.virtual_memory().used
                start_time = time.time()
                
                try:
                    original_func(*test_args, **test_kwargs)
                except Exception as e:
                    self.logger.error(f"原版函数执行失败: {e}")
                    
                end_time = time.time()
                end_memory = psutil.virtual_memory().used
                
                duration_ms = (end_time - start_time) * 1000
                original_times.append(duration_ms)
                
        # 测试优化版性能
        for i in range(iterations):
            with self.monitor_operation(f"{scenario_name}_optimized_{i}"):
                start_memory = psutil.virtual_memory().used
                start_time = time.time()
                
                try:
                    optimized_func(*test_args, **test_kwargs)
                except Exception as e:
                    self.logger.error(f"优化版函数执行失败: {e}")
                    
                end_time = time.time()
                end_memory = psutil.virtual_memory().used
                
                duration_ms = (end_time - start_time) * 1000
                optimized_times.append(duration_ms)
        
        # 计算对比结果
        avg_original = sum(original_times) / len(original_times)
        avg_optimized = sum(optimized_times) / len(optimized_times)
        
        improvement_percent = ((avg_original - avg_optimized) / avg_original * 100) if avg_original > 0 else 0
        
        comparison = PerformanceComparison(
            scenario_name=scenario_name,
            original_time_ms=avg_original,
            optimized_time_ms=avg_optimized,
            improvement_percent=improvement_percent,
            memory_saving_mb=0,  # TODO: 实现内存对比
            description=f"经过{iterations}次测试，平均性能提升{improvement_percent:.1f}%"
        )
        
        self.comparisons.append(comparison)
        
        self.logger.info(f"性能对比完成: {scenario_name}")
        self.logger.info(f"  原版平均时间: {avg_original:.2f}ms")
        self.logger.info(f"  优化版平均时间: {avg_optimized:.2f}ms") 
        self.logger.info(f"  性能提升: {improvement_percent:.1f}%")
        
        return comparison
        
    def analyze_pagination_performance(self) -> Dict[str, Any]:
        """专门分析分页性能"""
        
        self.logger.info("开始分析分页性能...")
        
        # 获取分页相关的性能数据
        pagination_analysis = self.performance_analyzer.analyze_pagination_performance()
        
        # 添加自定义分析
        bottleneck_analysis = {
            "critical_bottlenecks": [b for b in self.bottlenecks if b.severity == "critical"],
            "high_bottlenecks": [b for b in self.bottlenecks if b.severity == "high"],
            "optimization_priorities": []
        }
        
        # 生成优化优先级建议
        all_bottlenecks = bottleneck_analysis["critical_bottlenecks"] + bottleneck_analysis["high_bottlenecks"]
        for bottleneck in all_bottlenecks:
            if "pagination" in bottleneck.operation_name.lower() or "page" in bottleneck.operation_name.lower():
                bottleneck_analysis["optimization_priorities"].extend(bottleneck.optimization_suggestions)
        
        # 合并分析结果
        comprehensive_analysis = {
            **pagination_analysis,
            "bottleneck_analysis": bottleneck_analysis,
            "performance_comparisons": [c.__dict__ for c in self.comparisons if "pagination" in c.scenario_name.lower()],
            "system_health": self._get_system_health_summary()
        }
        
        return comprehensive_analysis
        
    def _get_system_health_summary(self) -> Dict[str, Any]:
        """获取系统健康状况摘要"""
        
        if not self.system_metrics['cpu_usage']:
            return {"status": "no_data", "message": "系统监控数据不足"}
            
        # 计算最近5分钟的平均值
        recent_cpu = [cpu for _, cpu in self.system_metrics['cpu_usage'][-60:]]  # 最近60个数据点
        recent_memory = [mem for _, mem in self.system_metrics['memory_usage'][-60:]]
        
        avg_cpu = sum(recent_cpu) / len(recent_cpu) if recent_cpu else 0
        avg_memory = sum(recent_memory) / len(recent_memory) if recent_memory else 0
        
        # 健康状态评估
        health_status = "excellent"
        if avg_cpu > 70 or avg_memory > 80:
            health_status = "warning"
        if avg_cpu > 90 or avg_memory > 95:
            health_status = "critical"
            
        return {
            "status": health_status,
            "avg_cpu_percent": round(avg_cpu, 1),
            "avg_memory_percent": round(avg_memory, 1),
            "monitoring_duration_minutes": len(self.system_metrics['cpu_usage']) / 60,
            "recommendations": self._generate_health_recommendations(avg_cpu, avg_memory)
        }
        
    def _generate_health_recommendations(self, avg_cpu: float, avg_memory: float) -> List[str]:
        """生成系统健康建议"""
        recommendations = []
        
        if avg_cpu > 70:
            recommendations.append("CPU使用率较高，考虑优化计算密集型操作")
        if avg_memory > 80:
            recommendations.append("内存使用率较高，检查是否存在内存泄漏")
        if avg_cpu < 10 and avg_memory < 30:
            recommendations.append("系统资源充足，可以考虑启用更多功能特性")
            
        return recommendations
        
    def generate_comprehensive_report(self) -> str:
        """生成综合性能报告"""
        
        report = []
        report.append("=" * 80)
        report.append("综合性能监控报告")
        report.append("=" * 80)
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 性能对比总结
        if self.comparisons:
            report.append("性能优化对比:")
            report.append("-" * 40)
            for comp in self.comparisons:
                report.append(f"场景: {comp.scenario_name}")
                report.append(f"  优化前: {comp.original_time_ms:.2f}ms")
                report.append(f"  优化后: {comp.optimized_time_ms:.2f}ms")
                report.append(f"  性能提升: {comp.improvement_percent:.1f}%")
                report.append("")
        
        # 瓶颈分析
        critical_bottlenecks = [b for b in self.bottlenecks if b.severity == "critical"]
        if critical_bottlenecks:
            report.append("关键性能瓶颈:")
            report.append("-" * 40)
            for bottleneck in critical_bottlenecks[-5:]:  # 最近5个关键瓶颈
                report.append(f"操作: {bottleneck.operation_name}")
                report.append(f"  耗时: {bottleneck.duration_ms:.1f}ms")
                report.append(f"  瓶颈类型: {bottleneck.bottleneck_type}")
                report.append(f"  优化建议: {'; '.join(bottleneck.optimization_suggestions)}")
                report.append("")
        
        # 系统健康状况
        health = self._get_system_health_summary()
        report.append("系统健康状况:")
        report.append("-" * 40)
        report.append(f"状态: {health.get('status', 'unknown')}")
        report.append(f"平均CPU使用率: {health.get('avg_cpu_percent', 0)}%")
        report.append(f"平均内存使用率: {health.get('avg_memory_percent', 0)}%")
        if health.get('recommendations'):
            report.append("系统建议:")
            for rec in health['recommendations']:
                report.append(f"  - {rec}")
        
        return "\n".join(report)


# 全局性能监控实例
_global_performance_monitor: Optional[ComprehensivePerformanceMonitor] = None


def get_performance_monitor() -> ComprehensivePerformanceMonitor:
    """获取全局性能监控器"""
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = ComprehensivePerformanceMonitor()
    return _global_performance_monitor


if __name__ == "__main__":
    """测试综合性能监控系统"""
    
    print("[测试] 综合性能监控系统...")
    
    monitor = get_performance_monitor()
    
    # 启动系统监控
    monitor.start_monitoring(interval_seconds=0.5)
    
    # 模拟一些操作
    def slow_operation():
        time.sleep(0.1)
        return "slow_result"
        
    def fast_operation():
        time.sleep(0.01)
        return "fast_result"
    
    # 性能对比测试
    comparison = monitor.compare_performance(
        "operation_optimization",
        slow_operation,
        fast_operation,
        iterations=3
    )
    
    # 分页性能分析
    with monitor.monitor_operation("pagination_test", {"page": 1, "size": 50}):
        time.sleep(0.2)  # 模拟分页处理
    
    # 等待一些监控数据
    time.sleep(2)
    
    # 生成报告
    report = monitor.generate_comprehensive_report()
    print("\n" + report)
    
    # 停止监控
    monitor.stop_monitoring()
    
    print("\n[测试] 综合性能监控系统测试完成")