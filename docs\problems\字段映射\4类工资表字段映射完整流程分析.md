# 4类工资表字段映射完整流程分析

## 概述

本文档详细分析了工资系统中4类人员（离休、退休、在职、A岗）工资表的字段映射机制，包括Excel表头清理、数据库字段映射、界面显示字段的完整流程。

## 1. 字段映射三层架构

### 1.1 架构设计

字段映射采用三层架构设计：

1. **Excel原始表头层**：包含特殊字符、换行符的原始字段名
2. **数据库字段层**：标准化的英文字段名，用于数据存储
3. **显示字段层**：用户友好的中文字段名，用于界面显示

### 1.2 处理流程

```
Excel表头 → 字段清理 → 数据库字段 → 显示字段 → 界面展示
```

### 1.3 架构图

```mermaid
graph TD
    A[Excel原始表头<br/>含特殊字符、换行符] --> B[字段清理与标准化<br/>FieldMapper处理]
    B --> C[数据库字段名<br/>英文标准字段]
    C --> D[显示字段名<br/>中文友好显示]

    E[field_mappings.json<br/>配置文件] --> B
    F[DynamicTableManager<br/>表结构模板] --> C
    G[主界面显示<br/>TableWidget] --> D

    H[离休人员工资表<br/>16个字段] --> A
    I[退休人员工资表<br/>24个字段] --> A
    J[全部在职人员工资表<br/>23个字段] --> A
    K[A岗职工工资表<br/>21个字段] --> A

    style A fill:#ffcccc
    style C fill:#ccffcc
    style D fill:#ccccff
    style H fill:#ffe6cc
    style I fill:#ffe6cc
    style J fill:#ffe6cc
    style K fill:#ffe6cc
```

## 2. 4类工资表字段对应关系

### 2.1 离休人员工资表（16个字段）

| Excel原始表头 | 数据库字段名 | 主界面显示字段 | 字段类型 | 说明 |
|--------------|-------------|---------------|---------|------|
| 序号 | sequence_number | 序号 | INTEGER | 排序字段 |
| 人员代码 | employee_id | 人员代码 | TEXT | 主要匹配字段 |
| 姓名 | employee_name | 姓名 | TEXT | 员工姓名 |
| 部门名称 | department | 部门名称 | TEXT | 所属部门 |
| 基本离休费 | basic_retirement_salary | 基本离休费 | REAL | 基础离休费用 |
| 结余津贴 | balance_allowance | 结余津贴 | REAL | 结余补贴 |
| 生活补贴 | living_allowance | 生活补贴 | REAL | 生活补助 |
| 住房补贴 | housing_allowance | 住房补贴 | REAL | 住房补助 |
| 物业补贴 | property_allowance | 物业补贴 | REAL | 物业费补贴 |
| 离休补贴 | retirement_allowance | 离休补贴 | REAL | 离休专项补贴 |
| 护理费 | nursing_fee | 护理费 | REAL | 护理费用 |
| 增发一次性生活补贴 | one_time_living_allowance | 增发一次性生活补贴 | REAL | 一次性补贴 |
| 补发 | supplement | 补发 | REAL | 补发金额 |
| 合计 | total | 合计 | REAL | 总计金额 |
| 借支 | advance | 借支 | REAL | 预支金额 |
| 备注 | remarks | 备注 | TEXT | 备注信息 |

### 2.2 退休人员工资表（24个字段）

| Excel原始表头 | 数据库字段名 | 主界面显示字段 | 字段类型 | 说明 |
|--------------|-------------|---------------|---------|------|
| 序号 | sequence_number | 序号 | INTEGER | 排序字段 |
| 人员代码 | employee_id | 人员代码 | TEXT | 主要匹配字段 |
| 姓名 | employee_name | 姓名 | TEXT | 员工姓名 |
| 部门名称 | department | 部门名称 | TEXT | 所属部门 |
| 人员类别代码 | employee_type_code | 人员类别代码 | TEXT | 人员分类编码 |
| 基本退休费 | basic_retirement_salary | 基本退休费 | REAL | 基础退休费 |
| 津贴 | allowance | 津贴 | REAL | 各类津贴 |
| 结余津贴 | balance_allowance | 结余津贴 | REAL | 结余补贴 |
| 离退休生活补贴 | retirement_living_allowance | 离退休生活补贴 | REAL | 生活补助 |
| 护理费 | nursing_fee | 护理费 | REAL | 护理费用 |
| 物业补贴 | property_allowance | 物业补贴 | REAL | 物业费补贴 |
| 住房补贴 | housing_allowance | 住房补贴 | REAL | 住房补助 |
| 增资预付 | salary_advance | 增资预付 | REAL | 预付增资 |
| 2016待遇调整 | adjustment_2016 | 2016待遇调整 | REAL | 2016年调整 |
| 2017待遇调整 | adjustment_2017 | 2017待遇调整 | REAL | 2017年调整 |
| 2018待遇调整 | adjustment_2018 | 2018待遇调整 | REAL | 2018年调整 |
| 2019待遇调整 | adjustment_2019 | 2019待遇调整 | REAL | 2019年调整 |
| 2020待遇调整 | adjustment_2020 | 2020待遇调整 | REAL | 2020年调整 |
| 2021待遇调整 | adjustment_2021 | 2021待遇调整 | REAL | 2021年调整 |
| 2022待遇调整 | adjustment_2022 | 2022待遇调整 | REAL | 2022年调整 |
| 2023待遇调整 | adjustment_2023 | 2023待遇调整 | REAL | 2023年调整 |
| 补发 | supplement | 补发 | REAL | 补发金额 |
| 借支 | advance | 借支 | REAL | 预支金额 |
| 应发工资 | total_salary | 应发工资 | REAL | 应发总额 |
| 公积 | provident_fund | 公积 | REAL | 公积金 |
| 保险扣款 | insurance_deduction | 保险扣款 | REAL | 保险费扣除 |
| 备注 | remarks | 备注 | TEXT | 备注信息 |

### 2.3 全部在职人员工资表（23个字段）

| Excel原始表头 | 数据库字段名 | 主界面显示字段 | 字段类型 | 说明 |
|--------------|-------------|---------------|---------|------|
| 序号 | sequence_number | 序号 | INTEGER | 排序字段 |
| 工号 | employee_id | 工号 | TEXT | 主要匹配字段 |
| 姓名 | employee_name | 姓名 | TEXT | 员工姓名 |
| 部门名称 | department | 部门名称 | TEXT | 所属部门 |
| 人员类别代码 | employee_type_code | 人员类别代码 | TEXT | 人员分类编码 |
| 人员类别 | employee_type | 人员类别 | TEXT | 人员分类名称 |
| 2025年岗位工资 | position_salary_2025 | 2025年岗位工资 | REAL | 岗位工资 |
| 2025年薪级工资 | grade_salary_2025 | 2025年薪级工资 | REAL | 薪级工资 |
| 津贴 | allowance | 津贴 | REAL | 各类津贴 |
| 结余津贴 | balance_allowance | 结余津贴 | REAL | 结余补贴 |
| 2025年基础性绩效 | basic_performance_2025 | 2025年基础性绩效 | REAL | 基础绩效 |
| 卫生费 | health_fee | 卫生费 | REAL | 卫生费用 |
| 交通补贴 | transport_allowance | 交通补贴 | REAL | 交通补助 |
| 物业补贴 | property_allowance | 物业补贴 | REAL | 物业费补贴 |
| 住房补贴 | housing_allowance | 住房补贴 | REAL | 住房补助 |
| 车补 | car_allowance | 车补 | REAL | 车辆补贴 |
| 通讯补贴 | communication_allowance | 通讯补贴 | REAL | 通讯补助 |
| 2025年奖励性绩效预发 | performance_bonus_2025 | 2025年奖励性绩效预发 | REAL | 奖励绩效 |
| 补发 | supplement | 补发 | REAL | 补发金额 |
| 借支 | advance | 借支 | REAL | 预支金额 |
| 应发工资 | total_salary | 应发工资 | REAL | 应发总额 |
| 2025公积金 | provident_fund_2025 | 2025公积金 | REAL | 公积金 |
| 代扣代存养老保险 | pension_insurance | 代扣代存养老保险 | REAL | 养老保险 |

### 2.4 A岗职工工资表（21个字段）

| Excel原始表头 | 数据库字段名 | 主界面显示字段 | 字段类型 | 说明 |
|--------------|-------------|---------------|---------|------|
| 序号 | sequence_number | 序号 | INTEGER | 排序字段 |
| 工号 | employee_id | 工号 | TEXT | 主要匹配字段 |
| 姓名 | employee_name | 姓名 | TEXT | 员工姓名 |
| 部门名称 | department | 部门名称 | TEXT | 所属部门 |
| 人员类别 | employee_type | 人员类别 | TEXT | 人员分类名称 |
| 人员类别代码 | employee_type_code | 人员类别代码 | TEXT | 人员分类编码 |
| 2025年岗位工资 | position_salary_2025 | 2025年岗位工资 | REAL | 岗位工资 |
| 2025年校龄工资 | seniority_salary_2025 | 2025年校龄工资 | REAL | 校龄工资 |
| 津贴 | allowance | 津贴 | REAL | 各类津贴 |
| 结余津贴 | balance_allowance | 结余津贴 | REAL | 结余补贴 |
| 2025年基础性绩效 | basic_performance_2025 | 2025年基础性绩效 | REAL | 基础绩效 |
| 卫生费 | health_fee | 卫生费 | REAL | 卫生费用 |
| 2025年生活补贴 | living_allowance_2025 | 2025年生活补贴 | REAL | 生活补助 |
| 车补 | car_allowance | 车补 | REAL | 车辆补贴 |
| 2025年奖励性绩效预发 | performance_bonus_2025 | 2025年奖励性绩效预发 | REAL | 奖励绩效 |
| 补发 | supplement | 补发 | REAL | 补发金额 |
| 借支 | advance | 借支 | REAL | 预支金额 |
| 应发工资 | total_salary | 应发工资 | REAL | 应发总额 |
| 2025公积金 | provident_fund_2025 | 2025公积金 | REAL | 公积金 |
| 保险扣款 | insurance_deduction | 保险扣款 | REAL | 保险费扣除 |
| 代扣代存养老保险 | pension_insurance | 代扣代存养老保险 | REAL | 养老保险 |

## 3. 字段清理规则

Excel表头中的特殊字符处理规则：

| 特殊字符类型 | 处理方式 | 示例 |
|-------------|---------|------|
| 换行符 `\n` | 替换为空格或删除 | `"基本\n离休费"` → `"基本离休费"` |
| 多余空格 | 去除首尾空格，中间多空格合并 | `" 工号 "` → `"工号"` |
| 制表符 `\t` | 替换为空格 | `"姓名\t"` → `"姓名"` |
| 特殊符号 | 根据业务规则处理 | `"2025年岗位工资"` 保持不变 |

## 4. 字段映射处理时序图

```mermaid
sequenceDiagram
    participant E as Excel工资表
    participant F as FieldMapper
    participant A as AutoFieldMappingGenerator
    participant D as DynamicTableManager
    participant C as ConfigManager
    participant G as GUI显示

    E->>F: 1. 读取Excel表头
    Note over F: 清理特殊字符、换行符
    F->>A: 2. 生成字段映射
    A->>A: 3. 多策略映射生成
    Note over A: 精确匹配→模糊匹配→保持原名
    A->>C: 4. 保存映射配置
    Note over C: field_mappings.json
    C->>D: 5. 创建数据库表
    Note over D: 应用表结构模板
    D->>G: 6. 配置显示字段
    Note over G: 主界面表格显示
```

## 5. 涉及的配置文件和代码文件

### 5.1 核心配置文件

| 文件路径 | 作用 | 内容说明 |
|---------|------|---------|
| `state/data/field_mappings.json` | 字段映射配置 | 存储所有表的字段映射关系 |
| `config.json` | 系统配置 | 字段映射相关的全局设置 |
| `user_preferences.json` | 用户偏好 | 用户自定义的字段显示设置 |

### 5.2 核心代码文件

| 文件路径 | 功能 | 主要类/方法 |
|---------|------|------------|
| `src/modules/data_import/field_mapper.py` | 字段映射器 | `FieldMapper.map_fields()` |
| `src/modules/data_import/auto_field_mapping_generator.py` | 自动映射生成 | `AutoFieldMappingGenerator.generate_mapping()` |
| `src/modules/data_import/field_mapping_migrator.py` | 映射迁移器 | `FieldMappingMigrator.convert_excel_to_db_mapping()` |
| `src/modules/data_storage/dynamic_table_manager.py` | 动态表管理 | `DynamicTableManager.create_salary_table()` |
| `src/modules/system_config/template_manager.py` | 模板管理 | `TemplateManager.register_template()` |

## 6. 映射配置示例

```json
{
  "version": "2.0",
  "global_settings": {
    "auto_generate_mappings": true,
    "preserve_chinese_headers": true
  },
  "table_mappings": {
    "salary_data_2027_05_active_employees": {
      "field_mappings": {
        "employee_id": "工号",
        "employee_name": "姓名",
        "position_salary_2025": "2025年岗位工资"
      }
    }
  }
}
```

## 7. 问题发现与分析

### 7.1 实际问题确认

通过深入分析发现，当前系统存在一个根本性问题：

**🚨 所有4类工资表使用了完全相同的数据库结构！**

| 表类型 | 应有字段数 | 实际字段数 | 问题描述 |
|--------|-----------|-----------|---------|
| 离休人员工资表 | 16个专用字段 | 16个通用字段 | 专用字段如"基本离休费"、"护理费"丢失 |
| 退休人员工资表 | 27个专用字段 | 16个通用字段 | 历年调整字段、退休费等信息丢失 |
| 全部在职人员工资表 | 23个专用字段 | 16个通用字段 | 岗位工资、绩效工资等字段丢失 |
| A岗职工工资表 | 21个专用字段 | 16个通用字段 | 校龄工资、生活补贴等字段丢失 |

### 7.2 问题根源

1. **数据导入使用统一模板**：所有表都使用 `salary_data` 通用模板
2. **表结构创建逻辑缺陷**：没有根据Excel表头创建专用表结构
3. **字段映射配置错位**：配置中的专用字段在数据库中不存在

### 7.3 解决方案

#### 短期方案（已实施）
- ✅ 修复字段映射配置，确保现有16个通用字段正确显示中文名称
- ✅ 优化显示效果，提升用户体验

#### 长期方案（建议实施）
- 🔄 为4类工资表创建专用表结构模板
- 🔄 修改数据导入逻辑，根据表类型选择对应模板
- 🔄 重新导入数据，使用专用表结构

## 8. 总结

这个完整的字段映射流程分析揭示了：

1. **数据一致性**：当前通过统一模板实现，但丢失了专用字段信息
2. **显示友好性**：已通过字段映射修复，中文字段名正确显示
3. **扩展性**：需要建立专用表结构才能真正支持4类工资表的完整字段
4. **维护性**：配置化管理已实现，但需要与表结构保持一致

通过本次分析，我们不仅解决了当前的显示问题，还发现了系统架构层面的改进空间，为未来的系统优化指明了方向。
