#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的数据流转和格式化一致性
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_complete_format_workflow():
    """测试完整的格式化工作流程"""
    print("测试完整的格式化工作流程")
    print("=" * 50)
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 初始化格式管理器
        field_mapping_path = "state/data/field_mappings.json"
        format_config_path = "state/format_config.json"
        
        format_manager = UnifiedFormatManager(
            config_path=format_config_path,
            field_mapping_path=field_mapping_path
        )
        
        print("统一格式管理器初始化成功")
        
        # 创建模拟的离休人员数据
        test_data = {
            'employee_id': ['001', '002', '003'],
            'employee_name': ['张三', '李四', '王五'],
            'department': ['财务部', '人事部', '技术部'],
            'basic_retirement_salary': [5000.00, None, 0],
            'balance_allowance': [1200.50, np.nan, '0'],
            'living_allowance': [800.25, 'nan', 'null'],
            'housing_allowance': [2000.00, '', ' '],
            'property_allowance': [300.15, 0, '0.0'],
            'retirement_allowance': [1000.00, None, ''],
            'nursing_fee': [500.00, '800.25', None],
            'one_time_living_allowance': [2000.00, 0.0, None],
            'supplement': [0, '', None],
            'total': [12000.90, 3200.75, 0],
            'advance': [100.00, 0, None],
            'month': ['202507', '07', '7'],
            'year': ['2025', 2025, '25'],
            'remarks': ['正常', '', None],
            # 应该被隐藏的字段
            'id': [1, 2, 3],
            'sequence_number': [1, 2, 3],
            'created_at': ['2025-07-30 10:00:00', '2025-07-30 11:00:00', '2025-07-30 12:00:00'],
            'updated_at': ['2025-07-30 15:00:00', '2025-07-30 16:00:00', '2025-07-30 17:00:00']
        }
        
        original_df = pd.DataFrame(test_data)
        table_type = "retired_employees"
        
        print(f"\n1. 原始数据：{len(original_df)} 行 x {len(original_df.columns)} 列")
        print("原始列名：", list(original_df.columns))
        
        # 测试完整表格格式化
        print(f"\n2. 执行完整表格格式化...")
        formatted_headers, formatted_data = format_manager.format_table_complete(
            original_df, table_type
        )
        
        print(f"格式化后：{len(formatted_data)} 行 x {len(formatted_data.columns)} 列")
        print("格式化后的表头：", formatted_headers)
        
        # 验证隐藏字段
        print(f"\n3. 验证隐藏字段：")
        hidden_fields_check = ['自增主键', '序号', '创建时间', '更新时间']
        found_hidden = [field for field in hidden_fields_check if field in formatted_headers]
        if found_hidden:
            print(f"   警告：发现应该被隐藏的字段：{found_hidden}")
        else:
            print("   ✓ 所有隐藏字段都已正确处理")
        
        # 验证浮点数格式化
        print(f"\n4. 验证浮点数格式化：")
        float_columns = [col for col in formatted_data.columns 
                        if any(keyword in col for keyword in ['离休费', '津贴', '补贴', '护理费', '补发', '合计', '借支'])]
        
        all_correct = True
        for col in float_columns:
            values = formatted_data[col].tolist()
            print(f"   {col}: {values}")
            
            for val in values:
                if val == '0.00':
                    continue  # 正确的零值显示
                elif val == '':
                    continue  # 空值也是可以的
                else:
                    # 检查是否是正确的浮点数格式
                    try:
                        if '.' in str(val):
                            decimal_places = len(str(val).split('.')[-1])
                            if decimal_places != 2:
                                print(f"   ⚠️  {col} 中的值 {val} 小数位数不是2位")
                                all_correct = False
                    except:
                        print(f"   ⚠️  {col} 中的值 {val} 格式异常")
                        all_correct = False
        
        if all_correct:
            print("   ✓ 所有浮点数字段格式正确")
        
        # 验证月份和年份
        print(f"\n5. 验证月份和年份字段：")
        if '月份' in formatted_data.columns:
            month_values = formatted_data['月份'].tolist()
            print(f"   月份: {month_values}")
            for val in month_values:
                if val and (not isinstance(val, str) or len(val) != 2):
                    print(f"   ⚠️  月份值 {val} 格式不正确")
                    all_correct = False
        
        if '年份' in formatted_data.columns:
            year_values = formatted_data['年份'].tolist()
            print(f"   年份: {year_values}")
            for val in year_values:
                if val and not isinstance(val, str):
                    print(f"   ⚠️  年份值 {val} 不是字符串类型")
                    all_correct = False
        
        if all_correct:
            print("   ✓ 月份和年份格式正确")
        
        # 输出完整的格式化结果
        print(f"\n6. 完整的格式化结果：")
        print(formatted_data.to_string(index=False))
        
        print(f"\n✓ 完整工作流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_complete_format_workflow()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())