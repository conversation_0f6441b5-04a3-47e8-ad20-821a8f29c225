#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0级修复验证测试
测试QTimer线程安全、表头重影、列宽管理统一化的修复效果
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, QThread
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
from src.utils.thread_safe_timer import safe_single_shot

class P0FixesTestWindow(QMainWindow):
    """P0修复验证测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("P0级修复验证测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备开始P0修复验证测试...")
        layout.addWidget(self.status_label)
        
        # 创建测试表格
        self.table = VirtualizedExpandableTable()
        layout.addWidget(self.table)
        
        # 测试按钮
        self.test_button1 = QPushButton("测试1: 数据导入+列宽调整+排序")
        self.test_button1.clicked.connect(self.test_data_import_scenario)
        layout.addWidget(self.test_button1)
        
        self.test_button2 = QPushButton("测试2: 导航切换+列宽调整+排序")
        self.test_button2.clicked.connect(self.test_navigation_scenario)
        layout.addWidget(self.test_button2)
        
        self.test_button3 = QPushButton("测试3: 分页操作+QTimer检查")
        self.test_button3.clicked.connect(self.test_pagination_scenario)
        layout.addWidget(self.test_button3)
        
        # 测试结果
        self.results = {
            'qtimer_warnings': 0,
            'header_display_issues': 0,
            'column_width_resets': 0
        }
        
        # 监控控制台输出
        self.setup_console_monitoring()
    
    def setup_console_monitoring(self):
        """设置控制台输出监控"""
        # 这里可以添加日志监控逻辑
        pass
    
    def test_data_import_scenario(self):
        """测试场景1：数据导入+列宽调整+排序"""
        self.status_label.setText("🧪 测试1: 数据导入场景...")
        
        try:
            # 模拟数据导入
            test_data = [
                {'姓名': '张三', '部门': '技术部', '职位': '工程师', '工资': 8000},
                {'姓名': '李四', '部门': '销售部', '职位': '销售员', '工资': 6000},
                {'姓名': '王五', '部门': '人事部', '职位': '专员', '工资': 5000},
            ]
            
            # 设置数据（模拟数据导入）
            self.table.set_data(test_data, table_name="test_salary_table")
            
            # 等待数据设置完成
            QApplication.processEvents()
            time.sleep(0.2)
            
            # 记录初始表头状态
            initial_headers = []
            for i in range(self.table.columnCount()):
                header_text = self.table.horizontalHeaderItem(i)
                if header_text:
                    initial_headers.append(header_text.text())
                else:
                    initial_headers.append(f"列{i}")
            
            self.status_label.setText(f"🧪 初始表头: {initial_headers}")
            
            # 模拟用户调整列宽
            if self.table.columnCount() > 0:
                self.table.setColumnWidth(0, 150)  # 调整第一列宽度
                original_width = 150
                
                # 等待列宽设置完成
                QApplication.processEvents()
                time.sleep(0.1)
                
                # 模拟点击表头排序
                header = self.table.horizontalHeader()
                if header:
                    # 触发排序
                    header.sectionClicked.emit(0)
                    
                    # 等待排序完成
                    QApplication.processEvents()
                    time.sleep(0.3)
                    
                    # 检查表头状态
                    after_sort_headers = []
                    for i in range(self.table.columnCount()):
                        header_text = self.table.horizontalHeaderItem(i)
                        if header_text:
                            after_sort_headers.append(header_text.text())
                        else:
                            after_sort_headers.append(f"列{i}")
                    
                    # 检查列宽是否保持
                    current_width = self.table.columnWidth(0)
                    
                    # 验证结果
                    header_ok = all(not h.isdigit() for h in after_sort_headers)
                    width_ok = abs(current_width - original_width) < 10  # 允许小误差
                    
                    result_text = f"🧪 测试1结果:\n"
                    result_text += f"表头显示: {'✅正常' if header_ok else '❌异常'} {after_sort_headers}\n"
                    result_text += f"列宽保持: {'✅正常' if width_ok else '❌异常'} ({original_width} -> {current_width})"
                    
                    self.status_label.setText(result_text)
                    
                    if not header_ok:
                        self.results['header_display_issues'] += 1
                    if not width_ok:
                        self.results['column_width_resets'] += 1
            
        except Exception as e:
            self.status_label.setText(f"❌ 测试1失败: {e}")
    
    def test_navigation_scenario(self):
        """测试场景2：导航切换+列宽调整+排序"""
        self.status_label.setText("🧪 测试2: 导航切换场景...")
        
        try:
            # 模拟导航切换到不同表
            test_data = [
                {'员工ID': 'E001', '姓名': '赵六', '基本工资': 7000, '绩效奖金': 1000},
                {'员工ID': 'E002', '姓名': '钱七', '基本工资': 6500, '绩效奖金': 800},
            ]
            
            # 设置数据（模拟导航切换）
            self.table.set_data(test_data, table_name="test_performance_table")
            
            # 等待数据设置完成
            QApplication.processEvents()
            time.sleep(0.2)
            
            # 模拟用户调整列宽
            if self.table.columnCount() > 1:
                self.table.setColumnWidth(1, 200)  # 调整第二列宽度
                original_width = 200
                
                # 等待列宽设置完成
                QApplication.processEvents()
                time.sleep(0.1)
                
                # 模拟点击表头排序
                header = self.table.horizontalHeader()
                if header:
                    header.sectionClicked.emit(1)
                    
                    # 等待排序完成
                    QApplication.processEvents()
                    time.sleep(0.3)
                    
                    # 检查列宽是否保持
                    current_width = self.table.columnWidth(1)
                    width_ok = abs(current_width - original_width) < 10
                    
                    result_text = f"🧪 测试2结果:\n"
                    result_text += f"列宽保持: {'✅正常' if width_ok else '❌异常'} ({original_width} -> {current_width})"
                    
                    self.status_label.setText(result_text)
                    
                    if not width_ok:
                        self.results['column_width_resets'] += 1
            
        except Exception as e:
            self.status_label.setText(f"❌ 测试2失败: {e}")
    
    def test_pagination_scenario(self):
        """测试场景3：分页操作+QTimer检查"""
        self.status_label.setText("🧪 测试3: 分页操作场景...")
        
        try:
            # 创建大量数据触发分页
            test_data = []
            for i in range(100):
                test_data.append({
                    '序号': i+1,
                    '姓名': f'员工{i+1:03d}',
                    '部门': f'部门{(i%5)+1}',
                    '工资': 5000 + (i * 100)
                })
            
            # 设置数据
            self.table.set_data(test_data, table_name="test_large_table")
            
            # 等待数据设置完成
            QApplication.processEvents()
            time.sleep(0.5)
            
            # 模拟分页操作
            if hasattr(self.table, 'pagination_manager'):
                # 触发分页操作
                for page in range(3):
                    # 模拟点击下一页
                    QApplication.processEvents()
                    time.sleep(0.2)
            
            # 检查是否有QTimer警告（这里需要实际的日志监控）
            result_text = f"🧪 测试3结果:\n"
            result_text += f"分页操作: ✅完成\n"
            result_text += f"QTimer警告: {self.results['qtimer_warnings']} 个"
            
            self.status_label.setText(result_text)
            
        except Exception as e:
            self.status_label.setText(f"❌ 测试3失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = P0FixesTestWindow()
    test_window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
