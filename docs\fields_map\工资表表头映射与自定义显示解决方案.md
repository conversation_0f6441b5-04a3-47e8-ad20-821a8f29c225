# 工资表表头映射与自定义显示解决方案

## 问题背景

当前系统在处理工资表数据时存在以下问题：

1. **表头映射不一致**：Excel导入时的表头映射没有正确保存或应用到主界面显示
2. **固定表头限制**：主界面列表区域使用固定表头，无法适应不同工资表的实际结构
3. **数据库字段与显示名不匹配**：数据库使用英文字段名，而界面需要显示中文名
4. **用户自定义需求**：用户需要能够自定义选择显示哪些字段

## 期望效果

1. 在数据导入时，自动将Excel文档中4个sheet的表头进行映射，并正确显示在主界面列表区域
2. 主界面列表区域表头可以进行定制，用户可以选择自己常用的表头显示
3. 按需从数据库读取相应数据记录，不显示的记录不需要读取

## 解决方案架构

```mermaid
graph TB
    A[Excel数据导入] --> B[表头映射提取与保存]
    B --> C[数据库字段映射]
    C --> D[主界面表头配置]
    D --> E[用户自定义表头]
    E --> F[按需数据加载]
    F --> G[动态表头显示]
```

## 详细实现方案

### 1. 表头映射提取与保存

在Excel导入过程中，需要提取表头并保存映射关系：

```python
def _process_sheet_data(self, df, sheet_name, year=None, month=None):
    # 现有代码...
    
    # 获取Excel原始表头
    excel_headers = df.columns.tolist()
    
    # 获取实际数据库字段名
    table_name = self._generate_table_name(sheet_name, year, month)
    actual_db_fields = self._get_actual_db_fields(table_name)
    
    # 生成并保存字段映射
    field_mapping = self._generate_field_mapping(excel_headers, actual_db_fields, table_name)
    
    # 保存映射到配置
    self.config_sync_manager.save_mapping(
        table_name=table_name,
        mapping=field_mapping,
        metadata={"source": "excel_import", "sheet_name": sheet_name}
    )
    
    # 发送映射更新信号
    self.mapping_updated.emit(table_name)
    
    # 继续现有代码...
```

### 2. 获取实际数据库字段

添加方法获取数据库表的实际字段：

```python
def _get_actual_db_fields(self, table_name):
    """获取数据库表的实际字段名"""
    try:
        # 使用数据库管理器获取表结构
        query = f"PRAGMA table_info({table_name})"
        result = self.db_manager.execute_query(query)
        
        # 提取字段名
        db_fields = [row[1] for row in result]  # 字段名在第二列
        return db_fields
    except Exception as e:
        self.logger.error(f"获取表 {table_name} 字段结构失败: {e}")
        return []
```

### 3. 智能字段映射生成

创建智能字段映射生成器：

```python
class AutoFieldMappingGenerator:
    """自动字段映射生成器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.logger = setup_logger(__name__)
        
        # 常见字段映射规则
        self.common_mappings = {
            # 员工ID相关
            'employee_id': ['工号', '职工编号', '员工号', '编号'],
            'employee_name': ['姓名', '职工姓名', '员工姓名', '名字'],
            'department': ['部门', '部门名称', '所属部门'],
            'position': ['职位', '岗位', '职务'],
            'id_card': ['身份证号', '身份证', '证件号'],
            
            # 工资相关
            'basic_salary': ['基本工资', '基础工资', '岗位工资'],
            'performance_bonus': ['绩效工资', '绩效奖金', '奖励性绩效'],
            'allowance': ['津贴', '津贴补贴', '补贴'],
            'overtime_pay': ['加班费', '加班工资'],
            'deduction': ['扣款', '扣除', '扣减'],
            'total_salary': ['应发工资', '工资合计', '应发合计'],
            
            # 其他
            'year': ['年份', '年度'],
            'month': ['月份', '月度']
        }
    
    def generate_mapping(self, excel_headers, db_fields, table_name):
        """生成字段映射"""
        mapping = {}
        
        # 1. 直接匹配：Excel表头与数据库字段完全相同
        for excel_col in excel_headers:
            if excel_col in db_fields:
                mapping[excel_col] = excel_col
        
        # 2. 智能匹配：根据常见映射规则
        for db_field, possible_names in self.common_mappings.items():
            if db_field in db_fields:  # 确保数据库有此字段
                for excel_col in excel_headers:
                    if excel_col in possible_names and excel_col not in mapping:
                        mapping[db_field] = excel_col
                        break
        
        # 3. 默认映射：对于未匹配的数据库字段，使用其本身作为显示名
        for db_field in db_fields:
            if db_field not in mapping.keys():
                # 尝试将下划线分隔的字段名转为更友好的显示
                display_name = db_field.replace('_', ' ').title()
                mapping[db_field] = display_name
        
        return mapping
```

### 4. 主界面表头配置与显示

修改表格组件，支持动态表头：

```python
def set_data(self, data, headers=None, current_table_name=None):
    """设置表格数据并应用字段映射"""
    self.current_table_name = current_table_name
    
    # 保存原始数据和表头
    self.original_data = data
    self.original_headers = headers or (data.columns.tolist() if hasattr(data, 'columns') else [])
    
    # 应用字段映射到表头
    self.displayed_headers = self._apply_field_mapping(self.original_headers)
    
    # 设置表格列数和表头
    self.setColumnCount(len(self.displayed_headers))
    self.setHorizontalHeaderLabels(self.displayed_headers)
    
    # 设置数据
    # ... 现有数据填充代码 ...

def _apply_field_mapping(self, headers):
    """应用字段映射到表头"""
    if not hasattr(self, 'current_table_name') or not self.current_table_name:
        return headers
    
    try:
        # 从ConfigSyncManager获取字段映射
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        config_sync = ConfigSyncManager()
        mapping = config_sync.load_mapping(self.current_table_name)
        
        if not mapping:
            return headers
        
        # 应用映射
        mapped_headers = []
        for header in headers:
            # 如果有映射则使用映射值，否则保持原样
            mapped_headers.append(mapping.get(header, header))
        
        return mapped_headers
    except Exception as e:
        print(f"应用字段映射失败: {e}")
        return headers
```

### 5. 用户自定义表头功能

添加用户自定义表头的功能：

```python
def _setup_custom_header_menu(self):
    """设置自定义表头菜单"""
    self.header_menu = QMenu(self)
    self.header_menu.setTitle("自定义表头")
    
    # 添加到主菜单
    self.menubar.addMenu(self.header_menu)
    
    # 添加自定义表头动作
    customize_action = QAction("配置显示字段", self)
    customize_action.triggered.connect(self._show_header_customization_dialog)
    self.header_menu.addAction(customize_action)
    
    # 添加重置表头动作
    reset_action = QAction("重置为默认表头", self)
    reset_action.triggered.connect(self._reset_headers)
    self.header_menu.addAction(reset_action)

def _show_header_customization_dialog(self):
    """显示表头自定义对话框"""
    if not self.current_table_name:
        QMessageBox.information(self, "提示", "请先选择一个表格")
        return
    
    # 获取当前表的所有可用字段
    all_fields = self._get_all_available_fields(self.current_table_name)
    if not all_fields:
        QMessageBox.warning(self, "错误", "无法获取表格字段")
        return
    
    # 获取当前显示的字段
    current_fields = self._get_current_displayed_fields()
    
    # 创建并显示自定义对话框
    from src.gui.dialogs import HeaderCustomizationDialog
    dialog = HeaderCustomizationDialog(
        all_fields=all_fields,
        selected_fields=current_fields,
        parent=self
    )
    
    if dialog.exec_() == QDialog.Accepted:
        # 获取用户选择的字段
        selected_fields = dialog.get_selected_fields()
        
        # 保存用户自定义配置
        self._save_user_header_preference(selected_fields)
        
        # 刷新表格显示
        self._reload_current_table_data()
```

### 6. 表头自定义对话框

创建表头自定义对话框：

```python
class HeaderCustomizationDialog(QDialog):
    """表头自定义对话框"""
    
    def __init__(self, all_fields, selected_fields, parent=None):
        super().__init__(parent)
        self.all_fields = all_fields
        self.selected_fields = selected_fields
        
        self.setWindowTitle("自定义表头显示")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        self._init_ui()
    
    def _init_ui(self):
        layout = QVBoxLayout()
        
        # 说明标签
        label = QLabel("请选择要显示的字段（勾选表示显示）：")
        layout.addWidget(label)
        
        # 创建字段列表
        self.fields_list = QListWidget()
        self.fields_list.setSelectionMode(QListWidget.MultiSelection)
        
        # 添加所有字段到列表
        for field, display_name in self.all_fields.items():
            item = QListWidgetItem(f"{display_name} ({field})")
            item.setData(Qt.UserRole, field)
            item.setCheckState(
                Qt.Checked if field in self.selected_fields else Qt.Unchecked
            )
            self.fields_list.addItem(item)
        
        layout.addWidget(self.fields_list)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self._select_all)
        button_layout.addWidget(select_all_btn)
        
        deselect_all_btn = QPushButton("取消全选")
        deselect_all_btn.clicked.connect(self._deselect_all)
        button_layout.addWidget(deselect_all_btn)
        
        button_layout.addStretch()
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def _select_all(self):
        """全选所有字段"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            item.setCheckState(Qt.Checked)
    
    def _deselect_all(self):
        """取消全选"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            item.setCheckState(Qt.Unchecked)
    
    def get_selected_fields(self):
        """获取用户选择的字段"""
        selected = []
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            if item.checkState() == Qt.Checked:
                field = item.data(Qt.UserRole)
                selected.append(field)
        return selected
```

### 7. 用户表头偏好保存与加载

添加用户表头偏好的保存和加载功能：

```python
def save_user_header_preference(self, table_name, selected_fields):
    """保存用户表头显示偏好"""
    try:
        # 获取当前用户配置
        user_config = self.get_user_config()
        
        # 确保header_preferences存在
        if 'header_preferences' not in user_config:
            user_config['header_preferences'] = {}
        
        # 保存表头偏好
        user_config['header_preferences'][table_name] = selected_fields
        
        # 写入配置文件
        self.save_user_config(user_config)
        return True
    except Exception as e:
        self.logger.error(f"保存用户表头偏好失败: {e}")
        return False

def get_user_header_preference(self, table_name):
    """获取用户表头显示偏好"""
    try:
        user_config = self.get_user_config()
        
        # 如果有用户偏好，返回用户偏好
        if 'header_preferences' in user_config and table_name in user_config['header_preferences']:
            return user_config['header_preferences'][table_name]
        
        # 否则返回None，表示使用默认全部字段
        return None
    except Exception as e:
        self.logger.error(f"获取用户表头偏好失败: {e}")
        return None
```

### 8. 按需加载数据

修改数据加载逻辑，支持按需加载字段：

```python
def _load_table_data(self, table_name):
    """加载表格数据，支持按需加载字段"""
    try:
        # 获取用户表头偏好
        user_fields = self.config_manager.get_user_header_preference(table_name)
        
        # 构建SQL查询
        if user_fields:
            # 如果有用户偏好，只加载选定字段
            fields_str = ", ".join(user_fields)
            query = f"SELECT {fields_str} FROM {table_name}"
        else:
            # 否则加载所有字段
            query = f"SELECT * FROM {table_name}"
        
        # 执行查询
        result = self.db_manager.execute_query(query)
        
        # 获取列名
        if user_fields:
            headers = user_fields
        else:
            # 从查询结果获取列名
            cursor_description = self.db_manager.get_cursor_description()
            headers = [col[0] for col in cursor_description]
        
        # 转换为DataFrame
        import pandas as pd
        df = pd.DataFrame(result, columns=headers)
        
        # 设置表格数据
        self.data_table.set_data(df, headers, current_table_name=table_name)
        
        # 更新状态
        self.current_table_name = table_name
        self.statusBar().showMessage(f"已加载表 {table_name}，共 {len(df)} 条记录")
        
        return True
    except Exception as e:
        self.logger.error(f"加载表 {table_name} 数据失败: {e}")
        self.statusBar().showMessage(f"加载表 {table_name} 数据失败")
        return False
```

## 实现流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Excel as Excel导入
    participant Mapper as 字段映射器
    participant DB as 数据库
    participant Config as 配置管理器
    participant UI as 主界面

    User->>Excel: 导入Excel文件
    Excel->>DB: 创建数据表
    Excel->>DB: 获取实际数据库字段
    Excel->>Mapper: 生成字段映射
    Mapper->>Config: 保存字段映射配置
    
    User->>UI: 查看数据表
    UI->>Config: 获取用户表头偏好
    UI->>Config: 获取字段映射
    UI->>DB: 按需查询数据
    UI->>User: 显示带有中文表头的数据
    
    User->>UI: 自定义表头
    UI->>User: 显示字段选择对话框
    User->>UI: 选择要显示的字段
    UI->>Config: 保存用户表头偏好
    UI->>DB: 重新按需查询数据
    UI->>User: 显示自定义表头的数据
```

## 实施步骤

1. **创建自动字段映射生成器**
   - 实现 `AutoFieldMappingGenerator` 类
   - 添加智能映射规则

2. **增强数据导入流程**
   - 修改 `MultiSheetImporter` 类
   - 添加数据库字段检测和映射生成

3. **改进表格组件**
   - 修改 `VirtualizedExpandableTable` 类
   - 添加字段映射应用功能

4. **添加用户自定义功能**
   - 创建表头自定义对话框
   - 实现用户偏好保存和加载

5. **优化数据加载**
   - 修改数据查询逻辑
   - 实现按需加载字段

## 测试验证

1. **导入测试**
   - 导入Excel文件，验证表头映射是否正确保存
   - 检查数据库字段与Excel表头的映射关系

2. **显示测试**
   - 验证主界面表头是否正确显示中文
   - 测试不同表格切换时表头是否正确更新

3. **自定义测试**
   - 测试表头自定义功能
   - 验证用户偏好是否正确保存和应用

4. **性能测试**
   - 测试大数据量下的按需加载性能
   - 验证字段映射对系统性能的影响

## 总结

本解决方案通过以下几个关键步骤解决了工资表表头映射与自定义显示的问题：

1. 在Excel导入时自动提取和保存表头映射关系
2. 使用智能字段映射生成器创建数据库字段到显示名的映射
3. 改进表格组件，支持动态应用字段映射
4. 添加用户自定义表头功能，允许用户选择显示的字段
5. 优化数据加载逻辑，支持按需加载字段

通过这些改进，系统将能够正确处理Excel导入的表头映射，并支持用户自定义表头显示，满足用户需求。