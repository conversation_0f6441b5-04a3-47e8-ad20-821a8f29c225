#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt定时器修复应用脚本

应用线程安全的定时器管理和清理分散的导入。

创建时间: 2025-07-25
"""

import sys
import os
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def apply_timer_fixes():
    """应用定时器修复"""
    
    print("🔧 [修复应用] 开始应用Qt定时器修复...")
    
    target_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        # 读取文件内容
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = f"{target_file}.backup.timer_fix"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建备份文件: {backup_file}")
        
        # 应用修复
        modified_content = content
        
        # 1. 移除分散的QTimer导入
        print("🔧 [修复1] 移除分散的QTimer导入...")
        
        # 要移除的导入模式
        patterns_to_remove = [
            r'\s*from PyQt5\.QtCore import QTimer\s*\n',
            r'\s*from PyQt5\.QtCore import QTimer, QThread, QTimer\s*\n',
            r'\s*from PyQt5\.QtCore import Qt, QThread, QTimer\s*\n'
        ]
        
        for pattern in patterns_to_remove:
            matches = re.findall(pattern, modified_content)
            if matches:
                print(f"  - 移除模式: {pattern}")
                print(f"    找到 {len(matches)} 个匹配")
                modified_content = re.sub(pattern, '', modified_content)
        
        # 2. 在文件顶部添加线程安全定时器管理器导入
        print("🔧 [修复2] 添加线程安全定时器管理器...")
        
        # 找到现有导入的结束位置
        import_end_pattern = r'(from PyQt5\.QtGui import[^)]*\))'
        import_end_match = re.search(import_end_pattern, modified_content, re.DOTALL)
        
        if import_end_match:
            insert_position = import_end_match.end()
            
            timer_manager_import = """

# 🆕 [Qt定时器修复] 线程安全定时器管理
from temp.thread_safe_timer_manager import get_timer_manager, safe_single_shot
"""
            
            modified_content = (modified_content[:insert_position] + 
                              timer_manager_import + 
                              modified_content[insert_position:])
            print("  ✅ 已添加线程安全定时器管理器导入")
        
        # 3. 替换不安全的QTimer.singleShot调用
        print("🔧 [修复3] 替换不安全的QTimer.singleShot调用...")
        
        # 查找所有QTimer.singleShot调用
        singleshot_pattern = r'QTimer\.singleShot\((\d+),\s*([^)]+)\)'
        singleshot_matches = re.findall(singleshot_pattern, modified_content)
        
        print(f"  找到 {len(singleshot_matches)} 个QTimer.singleShot调用")
        for delay, callback in singleshot_matches:
            print(f"    - 延迟: {delay}ms, 回调: {callback[:50]}...")
        
        # 替换为线程安全版本
        def replace_singleshot(match):
            delay = match.group(1)
            callback = match.group(2)
            return f'safe_single_shot({delay}, {callback})'
        
        modified_content = re.sub(singleshot_pattern, replace_singleshot, modified_content)
        print("  ✅ 已替换为safe_single_shot调用")
        
        # 4. 为定时器变量添加清理机制
        print("🔧 [修复4] 添加定时器清理机制...")
        
        # 在__init__方法中初始化定时器管理器
        init_pattern = r'(def __init__\(self[^)]*\):[^:]*?\n\s*super\(\).__init__\([^)]*\)\s*\n)'
        init_match = re.search(init_pattern, modified_content, re.DOTALL)
        
        if init_match:
            init_end = init_match.end()
            timer_init_code = """
        # 🆕 [Qt定时器修复] 初始化线程安全定时器管理
        self._timer_manager = get_timer_manager()
        self._timer_ids = set()  # 跟踪创建的定时器ID
"""
            modified_content = (modified_content[:init_end] + 
                              timer_init_code + 
                              modified_content[init_end:])
            print("  ✅ 已在__init__中添加定时器管理器初始化")
        
        # 5. 添加清理方法到 closeEvent 或析构函数
        cleanup_method = """
    def _cleanup_timers(self):
        \"\"\"🆕 [Qt定时器修复] 清理所有定时器\"\"\"
        try:
            if hasattr(self, '_timer_manager') and hasattr(self, '_timer_ids'):
                for timer_id in self._timer_ids.copy():
                    self._timer_manager.stop_timer(timer_id)
                    self._timer_ids.discard(timer_id)
                self.logger.debug("🆕 [定时器清理] 所有定时器已清理")
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"🆕 [定时器清理] 清理定时器时出错: {e}")

"""
        
        # 在文件末尾添加清理方法
        class_end_pattern = r'(\n    def [^:]+:[^}]+?)(\n\nclass|\n\n# |\Z)'
        if re.search(class_end_pattern, modified_content, re.DOTALL):
            # 在类的最后一个方法后添加
            modified_content = re.sub(
                r'(\n)(class |\n# |\Z)', 
                cleanup_method + r'\1\2', 
                modified_content
            )
            print("  ✅ 已添加定时器清理方法")
        
        # 写入修改后的文件
        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 修复已应用到: {target_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用修复时出错: {e}")
        return False

def verify_fixes():
    """验证修复效果"""
    
    print("\n🔍 [验证] 验证修复效果...")
    
    target_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查分散导入是否已清理
        scattered_imports = re.findall(r'from PyQt5\.QtCore import.*QTimer', content)
        scattered_count = len([imp for imp in scattered_imports if 'QTimer, QThread, QTimer' in imp])
        
        print(f"📊 剩余分散QTimer导入: {scattered_count} 个")
        
        # 检查safe_single_shot使用
        safe_calls = len(re.findall(r'safe_single_shot\(', content))
        old_calls = len(re.findall(r'QTimer\.singleShot\(', content))
        
        print(f"📊 safe_single_shot调用: {safe_calls} 个")
        print(f"📊 剩余QTimer.singleShot调用: {old_calls} 个")
        
        # 检查清理机制
        has_cleanup = '_cleanup_timers' in content
        has_timer_manager = '_timer_manager' in content
        
        print(f"📊 定时器清理机制: {'✅' if has_cleanup else '❌'}")
        print(f"📊 定时器管理器: {'✅' if has_timer_manager else '❌'}")
        
        if scattered_count == 0 and safe_calls > 0 and has_cleanup and has_timer_manager:
            print("✅ 所有修复验证通过")
            return True
        else:
            print("⚠️ 部分修复可能需要手动调整")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 [Qt定时器修复] 开始应用修复...")
    
    # 应用修复
    if apply_timer_fixes():
        print("✅ 修复应用成功")
    else:
        print("❌ 修复应用失败")
        return
    
    # 验证修复效果
    if verify_fixes():
        print("✅ 修复验证通过")
    else:
        print("⚠️ 修复验证有问题，请检查")
    
    print("\n📋 [后续步骤]:")
    print("1. 测试应用程序运行，确认QBasicTimer警告消除")
    print("2. 检查日志中是否还有定时器相关错误")
    print("3. 如有问题，可使用备份文件恢复")
    
    print("\n✅ Qt定时器修复应用完毕")

if __name__ == "__main__":
    main() 