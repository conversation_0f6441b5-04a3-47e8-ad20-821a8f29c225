#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试离休人员工资表格式化功能
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_format_renderer():
    """测试FormatRenderer的浮点数格式化"""
    print("测试FormatRenderer的浮点数格式化功能")
    print("=" * 50)
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化组件
        config_path = "state/format_config.json"
        field_mapping_path = "state/data/field_mappings.json"
        
        format_config = FormatConfig(config_path)
        field_registry = FieldRegistry(field_mapping_path)
        renderer = FormatRenderer(format_config, field_registry)
        
        print("组件初始化成功")
        
        # 测试各种浮点数值
        test_values = [
            None,
            np.nan, 
            0,
            0.0,
            '',
            '  ',
            'None',
            'nan',
            'null',
            5000.00,
            1234.567,
            '2000.50'
        ]
        
        print("\n测试浮点数格式化:")
        for i, value in enumerate(test_values):
            result = renderer.render_value(value, 'float')
            print(f"  {i+1:2d}. {repr(value):<15} -> '{result}'")
        
        # 测试月份字符串
        print("\n测试月份字符串格式化:")
        month_values = ['202507', '25年07月', '07', '7', 2507]
        for i, value in enumerate(month_values):
            result = renderer.render_value(value, 'month_string')
            print(f"  {i+1:2d}. {repr(value):<15} -> '{result}'")
        
        # 测试年份字符串
        print("\n测试年份字符串格式化:")
        year_values = ['2025', 2025, '25', 25, '2025年']
        for i, value in enumerate(year_values):
            result = renderer.render_value(value, 'year_string')
            print(f"  {i+1:2d}. {repr(value):<15} -> '{result}'")
        
        print("\n测试完成!")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_format_renderer()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())