# max_visible_rows 功能实现文档

## 概述

为 `VirtualizedExpandableTable` 组件添加了 `max_visible_rows` 属性，用于控制表格的最大可见行数，以优化大数据量场景下的性能表现。

## 功能特性

### 1. 构造函数参数化
- 支持在创建表格实例时指定 `max_visible_rows` 参数
- 默认值为 1000 行

```python
# 使用默认值
table = VirtualizedExpandableTable()

# 指定自定义值
table = VirtualizedExpandableTable(max_visible_rows=500)
```

### 2. 动态设置和获取
- `set_max_visible_rows(max_rows: int) -> bool`: 设置最大可见行数
- `get_max_visible_rows() -> int`: 获取当前最大可见行数

```python
# 设置最大可见行数
success = table.set_max_visible_rows(200)
print(f"设置成功: {success}")

# 获取当前值
current_max = table.get_max_visible_rows()
print(f"当前最大可见行数: {current_max}")
```

### 3. 参数验证
- 最大可见行数必须大于 0
- 小于 10 时会发出警告
- 设置无效值时返回 False

```python
# 无效值示例
success = table.set_max_visible_rows(-1)  # 返回 False
success = table.set_max_visible_rows(0)   # 返回 False
```

### 4. 自动调整功能
- `auto_adjust_max_visible_rows(data_count: int) -> bool`: 根据数据量自动调整

自动调整策略：
- ≤ 100 行：显示全部
- 100-1000 行：显示 50%
- 1000-10000 行：显示 10%
- > 10000 行：最多显示 1000 行

```python
# 自动调整示例
success = table.auto_adjust_max_visible_rows(2000)
print(f"调整成功: {success}")
```

### 5. 数据设置时自动调整
- `set_data()` 方法增加了 `auto_adjust_visible_rows` 参数
- 默认启用自动调整功能

```python
# 设置数据时自动调整
table.set_data(data, headers, auto_adjust_visible_rows=True)

# 禁用自动调整
table.set_data(data, headers, auto_adjust_visible_rows=False)
```

### 6. 性能统计集成
- 性能统计中包含 `max_visible_rows` 信息

```python
stats = table.get_performance_stats()
print(f"最大可见行数: {stats['max_visible_rows']}")
print(f"总行数: {stats['total_rows']}")
```

## 实现细节

### 修改的文件
- `src/gui/prototype/widgets/virtualized_expandable_table.py`

### 新增的方法
1. `__init__(parent=None, max_visible_rows: int = 1000)` - 修改构造函数
2. `set_max_visible_rows(max_rows: int) -> bool` - 设置方法
3. `get_max_visible_rows() -> int` - 获取方法
4. `auto_adjust_max_visible_rows(data_count: int) -> bool` - 自动调整方法

### 修改的方法
1. `set_data()` - 增加自动调整参数
2. `get_performance_stats()` - 包含 max_visible_rows 信息
3. `_populate_visible_data()` - 使用 max_visible_rows 限制渲染

## 使用示例

```python
#!/usr/bin/env python3
import sys
from PyQt5.QtWidgets import QApplication
from virtualized_expandable_table import VirtualizedExpandableTable, create_sample_data

# 创建应用
app = QApplication(sys.argv)

# 创建表格，指定最大可见行数
table = VirtualizedExpandableTable(max_visible_rows=500)

# 创建大量测试数据
data, headers = create_sample_data(2000)

# 设置数据，启用自动调整
table.set_data(data, headers, auto_adjust_visible_rows=True)

# 查看性能统计
stats = table.get_performance_stats()
print(f"性能统计: {stats}")

# 手动调整
table.set_max_visible_rows(300)
print(f"调整后最大可见行数: {table.get_max_visible_rows()}")
```

## 性能影响

### 内存优化
- 只渲染指定数量的可见行，减少内存占用
- 大数据量场景下内存使用更加可控

### 渲染性能
- 减少了需要渲染的 DOM 元素数量
- 提高了表格滚动和交互的响应速度

### 建议配置
- 小型数据集（< 1000 行）：使用默认值或更大值
- 中型数据集（1000-10000 行）：建议 500-1000
- 大型数据集（> 10000 行）：建议 200-500

## 注意事项

1. **数据一致性**: `max_visible_rows` 只影响渲染，不影响数据存储
2. **功能兼容性**: 所有现有功能（编辑、选择、展开等）保持正常
3. **自动调整**: 建议在数据量变化时使用自动调整功能
4. **性能监控**: 可通过性能统计监控内存和渲染性能

## 测试验证

```bash
# 运行测试脚本
python test_max_visible_rows.py

# 快速验证
python -c "from test_script import test_basic_functionality; test_basic_functionality()"
```

## 版本信息
- 实现版本: v1.0.0
- 实现日期: 2025-06-19
- 作者: PyQt5重构项目组
- 兼容性: Python 3.9+, PyQt5 