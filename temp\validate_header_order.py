#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证表头顺序修复效果

临时验证脚本，检查field_mappings.json中的字段顺序是否已正确修复
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

def validate_employee_type_order():
    """验证人员类别字段的顺序"""
    print("=== 表头顺序验证 ===")
    
    # 读取配置文件
    config_path = Path("state/data/field_mappings.json")
    if not config_path.exists():
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    table_mappings = config_data.get("table_mappings", {})
    
    # 重点检查问题表
    target_tables = [
        "active_employees",
        "salary_data_2025_08_active_employees"
    ]
    
    all_passed = True
    
    for table_name in target_tables:
        if table_name not in table_mappings:
            print(f"⚠️  表 {table_name} 不存在")
            continue
            
        table_config = table_mappings[table_name]
        if "field_mappings" not in table_config:
            print(f"⚠️  表 {table_name} 缺少 field_mappings")
            continue
        
        field_mappings = table_config["field_mappings"]
        field_order = list(field_mappings.keys())
        
        # 检查人员类别字段顺序
        if "employee_type" in field_order and "employee_type_code" in field_order:
            pos_type = field_order.index("employee_type")
            pos_code = field_order.index("employee_type_code")
            
            print(f"\n📋 表: {table_name}")
            print(f"   人员类别 位置: {pos_type + 1}")
            print(f"   人员类别代码 位置: {pos_code + 1}")
            
            if pos_type < pos_code:
                print(f"   ✅ 顺序正确: 人员类别 在 人员类别代码 之前")
            else:
                print(f"   ❌ 顺序错误: 人员类别 在 人员类别代码 之后")
                all_passed = False
        else:
            print(f"\n📋 表: {table_name}")
            print(f"   ⚠️  缺少人员类别相关字段")
    
    # 检查display_order配置
    print(f"\n🔍 检查display_order配置:")
    for table_name in target_tables:
        if table_name in table_mappings:
            table_config = table_mappings[table_name]
            if "display_order" in table_config:
                display_order = table_config["display_order"]
                if "employee_type" in display_order and "employee_type_code" in display_order:
                    pos_type = display_order.index("employee_type")
                    pos_code = display_order.index("employee_type_code")
                    
                    print(f"   {table_name}: display_order 中人员类别({pos_type+1}) vs 人员类别代码({pos_code+1})")
                    if pos_type < pos_code:
                        print(f"      ✅ display_order 顺序正确")
                    else:
                        print(f"      ❌ display_order 顺序错误")
                        all_passed = False
    
    print(f"\n🎯 总体验证结果: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    return all_passed

if __name__ == "__main__":
    validate_employee_type_order()