#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一格式管理系统 - 响应式更新示例

演示如何使用新架构的事件驱动和响应式更新功能。
展示配置变更如何自动触发相关组件的更新。

🎯 [新架构特性]:
- 事件驱动架构：配置变更时自动发布事件
- 响应式更新：组件自动响应配置变更
- 状态同步：统一状态管理器保持状态一致
- 依赖注入：组件通过依赖注入获取服务

使用场景：
- 配置文件热更新
- 字段映射动态调整
- 格式规则实时变更
- 系统状态监控

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import asyncio
import time
import pandas as pd
from typing import Dict, Any, List
import json
from pathlib import Path

# 导入统一格式管理系统
from src.modules.format_management import (
    UnifiedFormatManager, FormatEventType, FormatEventBuilder,
    LoggingFormatEventHandler, MetricsFormatEventHandler
)
from src.core.event_bus import EventBus
from src.core.state_manager import UnifiedStateManager
from src.utils.log_config import setup_logger


class ReactiveUpdateDemo:
    """响应式更新演示类"""
    
    def __init__(self):
        self.logger = setup_logger("ReactiveUpdateDemo")
        
        # 创建核心组件
        self.event_bus = EventBus()
        self.state_manager = UnifiedStateManager()
        
        # 创建格式管理器（依赖注入）
        self.format_manager = UnifiedFormatManager(
            event_bus=self.event_bus,
            state_manager=self.state_manager
        )
        
        # 创建事件处理器
        self.logging_handler = LoggingFormatEventHandler(self.logger)
        self.metrics_handler = MetricsFormatEventHandler()
        
        # 订阅格式管理事件
        self._subscribe_to_format_events()
        
        # 演示数据
        self.demo_data = pd.DataFrame([
            {'employee_id': 'E001', 'employee_name': '张三', 'total_salary': 12345.67},
            {'employee_id': 'E002', 'employee_name': '李四', 'total_salary': 23456.78},
            {'employee_id': 'E003', 'employee_name': '王五', 'total_salary': 34567.89}
        ])
        
        self.logger.info("🎯 [响应式更新演示] 系统初始化完成")
    
    def _subscribe_to_format_events(self):
        """订阅格式管理事件"""
        # 格式化完成事件
        self.event_bus.subscribe("format_table_completed", self._on_format_completed)
        
        # 配置更新事件
        self.event_bus.subscribe("format_config_updated", self._on_config_updated)
        self.event_bus.subscribe("field_mapping_updated", self._on_field_mapping_updated)
        
        # 缓存清除事件
        self.event_bus.subscribe("cache_cleared", self._on_cache_cleared)
        
        # 错误事件
        self.event_bus.subscribe("format_table_failed", self._on_format_failed)
        
        # 状态变更事件
        self.event_bus.subscribe("system_state_changed", self._on_system_state_changed)
        
        self.logger.info("🎯 [响应式更新] 事件订阅完成")
    
    def _on_format_completed(self, event_data: Dict[str, Any]):
        """处理格式化完成事件"""
        self.logger.info(f"🎯 [响应式更新] 格式化完成: {event_data['table_type']}, "
                        f"数据量: {event_data['data_count']}, "
                        f"耗时: {event_data['processing_time']:.2f}ms")
        
        # 更新格式化统计
        self.metrics_handler.handle_event(
            FormatEventBuilder.create_table_completed_event(**event_data)
        )
    
    def _on_config_updated(self, event_data: Dict[str, Any]):
        """处理配置更新事件"""
        self.logger.info(f"🎯 [响应式更新] 配置更新: {event_data['config_key']} = {event_data['config_value']}")
        
        # 触发相关组件更新
        self._trigger_dependent_updates("config_change", event_data)
    
    def _on_field_mapping_updated(self, event_data: Dict[str, Any]):
        """处理字段映射更新事件"""
        self.logger.info(f"🎯 [响应式更新] 字段映射更新: {event_data['table_type']}")
        
        # 触发相关组件更新
        self._trigger_dependent_updates("field_mapping_change", event_data)
    
    def _on_cache_cleared(self, event_data: Dict[str, Any]):
        """处理缓存清除事件"""
        self.logger.info(f"🎯 [响应式更新] 缓存清除: {event_data['cleared_count']} 项")
    
    def _on_format_failed(self, event_data: Dict[str, Any]):
        """处理格式化失败事件"""
        self.logger.error(f"🎯 [响应式更新] 格式化失败: {event_data['error']}")
    
    def _on_system_state_changed(self, event_data: Dict[str, Any]):
        """处理系统状态变更事件"""
        self.logger.info(f"🎯 [响应式更新] 系统状态变更: {event_data['state_key']}")
    
    def _trigger_dependent_updates(self, change_type: str, event_data: Dict[str, Any]):
        """触发依赖组件更新"""
        self.logger.info(f"🎯 [响应式更新] 触发依赖更新: {change_type}")
        
        # 模拟触发UI组件更新
        self.event_bus.publish(Event(event_type="ui_update_required", data={
            "change_type": change_type,
            "affected_components": ["table_view", "header_view"],
            "event_data": event_data
        }))
        
        # 模拟触发数据重新加载
        self.event_bus.publish(Event(event_type="data_reload_required", data={
            "change_type": change_type,
            "affected_tables": [event_data.get('table_type', 'all')],
            "event_data": event_data
        }))
    
    async def demo_basic_formatting(self):
        """演示基础格式化功能"""
        self.logger.info("🎯 [演示1] 基础格式化功能")
        
        # 格式化表格
        headers, formatted_data = self.format_manager.format_table_complete(
            self.demo_data, 
            'active_employees',
            'demo_basic_formatting'
        )
        
        self.logger.info(f"格式化表头: {headers}")
        self.logger.info(f"格式化数据: {formatted_data.head().to_dict()}")
        
        # 等待事件处理
        await asyncio.sleep(0.1)
    
    async def demo_config_update(self):
        """演示配置更新和响应式更新"""
        self.logger.info("🎯 [演示2] 配置更新和响应式更新")
        
        # 更新货币格式配置
        old_symbol = self.format_manager.format_config.get_config('default_formats.currency.symbol', '¥')
        self.logger.info(f"当前货币符号: {old_symbol}")
        
        # 更新配置
        self.format_manager.update_format_config(
            'default_formats.currency.symbol', 
            '$'
        )
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        # 重新格式化数据验证变更
        headers, formatted_data = self.format_manager.format_table_complete(
            self.demo_data, 
            'active_employees',
            'demo_config_update'
        )
        
        self.logger.info(f"更新后的格式化数据: {formatted_data.head().to_dict()}")
        
        # 恢复原配置
        self.format_manager.update_format_config(
            'default_formats.currency.symbol', 
            old_symbol
        )
        
        await asyncio.sleep(0.1)
    
    async def demo_field_mapping_update(self):
        """演示字段映射更新"""
        self.logger.info("🎯 [演示3] 字段映射更新")
        
        # 更新字段映射
        new_mappings = {
            'employee_id': '员工编号',
            'employee_name': '员工姓名',
            'total_salary': '月薪总额'
        }
        
        self.format_manager.update_field_mapping('active_employees', new_mappings)
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        # 重新格式化验证变更
        headers, formatted_data = self.format_manager.format_table_complete(
            self.demo_data, 
            'active_employees',
            'demo_field_mapping_update'
        )
        
        self.logger.info(f"更新后的表头: {headers}")
        
        await asyncio.sleep(0.1)
    
    async def demo_cache_management(self):
        """演示缓存管理"""
        self.logger.info("🎯 [演示4] 缓存管理")
        
        # 多次格式化同样数据，观察缓存效果
        for i in range(3):
            self.logger.info(f"第 {i+1} 次格式化")
            headers, formatted_data = self.format_manager.format_table_complete(
                self.demo_data, 
                'active_employees',
                f'demo_cache_management_{i}'
            )
            await asyncio.sleep(0.05)
        
        # 清除缓存
        self.logger.info("清除缓存")
        cleared_count = self.format_manager.clear_cache()
        self.logger.info(f"已清除 {cleared_count} 个缓存项")
        
        # 再次格式化
        self.logger.info("缓存清除后再次格式化")
        headers, formatted_data = self.format_manager.format_table_complete(
            self.demo_data, 
            'active_employees',
            'demo_cache_management_after_clear'
        )
        
        await asyncio.sleep(0.1)
    
    async def demo_system_monitoring(self):
        """演示系统监控"""
        self.logger.info("🎯 [演示5] 系统监控")
        
        # 获取系统状态
        status = self.format_manager.get_system_status()
        self.logger.info(f"系统状态: {status}")
        
        # 获取性能统计
        metrics = self.metrics_handler.get_metrics()
        self.logger.info(f"性能统计: {metrics}")
        
        # 获取状态管理器状态
        state_data = self.state_manager.get_state("format_management")
        self.logger.info(f"状态管理器数据: {state_data}")
        
        await asyncio.sleep(0.1)
    
    async def demo_error_handling(self):
        """演示错误处理"""
        self.logger.info("🎯 [演示6] 错误处理")
        
        # 故意使用错误的表类型
        headers, formatted_data = self.format_manager.format_table_complete(
            self.demo_data, 
            'nonexistent_table_type',
            'demo_error_handling'
        )
        
        self.logger.info(f"错误处理后的表头: {headers}")
        
        await asyncio.sleep(0.1)
    
    async def run_all_demos(self):
        """运行所有演示"""
        self.logger.info("🎯 [响应式更新演示] 开始运行所有演示")
        
        try:
            await self.demo_basic_formatting()
            await self.demo_config_update()
            await self.demo_field_mapping_update()
            await self.demo_cache_management()
            await self.demo_system_monitoring()
            await self.demo_error_handling()
            
            self.logger.info("🎯 [响应式更新演示] 所有演示完成")
            
            # 输出最终统计
            final_metrics = self.metrics_handler.get_metrics()
            self.logger.info(f"🎯 [最终统计] {final_metrics}")
            
        except Exception as e:
            self.logger.error(f"🎯 [响应式更新演示] 演示过程中发生错误: {e}")


class ConfigFileWatcher:
    """配置文件监视器示例"""
    
    def __init__(self, format_manager: UnifiedFormatManager):
        self.format_manager = format_manager
        self.logger = setup_logger("ConfigFileWatcher")
        
        # 监视的配置文件路径
        self.config_file = Path("state/data/format_config.json")
        self.field_mapping_file = Path("state/data/field_mappings.json")
        
        # 文件修改时间缓存
        self.last_modified = {}
        
        self.logger.info("🎯 [配置文件监视器] 初始化完成")
    
    def check_file_changes(self):
        """检查文件变更"""
        files_to_check = [
            ("config", self.config_file),
            ("field_mapping", self.field_mapping_file)
        ]
        
        for file_type, file_path in files_to_check:
            if file_path.exists():
                current_mtime = file_path.stat().st_mtime
                last_mtime = self.last_modified.get(str(file_path), 0)
                
                if current_mtime > last_mtime:
                    self.logger.info(f"🎯 [配置文件监视器] 检测到文件变更: {file_path}")
                    self.last_modified[str(file_path)] = current_mtime
                    
                    # 发布文件变更事件
                    if file_type == "config":
                        self.format_manager.event_bus.publish(Event(event_type="config_file_changed", data={
                            "file_path": str(file_path),
                            "file_type": file_type,
                            "change_type": "modified",
                            "modification_time": current_mtime
                        }))
                    elif file_type == "field_mapping":
                        self.format_manager.event_bus.publish(Event(event_type="field_mapping_file_changed", data={
                            "file_path": str(file_path),
                            "file_type": file_type,
                            "change_type": "modified",
                            "modification_time": current_mtime
                        }))
    
    async def watch_files(self, interval: float = 1.0):
        """监视文件变更"""
        self.logger.info(f"🎯 [配置文件监视器] 开始监视文件变更，间隔: {interval}秒")
        
        while True:
            try:
                self.check_file_changes()
                await asyncio.sleep(interval)
            except KeyboardInterrupt:
                self.logger.info("🎯 [配置文件监视器] 停止监视")
                break
            except Exception as e:
                self.logger.error(f"🎯 [配置文件监视器] 监视过程中发生错误: {e}")
                await asyncio.sleep(interval)


async def main():
    """主函数"""
    print("🎯 [统一格式管理系统] 响应式更新演示")
    print("=" * 60)
    
    # 创建演示实例
    demo = ReactiveUpdateDemo()
    
    # 运行演示
    await demo.run_all_demos()
    
    print("\n🎯 [演示结束] 响应式更新演示完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())