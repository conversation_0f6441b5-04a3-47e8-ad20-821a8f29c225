{"name": "CC Server", "host": "*************", "protocol": "sftp", "port": 22, "username": "ubuntu", "privateKeyPath": "~/.ssh/id_rsa", "remotePath": "/home/<USER>/salary_changes", "uploadOnSave": true, "downloadOnOpen": true, "syncMode": "update", "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": true}, "useTempFile": false, "openSsh": true, "ignore": [".vscode/**", ".git/**", "node_modules/**", "**/.DS_Store", "**/Thumbs.db", "**/__pycache__/**", "**/*.pyc", "temp/**", "output/**"]}