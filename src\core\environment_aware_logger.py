#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
环境感知日志系统 - Environment Aware Logger

解决用户提出的开发阶段和生产阶段切换问题，提供：
1. 自动环境检测（开发/生产）
2. 环境特定的日志级别和格式
3. 开发阶段详细调试信息
4. 生产阶段简洁性能日志
5. 动态日志配置热更新

设计原则：
- 开发环境：详细调试，控制台输出，性能监控
- 生产环境：简洁日志，文件输出，错误重点关注
- 自动检测：基于环境变量和运行环境判断

Author: Claude Code  
Created: 2025-07-26
Phase: 1.3 日志系统规范化
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
from enum import Enum
from datetime import datetime

try:
    from src.utils.log_config import setup_logger
    _loguru_available = True
except ImportError:
    # 回退到标准 logging
    import logging
    _loguru_available = False
    def setup_logger(module_name=None, **kwargs):
        name = module_name or __name__
        logger = logging.getLogger(name)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger


class Environment(Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    PRODUCTION = "production" 
    TESTING = "testing"


class EnvironmentAwareLogger:
    """环境感知日志管理器"""
    
    def __init__(self):
        self.current_env = self._detect_environment()
        self.config = self._load_environment_config()
        self.logger = None
        self._initialize_logger()
        
    def _detect_environment(self) -> Environment:
        """智能检测当前运行环境"""
        
        # 1. 显式环境变量检测
        env_var = os.getenv('SALARY_SYSTEM_ENV', '').lower()
        if env_var == 'production':
            return Environment.PRODUCTION
        elif env_var == 'testing':
            return Environment.TESTING
        elif env_var == 'development':
            return Environment.DEVELOPMENT
            
        # 2. 运行环境特征检测
        # 检查是否在打包环境中运行
        if getattr(sys, 'frozen', False):
            return Environment.PRODUCTION
            
        # 3. 开发环境指标检测
        dev_indicators = [
            'pytest' in sys.modules,  # 正在运行测试
            '__main__' in sys.argv[0] if sys.argv else False,  # 直接运行脚本
            os.path.exists('.git'),  # Git仓库目录
            os.path.exists('requirements.txt'),  # 开发依赖文件
            'python' in sys.executable.lower()  # 使用Python解释器
        ]
        
        if any(dev_indicators):
            return Environment.DEVELOPMENT
            
        # 4. 默认生产环境
        return Environment.PRODUCTION
        
    def _load_environment_config(self) -> Dict[str, Any]:
        """加载环境特定的日志配置"""
        
        base_config = {
            Environment.DEVELOPMENT: {
                "log_level": "DEBUG",
                "log_to_console": True,
                "log_to_file": True,
                "console_format": "detailed",
                "file_format": "detailed", 
                "performance_monitoring": True,
                "debug_sql": True,
                "max_file_size": "50 MB",
                "retention_days": 7,
                "enable_colors": True
            },
            Environment.PRODUCTION: {
                "log_level": "INFO", 
                "log_to_console": False,
                "log_to_file": True,
                "console_format": "simple",
                "file_format": "structured",
                "performance_monitoring": False,
                "debug_sql": False,
                "max_file_size": "100 MB", 
                "retention_days": 30,
                "enable_colors": False
            },
            Environment.TESTING: {
                "log_level": "WARNING",
                "log_to_console": True,
                "log_to_file": False,
                "console_format": "simple",
                "file_format": "simple",
                "performance_monitoring": False,
                "debug_sql": False,
                "max_file_size": "10 MB",
                "retention_days": 1,
                "enable_colors": True
            }
        }
        
        # 尝试从配置文件加载自定义配置
        config_file = Path("config/logging_config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    custom_config = json.load(f)
                    # 合并自定义配置
                    for env in Environment:
                        if env.value in custom_config:
                            base_config[env].update(custom_config[env.value])
            except Exception as e:
                print(f"[警告] 加载自定义日志配置失败: {e}")
                
        return base_config[self.current_env]
        
    def _initialize_logger(self):
        """初始化环境特定的日志器"""
        
        try:
            # 构建日志文件名（环境特定）
            log_filename = f"salary_system_{self.current_env.value}.log"
            
            self.logger = setup_logger(
                module_name="EnvironmentAwareLogger",
                log_level=self.config["log_level"],
                log_to_file=self.config["log_to_file"],
                log_to_console=self.config["log_to_console"],
                log_file_name=log_filename,
                max_file_size=self.config["max_file_size"],
                rotation_count=self.config["retention_days"]
            )
            
            # 记录环境信息
            self.logger.info(f"环境感知日志系统初始化完成")
            self.logger.info(f"检测到运行环境: {self.current_env.value.upper()}")
            self.logger.info(f"日志级别: {self.config['log_level']}")
            self.logger.info(f"性能监控: {'启用' if self.config['performance_monitoring'] else '禁用'}")
            
        except Exception as e:
            print(f"[错误] 环境感知日志初始化失败: {e}")
            # 回退到基础日志
            self.logger = setup_logger("EnvironmentAwareLogger")
            
    def get_logger(self, module_name: str = None):
        """获取环境感知的日志器"""
        if module_name:
            return setup_logger(
                module_name=module_name,
                log_level=self.config["log_level"],
                log_to_file=self.config["log_to_file"],
                log_to_console=self.config["log_to_console"]
            )
        return self.logger
        
    def is_development(self) -> bool:
        """检查是否为开发环境"""
        return self.current_env == Environment.DEVELOPMENT
        
    def is_production(self) -> bool:
        """检查是否为生产环境"""
        return self.current_env == Environment.PRODUCTION
        
    def enable_performance_monitoring(self) -> bool:
        """检查是否启用性能监控"""
        return self.config.get("performance_monitoring", False)
        
    def enable_debug_sql(self) -> bool:
        """检查是否启用SQL调试"""
        return self.config.get("debug_sql", False)
        
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "environment": self.current_env.value,
            "config": self.config.copy(),
            "python_version": sys.version,
            "platform": sys.platform,
            "executable": sys.executable,
            "frozen": getattr(sys, 'frozen', False),
            "detection_time": datetime.now().isoformat()
        }


# 全局环境感知日志管理器实例
_global_env_logger: Optional[EnvironmentAwareLogger] = None


def get_environment_aware_logger() -> EnvironmentAwareLogger:
    """获取全局环境感知日志管理器"""
    global _global_env_logger
    if _global_env_logger is None:
        _global_env_logger = EnvironmentAwareLogger()
    return _global_env_logger


def get_env_logger(module_name: str):
    """便捷函数：获取环境感知的模块日志器"""
    env_logger = get_environment_aware_logger()
    return env_logger.get_logger(module_name)


def log_environment_info():
    """记录环境信息到日志"""
    env_logger = get_environment_aware_logger()
    info = env_logger.get_environment_info()
    
    logger = env_logger.get_logger("EnvironmentInfo")
    logger.info("系统环境信息:")
    for key, value in info.items():
        if key != "config":  # 配置信息单独处理
            logger.info(f"  {key}: {value}")
    
    logger.debug("详细配置信息:")
    for key, value in info["config"].items():
        logger.debug(f"  {key}: {value}")


if __name__ == "__main__":
    """测试环境感知日志系统"""
    
    print("[测试] 环境感知日志系统...")
    
    # 测试环境检测
    env_logger = get_environment_aware_logger()
    print(f"检测到环境: {env_logger.current_env.value}")
    
    # 测试不同模块的日志器
    test_logger = get_env_logger("TestModule")
    test_logger.info("这是环境感知日志测试")
    test_logger.debug("这是调试信息（仅开发环境显示）")
    
    # 测试环境特性
    print(f"开发环境: {env_logger.is_development()}")
    print(f"生产环境: {env_logger.is_production()}")
    print(f"性能监控: {env_logger.enable_performance_monitoring()}")
    
    # 记录环境信息
    log_environment_info()
    
    print("[测试] 环境感知日志系统测试完成")