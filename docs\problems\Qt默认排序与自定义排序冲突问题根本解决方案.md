# Qt默认排序与自定义排序冲突问题根本解决方案

## 🎯 问题概述

**用户操作**：将`setSortingEnabled(False)`改为`setSortingEnabled(True)`

**问题现象**：
- ✅ 点击表头可以排序
- ❌ 点击"下一页"按钮，页面切换但内容还是上一页的内容  
- ❌ 只有点击"末页"按钮，页面才会显示不同内容

## 🔍 根本原因分析

### 核心问题：**双重排序机制冲突**

项目中同时存在两套排序机制：

#### 1. Qt默认排序机制（您启用的）
- **作用范围**：仅对当前页面内存中的数据进行排序
- **优点**：响应快速，用户立即看到排序效果
- **局限性**：只能排序当前页的50条数据，不是全局排序

#### 2. 自定义排序机制（项目原有）
- **作用范围**：通过数据库SQL查询进行全局排序
- **优点**：支持分页的全局排序
- **实现方式**：事件总线 + 排序状态管理器 + 数据库排序查询

### 冲突机制详解

```mermaid
graph TD
    A[用户点击表头] --> B[Qt默认排序]
    A --> C[自定义排序系统]
    
    B --> D[当前页面数据排序 ✅]
    C --> E[排序状态未更新 ❌]
    
    F[用户点击下一页] --> G[分页系统查询]
    G --> H[从数据库获取原始数据]
    H --> I[未考虑Qt排序状态]
    I --> J[显示未排序数据 ❌]
```

### 具体数据流程冲突

**正常情况（setSortingEnabled=False）**：
1. 点击表头 → 自定义排序事件 → 数据库排序查询 → 更新当前页
2. 点击下一页 → 分页查询（带排序） → 显示排序后的第2页数据

**冲突情况（setSortingEnabled=True）**：
1. 点击表头 → Qt排序（仅当前页） + 自定义排序事件（可能失败）
2. 点击下一页 → 分页查询（无排序信息） → 显示原始第2页数据
3. 用户看到：第1页排序了，第2页没排序

## 🔧 解决方案

### 方案1：恢复原设计（推荐）

**原理**：维持项目的统一架构，使用自定义排序系统

**修改**：已完成
```python
# src/gui/prototype/widgets/virtualized_expandable_table.py 第2178行
self.setSortingEnabled(False)  # 🔧 [修复] 禁用Qt默认排序，避免冲突
```

**优点**：
- ✅ 保持架构一致性
- ✅ 支持全局分页排序
- ✅ 排序状态持久化
- ✅ 多表排序状态隔离

### 方案2：Qt排序与自定义排序桥接（复杂）

如果一定要使用Qt默认排序，需要实现桥接机制：

```python
def _on_qt_sort_indicator_changed(self, logical_index, order):
    """Qt排序状态变化时，同步到自定义排序系统"""
    # 1. 获取列名
    column_name = self.get_column_name(logical_index)
    
    # 2. 转换排序方向
    sort_order = 'ascending' if order == Qt.AscendingOrder else 'descending'
    
    # 3. 触发自定义排序
    self._trigger_custom_sort([{
        'column_name': column_name,
        'order': sort_order
    }])
    
    # 4. 禁用Qt排序的实际效果，只保留视觉指示器
    self.setSortingEnabled(False)
    header = self.horizontalHeader()
    header.setSortIndicator(logical_index, order)
```

**缺点**：实现复杂，容易引入新bug

## 📋 项目架构说明

### 为什么项目使用自定义排序？

1. **数据量大**：工资表可能有数千条记录，需要数据库级排序
2. **分页支持**：Qt默认排序不支持跨页排序
3. **字段映射**：需要处理中英文字段名映射
4. **状态管理**：需要保存用户的排序偏好
5. **多表支持**：不同工资表有不同的排序逻辑

### 自定义排序的完整流程

```python
# 1. 表头点击 → 排序事件
header.sectionClicked.connect(self._on_header_clicked)

# 2. 事件处理 → 排序请求
def _on_header_clicked(self, logical_index):
    sort_request = SortRequestEvent(...)
    self.event_bus.publish(sort_request)

# 3. 数据服务 → 数据库查询
def handle_sort_request(self, event):
    df = self.db_manager.get_dataframe_paginated_with_sort(
        table_name, page, page_size, sort_columns
    )

# 4. 数据更新 → UI刷新
data_event = DataUpdatedEvent(...)
self.event_bus.publish(data_event)
```

## 🎯 解决方案验证

修复后的预期行为：
1. ✅ 点击表头排序 → 整个表格按该列全局排序
2. ✅ 点击下一页 → 显示排序后的第2页内容
3. ✅ 排序状态在分页间保持一致
4. ✅ 不同工资表的排序状态独立管理

## 📚 相关文档

- `docs/problems/排序分页功能根本修复报告.md` - 历史排序问题修复记录
- `docs/problems/排序功能深度修复报告.md` - 深层次排序问题分析
- `src/core/table_sort_state_manager.py` - 排序状态管理器
- `src/services/table_data_service.py` - 数据服务层

## 🏷️ 标签

`排序冲突` `Qt默认排序` `自定义排序` `分页问题` `架构设计` 