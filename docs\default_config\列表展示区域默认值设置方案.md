# 列表展示区域默认值设置方案

## 问题描述

当前系统启动后，主界面右侧列表展示区域显示的是统一表头的空表，不符合实际使用需求。用户期望系统启动后能够自动显示有意义的数据，具体要求：

- **左侧数据导航区域**：自动选中"工资表"最新年度最新月份"全部在职人员"
- **右侧列表展示区域**：自动显示对应的工资数据

## 当前状况分析

通过代码分析发现的问题：

1. **主界面启动时显示空表格**：在 `prototype_main_window.py` 中，`_initialize_data()` 方法只是显示空表格和提示信息
2. **导航面板没有默认选择**：虽然有智能展开算法，但没有自动选择最新数据并加载
3. **缺少启动时的数据自动加载机制**：需要用户手动点击导航项才能加载数据

## 解决方案设计

### 方案概述

为主界面设置默认值，使系统启动后自动显示"工资表"最新年度最新月份"全部在职人员"的数据。

### 核心思路

1. **获取最新数据路径**：从数据库中查询最新年度最新月份的工资数据
2. **自动导航选择**：启动时自动选中对应的导航项
3. **自动数据加载**：触发数据加载显示在右侧列表区域
4. **优雅降级**：如果没有数据则显示友好提示

### 详细实现方案

#### 1. 数据查询层改进

在 `DynamicTableManager` 中添加获取最新数据路径的方法：

```python
def get_latest_salary_data_path(self) -> Optional[str]:
    """获取最新工资数据的导航路径"""
    # 查询最新年份最新月份的全部在职人员数据
    # 返回格式: "工资表 > 2025年 > 06月 > 全部在职人员"
```

#### 2. 导航面板改进

在 `EnhancedNavigationPanel` 中添加自动选择逻辑：

```python
def auto_select_latest_data(self):
    """自动选择最新数据并触发加载"""
    # 获取最新数据路径
    # 自动展开并选中对应节点
    # 触发数据加载事件
```

#### 3. 主窗口初始化改进

在 `PrototypeMainWindow` 中修改初始化逻辑：

```python
def _initialize_data(self):
    """初始化加载数据 - 自动加载最新数据"""
    # 检查是否有工资数据
    # 如果有，自动选择最新数据
    # 如果没有，显示友好提示
```

## 流程设计

### 主要流程步骤

1. **系统启动** → 初始化主窗口
2. **初始化导航面板** → 检查数据库中是否有工资数据
3. **有数据分支**：
   - 获取最新年度最新月份数据路径
   - 解析路径信息
   - 自动展开导航树到目标路径
   - 自动选中"全部在职人员"节点
   - 触发数据加载事件
   - 在右侧列表区域显示数据
4. **无数据分支**：
   - 显示空表格和导入提示
   - 显示友好提示信息
5. **更新状态栏信息** → 完成初始化

### 时序交互

1. **主窗口** → 初始化导航面板
2. **导航面板** → 检查数据库是否有工资数据表
3. **数据库管理器** → 返回表列表
4. **有数据情况下**：
   - 导航面板 → 获取最新数据路径
   - 表格管理器 → 查询最新年月数据
   - 数据库管理器 → 返回最新数据信息
   - 表格管理器 → 返回路径格式
   - 导航面板 → 解析路径并展开导航树
   - 导航面板 → 自动选中目标节点
   - 导航面板 → 发送导航变化事件
   - 主窗口 → 根据路径生成表名并加载数据
   - 数据库管理器 → 返回数据DataFrame
   - 主窗口 → 设置数据到列表展示区域
   - 主窗口 → 更新状态栏信息

## 具体实现要点

### 1. 最新数据路径获取算法

```python
def get_latest_salary_data_path(self) -> Optional[str]:
    """
    获取最新工资数据的导航路径
    
    算法：
    1. 查询所有salary_data表
    2. 按年份降序、月份降序排序
    3. 优先选择"全部在职人员"类别
    4. 构造标准导航路径格式
    """
```

### 2. 自动导航选择机制

```python
def auto_select_latest_data(self):
    """
    自动选择最新数据机制
    
    步骤：
    1. 获取最新数据路径
    2. 逐级展开导航树节点
    3. 选中目标节点
    4. 触发选择变化事件
    5. 自动加载对应数据
    """
```

### 3. 优雅降级处理

- **无数据情况**：显示友好的导入提示
- **数据加载失败**：显示错误信息并提供重试选项
- **部分数据缺失**：选择可用的最接近数据

### 4. 用户体验优化

- **加载指示器**：显示数据加载进度
- **状态反馈**：实时更新状态栏信息
- **记忆功能**：记住用户上次的选择偏好

## 预期效果

实施此方案后，用户启动系统将看到：

1. **左侧导航区域**：自动展开到"工资表 > 最新年度 > 最新月份 > 全部在职人员"
2. **右侧列表区域**：自动显示最新月份全部在职人员的工资数据
3. **状态栏**：显示当前加载的数据信息和记录数量
4. **无数据时**：显示清晰的导入数据指引

这样可以大大提升用户体验，让系统启动后立即展示有用的信息，而不是空白的表格。

## 技术实现细节

### 涉及的主要文件

1. `src/modules/data_storage/dynamic_table_manager.py` - 添加最新数据路径查询方法
2. `src/gui/prototype/widgets/enhanced_navigation_panel.py` - 添加自动选择逻辑
3. `src/gui/prototype/prototype_main_window.py` - 修改初始化数据加载逻辑

### 关键方法改进

1. **DynamicTableManager.get_latest_salary_data_path()**
   - 查询数据库中最新的工资数据表
   - 构造标准的导航路径格式
   - 优先选择"全部在职人员"类别

2. **EnhancedNavigationPanel.auto_select_latest_data()**
   - 获取最新数据路径
   - 自动展开导航树节点
   - 选中目标节点并触发事件

3. **PrototypeMainWindow._initialize_data()**
   - 检查数据可用性
   - 调用自动选择逻辑
   - 处理无数据情况

### 错误处理策略

1. **数据库连接失败**：显示连接错误提示
2. **无工资数据**：显示导入数据指引
3. **数据加载超时**：提供重试机制
4. **路径解析失败**：回退到默认空表格

## 后续优化建议

1. **用户偏好记忆**：记住用户最后查看的数据类型
2. **智能推荐**：根据使用频率推荐默认数据
3. **快速切换**：提供快捷键快速切换到最新数据
4. **数据预加载**：在后台预加载常用数据提升响应速度

---

*文档创建时间：2025-06-26*  
*版本：v1.0*  
*状态：设计方案 - 待实施*
