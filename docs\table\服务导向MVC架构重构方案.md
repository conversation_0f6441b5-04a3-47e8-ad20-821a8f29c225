# 核心问题分析与架构重构方案

## 1. 背景：为何需要重构？

在解决了近期数据导航与显示不同步的问题后，我们达成共识：当前的修复方式仅仅是"治标不治本"。频繁出现此类需要局部修补的问题，暴露出系统底层架构存在脆弱性。

**当前方案旨在从根本上解决以下观察到的问题：**

*   **问题反复出现**：一个看似简单的功能（如导航切换）的修复，可能会引发其他区域的未知问题，导致开发和维护成本增高。
*   **修复即补丁**：目前的修复手段往往是在复杂的逻辑上增加更多的补丁，这使得代码越来越难以理解和维护，技术债台高筑。
*   **牵一发而动全身**：UI组件之间、UI与业务逻辑之间过度耦合，导致任何微小的改动都可能带来不可预见的连锁反应。

因此，本重构方案的目标是建立一个健壮、清晰、可扩展的软件架构，彻底根除这些问题，为项目的长期稳定和未来功能迭代奠定坚实的基础。

## 2. 根本原因分析：三大技术债

当前 `src/gui/prototype/prototype_main_window.py` 文件中的 `PrototypeMainWindow` 类存在严重的架构问题，这是导致上述现象的根本原因。主要体现在：

-   **高耦合 (High Coupling)**: `PrototypeMainWindow` 与其子组件（如 `EnhancedNavigationPanel`, `MainWorkspaceArea`）之间存在复杂的双向依赖。主窗口直接操作子组件的内部，子组件也频繁回调主窗口，形成了一个脆弱且混乱的通信网络。
-   **职责不清 (Unclear Responsibilities)**: `PrototypeMainWindow` 类承担了过多的职责，集UI布局、事件处理、数据获取、线程管理、状态管理于一身，严重违反了单一职责原则（SRP）。
-   **状态管理分散 (Scattered State Management)**: 应用的核心状态（如 `current_nav_path`）直接作为主窗口的实例属性存在，导致状态的变更和同步逻辑分散在代码各处，难以追踪和维护。

## 3. 解决方案：重构为服务导向的MVC架构

为了从根源上解决问题，提出以下重构方案，将应用重构为一种现代、服务导向的MVC（Model-View-Controller）变体模式，核心是彻底分离**数据（Model）**、**界面（View）**和**逻辑（Controller）**。

### 架构示意图

#### 组件关系图 (Component Diagram)

```mermaid
graph TD
    subgraph View Layer
        A[EnhancedNavigationPanel] -- 触发用户操作 --> B{ApplicationController};
        C[MainWorkspaceArea] -- 订阅状态变更 --> D{ApplicationStateService};
        C -- 显示数据 --> C;
    end

    subgraph Controller Layer
        B -- 处理业务逻辑 --> B;
        B -- 更新状态 --> D;
        B -- 调用数据服务 --> E[Data Services];
    end

    subgraph Model Layer
        D -- 持有应用状态 --> D;
        D -- 发出状态变更信号 --> C;
        E[Data Services]
        subgraph Data Services
            F[DynamicTableManager]
            G[DatabaseManager]
        end
    end
    
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style D fill:#9f9,stroke:#333,stroke-width:2px
```

### 4. 核心组件职责划分

#### a. 模型层 (Model Layer) - 数据的来源和状态中心

1.  **数据服务 (Data Services)**:
    *   保持现有 `DynamicTableManager`, `DatabaseManager` 等底层数据交互服务不变。
2.  **应用状态服务 (`ApplicationStateService`)** - **[新增核心]**:
    *   **职责**:
        *   作为应用状态的**唯一真相来源 (Single Source of Truth)**，持有如 `current_navigation_path`, `current_data_frame`, `loading_status` 等全局状态。
        *   提供统一的方法来更新和查询状态。
        *   定义PyQt信号（如 `data_updated`, `status_changed`），在状态变化时向外广播。

#### b. 视图层 (View Layer) - 用户所见即所得

1.  **`EnhancedNavigationPanel` (左侧导航)**:
    *   **职责**: 纯粹的UI展示和用户输入捕获。
    *   **改造**: 当用户点击导航项时，直接调用 `ApplicationController` 的方法（如 `controller.navigate_to(path)`），将用户"意图"传递出去，自身不处理任何业务逻辑。
2.  **`MainWorkspaceArea` (右侧工作区)**:
    *   **职责**: 纯粹的数据展示组件。
    *   **改造**: 订阅 `ApplicationStateService` 的信号（如 `data_updated`），接收到新数据后更新UI。所有按钮点击事件（如刷新、导出）都转发给 `ApplicationController`。
3.  **`PrototypeMainWindow`**:
    *   **职责**: 退化为纯粹的"布局容器"。
    *   **改造**: 只负责创建和组装UI控件，移除所有业务逻辑和状态管理。

#### c. 控制器层 (Controller Layer) - 应用的大脑

1.  **`ApplicationController`** - **[新增核心]**:
    *   **职责**:
        *   作为应用的总指挥，接收并处理所有来自视图层的用户操作请求。
        *   调用数据服务来获取数据。
        *   在数据处理完成后，调用 `ApplicationStateService` 更新全局状态。
        *   封装所有业务逻辑，如表名生成、数据校验、错误处理等。

### 5. 重构后的数据流（以导航切换为例）

#### 时序图 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant V as View (NavigationPanel)
    participant C as ApplicationController
    participant M_State as ApplicationStateService
    participant M_Data as DataServices (e.g., DynamicTableManager)
    participant V_Workspace as View (WorkspaceArea)

    V->>+C: 用户点击导航，调用 navigate_to(path)
    C->>C: 处理路径，生成表名
    C->>+M_State: 更新状态 (e.g., set_loading(True))
    M_State-->>-V_Workspace: (信号) status_changed("加载中...")
    V_Workspace->>V_Workspace: 显示加载提示

    C->>+M_Data: (在新线程中) 请求数据 get_dataframe(table_name)
    M_Data-->>-C: 返回DataFrame
    
    C->>+M_State: 更新状态 set_current_data(df)
    M_State-->>-V_Workspace: (信号) data_updated(df)
    V_Workspace->>V_Workspace: 接收新数据并更新表格UI
    
    C->>+M_State: 更新状态 set_loading(False)
    M_State-->>-V_Workspace: (信号) status_changed("加载完成")
    V_Workspace->>V_Workspace: 隐藏加载提示
```

### 6. 新架构的优势

-   **彻底解耦**: UI、业务逻辑和数据模型分离，修改互不影响。
-   **职责清晰**: 每个类都遵循单一职责原则，代码更易于理解和维护。
-   **可测试性强**: 核心逻辑组件（Controller, StateService）均为非GUI类，可进行独立的单元测试。
-   **可扩展性好**: 增加新功能只需与核心服务交互，无需修改现有UI组件。

该方案旨在通过建立一个健壮、清晰的软件架构，从根本上解决当前面临的各种UI与逻辑耦合问题，为项目的长期健康发展奠定坚实基础。 