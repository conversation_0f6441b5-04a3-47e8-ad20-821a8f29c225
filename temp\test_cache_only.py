#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立缓存测试
验证缓存优化修复的有效性，不依赖真实数据服务
"""

import time
import sys
import os
from typing import Dict, Any, Optional, List, Tuple

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from cache_optimization_fix import OptimizedDataCache, CacheOptimizedDataLoader
from src.utils.log_config import setup_logger


class MockDataResponse:
    """模拟数据响应"""
    def __init__(self, data: Any, total_records: int = 0, success: bool = True, error: str = None):
        self.data = data
        self.total_records = total_records
        self.success = success
        self.error = error
        self.processing_time_ms = 100  # 模拟处理时间


class MockTableDataService:
    """模拟表数据服务"""
    
    def __init__(self):
        self.logger = setup_logger(__name__ + ".MockTableDataService")
        self._call_count = 0
        
    def load_table_data(self, table_name: str, page: int, page_size: int = 50,
                       sort_columns: Optional[List[Dict]] = None,
                       force_reload: bool = False) -> MockDataResponse:
        """模拟数据加载"""
        
        self._call_count += 1
        
        # 模拟数据库查询延迟
        time.sleep(0.1)
        
        # 生成模拟数据
        mock_data = []
        start_idx = (page - 1) * page_size
        for i in range(page_size):
            record_id = start_idx + i + 1
            mock_data.append({
                'id': record_id,
                'name': f'员工{record_id:03d}',
                'department': f'部门{(record_id % 5) + 1}',
                'salary': 5000 + (record_id * 100),
                'create_time': f'2025-07-{(record_id % 30) + 1:02d}'
            })
        
        total_records = 1000  # 模拟总记录数
        
        self.logger.info(f"🧪 [模拟数据] 第{self._call_count}次调用 - {table_name} 第{page}页")
        
        return MockDataResponse(
            data=mock_data,
            total_records=total_records,
            success=True
        )


class CacheTestRunner:
    """🧪 [缓存测试] 缓存测试运行器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__ + ".CacheTestRunner")
        
        # 修改缓存加载器以使用模拟服务
        self.cache_loader = CacheOptimizedDataLoader()
        self.mock_service = MockTableDataService()
        
        # 替换真实的服务调用
        self._patch_data_service()
        
        self.logger.info("🧪 [缓存测试] 测试运行器初始化完成")
    
    def _patch_data_service(self):
        """替换真实的数据服务为模拟服务"""
        
        original_load_method = self.cache_loader._load_from_database
        
        def patched_load_method(table_name: str, page: int, page_size: int,
                               sort_columns: Optional[List[Dict]] = None) -> Tuple[Any, Dict, bool]:
            """修补的数据加载方法"""
            try:
                # 使用模拟服务
                response = self.mock_service.load_table_data(
                    table_name=table_name,
                    page=page,
                    page_size=page_size,
                    sort_columns=sort_columns,
                    force_reload=False
                )
                
                if response.success:
                    metadata = {
                        'total_records': response.total_records,
                        'current_page': page,
                        'page_size': page_size,
                        'sort_columns': sort_columns,
                        'from_cache': False,
                        'processing_time_ms': response.processing_time_ms
                    }
                    
                    # 缓存结果
                    self.cache_loader.cache.cache_page_data(
                        table_name, page, page_size, response.data, metadata, sort_columns
                    )
                    
                    return response.data, metadata, False
                else:
                    raise Exception(f"数据加载失败: {response.error}")
                    
            except Exception as e:
                self.logger.error(f"🧪 [模拟加载失败] {e}")
                return None, {'error': str(e), 'from_cache': False}, False
        
        # 替换方法
        self.cache_loader._load_from_database = patched_load_method
    
    def test_basic_caching(self):
        """🧪 [测试1] 基础缓存功能测试"""
        
        print("\n🧪 [测试1] 基础缓存功能测试")
        print("=" * 50)
        
        table_name = "员工表"
        page = 1
        page_size = 20
        
        # 第一次加载 - 应该从数据库加载
        print("第一次加载...")
        start_time = time.time()
        data1, meta1, from_cache1 = self.cache_loader.load_table_data_with_cache(table_name, page, page_size)
        load_time1 = (time.time() - start_time) * 1000
        
        print(f"  ✓ 从缓存: {from_cache1}")
        print(f"  ✓ 加载时间: {load_time1:.1f}ms")
        print(f"  ✓ 数据条数: {len(data1) if data1 else 0}")
        print(f"  ✓ 总记录数: {meta1.get('total_records', 0)}")
        
        # 第二次加载 - 应该从缓存加载
        print("\n第二次加载（相同参数）...")
        start_time = time.time()
        data2, meta2, from_cache2 = self.cache_loader.load_table_data_with_cache(table_name, page, page_size)
        load_time2 = (time.time() - start_time) * 1000
        
        print(f"  ✓ 从缓存: {from_cache2}")
        print(f"  ✓ 加载时间: {load_time2:.1f}ms")
        print(f"  ✓ 数据条数: {len(data2) if data2 else 0}")
        print(f"  ✓ 缓存命中性能提升: {((load_time1 - load_time2) / load_time1 * 100):.1f}%")
        
        # 验证缓存命中
        assert from_cache1 == False, "第一次加载应该不是从缓存"
        assert from_cache2 == True, "第二次加载应该是从缓存"
        assert data1 == data2, "缓存的数据应该与原数据相同"
        
        print("\n  ✅ 基础缓存功能测试通过")
    
    def test_different_pages(self):
        """🧪 [测试2] 不同页面缓存测试"""
        
        print("\n🧪 [测试2] 不同页面缓存测试")
        print("=" * 50)
        
        table_name = "员工表"
        page_size = 15
        
        # 加载不同页面
        pages_to_test = [1, 2, 3]
        load_times = []
        
        for page in pages_to_test:
            print(f"\n加载第{page}页...")
            start_time = time.time()
            data, meta, from_cache = self.cache_loader.load_table_data_with_cache(table_name, page, page_size)
            load_time = (time.time() - start_time) * 1000
            load_times.append(load_time)
            
            print(f"  ✓ 从缓存: {from_cache}")
            print(f"  ✓ 加载时间: {load_time:.1f}ms")
            print(f"  ✓ 数据条数: {len(data) if data else 0}")
            
            assert from_cache == False, f"第{page}页首次加载应该不是从缓存"
        
        # 重新加载第1页，应该从缓存
        print(f"\n重新加载第1页...")
        start_time = time.time()
        data, meta, from_cache = self.cache_loader.load_table_data_with_cache(table_name, 1, page_size)
        cache_load_time = (time.time() - start_time) * 1000
        
        print(f"  ✓ 从缓存: {from_cache}")
        print(f"  ✓ 加载时间: {cache_load_time:.1f}ms")
        print(f"  ✓ 性能提升: {((load_times[0] - cache_load_time) / load_times[0] * 100):.1f}%")
        
        assert from_cache == True, "重新加载第1页应该从缓存"
        
        print("\n  ✅ 不同页面缓存测试通过")
    
    def test_sort_caching(self):
        """🧪 [测试3] 排序缓存测试"""
        
        print("\n🧪 [测试3] 排序缓存测试")
        print("=" * 50)
        
        table_name = "员工表"
        page = 1
        page_size = 20
        
        # 无排序加载
        print("无排序加载...")
        data1, meta1, from_cache1 = self.cache_loader.load_table_data_with_cache(table_name, page, page_size)
        print(f"  ✓ 从缓存: {from_cache1}")
        
        # 带排序加载
        sort_columns = [{'column_name': 'salary', 'order': 'desc'}]
        print("\n带排序加载...")
        data2, meta2, from_cache2 = self.cache_loader.load_table_data_with_cache(
            table_name, page, page_size, sort_columns
        )
        print(f"  ✓ 从缓存: {from_cache2}")
        
        # 重复相同排序
        print("\n重复相同排序...")
        data3, meta3, from_cache3 = self.cache_loader.load_table_data_with_cache(
            table_name, page, page_size, sort_columns
        )
        print(f"  ✓ 从缓存: {from_cache3}")
        
        # 验证排序缓存
        assert from_cache2 == False, "带排序的首次加载应该不是从缓存"
        assert from_cache3 == True, "重复排序加载应该从缓存"
        
        print("\n  ✅ 排序缓存测试通过")
    
    def test_cache_performance(self):
        """🧪 [测试4] 缓存性能统计测试"""
        
        print("\n🧪 [测试4] 缓存性能统计测试")
        print("=" * 50)
        
        table_name = "性能测试表"
        
        # 执行多次加载操作
        operations = [
            (1, 20, None),  # 第1页，无排序
            (2, 20, None),  # 第2页，无排序
            (1, 20, None),  # 重复第1页
            (1, 20, [{'column_name': 'name', 'order': 'asc'}]),  # 第1页，带排序
            (2, 20, None),  # 重复第2页
            (1, 20, [{'column_name': 'name', 'order': 'asc'}]),  # 重复排序
        ]
        
        cache_hits = 0
        total_operations = len(operations)
        
        for i, (page, page_size, sort_columns) in enumerate(operations):
            print(f"\n操作 {i+1}: 第{page}页, 排序={bool(sort_columns)}")
            data, meta, from_cache = self.cache_loader.load_table_data_with_cache(
                table_name, page, page_size, sort_columns
            )
            
            if from_cache:
                cache_hits += 1
                print(f"  ✓ 缓存命中")
            else:
                print(f"  ✓ 数据库查询")
        
        # 获取性能报告
        print(f"\n📊 性能统计:")
        print(f"  • 总操作数: {total_operations}")
        print(f"  • 缓存命中: {cache_hits}")
        print(f"  • 命中率: {(cache_hits / total_operations * 100):.1f}%")
        print(f"  • 数据库调用: {self.mock_service._call_count}")
        
        performance_report = self.cache_loader.get_cache_performance_report()
        print(performance_report)
        
        print("\n  ✅ 缓存性能统计测试通过")
    
    def run_all_tests(self):
        """🧪 [执行] 运行所有缓存测试"""
        
        print("🧪 [开始] 缓存优化测试套件")
        print("=" * 60)
        
        try:
            self.test_basic_caching()
            self.test_different_pages()
            self.test_sort_caching()
            self.test_cache_performance()
            
            print("\n🎉 [成功] 所有缓存测试通过！")
            print("=" * 60)
            
            # 最终统计
            print(f"\n📊 最终统计:")
            print(f"  • 模拟数据库调用总数: {self.mock_service._call_count}")
            stats = self.cache_loader.cache.get_cache_stats()
            print(f"  • 分页缓存条目: {stats['page_cache_size']}")
            print(f"  • 分页命中率: {stats['page_hit_rate']}")
            print(f"  • 总缓存命中: {stats['page_hits']}")
            print(f"  • 总缓存未命中: {stats['page_misses']}")
            
        except Exception as e:
            print(f"\n❌ [失败] 测试失败: {e}")
            raise
        finally:
            # 清理
            self.cache_loader.shutdown()
            print("\n🧹 [清理] 测试环境已清理")


if __name__ == "__main__":
    """🧪 [入口] 缓存测试入口"""
    
    print("🚀 启动独立缓存测试...")
    
    test_runner = CacheTestRunner()
    test_runner.run_all_tests()
    
    print("\n✅ 缓存测试完成！") 