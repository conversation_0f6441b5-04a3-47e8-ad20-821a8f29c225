#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复QTimer.singleShot线程安全问题
"""

import os
import re
from pathlib import Path

def fix_qtimer_singleshot_in_file(file_path: Path) -> bool:
    """修复单个文件中的QTimer.singleShot调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 添加ThreadSafeTimer导入（如果还没有的话）
        if 'from src.utils.thread_safe_timer import ThreadSafeTimer' not in content:
            # 在文件开头的导入区域添加导入
            import_pattern = r'(from PyQt5\.QtCore.*?import.*?QTimer.*?\n)'
            if re.search(import_pattern, content):
                content = re.sub(
                    import_pattern,
                    r'\1from src.utils.thread_safe_timer import ThreadSafeTimer\n',
                    content
                )
            else:
                # 如果没有找到PyQt5导入，在第一个import后添加
                first_import_pattern = r'(^import.*?\n)'
                if re.search(first_import_pattern, content, re.MULTILINE):
                    content = re.sub(
                        first_import_pattern,
                        r'\1from src.utils.thread_safe_timer import ThreadSafeTimer\n',
                        content,
                        count=1
                    )
        
        # 替换QTimer.singleShot调用
        # 模式1: QTimer.singleShot(delay, callback)
        pattern1 = r'QTimer\.singleShot\((\d+),\s*([^)]+)\)'
        replacement1 = r'ThreadSafeTimer.safe_single_shot(\1, \2)'
        content = re.sub(pattern1, replacement1, content)
        
        # 检查是否有修改
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    # 需要修复的文件列表
    files_to_fix = [
        "src/gui/drag_drop_table.py",
        "src/gui/main_dialogs.py", 
        "src/gui/table_header_manager.py",
        "src/gui/state_manager.py",
        "src/gui/widgets.py",
        "src/gui/prototype/widgets/enhanced_navigation_panel.py",
        "src/gui/prototype/prototype_main_window.py",
        "src/gui/modern_demo.py",
        "src/gui/prototype/widgets/virtualized_expandable_table.py",
        "src/gui/prototype/widgets/optimized_table_renderer.py",
        "src/gui/prototype/widgets/material_tab_widget.py"
    ]
    
    print("🔧 开始批量修复QTimer.singleShot线程安全问题...")
    
    fixed_count = 0
    for file_path in files_to_fix:
        full_path = project_root / file_path
        if full_path.exists():
            if fix_qtimer_singleshot_in_file(full_path):
                fixed_count += 1
        else:
            print(f"⚠️ 文件不存在: {full_path}")
    
    print(f"\n✅ 批量修复完成！共修复 {fixed_count} 个文件")
    
    # 验证修复结果
    print("\n🔍 验证修复结果...")
    remaining_calls = 0
    for file_path in files_to_fix:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                calls = len(re.findall(r'QTimer\.singleShot', content))
                if calls > 0:
                    print(f"⚠️ {full_path}: 仍有 {calls} 个QTimer.singleShot调用")
                    remaining_calls += calls
            except Exception as e:
                print(f"❌ 验证失败 {full_path}: {e}")
    
    if remaining_calls == 0:
        print("✅ 所有QTimer.singleShot调用已成功替换为线程安全版本！")
    else:
        print(f"⚠️ 仍有 {remaining_calls} 个QTimer.singleShot调用需要手动处理")

if __name__ == "__main__":
    main()