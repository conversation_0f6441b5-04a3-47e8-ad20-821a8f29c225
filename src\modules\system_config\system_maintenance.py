"""
月度工资异动处理系统 - 系统维护工具模块

本模块实现系统维护相关功能：
- 系统诊断和健康检查
- 临时文件清理
- 日志文件管理
- 数据库维护
- 系统信息收集
- 性能监控和优化建议

功能函数：
- system_diagnosis(): 系统诊断
- cleanup_temp_files(): 清理临时文件
- manage_log_files(): 管理日志文件
- database_maintenance(): 数据库维护
- collect_system_info(): 收集系统信息
- performance_check(): 性能检查

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import os
import sys
import shutil
import sqlite3
import psutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import subprocess
import platform

# 导入项目内部模块
from src.utils.log_config import setup_logger
from .config_manager import ConfigManager

# 初始化日志
logger = setup_logger("system_config.system_maintenance")


class SystemMaintenance:
    """系统维护工具，提供系统诊断、清理和维护功能"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化系统维护工具
        
        Args:
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("system_config.system_maintenance")
        
        # 配置管理器
        self.config_manager = config_manager or ConfigManager()
        
        # 获取项目根目录
        current_file = Path(__file__)
        self.project_root = current_file.parent.parent.parent.parent
        
        # 系统路径
        self.temp_dir = self.project_root / "temp"
        self.logs_dir = self.project_root / "logs"
        self.output_dir = self.project_root / "output"
        
        self.logger.info("系统维护工具初始化完成")
    
    def system_diagnosis(self) -> Dict[str, Any]:
        """
        执行系统诊断
        
        Returns:
            Dict[str, Any]: 诊断结果
        """
        self.logger.info("开始系统诊断...")
        
        diagnosis_result = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unknown",
            "checks": {}
        }
        
        try:
            # 检查项目目录结构
            diagnosis_result["checks"]["directory_structure"] = self._check_directory_structure()
            
            # 检查配置文件
            diagnosis_result["checks"]["config_files"] = self._check_config_files()
            
            # 检查依赖库
            diagnosis_result["checks"]["dependencies"] = self._check_dependencies()
            
            # 检查数据库
            diagnosis_result["checks"]["database"] = self._check_database()
            
            # 检查磁盘空间
            diagnosis_result["checks"]["disk_space"] = self._check_disk_space()
            
            # 检查内存使用
            diagnosis_result["checks"]["memory_usage"] = self._check_memory_usage()
            
            # 检查日志文件
            diagnosis_result["checks"]["log_files"] = self._check_log_files()
            
            # 检查临时文件
            diagnosis_result["checks"]["temp_files"] = self._check_temp_files()
            
            # 计算总体状态
            diagnosis_result["overall_status"] = self._calculate_overall_status(
                diagnosis_result["checks"]
            )
            
            self.logger.info(f"系统诊断完成，总体状态: {diagnosis_result['overall_status']}")
            
        except Exception as e:
            self.logger.error(f"系统诊断失败: {str(e)}")
            diagnosis_result["error"] = str(e)
            diagnosis_result["overall_status"] = "error"
        
        return diagnosis_result
    
    def _check_directory_structure(self) -> Dict[str, Any]:
        """检查项目目录结构"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        required_dirs = [
            "src", "src/modules", "src/utils", "logs", "temp", 
            "output", "template", "data", "docs"
        ]
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                check_result["details"][dir_name] = "exists"
            else:
                check_result["details"][dir_name] = "missing"
                check_result["issues"].append(f"缺少目录: {dir_name}")
        
        if check_result["issues"]:
            check_result["status"] = "warning"
        
        return check_result
    
    def _check_config_files(self) -> Dict[str, Any]:
        """检查配置文件"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        config_files = [
            "config.json",
            "requirements.txt"
        ]
        
        for file_name in config_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                try:
                    # 检查配置文件是否有效
                    if file_name == "config.json":
                        config = self.config_manager.load_config()
                        check_result["details"][file_name] = "valid"
                    else:
                        check_result["details"][file_name] = "exists"
                except Exception as e:
                    check_result["details"][file_name] = f"invalid: {str(e)}"
                    check_result["issues"].append(f"配置文件 {file_name} 无效: {str(e)}")
            else:
                check_result["details"][file_name] = "missing"
                check_result["issues"].append(f"缺少配置文件: {file_name}")
        
        if check_result["issues"]:
            check_result["status"] = "fail" if "config.json" in str(check_result["issues"]) else "warning"
        
        return check_result
    
    def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖库"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        required_packages = [
            "pandas", "openpyxl", "xlrd", "PyQt5", "loguru", 
            "docxtpl", "python-docx", "pytest", "black", "flake8"
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                check_result["details"][package] = "installed"
            except ImportError:
                check_result["details"][package] = "missing"
                check_result["issues"].append(f"缺少依赖包: {package}")
        
        if check_result["issues"]:
            check_result["status"] = "warning"
        
        return check_result
    
    def _check_database(self) -> Dict[str, Any]:
        """检查数据库"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        try:
            # 获取数据库配置
            db_name = self.config_manager.get_config_value("database.db_name", "salary_system.db")
            db_path = self.project_root / db_name
            
            if db_path.exists():
                # 尝试连接数据库
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                # 检查数据库版本
                cursor.execute("PRAGMA user_version")
                version = cursor.fetchone()[0]
                check_result["details"]["version"] = version
                
                # 检查表数量
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                check_result["details"]["table_count"] = table_count
                
                # 检查数据库大小
                check_result["details"]["size_mb"] = round(db_path.stat().st_size / 1024 / 1024, 2)
                
                conn.close()
                check_result["details"]["status"] = "accessible"
            else:
                check_result["details"]["status"] = "not_found"
                check_result["issues"].append("数据库文件不存在")
                check_result["status"] = "warning"
        
        except Exception as e:
            check_result["details"]["status"] = f"error: {str(e)}"
            check_result["issues"].append(f"数据库检查失败: {str(e)}")
            check_result["status"] = "fail"
        
        return check_result
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        try:
            # 获取项目目录所在磁盘的空间信息
            disk_usage = shutil.disk_usage(self.project_root)
            
            total_gb = disk_usage.total / (1024**3)
            free_gb = disk_usage.free / (1024**3)
            used_gb = (disk_usage.total - disk_usage.free) / (1024**3)
            
            free_percentage = (free_gb / total_gb) * 100
            
            check_result["details"] = {
                "total_gb": round(total_gb, 2),
                "used_gb": round(used_gb, 2),
                "free_gb": round(free_gb, 2),
                "free_percentage": round(free_percentage, 2)
            }
            
            # 判断磁盘空间状态
            if free_percentage < 5:
                check_result["status"] = "fail"
                check_result["issues"].append("磁盘空间严重不足（<5%）")
            elif free_percentage < 15:
                check_result["status"] = "warning"
                check_result["issues"].append("磁盘空间不足（<15%）")
        
        except Exception as e:
            check_result["status"] = "error"
            check_result["issues"].append(f"磁盘空间检查失败: {str(e)}")
        
        return check_result
    
    def _check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        try:
            # 获取系统内存信息
            memory = psutil.virtual_memory()
            
            check_result["details"] = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": round(memory.percent, 2)
            }
            
            # 判断内存使用状态
            if memory.percent > 90:
                check_result["status"] = "fail"
                check_result["issues"].append("内存使用率过高（>90%）")
            elif memory.percent > 80:
                check_result["status"] = "warning"
                check_result["issues"].append("内存使用率较高（>80%）")
        
        except Exception as e:
            check_result["status"] = "error"
            check_result["issues"].append(f"内存检查失败: {str(e)}")
        
        return check_result
    
    def _check_log_files(self) -> Dict[str, Any]:
        """检查日志文件"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        try:
            if self.logs_dir.exists():
                log_files = list(self.logs_dir.glob("*.log"))
                total_size = sum(f.stat().st_size for f in log_files)
                
                check_result["details"] = {
                    "file_count": len(log_files),
                    "total_size_mb": round(total_size / (1024**2), 2)
                }
                
                # 检查日志文件大小
                if total_size > 100 * 1024**2:  # 100MB
                    check_result["status"] = "warning"
                    check_result["issues"].append("日志文件总大小过大（>100MB）")
            else:
                check_result["details"] = {"file_count": 0, "total_size_mb": 0}
                check_result["issues"].append("日志目录不存在")
                check_result["status"] = "warning"
        
        except Exception as e:
            check_result["status"] = "error"
            check_result["issues"].append(f"日志文件检查失败: {str(e)}")
        
        return check_result
    
    def _check_temp_files(self) -> Dict[str, Any]:
        """检查临时文件"""
        check_result = {
            "status": "pass",
            "details": {},
            "issues": []
        }
        
        try:
            if self.temp_dir.exists():
                temp_files = list(self.temp_dir.rglob("*"))
                temp_files = [f for f in temp_files if f.is_file()]
                total_size = sum(f.stat().st_size for f in temp_files)
                
                check_result["details"] = {
                    "file_count": len(temp_files),
                    "total_size_mb": round(total_size / (1024**2), 2)
                }
                
                # 检查临时文件大小
                if total_size > 50 * 1024**2:  # 50MB
                    check_result["status"] = "warning"
                    check_result["issues"].append("临时文件总大小过大（>50MB）")
            else:
                check_result["details"] = {"file_count": 0, "total_size_mb": 0}
        
        except Exception as e:
            check_result["status"] = "error"
            check_result["issues"].append(f"临时文件检查失败: {str(e)}")
        
        return check_result
    
    def _calculate_overall_status(self, checks: Dict[str, Dict[str, Any]]) -> str:
        """计算总体状态"""
        statuses = [check.get("status", "unknown") for check in checks.values()]
        
        if "fail" in statuses:
            return "fail"
        elif "error" in statuses:
            return "error"
        elif "warning" in statuses:
            return "warning"
        else:
            return "pass"
    
    def cleanup_temp_files(self, older_than_days: int = 7) -> Dict[str, Any]:
        """
        清理临时文件
        
        Args:
            older_than_days: 清理多少天前的文件
            
        Returns:
            Dict[str, Any]: 清理结果
        """
        self.logger.info(f"开始清理 {older_than_days} 天前的临时文件...")
        
        cleanup_result = {
            "timestamp": datetime.now().isoformat(),
            "deleted_files": 0,
            "deleted_size_mb": 0,
            "errors": []
        }
        
        try:
            if not self.temp_dir.exists():
                self.logger.warning("临时目录不存在")
                return cleanup_result
            
            cutoff_time = datetime.now() - timedelta(days=older_than_days)
            
            for file_path in self.temp_dir.rglob("*"):
                if file_path.is_file():
                    try:
                        # 检查文件修改时间
                        file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                        
                        if file_mtime < cutoff_time:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            
                            cleanup_result["deleted_files"] += 1
                            cleanup_result["deleted_size_mb"] += file_size / (1024**2)
                            
                            self.logger.debug(f"已删除临时文件: {file_path}")
                    
                    except Exception as e:
                        error_msg = f"删除文件失败 {file_path}: {str(e)}"
                        cleanup_result["errors"].append(error_msg)
                        self.logger.error(error_msg)
            
            # 删除空目录
            for dir_path in reversed(list(self.temp_dir.rglob("*"))):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    try:
                        dir_path.rmdir()
                        self.logger.debug(f"已删除空目录: {dir_path}")
                    except Exception as e:
                        error_msg = f"删除空目录失败 {dir_path}: {str(e)}"
                        cleanup_result["errors"].append(error_msg)
            
            cleanup_result["deleted_size_mb"] = round(cleanup_result["deleted_size_mb"], 2)
            
            self.logger.info(
                f"临时文件清理完成: 删除 {cleanup_result['deleted_files']} 个文件, "
                f"释放 {cleanup_result['deleted_size_mb']} MB 空间"
            )
        
        except Exception as e:
            error_msg = f"清理临时文件失败: {str(e)}"
            cleanup_result["errors"].append(error_msg)
            self.logger.error(error_msg)
        
        return cleanup_result
    
    def manage_log_files(self, max_size_mb: int = 50, max_age_days: int = 30) -> Dict[str, Any]:
        """
        管理日志文件
        
        Args:
            max_size_mb: 日志文件最大大小（MB）
            max_age_days: 日志文件最大保留天数
            
        Returns:
            Dict[str, Any]: 管理结果
        """
        self.logger.info("开始日志文件管理...")
        
        management_result = {
            "timestamp": datetime.now().isoformat(),
            "archived_files": 0,
            "deleted_files": 0,
            "total_freed_mb": 0,
            "errors": []
        }
        
        try:
            if not self.logs_dir.exists():
                self.logger.warning("日志目录不存在")
                return management_result
            
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            max_size_bytes = max_size_mb * 1024**2
            
            log_files = list(self.logs_dir.glob("*.log"))
            
            for log_file in log_files:
                try:
                    file_stat = log_file.stat()
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
                    file_size = file_stat.st_size
                    
                    # 删除过旧的日志文件
                    if file_mtime < cutoff_time:
                        log_file.unlink()
                        management_result["deleted_files"] += 1
                        management_result["total_freed_mb"] += file_size / (1024**2)
                        self.logger.info(f"已删除过旧日志文件: {log_file}")
                        continue
                    
                    # 压缩过大的日志文件
                    if file_size > max_size_bytes:
                        archived_file = self._archive_log_file(log_file)
                        if archived_file:
                            management_result["archived_files"] += 1
                            self.logger.info(f"已归档大日志文件: {log_file} -> {archived_file}")
                
                except Exception as e:
                    error_msg = f"处理日志文件失败 {log_file}: {str(e)}"
                    management_result["errors"].append(error_msg)
                    self.logger.error(error_msg)
            
            management_result["total_freed_mb"] = round(management_result["total_freed_mb"], 2)
            
            self.logger.info(
                f"日志文件管理完成: 归档 {management_result['archived_files']} 个文件, "
                f"删除 {management_result['deleted_files']} 个文件, "
                f"释放 {management_result['total_freed_mb']} MB 空间"
            )
        
        except Exception as e:
            error_msg = f"日志文件管理失败: {str(e)}"
            management_result["errors"].append(error_msg)
            self.logger.error(error_msg)
        
        return management_result
    
    def _archive_log_file(self, log_file: Path) -> Optional[Path]:
        """
        归档日志文件
        
        Args:
            log_file: 日志文件路径
            
        Returns:
            Optional[Path]: 归档文件路径，失败返回None
        """
        try:
            # 创建归档目录
            archive_dir = self.logs_dir / "archived"
            archive_dir.mkdir(exist_ok=True)
            
            # 生成归档文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"{log_file.stem}_{timestamp}.log"
            archive_path = archive_dir / archive_name
            
            # 移动文件到归档目录
            shutil.move(str(log_file), str(archive_path))
            
            return archive_path
        
        except Exception as e:
            self.logger.error(f"归档日志文件失败: {str(e)}")
            return None
    
    def collect_system_info(self) -> Dict[str, Any]:
        """
        收集系统信息
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        self.logger.info("收集系统信息...")
        
        system_info = {
            "timestamp": datetime.now().isoformat(),
            "platform": {},
            "python": {},
            "hardware": {},
            "project": {}
        }
        
        try:
            # 平台信息
            system_info["platform"] = {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor()
            }
            
            # Python信息
            system_info["python"] = {
                "version": sys.version,
                "executable": sys.executable,
                "platform": sys.platform
            }
            
            # 硬件信息
            memory = psutil.virtual_memory()
            disk_usage = shutil.disk_usage(self.project_root)
            
            system_info["hardware"] = {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "disk_total_gb": round(disk_usage.total / (1024**3), 2),
                "disk_free_gb": round(disk_usage.free / (1024**3), 2)
            }
            
            # 项目信息
            config = self.config_manager.load_config()
            system_info["project"] = {
                "version": config.get("version", "unknown"),
                "root_path": str(self.project_root),
                "debug_mode": config.get("debug_mode", False),
                "log_level": config.get("log_level", "INFO")
            }
        
        except Exception as e:
            self.logger.error(f"收集系统信息失败: {str(e)}")
            system_info["error"] = str(e)
        
        return system_info
    
    def performance_check(self) -> Dict[str, Any]:
        """
        性能检查和优化建议
        
        Returns:
            Dict[str, Any]: 性能检查结果
        """
        self.logger.info("开始性能检查...")
        
        performance_result = {
            "timestamp": datetime.now().isoformat(),
            "cpu_usage": 0,
            "memory_usage": 0,
            "recommendations": [],
            "scores": {}
        }
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            performance_result["cpu_usage"] = cpu_percent
            
            # 内存使用率
            memory = psutil.virtual_memory()
            performance_result["memory_usage"] = memory.percent
            
            # 性能评分和建议
            scores = {}
            
            # CPU评分
            if cpu_percent < 20:
                scores["cpu"] = "excellent"
            elif cpu_percent < 50:
                scores["cpu"] = "good"
            elif cpu_percent < 80:
                scores["cpu"] = "fair"
                performance_result["recommendations"].append("CPU使用率较高，考虑关闭不必要的程序")
            else:
                scores["cpu"] = "poor"
                performance_result["recommendations"].append("CPU使用率过高，建议重启系统或优化后台程序")
            
            # 内存评分
            if memory.percent < 50:
                scores["memory"] = "excellent"
            elif memory.percent < 70:
                scores["memory"] = "good"
            elif memory.percent < 85:
                scores["memory"] = "fair"
                performance_result["recommendations"].append("内存使用率较高，建议关闭不必要的应用程序")
            else:
                scores["memory"] = "poor"
                performance_result["recommendations"].append("内存使用率过高，建议释放内存或增加内存容量")
            
            # 磁盘空间评分
            disk_usage = shutil.disk_usage(self.project_root)
            free_percentage = (disk_usage.free / disk_usage.total) * 100
            
            if free_percentage > 30:
                scores["disk"] = "excellent"
            elif free_percentage > 15:
                scores["disk"] = "good"
            elif free_percentage > 5:
                scores["disk"] = "fair"
                performance_result["recommendations"].append("磁盘空间不足，建议清理不必要的文件")
            else:
                scores["disk"] = "poor"
                performance_result["recommendations"].append("磁盘空间严重不足，请立即清理磁盘空间")
            
            performance_result["scores"] = scores
            
            # 项目特定的优化建议
            if not performance_result["recommendations"]:
                performance_result["recommendations"].append("系统性能良好，无需特别优化")
            
            self.logger.info("性能检查完成")
        
        except Exception as e:
            self.logger.error(f"性能检查失败: {str(e)}")
            performance_result["error"] = str(e)
        
        return performance_result 