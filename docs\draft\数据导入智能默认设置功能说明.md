# 数据导入智能默认设置功能说明

## 概述

本文档详细说明了月度工资异动处理系统中新实现的数据导入智能默认设置功能。该功能通过智能算法自动优化数据导入流程，提升用户体验和工作效率。

## 功能特性

### 1. 智能工作表匹配

系统能够根据用户选择的人员类别，自动匹配Excel文件中最合适的工作表。

#### 匹配算法

- **精确匹配**：工作表名称与人员类别完全一致
- **模糊匹配**：基于关键词包含关系的匹配
- **语义匹配**：基于语义相似度的智能匹配
- **用户偏好**：学习并应用用户的历史选择偏好

#### 支持的人员类别

| 人员类别 | 匹配关键词 |
|---------|-----------|
| 全部在职人员 | 全部在职人员、在职人员、全部人员、所有人员、全员、在职 |
| 离休人员 | 离休人员、离休、离休干部、离休职工 |
| 退休人员 | 退休人员、退休、退休职工、退休干部 |
| A岗职工 | A岗职工、A岗、A类人员、A岗人员 |
| 临时工 | 临时工、临时人员、临聘人员、临时职工 |
| 实习生 | 实习生、实习人员、见习生、实习 |

### 2. 智能默认设置

系统根据不同的人员类别自动调整导入参数，提供最优的导入配置。

#### 默认设置项

- **起始行**：默认值为1（之前为2）
- **导入模式**：默认选择"多Sheet模式"（之前为单Sheet模式）
- **包含表头**：默认启用
- **跳过空行**：默认启用
- **自动匹配工作表**：默认启用

#### 类别特定设置

| 人员类别 | 导入模式 | 表创建模式 | 说明 |
|---------|---------|-----------|------|
| 全部在职人员 | 多Sheet模式 | 工作表名 | 适合处理大量数据 |
| 离休人员 | 单Sheet模式 | 工作表名 | 数据量较少，单表处理更高效 |
| 退休人员 | 单Sheet模式 | 工作表名 | 数据量较少，单表处理更高效 |
| 临时工 | 单Sheet模式 | 自定义名称 | 需要特殊标识 |
| 实习生 | 单Sheet模式 | 自定义名称 | 需要特殊标识 |

### 3. 用户反馈系统

系统提供实时的状态反馈，让用户了解自动匹配的结果和建议。

#### 反馈类型

- **成功提示**（绿色）：成功匹配工作表
- **警告提示**（黄色）：未找到匹配项，需要手动选择
- **信息提示**（蓝色）：系统建议和优化提示
- **错误提示**（红色）：操作失败或异常情况

## 使用方法

### 基本使用流程

1. **打开数据导入对话框**
   - 点击主界面的"导入Excel工资表"按钮

2. **选择数据目标位置**
   - 在"数据目标位置"区域选择年份、月份和人员类别
   - 系统会自动应用该类别的智能默认设置

3. **选择Excel文件**
   - 点击"浏览..."按钮选择Excel文件
   - 系统会自动加载工作表列表并执行智能匹配

4. **确认匹配结果**
   - 查看状态消息了解匹配结果
   - 如需要可手动调整工作表选择

5. **开始导入**
   - 确认所有设置后点击"开始导入"

### 高级功能

#### 自定义匹配规则

用户可以通过配置文件自定义匹配规则：

```json
{
  "category_sheet_mapping": {
    "自定义类别": ["关键词1", "关键词2", "关键词3"]
  }
}
```

#### 用户偏好学习

系统会自动记录用户的选择偏好：
- 当用户多次为同一类别选择特定工作表时
- 系统会学习这个偏好并在后续匹配中优先推荐

## 技术实现

### 核心组件

#### 1. ImportDefaultsManager
- **功能**：管理导入默认设置
- **位置**：`src/modules/data_import/import_defaults_manager.py`
- **主要方法**：
  - `load_settings()`: 加载设置
  - `save_settings()`: 保存设置
  - `find_matching_sheet()`: 查找匹配工作表
  - `get_smart_defaults_for_category()`: 获取类别特定设置

#### 2. SmartSheetMatcher
- **功能**：智能工作表匹配
- **位置**：`src/modules/data_import/smart_sheet_matcher.py`
- **主要方法**：
  - `find_best_match()`: 查找最佳匹配
  - `get_match_suggestions()`: 获取匹配建议
  - `record_user_choice()`: 记录用户选择

#### 3. DataImportDialog增强
- **功能**：集成智能功能到用户界面
- **位置**：`src/gui/dialogs.py`
- **新增方法**：
  - `_apply_default_settings()`: 应用默认设置
  - `_auto_select_sheet_by_category()`: 自动选择工作表
  - `show_status_message()`: 显示状态消息

### 配置文件

#### 默认设置配置
- **文件路径**：`state/data/import_defaults.json`
- **内容**：导入参数的默认值

#### 类别映射配置
- **文件路径**：`state/data/category_sheet_mapping.json`
- **内容**：人员类别与工作表关键词的映射关系

### 匹配算法详解

#### 得分计算

1. **精确匹配**：得分 = 1.0
2. **模糊匹配**：得分 = 关键词长度 / 工作表名长度 × 0.9
3. **语义匹配**：得分 = 语义相似度 × 0.7
4. **用户偏好**：得分 = min(1.0, 使用次数 × 0.1)

#### 匹配阈值

- 最低匹配阈值：0.3
- 只有得分超过阈值的匹配才会被采用

## 配置和自定义

### 修改默认设置

用户可以通过修改配置文件来自定义默认设置：

```json
{
  "start_row": 1,
  "import_mode": "multi_sheet",
  "auto_match_sheet": true,
  "include_header": true,
  "skip_empty_rows": true
}
```

### 添加新的人员类别

1. 在目标选择组件中添加新类别
2. 在映射配置中定义匹配关键词
3. 可选：为新类别定义特定的默认设置

### 调整匹配算法

可以通过修改 `SmartSheetMatcher` 类来调整匹配算法：
- 修改得分权重
- 添加新的匹配类型
- 调整匹配阈值

## 故障排除

### 常见问题

#### 1. 自动匹配不工作
- **原因**：可能是配置文件损坏或缺失
- **解决**：删除配置文件让系统重新生成默认配置

#### 2. 匹配结果不准确
- **原因**：关键词映射不完整
- **解决**：更新类别映射配置，添加更多关键词

#### 3. 状态消息不显示
- **原因**：界面组件初始化问题
- **解决**：重启应用程序

### 日志调试

系统会记录详细的操作日志：
- 匹配过程和结果
- 用户选择记录
- 错误和异常信息

日志文件位置：`logs/salary_system.log`

## 性能考虑

### 优化措施

1. **缓存机制**：缓存工作表列表和匹配结果
2. **延迟加载**：只在需要时加载配置文件
3. **异步处理**：大文件的工作表加载使用异步处理

### 性能指标

- 匹配算法响应时间：< 100ms
- 配置文件加载时间：< 50ms
- 用户界面响应时间：< 200ms

## 未来改进

### 计划功能

1. **机器学习增强**：使用更高级的机器学习算法改进匹配准确性
2. **云端同步**：支持配置和偏好的云端同步
3. **批量处理**：支持批量文件的智能匹配
4. **API接口**：提供API接口供其他系统调用

### 反馈和建议

如有任何问题或建议，请联系开发团队或在系统中提交反馈。

---

*文档版本：1.0*  
*最后更新：2025-01-20*  
*作者：系统开发团队*
