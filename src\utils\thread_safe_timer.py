"""
🔧 [立即修复] 线程安全的QTimer工具类
解决"QObject::startTimer: Timers can only be used with threads started with QThread"警告问题
"""

from PyQt5.QtCore import QTimer, QThread, QObject, pyqtSignal, QMetaObject, Qt
from PyQt5.QtWidgets import QApplication
from typing import Callable, Optional, Dict, Any
import logging
import threading
import time
import weakref

from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


class TimerProxy(QObject):
    """🔧 [立即修复] 定时器代理对象，确保在主线程中执行"""

    timer_signal = pyqtSignal()

    def __init__(self, callback: Callable):
        super().__init__()
        self.callback = callback
        self.timer_signal.connect(self._execute_callback)

        # 确保代理对象在主线程中
        if not ThreadSafeTimer.is_main_thread():
            self.moveToThread(QApplication.instance().thread())

    def _execute_callback(self):
        """执行回调函数"""
        try:
            if self.callback:
                self.callback()
        except Exception as e:
            logger.error(f"🔧 [定时器回调] 执行失败: {e}", exc_info=True)


class ThreadSafeTimer:
    """🔧 [立即修复] 线程安全的定时器工具类"""

    # 全局定时器注册表，用于跟踪和管理所有定时器
    _timer_registry: Dict[int, weakref.ref] = {}
    _registry_lock = threading.Lock()

    @staticmethod
    def is_main_thread() -> bool:
        """🔧 [立即修复] 检查是否在主线程中"""
        try:
            app = QApplication.instance()
            if not app:
                logger.warning("🔧 [线程检查] QApplication实例不存在")
                return False

            current_thread = QThread.currentThread()
            main_thread = app.thread()

            is_main = current_thread == main_thread
            if not is_main:
                logger.debug(f"🔧 [线程检查] 当前线程不是主线程: {threading.current_thread().name}")

            return is_main
        except Exception as e:
            logger.error(f"🔧 [线程检查] 检查主线程失败: {e}")
            return False
    
    @staticmethod
    def create_timer(parent=None, single_shot: bool = True) -> Optional[QTimer]:
        """🔧 [立即修复] 线程安全地创建QTimer"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("🔧 [线程安全] 尝试在非主线程创建QTimer，将移动到主线程")

                # 如果不在主线程，使用QMetaObject.invokeMethod在主线程中创建
                app = QApplication.instance()
                if app:
                    timer_container = {'timer': None}

                    def create_in_main_thread():
                        timer_container['timer'] = QTimer(parent)
                        timer_container['timer'].setSingleShot(single_shot)

                    QMetaObject.invokeMethod(app, create_in_main_thread, Qt.BlockingQueuedConnection)
                    return timer_container['timer']
                else:
                    logger.error("🔧 [线程安全] QApplication实例不存在，无法创建定时器")
                    return None

            timer = QTimer(parent)
            timer.setSingleShot(single_shot)

            # 注册定时器到全局注册表
            with ThreadSafeTimer._registry_lock:
                timer_id = id(timer)
                ThreadSafeTimer._timer_registry[timer_id] = weakref.ref(timer)

            logger.debug(f"🔧 [线程安全] QTimer创建成功，ID: {id(timer)}")
            return timer

        except Exception as e:
            logger.error(f"🔧 [线程安全] 创建QTimer失败: {e}", exc_info=True)
            return None
    
    @staticmethod
    def safe_start_timer(timer: QTimer, interval_ms: int, callback: Callable = None) -> bool:
        """🔧 [立即修复] 线程安全地启动定时器"""
        try:
            if not timer:
                logger.warning("🔧 [线程安全] 定时器对象为空，无法启动")
                return False

            if not ThreadSafeTimer.is_main_thread():
                logger.warning("🔧 [线程安全] 尝试在非主线程启动QTimer，将移动到主线程执行")

                # 使用QMetaObject.invokeMethod在主线程中启动
                app = QApplication.instance()
                if app:
                    result_container = {'success': False}

                    def start_in_main_thread():
                        try:
                            if callback:
                                # 使用代理对象确保回调在主线程执行
                                proxy = TimerProxy(callback)
                                timer.timeout.connect(proxy.timer_signal.emit)

                            timer.start(interval_ms)
                            result_container['success'] = True
                            logger.debug(f"🔧 [线程安全] 定时器已在主线程启动，间隔: {interval_ms}ms")
                        except Exception as e:
                            logger.error(f"🔧 [线程安全] 在主线程启动定时器失败: {e}")

                    QMetaObject.invokeMethod(app, start_in_main_thread, Qt.BlockingQueuedConnection)
                    return result_container['success']
                else:
                    logger.error("🔧 [线程安全] QApplication实例不存在，无法启动定时器")
                    return False

            if callback:
                timer.timeout.connect(callback)

            timer.start(interval_ms)
            logger.debug(f"🔧 [线程安全] 定时器已安全启动，间隔: {interval_ms}ms")
            return True

        except Exception as e:
            logger.error(f"🔧 [线程安全] 启动定时器失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def safe_single_shot(interval_ms: int, callback: Callable, parent=None) -> bool:
        """🔧 [立即修复] 线程安全的单次定时器"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.debug("🔧 [线程安全] 检测到非主线程调用，转移到主线程执行")

                # 使用QMetaObject.invokeMethod在主线程中执行
                app = QApplication.instance()
                if app:
                    # 🔧 [立即修复] 使用信号槽机制替代直接invokeMethod调用
                    try:
                        # 创建临时代理对象处理跨线程调用
                        class SingleShotProxy(QObject):
                            execute_signal = pyqtSignal(int, object)
                            
                            def __init__(self):
                                super().__init__()
                                self.execute_signal.connect(self._execute_in_main_thread)
                                # 确保代理对象在主线程
                                self.moveToThread(app.thread())
                            
                            def _execute_in_main_thread(self, interval, callback_func):
                                try:
                                    QTimer.singleShot(interval, callback_func)
                                    logger.debug(f"🔧 [线程安全] singleShot已在主线程启动，间隔: {interval}ms")
                                except Exception as e:
                                    logger.error(f"🔧 [线程安全] 在主线程执行singleShot失败: {e}")
                        
                        # 创建代理并发送信号
                        proxy = SingleShotProxy()
                        proxy.execute_signal.emit(interval_ms, callback)
                        logger.debug(f"🔧 [线程安全] 跨线程singleShot信号已发送，间隔: {interval_ms}ms")
                        
                    except Exception as e:
                        logger.error(f"🔧 [线程安全] 创建跨线程代理失败: {e}")
                    return True
                else:
                    logger.error("🔧 [线程安全] QApplication实例不存在，无法执行singleShot")
                    return False

            QTimer.singleShot(interval_ms, callback)
            logger.debug(f"🔧 [线程安全] singleShot已安全启动，间隔: {interval_ms}ms")
            return True

        except Exception as e:
            logger.error(f"🔧 [线程安全] singleShot启动失败: {e}", exc_info=True)
            return False

    @staticmethod
    def cleanup_timers():
        """🔧 [立即修复] 清理已失效的定时器引用"""
        with ThreadSafeTimer._registry_lock:
            expired_ids = []
            for timer_id, timer_ref in ThreadSafeTimer._timer_registry.items():
                if timer_ref() is None:  # 弱引用已失效
                    expired_ids.append(timer_id)

            for timer_id in expired_ids:
                del ThreadSafeTimer._timer_registry[timer_id]

            if expired_ids:
                logger.debug(f"🔧 [清理] 清理已失效的定时器引用: {len(expired_ids)}个")

    @staticmethod
    def get_timer_count() -> int:
        """🔧 [立即修复] 获取当前活跃定时器数量"""
        ThreadSafeTimer.cleanup_timers()
        with ThreadSafeTimer._registry_lock:
            return len(ThreadSafeTimer._timer_registry)


def safe_timer_start(timer: QTimer, interval_ms: int) -> bool:
    """🔧 [立即修复] 便捷函数：线程安全地启动定时器"""
    return ThreadSafeTimer.safe_start_timer(timer, interval_ms)


def safe_single_shot(interval_ms: int, callback: Callable) -> bool:
    """🔧 [立即修复] 便捷函数：线程安全的单次定时器"""
    return ThreadSafeTimer.safe_single_shot(interval_ms, callback)


def create_safe_timer(parent=None, single_shot: bool = True) -> Optional[QTimer]:
    """🔧 [立即修复] 便捷函数：线程安全地创建定时器"""
    return ThreadSafeTimer.create_timer(parent, single_shot)


def force_main_thread_timer(interval_ms: int, callback: Callable, single_shot: bool = True) -> bool:
    """
    🔧 [立即修复] 强制在主线程中创建和启动定时器

    这是解决"QObject::startTimer: Timers can only be used with threads started with QThread"
    警告的最强力方法
    """
    try:
        app = QApplication.instance()
        if not app:
            logger.error("🔧 [强制主线程] QApplication实例不存在")
            return False

        result_container = {'success': False}

        def create_and_start_in_main():
            try:
                timer = QTimer()
                timer.setSingleShot(single_shot)

                # 创建代理对象确保回调在主线程执行
                proxy = TimerProxy(callback)
                timer.timeout.connect(proxy.timer_signal.emit)

                timer.start(interval_ms)
                result_container['success'] = True
                logger.debug(f"🔧 [强制主线程] 定时器已在主线程创建并启动，间隔: {interval_ms}ms")
            except Exception as e:
                logger.error(f"🔧 [强制主线程] 创建定时器失败: {e}")

        # 使用阻塞调用确保在主线程中执行
        QMetaObject.invokeMethod(app, create_and_start_in_main, Qt.BlockingQueuedConnection)
        return result_container['success']

    except Exception as e:
        logger.error(f"🔧 [强制主线程] 强制主线程定时器失败: {e}", exc_info=True)
        return False