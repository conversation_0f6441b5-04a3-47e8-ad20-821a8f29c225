"""
月度工资异动处理系统 - GUI用户界面模块

本模块负责实现系统的图形用户界面，基于PyQt5开发。
包含主窗口、各功能窗口、数据展示控件和用户交互组件。

主要组件:
- ModernMainWindow: 现代化主窗口框架
- DataImportDialog: 数据导入对话框
- ChangeDetectionWindow: 异动检测窗口
- ReportGenerationWindow: 报告生成窗口
- SettingsDialog: 系统设置对话框
- ProgressDialog: 进度提示对话框
"""

from .prototype.prototype_main_window import PrototypeMainWindow as MainWindow
from .main_dialogs import (
    DataImportDialog,
    SettingsDialog,
    ProgressDialog,
    AboutDialog
)
# 使用绝对导入避免包名冲突
from src.gui.widgets import (
    DataTableWidget,
    ChangeDetectionWidget,
    ReportPreviewWidget
)
# 注释掉不存在的windows导入
# from .windows import (
#     ChangeDetectionWindow,
#     ReportGenerationWindow
# )

__all__ = [
    'MainWindow',
    'DataImportDialog',
    'SettingsDialog', 
    'ProgressDialog',
    'AboutDialog',
    'DataTableWidget',
    'ChangeDetectionWidget',
    'ReportPreviewWidget',
    # 'ChangeDetectionWindow',
    # 'ReportGenerationWindow'
]

__version__ = '2.0.0'
__author__ = '月度工资异动处理系统开发团队' 