#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证P0修复效果的测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.modules.format_management.field_registry import FieldRegistry

def test_field_registry_fix():
    """测试FieldRegistry修复效果"""
    print("测试FieldRegistry修复效果...")
    
    # 创建FieldRegistry实例
    registry = FieldRegistry()
    
    # 测试active_employees表的display_fields
    print("\n测试active_employees表的字段顺序:")
    display_fields = registry.get_display_fields('active_employees')
    
    if display_fields:
        print(f"SUCCESS: existing_display_fields不再为空: {len(display_fields)}个字段")
        
        # 检查关键字段顺序
        if "人员类别" in display_fields and "人员类别代码" in display_fields:
            type_idx = display_fields.index("人员类别")
            code_idx = display_fields.index("人员类别代码")
            
            if type_idx < code_idx:
                print(f"SUCCESS: 表头顺序正确: '人员类别'(位置{type_idx}) -> '人员类别代码'(位置{code_idx})")
            else:
                print(f"FAILED: 表头顺序错误: '人员类别'(位置{type_idx}) -> '人员类别代码'(位置{code_idx})")
        
        # 显示前10个字段
        print(f"前10个字段顺序: {display_fields[:10]}")
    else:
        print("FAILED: existing_display_fields仍然为空，修复失败")
    
    # 测试salary_data_2025_08_active_employees表
    print("\n📋 测试salary_data_2025_08_active_employees表的字段顺序:")
    specific_display_fields = registry.get_display_fields('salary_data_2025_08_active_employees')
    
    if specific_display_fields:
        print(f"✅ 具体表的existing_display_fields不再为空: {len(specific_display_fields)}个字段")
        
        # 检查关键字段顺序
        if "人员类别" in specific_display_fields and "人员类别代码" in specific_display_fields:
            type_idx = specific_display_fields.index("人员类别")
            code_idx = specific_display_fields.index("人员类别代码")
            
            if type_idx < code_idx:
                print(f"✅ 具体表表头顺序正确: '人员类别'(位置{type_idx}) -> '人员类别代码'(位置{code_idx})")
            else:
                print(f"❌ 具体表表头顺序错误: '人员类别'(位置{type_idx}) -> '人员类别代码'(位置{code_idx})")
        
        # 显示前10个字段
        print(f"📝 具体表前10个字段顺序: {specific_display_fields[:10]}")
    else:
        print("❌ 具体表existing_display_fields仍然为空，修复失败")

def test_field_mapping_fix():
    """测试field_mappings.json修复效果"""
    print("\n🧪 测试field_mappings.json修复效果...")
    
    try:
        with open('state/data/field_mappings.json', 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        
        # 检查salary_data_2025_08_active_employees的字段类型定义
        active_table = mappings.get('table_mappings', {}).get('salary_data_2025_08_active_employees', {})
        field_types = active_table.get('field_types', {})
        
        # 检查是否有sequence_number定义，没有sequence和row_number
        if 'sequence_number' in field_types:
            print("✅ sequence_number字段类型已定义")
        else:
            print("❌ sequence_number字段类型未定义")
        
        if 'sequence' not in field_types and 'row_number' not in field_types:
            print("✅ 多余的sequence和row_number字段类型已移除")
        else:
            print("❌ 仍存在多余的sequence或row_number字段类型")
        
        # 检查hidden_fields配置
        hidden_fields = active_table.get('hidden_fields', [])
        if 'sequence_number' in hidden_fields:
            print("✅ sequence_number在隐藏字段列表中")
        
        if 'sequence' not in hidden_fields and 'row_number' not in hidden_fields:
            print("✅ 多余的sequence和row_number已从隐藏字段列表移除")
        else:
            print("❌ 隐藏字段列表仍包含多余的sequence或row_number")
            
    except Exception as e:
        print(f"❌ 测试field_mappings.json失败: {e}")

def test_thread_safe_timer():
    """测试线程安全定时器"""
    print("\n🧪 测试线程安全定时器...")
    
    try:
        from src.utils.thread_safe_timer import ThreadSafeTimer
        
        # 测试主线程检查
        is_main = ThreadSafeTimer.is_main_thread()
        print(f"✅ 主线程检查功能正常: {is_main}")
        
        # 测试创建定时器
        timer = ThreadSafeTimer.create_timer()
        if timer:
            print("✅ 线程安全定时器创建成功")
        else:
            print("❌ 线程安全定时器创建失败")
            
    except Exception as e:
        print(f"❌ 测试线程安全定时器失败: {e}")

if __name__ == "__main__":
    print("开始验证P0修复效果...\n")
    
    test_field_registry_fix()
    test_field_mapping_fix()
    test_thread_safe_timer()
    
    print("\n修复验证完成！")