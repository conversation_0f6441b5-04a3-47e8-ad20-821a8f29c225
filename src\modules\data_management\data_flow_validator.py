"""
数据流一致性验证器 - 薪资管理系统核心修复模块

该模块负责确保数据在各个处理环节的一致性，防止排序空白列等问题。

主要功能:
- 数据与表头一致性验证
- 格式配置完整性检查
- 字段映射验证
- 数据流修复机制
- 性能监控和日志记录

技术特性:
- 多层验证策略
- 自动修复机制
- 降级处理
- 详细的错误报告

作者: 薪资管理系统修复团队
创建时间: 2025-08-01
版本: 1.0.0
"""

import time
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger


class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"      # 严格验证，发现问题立即报错
    MODERATE = "moderate"  # 中等验证，尝试修复
    LENIENT = "lenient"    # 宽松验证，记录警告但继续


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    issues: List[str]
    fixes_applied: List[str]
    data: Optional[List[Dict[str, Any]]] = None
    headers: Optional[List[str]] = None
    performance_ms: float = 0.0


@dataclass
class DataFlowMetrics:
    """数据流指标"""
    original_columns: int
    final_columns: int
    data_rows: int
    validation_time_ms: float
    fixes_count: int


class DataFlowValidator:
    """
    数据流一致性验证器
    
    负责验证和修复数据在各个处理环节的一致性问题
    """
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.MODERATE):
        """
        初始化验证器
        
        Args:
            validation_level: 验证级别
        """
        self.logger = setup_logger("DataFlowValidator")
        self.validation_level = validation_level
        self.metrics_history = []
        
        self.logger.info(f"🔧 [数据验证器] 初始化完成，验证级别: {validation_level.value}")
    
    def validate_data_consistency(
        self,
        data: List[Dict[str, Any]],
        headers: List[str],
        table_type: str = None,
        context: str = "unknown"
    ) -> ValidationResult:
        """
        验证数据一致性
        
        Args:
            data: 数据列表
            headers: 表头列表
            table_type: 表类型
            context: 验证上下文（用于日志）
            
        Returns:
            验证结果
        """
        start_time = time.time()
        issues = []
        fixes_applied = []
        
        try:
            self.logger.debug(f"🔧 [数据验证] 开始验证 - 上下文: {context}")

            # 🔧 [P0-CRITICAL修复] DataFrame类型安全处理
            import pandas as pd
            if isinstance(data, pd.DataFrame):
                if data.empty:
                    self.logger.debug("🔧 [P0-CRITICAL修复] DataFrame为空，返回空结果")
                    return ValidationResult(
                        is_valid=True,
                        issues=[],
                        fixes_applied=[],
                        data=[],
                        headers=headers or []
                    )
                # 转换DataFrame为字典列表
                data = data.to_dict('records')
                fixes_applied.append("DataFrame转换为字典列表")
                self.logger.debug(f"🔧 [P0-CRITICAL修复] DataFrame已转换: {len(data)}行")

            # 1. 基础数据验证
            basic_issues = self._validate_basic_data(data, headers)
            issues.extend(basic_issues)
            
            # 2. 列数一致性验证
            column_issues, fixed_data, fixed_headers = self._validate_column_consistency(data, headers)
            issues.extend(column_issues)
            
            if fixed_data is not None:
                data = fixed_data
                fixes_applied.append("列数一致性修复")
            
            if fixed_headers is not None:
                headers = fixed_headers
                fixes_applied.append("表头一致性修复")
            
            # 3. 字段类型验证
            type_issues = self._validate_field_types(data, headers)
            issues.extend(type_issues)
            
            # 4. 表类型特定验证
            if table_type:
                type_specific_issues = self._validate_table_type_specific(data, headers, table_type)
                issues.extend(type_specific_issues)
            
            # 5. 记录性能指标
            validation_time = (time.time() - start_time) * 1000

            # 🔧 [P0-CRITICAL修复] DataFrame安全计数
            import pandas as pd
            if isinstance(data, pd.DataFrame):
                data_rows = data.shape[0] if not data.empty else 0
            elif data:
                data_rows = len(data)
            else:
                data_rows = 0

            metrics = DataFlowMetrics(
                original_columns=len(headers),
                final_columns=len(headers),
                data_rows=data_rows,
                validation_time_ms=validation_time,
                fixes_count=len(fixes_applied)
            )
            self.metrics_history.append(metrics)
            
            # 6. 生成验证结果
            is_valid = len(issues) == 0 or self.validation_level == ValidationLevel.LENIENT
            
            if issues:
                self.logger.warning(f"🔧 [数据验证] 发现{len(issues)}个问题: {issues}")
            if fixes_applied:
                self.logger.info(f"🔧 [数据验证] 应用{len(fixes_applied)}个修复: {fixes_applied}")
            
            self.logger.debug(f"🔧 [数据验证] 验证完成 - 耗时: {validation_time:.2f}ms")
            
            return ValidationResult(
                is_valid=is_valid,
                issues=issues,
                fixes_applied=fixes_applied,
                data=data,
                headers=headers,
                performance_ms=validation_time
            )
            
        except Exception as e:
            self.logger.error(f"🔧 [数据验证] 验证过程异常: {e}")
            return ValidationResult(
                is_valid=False,
                issues=[f"验证异常: {str(e)}"],
                fixes_applied=[],
                data=data,
                headers=headers,
                performance_ms=(time.time() - start_time) * 1000
            )
    
    def _validate_basic_data(self, data: List[Dict[str, Any]], headers: List[str]) -> List[str]:
        """🔧 [P0-CRITICAL修复] 基础数据验证 - DataFrame安全处理"""
        issues = []

        if not headers:
            issues.append("表头列表为空")

        # 🔧 [P0-CRITICAL修复] DataFrame安全检查
        import pandas as pd
        if isinstance(data, pd.DataFrame):
            if data.empty:
                issues.append("DataFrame数据为空")
                return issues
        elif not data:
            issues.append("数据列表为空")
            return issues

        if not isinstance(data, (list, pd.DataFrame)):
            issues.append("数据不是列表或DataFrame类型")

        if not isinstance(headers, list):
            issues.append("表头不是列表类型")

        return issues
    
    def _validate_column_consistency(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str]
    ) -> Tuple[List[str], Optional[List[Dict[str, Any]]], Optional[List[str]]]:
        """验证列数一致性"""
        issues = []
        fixed_data = None
        fixed_headers = None
        
        # 🔧 [P0-CRITICAL修复] DataFrame安全检查
        import pandas as pd
        if isinstance(data, pd.DataFrame):
            if data.empty or not headers:
                return issues, fixed_data, fixed_headers
        elif not data or not headers:
            return issues, fixed_data, fixed_headers
        
        # 检查数据行的列数一致性
        data_columns = len(data[0]) if isinstance(data[0], dict) else 0
        header_columns = len(headers)
        
        if data_columns != header_columns:
            issues.append(f"列数不匹配: 数据{data_columns}列, 表头{header_columns}列")
            
            # 根据验证级别决定是否修复
            if self.validation_level in [ValidationLevel.MODERATE, ValidationLevel.LENIENT]:
                fixed_data, fixed_headers = self._fix_column_mismatch(data, headers)
        
        return issues, fixed_data, fixed_headers
    
    def _fix_column_mismatch(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str]
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """修复列数不匹配问题"""
        try:
            # 🔧 [P0-CRITICAL修复] DataFrame安全处理
            import pandas as pd
            if isinstance(data, pd.DataFrame):
                data_columns = data.shape[1] if not data.empty else 0
            elif data and isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                data_columns = len(data[0])
            else:
                data_columns = 0
            header_columns = len(headers)
            
            if data_columns > header_columns:
                # 数据列多，截断数据或补充表头
                self.logger.info(f"🔧 [列数修复] 数据列过多，从{data_columns}调整到{header_columns}")
                fixed_data = []
                for row in data:
                    if isinstance(row, dict):
                        fixed_row = {key: row.get(key, '') for key in headers if key in row}
                        fixed_data.append(fixed_row)
                    else:
                        fixed_data.append(row)
                return fixed_data, headers
                
            elif header_columns > data_columns:
                # 表头多，截断表头或补充数据
                self.logger.info(f"🔧 [列数修复] 表头过多，从{header_columns}调整到{data_columns}")
                # 🔧 [P0-CRITICAL修复] DataFrame安全处理
                import pandas as pd
                if isinstance(data, pd.DataFrame):
                    if not data.empty:
                        actual_headers = list(data.columns)
                    else:
                        actual_headers = []
                elif data_columns > 0 and isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                    actual_headers = list(data[0].keys())
                else:
                    actual_headers = []

                # 🔧 [P0-CRITICAL修复] 根据实际情况调整表头
                if len(actual_headers) > 0:
                    fixed_headers = actual_headers[:data_columns]
                else:
                    fixed_headers = headers[:data_columns]
                return data, fixed_headers
            
            return data, headers
            
        except Exception as e:
            self.logger.error(f"🔧 [列数修复] 修复失败: {e}")
            return data, headers
    
    def _validate_field_types(self, data: List[Dict[str, Any]], headers: List[str]) -> List[str]:
        """🔧 [P0-CRITICAL修复] 验证字段类型 - DataFrame安全处理"""
        issues = []

        # 🔧 [P0-CRITICAL修复] DataFrame安全检查
        import pandas as pd
        if isinstance(data, pd.DataFrame):
            if data.empty or not headers:
                return issues
        elif not data or not headers:
            return issues
        
        # 检查数据类型一致性
        try:
            for i, row in enumerate(data[:5]):  # 只检查前5行以提高性能
                if not isinstance(row, dict):
                    issues.append(f"第{i+1}行数据不是字典类型")
                    continue
                
                for header in headers:
                    if header not in row:
                        issues.append(f"第{i+1}行缺少字段: {header}")
        
        except Exception as e:
            issues.append(f"字段类型验证异常: {str(e)}")
        
        return issues
    
    def _validate_table_type_specific(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str], 
        table_type: str
    ) -> List[str]:
        """表类型特定验证"""
        issues = []
        
        # 定义各表类型的必需字段
        required_fields = {
            'active_employees': ['工号', '姓名', '部门名称'],
            'a_grade_employees': ['工号', '姓名', '部门名称'],
            'retired_employees': ['人员代码', '姓名', '部门名称'],
            'pension_employees': ['人员代码', '姓名', '部门名称']
        }
        
        table_required = required_fields.get(table_type, [])
        
        for field in table_required:
            if field not in headers:
                issues.append(f"表类型{table_type}缺少必需字段: {field}")
        
        return issues
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"message": "暂无性能数据"}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10次
        
        avg_time = sum(m.validation_time_ms for m in recent_metrics) / len(recent_metrics)
        total_fixes = sum(m.fixes_count for m in recent_metrics)
        
        return {
            "average_validation_time_ms": round(avg_time, 2),
            "total_validations": len(self.metrics_history),
            "recent_fixes_count": total_fixes,
            "validation_level": self.validation_level.value
        }
