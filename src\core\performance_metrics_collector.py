#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能度量收集器
遵循CLAUDE.md要求：数据流追踪日志，性能监控
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

from src.utils.log_config import setup_logger


@dataclass
class PerformanceMetrics:
    """性能度量数据类"""
    operation_type: str  # 'sort', 'pagination', 'load', 'render'
    data_size: int
    render_time_ms: float
    strategy_used: str
    user_wait_time_ms: float
    timestamp: datetime
    table_name: str = ""
    additional_info: Dict[str, Any] = None
    
    def __post_init__(self):
        """后处理初始化"""
        if self.additional_info is None:
            self.additional_info = {}


class PerformanceMetricsCollector:
    """
    性能度量收集器
    
    收集、分析和报告系统性能指标
    遵循CLAUDE.md原则：数据流追踪日志 + 性能优化
    """
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        初始化性能度量收集器
        
        Args:
            storage_path: 度量数据存储路径
        """
        self.logger = setup_logger(self.__class__.__name__)
        
        # 存储配置
        self._storage_path = Path(storage_path) if storage_path else Path("logs/performance_metrics.json")
        self._storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 内存中的度量数据
        self._metrics_history = []
        self._session_start_time = datetime.now()
        
        # 性能统计
        self._performance_stats = {
            'total_operations': 0,
            'avg_render_time': 0.0,
            'max_render_time': 0.0,
            'min_render_time': float('inf'),
            'operations_by_type': {},
            'performance_improvements': {}
        }
        
        # 加载历史数据
        self._load_historical_data()
        
        self.logger.info(f"性能度量收集器初始化完成，存储路径: {self._storage_path}")
    
    def record_operation(self, metrics: PerformanceMetrics):
        """
        记录操作性能
        
        Args:
            metrics: 性能度量数据
        """
        try:
            # 数据流追踪日志
            self.logger.info(f"[数据流追踪] 记录性能度量: {metrics.operation_type}, "
                           f"数据大小={metrics.data_size}, 渲染时间={metrics.render_time_ms:.1f}ms, "
                           f"策略={metrics.strategy_used}")
            
            # 添加到历史记录
            self._metrics_history.append(metrics)
            
            # 更新统计信息
            self._update_performance_stats(metrics)
            
            # 保持合理的历史记录数量
            if len(self._metrics_history) > 1000:
                self._metrics_history = self._metrics_history[-500:]
            
            # 定期保存到文件
            if len(self._metrics_history) % 50 == 0:
                self._save_to_file()
            
        except Exception as e:
            self.logger.error(f"记录性能度量失败: {e}")
    
    def _update_performance_stats(self, metrics: PerformanceMetrics):
        """更新性能统计信息"""
        stats = self._performance_stats
        
        # 总操作数
        stats['total_operations'] += 1
        
        # 渲染时间统计
        render_time = metrics.render_time_ms
        
        # 更新平均值
        if stats['total_operations'] == 1:
            stats['avg_render_time'] = render_time
        else:
            stats['avg_render_time'] = (
                (stats['avg_render_time'] * (stats['total_operations'] - 1) + render_time) 
                / stats['total_operations']
            )
        
        # 更新最大最小值
        stats['max_render_time'] = max(stats['max_render_time'], render_time)
        if stats['min_render_time'] == float('inf'):
            stats['min_render_time'] = render_time
        else:
            stats['min_render_time'] = min(stats['min_render_time'], render_time)
        
        # 按操作类型统计
        op_type = metrics.operation_type
        if op_type not in stats['operations_by_type']:
            stats['operations_by_type'][op_type] = {
                'count': 0,
                'avg_time': 0.0,
                'total_time': 0.0
            }
        
        op_stats = stats['operations_by_type'][op_type]
        op_stats['count'] += 1
        op_stats['total_time'] += render_time
        op_stats['avg_time'] = op_stats['total_time'] / op_stats['count']
    
    def get_performance_report(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """
        获取性能报告
        
        Args:
            time_range_hours: 报告时间范围（小时）
            
        Returns:
            性能报告字典
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
            
            # 过滤指定时间范围内的数据
            recent_metrics = [
                m for m in self._metrics_history 
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return {
                    'message': f'过去{time_range_hours}小时内无性能数据',
                    'time_range_hours': time_range_hours
                }
            
            # 计算统计信息
            total_operations = len(recent_metrics)
            avg_render_time = sum(m.render_time_ms for m in recent_metrics) / total_operations
            
            # 策略分布
            strategy_distribution = {}
            for metrics in recent_metrics:
                strategy = metrics.strategy_used
                strategy_distribution[strategy] = strategy_distribution.get(strategy, 0) + 1
            
            # 操作类型分布
            operation_distribution = {}
            for metrics in recent_metrics:
                op_type = metrics.operation_type
                operation_distribution[op_type] = operation_distribution.get(op_type, 0) + 1
            
            # 性能改进评估
            performance_improvement = self._calculate_performance_improvement(recent_metrics)
            
            report = {
                'time_range_hours': time_range_hours,
                'total_operations': total_operations,
                'average_render_time_ms': round(avg_render_time, 1),
                'strategy_distribution': strategy_distribution,
                'operation_distribution': operation_distribution,
                'performance_improvement': performance_improvement,
                'session_start_time': self._session_start_time.isoformat(),
                'report_generated_at': datetime.now().isoformat()
            }
            
            self.logger.info(f"[数据流追踪] 生成性能报告: {total_operations}个操作, "
                           f"平均渲染时间={avg_render_time:.1f}ms")
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return {'error': str(e)}
    
    def _calculate_performance_improvement(self, metrics_list: List[PerformanceMetrics]) -> str:
        """计算性能改进百分比"""
        if len(metrics_list) < 10:
            return "数据不足，无法计算改进"
        
        # 分割为前半部分和后半部分
        mid_point = len(metrics_list) // 2
        early_metrics = metrics_list[:mid_point]
        recent_metrics = metrics_list[mid_point:]
        
        early_avg = sum(m.render_time_ms for m in early_metrics) / len(early_metrics)
        recent_avg = sum(m.render_time_ms for m in recent_metrics) / len(recent_metrics)
        
        if early_avg > 0:
            improvement_pct = ((early_avg - recent_avg) / early_avg) * 100
            if improvement_pct > 0:
                return f"{improvement_pct:.1f}%提升"
            else:
                return f"{abs(improvement_pct):.1f}%降低"
        
        return "无法计算改进"
    
    def get_detailed_analysis(self) -> Dict[str, Any]:
        """获取详细性能分析"""
        try:
            if not self._metrics_history:
                return {'message': '暂无性能数据'}
            
            # 按数据大小分组分析
            size_groups = {
                'small': [m for m in self._metrics_history if m.data_size <= 75],
                'medium': [m for m in self._metrics_history if 75 < m.data_size <= 200],
                'large': [m for m in self._metrics_history if m.data_size > 200]
            }
            
            analysis = {}
            
            for group_name, metrics_group in size_groups.items():
                if metrics_group:
                    avg_time = sum(m.render_time_ms for m in metrics_group) / len(metrics_group)
                    avg_size = sum(m.data_size for m in metrics_group) / len(metrics_group)
                    
                    # 策略效果分析
                    strategy_performance = {}
                    for metrics in metrics_group:
                        strategy = metrics.strategy_used
                        if strategy not in strategy_performance:
                            strategy_performance[strategy] = []
                        strategy_performance[strategy].append(metrics.render_time_ms)
                    
                    # 计算每种策略的平均性能
                    strategy_avg = {}
                    for strategy, times in strategy_performance.items():
                        strategy_avg[strategy] = sum(times) / len(times)
                    
                    analysis[group_name] = {
                        'count': len(metrics_group),
                        'avg_render_time_ms': round(avg_time, 1),
                        'avg_data_size': round(avg_size, 1),
                        'strategy_performance': strategy_avg
                    }
            
            return {
                'analysis_by_data_size': analysis,
                'total_metrics': len(self._metrics_history),
                'global_stats': self._performance_stats
            }
            
        except Exception as e:
            self.logger.error(f"获取详细分析失败: {e}")
            return {'error': str(e)}
    
    def export_metrics_to_csv(self, output_path: Optional[str] = None) -> str:
        """
        导出度量数据到CSV文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            实际输出路径
        """
        try:
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"logs/performance_metrics_{timestamp}.csv"
            
            import csv
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if not self._metrics_history:
                    csvfile.write("没有性能数据可导出\n")
                    return output_path
                
                fieldnames = [
                    'timestamp', 'operation_type', 'data_size', 'render_time_ms',
                    'strategy_used', 'user_wait_time_ms', 'table_name'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for metrics in self._metrics_history:
                    row = {
                        'timestamp': metrics.timestamp.isoformat(),
                        'operation_type': metrics.operation_type,
                        'data_size': metrics.data_size,
                        'render_time_ms': metrics.render_time_ms,
                        'strategy_used': metrics.strategy_used,
                        'user_wait_time_ms': metrics.user_wait_time_ms,
                        'table_name': metrics.table_name
                    }
                    writer.writerow(row)
            
            self.logger.info(f"[数据流追踪] 性能度量已导出到: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"导出CSV失败: {e}")
            return ""
    
    def clear_old_metrics(self, days_to_keep: int = 7):
        """清理旧的度量数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            
            old_count = len(self._metrics_history)
            self._metrics_history = [
                m for m in self._metrics_history 
                if m.timestamp >= cutoff_time
            ]
            
            removed_count = old_count - len(self._metrics_history)
            
            if removed_count > 0:
                self.logger.info(f"[数据流追踪] 清理旧性能度量: 移除{removed_count}条记录")
                self._save_to_file()
            
        except Exception as e:
            self.logger.error(f"清理旧度量数据失败: {e}")
    
    def _save_to_file(self):
        """保存度量数据到文件"""
        try:
            # 转换为可序列化的格式
            serializable_data = []
            for metrics in self._metrics_history[-100:]:  # 只保存最近100条
                data = asdict(metrics)
                data['timestamp'] = metrics.timestamp.isoformat()
                serializable_data.append(data)
            
            save_data = {
                'session_start_time': self._session_start_time.isoformat(),
                'last_updated': datetime.now().isoformat(),
                'metrics': serializable_data,
                'stats': self._performance_stats
            }
            
            with open(self._storage_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"[数据流追踪] 性能度量已保存: {len(serializable_data)}条记录")
            
        except Exception as e:
            self.logger.error(f"保存度量数据失败: {e}")
    
    def _load_historical_data(self):
        """加载历史度量数据"""
        try:
            if self._storage_path.exists():
                with open(self._storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 加载度量记录
                for item in data.get('metrics', []):
                    metrics = PerformanceMetrics(
                        operation_type=item['operation_type'],
                        data_size=item['data_size'],
                        render_time_ms=item['render_time_ms'],
                        strategy_used=item['strategy_used'],
                        user_wait_time_ms=item['user_wait_time_ms'],
                        timestamp=datetime.fromisoformat(item['timestamp']),
                        table_name=item.get('table_name', ''),
                        additional_info=item.get('additional_info', {})
                    )
                    self._metrics_history.append(metrics)
                
                # 加载统计数据
                if 'stats' in data:
                    self._performance_stats.update(data['stats'])
                
                self.logger.info(f"[数据流追踪] 加载历史性能数据: {len(self._metrics_history)}条记录")
            
        except Exception as e:
            self.logger.warning(f"加载历史度量数据失败: {e}")


# 全局性能度量收集器
_performance_collector = None


def get_performance_metrics_collector() -> PerformanceMetricsCollector:
    """获取全局性能度量收集器单例"""
    global _performance_collector
    if _performance_collector is None:
        _performance_collector = PerformanceMetricsCollector()
    return _performance_collector