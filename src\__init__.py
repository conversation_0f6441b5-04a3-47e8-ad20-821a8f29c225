"""
月度工资异动处理系统 - 源码包初始化

本包包含系统的所有核心模块：
- core: 核心业务逻辑
- modules: 功能模块（数据导入、异动识别、报告生成等）
- gui: 用户界面模块
- utils: 工具库模块

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
"""

__version__ = "1.0.0"
__author__ = "月度工资异动处理系统开发团队"
__description__ = "基于PyQt5的桌面应用程序，用于月度工资异动的自动化处理"

# 导入日志配置
try:
    from .utils.log_config import logger, get_module_logger
    logger.info(f"月度工资异动处理系统 v{__version__} 初始化完成")
except ImportError as e:
    print(f"警告：无法导入日志配置模块: {e}")
    print("系统将使用标准日志输出")

__all__ = ["__version__", "__author__", "__description__"] 