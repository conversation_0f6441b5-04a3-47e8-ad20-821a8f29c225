#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一批次修复验证脚本（简化版）
验证P0-新2、P0-新4、P0-新5修复效果
"""

import sys
import os
import re
from pathlib import Path

def main():
    """主验证函数"""
    print("第一批次修复验证测试")
    print("=" * 60)
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("错误: PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n>>> P0-新2验证: 数据类型比较错误修复")
        p0_new2_checks = 0
        
        if "width_int = int(width) if isinstance(width, (str, float)) else width" in content:
            print("通过: 找到列宽数据类型转换修复")
            p0_new2_checks += 1
        
        if "sort_column_int = int(sort_column) if isinstance(sort_column, (str, float)) else sort_column" in content:
            print("通过: 找到排序列数据类型转换修复")
            p0_new2_checks += 1
            
        if "[P0-新2修复]" in content:
            print("通过: 找到P0-新2修复标识")
            p0_new2_checks += 1
        
        print("\n>>> P0-新4验证: UnifiedStateManager方法调用参数修复")
        p0_new4_checks = 0
        
        if "success = self.state_manager.restore_table_state_by_name(table_name)" in content:
            print("通过: 找到正确的方法调用")
            p0_new4_checks += 1
        
        if "restore_table_state_by_name(table_name, table_widget)" not in content:
            print("通过: 确认已移除错误的参数")
            p0_new4_checks += 1
            
        if "[P0-新4修复]" in content:
            print("通过: 找到P0-新4修复标识")
            p0_new4_checks += 1
        
        print("\n>>> P0-新5验证: HeaderUpdateManager参数修复")
        p0_new5_checks = 0
        
        if "headers = list(df_final.columns)" in content:
            print("通过: 找到headers参数准备")
            p0_new5_checks += 1
        
        if "start_record = 1  # 非分页模式起始记录为1" in content:
            print("通过: 找到start_record参数准备")
            p0_new5_checks += 1
        
        method_call_pattern = r"force_update_all\(\s*headers=headers,\s*start_record=start_record,\s*count=count\s*\)"
        if re.search(method_call_pattern, content, re.MULTILINE):
            print("通过: 找到正确的force_update_all方法调用")
            p0_new5_checks += 1
            
        if "[P0-新5修复]" in content:
            print("通过: 找到P0-新5修复标识")
            p0_new5_checks += 1
        
        print("\n" + "=" * 60)
        print("验证结果总结:")
        print(f"P0-新2: {p0_new2_checks}/3 项通过")
        print(f"P0-新4: {p0_new4_checks}/3 项通过")
        print(f"P0-新5: {p0_new5_checks}/4 项通过")
        
        total_checks = p0_new2_checks + p0_new4_checks + p0_new5_checks
        max_checks = 10
        
        print(f"\n总体结果: {total_checks}/{max_checks} 项验证通过")
        
        if total_checks >= 8:
            print("\n第一批次修复验证通过!")
            print("- 数据类型比较错误已修复")
            print("- UnifiedStateManager方法调用参数已修复") 
            print("- HeaderUpdateManager参数缺失已修复")
            print("\n建议重新启动系统测试修复效果!")
            return True
        else:
            print(f"\n警告: {max_checks - total_checks} 项验证失败，需要进一步检查")
            return False
            
    except Exception as e:
        print(f"验证异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)