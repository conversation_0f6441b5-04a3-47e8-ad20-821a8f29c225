#!/usr/bin/env python3
"""
测试Qt::Orientation最终修复
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_direct_connection_fix():
    """测试DirectConnection修复"""
    print("🔧 测试DirectConnection修复...")
    
    try:
        from PyQt5.QtCore import Qt, QObject, pyqtSignal, QCoreApplication
        
        # 创建应用程序上下文
        if not QCoreApplication.instance():
            app = QCoreApplication([])
        
        # 模拟表头信号
        class MockTableHeader(QObject):
            sortIndicatorChanged = pyqtSignal(int, Qt.SortOrder)
            sectionDoubleClicked = pyqtSignal(int)
            
            def __init__(self):
                super().__init__()
                self.sort_signal_received = False
                self.double_click_signal_received = False
            
            def connect_signals(self):
                # 使用DirectConnection连接
                self.sortIndicatorChanged.connect(
                    self.on_sort_changed,
                    Qt.DirectConnection
                )
                self.sectionDoubleClicked.connect(
                    self.on_double_clicked,
                    Qt.DirectConnection
                )
            
            def on_sort_changed(self, index, order):
                print(f"✅ 排序信号处理成功: index={index}, order={order}")
                self.sort_signal_received = True
            
            def on_double_clicked(self, index):
                print(f"✅ 双击信号处理成功: index={index}")
                self.double_click_signal_received = True
        
        # 测试信号连接
        header = MockTableHeader()
        header.connect_signals()
        
        # 发射信号
        header.sortIndicatorChanged.emit(0, Qt.SortOrder.AscendingOrder)
        header.sectionDoubleClicked.emit(1)
        
        # 验证结果
        if header.sort_signal_received and header.double_click_signal_received:
            print("✅ DirectConnection修复测试成功")
            return True
        else:
            print("❌ DirectConnection修复测试失败")
            return False
            
    except Exception as e:
        print(f"❌ DirectConnection修复测试失败: {e}")
        return False

def test_signal_reconnection():
    """测试信号重新连接"""
    print("🔧 测试信号重新连接...")
    
    try:
        from PyQt5.QtCore import Qt, QObject, pyqtSignal
        
        class MockTable(QObject):
            test_signal = pyqtSignal(int)
            
            def __init__(self):
                super().__init__()
                self.signal_count = 0
            
            def handler(self, value):
                self.signal_count += 1
                print(f"✅ 信号处理: value={value}, count={self.signal_count}")
            
            def reconnect_with_direct_connection(self):
                # 断开现有连接
                try:
                    self.test_signal.disconnect()
                except:
                    pass
                
                # 重新连接使用DirectConnection
                self.test_signal.connect(
                    self.handler,
                    Qt.DirectConnection
                )
        
        # 测试重新连接
        table = MockTable()
        table.reconnect_with_direct_connection()
        
        # 发射信号
        table.test_signal.emit(42)
        
        if table.signal_count == 1:
            print("✅ 信号重新连接测试成功")
            return True
        else:
            print("❌ 信号重新连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 信号重新连接测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🔧 开始测试Qt::Orientation最终修复...")
    print()
    
    tests = [
        test_direct_connection_fix,
        test_signal_reconnection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print(f"🎉 测试完成：{passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ Qt::Orientation最终修复测试全部通过！")
        print("✅ 主要修复包括：")
        print("  - 所有表头信号使用DirectConnection")
        print("  - 数据设置后强制重新连接信号")
        print("  - 表格基础信号使用DirectConnection")
        print("✅ 现在应该不会再出现Qt::Orientation错误了！")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)