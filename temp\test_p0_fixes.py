#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0级修复验证测试脚本

测试内容：
1. UI更新方法签名修复验证
2. 列宽保存失效问题修复验证
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger

def test_column_width_config_exists():
    """测试列宽配置文件是否存在和可读"""
    logger = setup_logger("P0Test")
    
    config_file = project_root / "state" / "column_widths.json"
    logger.info(f"检查列宽配置文件: {config_file}")
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"✅ 列宽配置文件存在且可读，包含 {len(config)} 个表格配置")
            
            # 显示配置内容
            for table_name, table_config in config.items():
                column_count = len(table_config.get('column_widths', []))
                logger.info(f"  - {table_name}: {column_count} 列")
            
            return True
        except Exception as e:
            logger.error(f"❌ 列宽配置文件读取失败: {e}")
            return False
    else:
        logger.info("ℹ️ 列宽配置文件不存在（首次运行正常）")
        return True

def test_ui_update_method_signature():
    """测试UI更新方法签名修复"""
    logger = setup_logger("P0Test")
    
    # 检查修复后的代码
    main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
    
    if not main_window_file.exists():
        logger.error(f"❌ 主窗口文件不存在: {main_window_file}")
        return False
    
    try:
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含修复后的代码
        if "headers = list(mapped_data[0].keys())" in content:
            logger.info("✅ UI更新方法签名修复已应用")
            return True
        else:
            logger.error("❌ UI更新方法签名修复未找到")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查UI更新修复失败: {e}")
        return False

def test_column_width_logic():
    """测试列宽保存逻辑修复"""
    logger = setup_logger("P0Test")
    
    # 检查修复后的代码
    table_file = project_root / "src" / "gui" / "prototype" / "widgets" / "virtualized_expandable_table.py"
    
    if not table_file.exists():
        logger.error(f"❌ 表格文件不存在: {table_file}")
        return False
    
    try:
        with open(table_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes_found = 0
        
        # 检查1：延迟恢复列宽
        if "QTimer.singleShot(100, delayed_restore_column_widths)" in content:
            logger.info("✅ 找到延迟恢复列宽修复")
            fixes_found += 1
        
        # 检查2：用户列宽检查逻辑
        if "检测到用户保存的列宽设置" in content:
            logger.info("✅ 找到用户列宽检查逻辑")
            fixes_found += 1
        
        # 检查3：跳过默认列宽设置
        if "用户有保存的列宽设置，跳过默认列宽调整" in content:
            logger.info("✅ 找到跳过默认列宽设置逻辑")
            fixes_found += 1
        
        if fixes_found >= 2:
            logger.info(f"✅ 列宽保存逻辑修复已应用 ({fixes_found}/3 个修复点)")
            return True
        else:
            logger.error(f"❌ 列宽保存逻辑修复不完整 ({fixes_found}/3 个修复点)")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查列宽逻辑修复失败: {e}")
        return False

def simulate_column_width_save_restore():
    """模拟列宽保存和恢复过程"""
    logger = setup_logger("P0Test")
    
    # 创建测试配置
    config_file = project_root / "state" / "column_widths.json"
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    test_config = {
        "test_table": {
            "column_widths": [200, 150, 180, 120],
            "column_names": ["工号", "姓名", "部门", "薪资"],
            "timestamp": time.time()
        }
    }
    
    try:
        # 保存测试配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ 测试列宽配置已保存")
        
        # 验证读取
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        if loaded_config == test_config:
            logger.info("✅ 测试列宽配置读取验证成功")
            return True
        else:
            logger.error("❌ 测试列宽配置读取验证失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 模拟列宽保存恢复失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_logger("P0Test")
    
    logger.info("🔧 开始P0级修复验证测试")
    logger.info("=" * 50)
    
    tests = [
        ("列宽配置文件检查", test_column_width_config_exists),
        ("UI更新方法签名修复", test_ui_update_method_signature),
        ("列宽保存逻辑修复", test_column_width_logic),
        ("列宽保存恢复模拟", simulate_column_width_save_restore),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"🔧 P0级修复验证完成: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有P0级修复验证通过！")
        return True
    else:
        logger.warning(f"⚠️ 有 {total - passed} 个测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
