#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分页状态调用脚本
"""

import os
import re


def add_debug_logs_to_pagination_calls():
    """在分页状态调用处添加调试日志"""
    
    file_path = "src/gui/prototype/prototype_main_window.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在添加调试日志: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 在第一个调用点添加调试日志（_set_paginated_data方法）
    pattern1 = r'(self\.expandable_table\.set_pagination_state\(pagination_state\))'
    replacement1 = '''self.logger.info(f"🔍 [调试] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                self.expandable_table.set_pagination_state(pagination_state)
                self.logger.info("🔍 [调试] set_pagination_state调用完成")'''
    
    # 2. 在第二个调用点添加调试日志（全局排序处理）
    pattern2 = r'(self\.expandable_table\.set_pagination_state\(pagination_state\))\s*\n\s*self\.logger\.info\(f"🔧 \[全局排序\] 页面重新加载完成'
    replacement2 = '''self.logger.info(f"🔍 [调试-排序] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                self.expandable_table.set_pagination_state(pagination_state)
                self.logger.info("🔍 [调试-排序] set_pagination_state调用完成")
                
                self.logger.info(f"🔧 [全局排序] 页面重新加载完成'''
    
    # 3. 在第三个调用点添加调试日志（分页数据加载）
    pattern3 = r'(self\.main_workspace\.expandable_table\.set_pagination_state\(pagination_state\))'
    replacement3 = '''self.logger.info(f"🔍 [调试-分页] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                    self.main_workspace.expandable_table.set_pagination_state(pagination_state)
                    self.logger.info("🔍 [调试-分页] set_pagination_state调用完成")'''
    
    # 应用替换
    modifications = 0
    
    if re.search(pattern1, content):
        content = re.sub(pattern1, replacement1, content)
        modifications += 1
        print("✅ 已添加调试日志到第一个调用点")
    
    if re.search(pattern2, content):
        content = re.sub(pattern2, replacement2, content)
        modifications += 1
        print("✅ 已添加调试日志到第二个调用点")
    
    if re.search(pattern3, content):
        content = re.sub(pattern3, replacement3, content)
        modifications += 1
        print("✅ 已添加调试日志到第三个调用点")
    
    if modifications > 0:
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ {file_path} 调试日志添加完成，共{modifications}处修改")
        return True
    else:
        print("❌ 没有找到需要修改的调用点")
        return False


def enhance_virtualized_table_debug():
    """增强表格组件的调试信息"""
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 正在增强表格调试: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在 set_pagination_state 方法开始处添加详细调试
    pattern = r'(def set_pagination_state\(self, pagination_state: dict\):.*?"设置分页状态信息.*?""")'
    
    enhanced_debug_start = '''def set_pagination_state(self, pagination_state: dict):
        """设置分页状态信息，用于正确显示分页行号
        
        Args:
            pagination_state: 包含分页信息的字典，应包含：
                - current_page: 当前页码
                - page_size: 每页大小
                - total_records: 总记录数
                - start_record: 当前页起始记录号
                - end_record: 当前页结束记录号
        """
        self.logger.info("🔍 [表格调试] ================== set_pagination_state 开始 ==================")
        self.logger.info(f"🔍 [表格调试] 收到分页状态参数: {pagination_state}")
        self.logger.info(f"🔍 [表格调试] 当前表格行数: {self.rowCount()}")
        self.logger.info(f"🔍 [表格调试] 当前表格列数: {self.columnCount()}")'''
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, enhanced_debug_start, content, flags=re.DOTALL)
        print("✅ 已增强set_pagination_state方法的调试信息")
    
    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 调试增强完成")
    return True


def create_debug_test_script():
    """创建调试测试脚本"""
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页调用调试测试脚本
"""

def test_pagination_calls():
    """测试分页调用是否发生"""
    print("🔍 分页调用调试测试")
    print("\\n启动系统后，请：")
    print("1. 导入数据")
    print("2. 切换分页")
    print("3. 查看日志中的以下标记：")
    print("   - 🔍 [调试] 即将调用set_pagination_state")
    print("   - 🔍 [调试] set_pagination_state调用完成")
    print("   - 🔍 [表格调试] set_pagination_state 开始")
    print("   - 🔧 [分页行号修复] 接收分页状态")
    print("\\n如果看不到这些日志，说明调用路径有问题")


if __name__ == "__main__":
    test_pagination_calls()
'''
    
    with open('temp/test_debug_pagination.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 调试测试脚本已创建: temp/test_debug_pagination.py")


def main():
    """执行调试日志添加"""
    print("🔍 开始添加分页调用调试日志...")
    
    try:
        # 添加主窗口调试日志
        if add_debug_logs_to_pagination_calls():
            print("✅ 主窗口调试日志添加完成")
        
        # 增强表格组件调试
        if enhance_virtualized_table_debug():
            print("✅ 表格组件调试增强完成")
        
        # 创建测试脚本
        create_debug_test_script()
        
        print("\n🎉 调试日志添加完成！")
        print("\n📋 添加的调试内容：")
        print("1. ✅ 主窗口三个set_pagination_state调用点的调试日志")
        print("2. ✅ 表格组件set_pagination_state方法的详细调试")
        print("3. ✅ 调试测试脚本")
        
        print("\n🧪 测试步骤：")
        print("1. 运行: python temp/test_debug_pagination.py")
        print("2. 启动系统: python main.py")
        print("3. 导入数据并切换分页")
        print("4. 查看日志中的调试信息")
        
        print("\n🔍 关键调试标记：")
        print("- 🔍 [调试] 即将调用set_pagination_state")
        print("- 🔍 [表格调试] set_pagination_state 开始")
        print("- 🔧 [分页行号修复] 接收分页状态")
        
    except Exception as e:
        print(f"❌ 调试日志添加失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 