#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A岗职工格式化功能测试脚本

测试所有用户需求的格式化功能：
1. 浮点型字段保留两位小数
2. 特殊值处理（None、nan、0、空字符串、"-"等）
3. 月份提取后两位转字符串
4. 年份转字符串
5. 隐藏字段不显示
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.format_management.unified_format_manager import get_unified_format_manager
from src.modules.format_management.field_registry import FieldRegistry
from src.modules.format_management.format_config import FormatConfig

def test_a_grade_formatting():
    """测试A岗职工格式化功能"""
    print("🧪 开始测试A岗职工格式化功能")
    
    # 初始化格式管理器
    format_manager = get_unified_format_manager()
    
    # 创建测试数据 - 包含各种特殊值
    test_data = {
        'employee_id': ['001', '002', '003', '004'],
        'employee_name': ['张三', '李四', '王五', '赵六'],
        'department': ['财务部', '人事部', '技术部', '市场部'],
        'employee_type': ['教师', '管理', '技术', '服务'],
        'employee_type_code': ['01', '02', '03', '04'],
        # 测试浮点型字段的各种特殊值
        'position_salary_2025': [3500.5, None, 0, '4200.00'],
        'seniority_salary_2025': ['', '-', np.nan, 1800.25],
        'allowance': [500.0, 0.0, '  ', 600.75],
        'balance_allowance': ['none', 'null', 200.5, 300.0],
        'basic_performance_2025': [1200.5, 1300.25, '', '-'],
        'health_fee': [50.0, 0, np.nan, 60.5],
        'living_allowance_2025': [800.0, None, 850.25, '900.00'],
        'car_allowance': [300.0, '-', 0.0, 350.5],  # 特别测试车补的"-"处理
        'performance_bonus_2025': [2000.0, 2100.5, '', 2200.25],
        'supplement': [0, None, 100.5, '-'],
        'advance': [500.0, 0.0, np.nan, '600.00'],
        'total_salary': [8500.0, 7200.5, 9100.25, 8800.0],
        'provident_fund_2025': [850.0, 720.0, 910.0, 880.0],
        'insurance_deduction': [300.0, 250.5, None, 320.25],
        'pension_insurance': [400.0, '-', 0, 420.5],
        # 测试月份和年份字段
        'month': ['202505', '05', 5, '2025年05月'],
        'year': [2025, '2025', '2025年', 2025],
        # 应该隐藏的字段
        'created_at': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31'],
        'updated_at': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31'],
        'id': [1, 2, 3, 4],
        'sequence_number': [1, 2, 3, 4]
    }
    
    df = pd.DataFrame(test_data)
    print(f"📊 原始测试数据: {len(df)}行, {len(df.columns)}列")
    
    # 测试格式化
    try:
        # 使用A岗职工表类型进行格式化
        formatted_headers, formatted_df = format_manager.format_table_complete(
            df, 
            table_type='salary_data_2025_07_a_grade_employees',
            data_source='test'
        )
        
        print(f"✅ 格式化成功!")
        print(f"📊 格式化后数据: {len(formatted_df)}行, {len(formatted_df.columns)}列")
        print(f"📋 表头: {formatted_headers}")
        
        # 验证隐藏字段
        hidden_fields = ['创建时间', '更新时间', '自增主键', '序号']
        for field in hidden_fields:
            if field in formatted_headers:
                print(f"❌ 错误：隐藏字段 '{field}' 仍在显示")
            else:
                print(f"✅ 隐藏字段 '{field}' 已正确隐藏")
        
        # 验证浮点型格式化
        float_fields = ['2025年岗位工资', '2025年校龄工资', '津贴', '车补', '应发工资']
        print("\n🔢 浮点型字段格式化验证:")
        for field in float_fields:
            if field in formatted_df.columns:
                values = formatted_df[field].tolist()
                print(f"  {field}: {values}")
                # 检查是否都是两位小数格式
                for val in values:
                    if isinstance(val, str) and '.' in val:
                        decimal_part = val.split('.')[-1]
                        if len(decimal_part) != 2:
                            print(f"    ❌ 值 '{val}' 不是两位小数格式")
                        else:
                            print(f"    ✅ 值 '{val}' 正确显示两位小数")
        
        # 验证月份和年份格式化
        print("\n📅 月份和年份字段格式化验证:")
        if '月份' in formatted_df.columns:
            month_values = formatted_df['月份'].tolist()
            print(f"  月份: {month_values}")
            for val in month_values:
                if isinstance(val, str) and len(val) <= 2 and val.isdigit():
                    print(f"    ✅ 月份 '{val}' 正确提取为两位字符串")
                else:
                    print(f"    ⚠️ 月份 '{val}' 格式需要检查")
        
        if '年份' in formatted_df.columns:
            year_values = formatted_df['年份'].tolist()
            print(f"  年份: {year_values}")
            for val in year_values:
                if isinstance(val, str):
                    print(f"    ✅ 年份 '{val}' 正确转换为字符串")
                else:
                    print(f"    ⚠️ 年份 '{val}' 应该是字符串类型")
        
        # 显示完整的格式化结果
        print("\n📋 完整格式化结果:")
        print(formatted_df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 格式化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_consistency():
    """测试格式化一致性 - 模拟不同场景"""
    print("\n🔄 测试格式化一致性")
    
    format_manager = get_unified_format_manager()
    
    # 相同数据的不同调用方式
    test_data = {
        'position_salary_2025': [None, 0, '-', ''],
        'car_allowance': [np.nan, 0.0, '-', '  '],
        'month': ['202505', '05'],
        'year': [2025, '2025']
    }
    
    df = pd.DataFrame(test_data)
    
    # 测试1: 通过format_data方法
    result1 = format_manager.format_data(df, 'salary_data_2025_07_a_grade_employees')
    
    # 测试2: 通过format_table_complete方法
    headers2, result2 = format_manager.format_table_complete(df, 'salary_data_2025_07_a_grade_employees')
    
    # 测试3: 通过兼容接口
    result3 = format_manager.format_table_data(df, 'salary_data_2025_07_a_grade_employees')
    
    print("🔍 一致性检查:")
    if result1.equals(result2) and result2.equals(result3):
        print("✅ 所有调用方式结果一致")
        return True
    else:
        print("❌ 不同调用方式结果不一致")
        print(f"Result1:\n{result1}")
        print(f"Result2:\n{result2}")
        print(f"Result3:\n{result3}")
        return False

if __name__ == '__main__':
    print("🚀 A岗职工格式化功能完整测试")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_a_grade_formatting()
    test2_passed = test_format_consistency()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"  格式化功能测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  一致性测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！A岗职工格式化功能已正确实现")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")