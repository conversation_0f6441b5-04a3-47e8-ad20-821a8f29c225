"""
数据流修复测试脚本

测试薪资管理系统的数据流一致性修复功能

测试内容:
1. 数据流验证器功能测试
2. 格式配置降级处理测试
3. 排序空白列修复测试
4. 状态管理器功能测试

运行方式:
python test/test_data_flow_fixes.py

作者: 薪资管理系统修复团队
创建时间: 2025-08-01
"""

import sys
import os
import unittest
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.modules.data_management.data_flow_validator import DataFlowValidator, ValidationLevel, ValidationResult
    from src.modules.state_management.table_state_manager import TableStateManager, TableState
    from src.modules.format_management.format_renderer import FormatRenderer
    from src.modules.format_management.field_registry import FieldRegistry
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行此测试")
    sys.exit(1)


class TestDataFlowValidator(unittest.TestCase):
    """数据流验证器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = DataFlowValidator(ValidationLevel.MODERATE)
    
    def test_basic_validation(self):
        """基础验证测试"""
        # 正常数据
        data = [
            {"工号": "001", "姓名": "张三", "部门": "技术部"},
            {"工号": "002", "姓名": "李四", "部门": "财务部"}
        ]
        headers = ["工号", "姓名", "部门"]
        
        result = self.validator.validate_data_consistency(data, headers, "active_employees", "test")
        
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.issues), 0)
        self.assertIsNotNone(result.data)
        self.assertIsNotNone(result.headers)
    
    def test_column_mismatch_fix(self):
        """列数不匹配修复测试"""
        # 数据列多于表头
        data = [
            {"工号": "001", "姓名": "张三", "部门": "技术部", "额外列": "值"}
        ]
        headers = ["工号", "姓名", "部门"]
        
        result = self.validator.validate_data_consistency(data, headers, "active_employees", "test")
        
        # 应该检测到问题并尝试修复
        self.assertGreater(len(result.issues), 0)
        self.assertGreater(len(result.fixes_applied), 0)
    
    def test_empty_data_handling(self):
        """空数据处理测试"""
        result = self.validator.validate_data_consistency([], [], None, "test")
        
        # 空数据应该正常处理
        self.assertEqual(result.data, [])
        self.assertEqual(result.headers, [])
    
    def test_performance_tracking(self):
        """性能跟踪测试"""
        data = [{"工号": "001", "姓名": "张三"}]
        headers = ["工号", "姓名"]
        
        # 执行多次验证
        for i in range(5):
            self.validator.validate_data_consistency(data, headers, "active_employees", f"test_{i}")
        
        summary = self.validator.get_performance_summary()
        
        self.assertIn("average_validation_time_ms", summary)
        self.assertIn("total_validations", summary)
        self.assertEqual(summary["total_validations"], 5)


class TestTableStateManager(unittest.TestCase):
    """表格状态管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.state_manager = TableStateManager()
    
    def test_save_and_restore_state(self):
        """保存和恢复状态测试"""
        table_name = "test_table"
        state_data = {
            "headers": ["工号", "姓名", "部门"],
            "sort_column": 1,
            "sort_order": 0,
            "column_widths": {"工号": 100, "姓名": 150}
        }
        
        # 保存状态
        success = self.state_manager.save_table_state(table_name, state_data)
        self.assertTrue(success)
        
        # 恢复状态
        restored_state = self.state_manager.restore_table_state(table_name)
        self.assertIsNotNone(restored_state)
        self.assertEqual(restored_state.table_name, table_name)
        self.assertEqual(restored_state.headers, state_data["headers"])
        self.assertEqual(restored_state.sort_column, state_data["sort_column"])
    
    def test_update_state_field(self):
        """更新状态字段测试"""
        table_name = "test_table"
        
        # 创建初始状态
        initial_state = {"headers": ["工号", "姓名"]}
        self.state_manager.save_table_state(table_name, initial_state)
        
        # 更新字段
        success = self.state_manager.update_state_field(table_name, "sort_column", 1)
        self.assertTrue(success)
        
        # 验证更新
        restored_state = self.state_manager.restore_table_state(table_name)
        self.assertEqual(restored_state.sort_column, 1)
    
    def test_clear_state(self):
        """清除状态测试"""
        table_name = "test_table"
        state_data = {"headers": ["工号", "姓名"]}
        
        # 保存状态
        self.state_manager.save_table_state(table_name, state_data)
        
        # 确认状态存在
        self.assertIsNotNone(self.state_manager.restore_table_state(table_name))
        
        # 清除状态
        success = self.state_manager.clear_table_state(table_name)
        self.assertTrue(success)
        
        # 确认状态已清除
        self.assertIsNone(self.state_manager.restore_table_state(table_name))
    
    def test_state_summary(self):
        """状态摘要测试"""
        # 创建几个状态
        for i in range(3):
            self.state_manager.save_table_state(f"table_{i}", {"headers": [f"col_{i}"]})
        
        summary = self.state_manager.get_state_summary()
        
        self.assertIn("active_memory_states", summary)
        self.assertEqual(summary["active_memory_states"], 3)


class TestFormatRendererFixes(unittest.TestCase):
    """格式渲染器修复测试"""
    
    def setUp(self):
        """测试前准备"""
        try:
            self.format_renderer = FormatRenderer()
            self.field_registry = FieldRegistry()
        except Exception as e:
            self.skipTest(f"无法初始化格式组件: {e}")
    
    def test_default_display_fields(self):
        """默认显示字段测试"""
        # 测试获取默认字段配置
        available_columns = ["工号", "姓名", "部门名称", "人员类别代码", "人员类别"]
        
        default_fields = self.format_renderer._get_default_display_fields("active_employees", available_columns)
        
        self.assertIsInstance(default_fields, list)
        self.assertGreater(len(default_fields), 0)
        
        # 确保返回的字段都在可用列中
        for field in default_fields:
            self.assertIn(field, available_columns)
    
    def test_field_registry_fallback(self):
        """字段注册表降级处理测试"""
        # 测试未知表类型的降级处理
        default_fields = self.field_registry._get_default_display_fields("unknown_table_type")
        
        # 应该返回空列表或默认配置
        self.assertIsInstance(default_fields, list)


def run_integration_test():
    """集成测试：模拟实际使用场景"""
    print("\n" + "="*60)
    print("🔧 数据流修复集成测试")
    print("="*60)
    
    # 1. 创建验证器和状态管理器
    validator = DataFlowValidator(ValidationLevel.MODERATE)
    state_manager = TableStateManager()
    
    print("✅ 组件初始化成功")
    
    # 2. 模拟数据流问题场景
    print("\n📊 测试场景1: 列数不匹配修复")
    
    # 模拟数据库返回28列，但表头只有24列的情况
    mock_data = []
    for i in range(5):
        row = {f"列{j}": f"值{i}_{j}" for j in range(28)}  # 28列数据
        mock_data.append(row)
    
    mock_headers = [f"表头{j}" for j in range(24)]  # 24列表头
    
    print(f"   原始数据: {len(mock_data[0])}列")
    print(f"   原始表头: {len(mock_headers)}列")
    
    # 执行验证和修复
    result = validator.validate_data_consistency(
        mock_data, mock_headers, "active_employees", "integration_test"
    )
    
    print(f"   修复后数据: {len(result.data[0]) if result.data else 0}列")
    print(f"   修复后表头: {len(result.headers) if result.headers else 0}列")
    print(f"   发现问题: {len(result.issues)}个")
    print(f"   应用修复: {len(result.fixes_applied)}个")
    
    # 3. 测试状态管理
    print("\n🔄 测试场景2: 状态管理")
    
    table_name = "全部在职人员工资表"
    state_data = {
        "headers": result.headers if result.headers else mock_headers,
        "sort_column": 3,
        "sort_order": 0,
        "column_widths": {f"表头{i}": 100 + i*10 for i in range(5)}
    }
    
    # 保存状态
    save_success = state_manager.save_table_state(table_name, state_data)
    print(f"   状态保存: {'成功' if save_success else '失败'}")
    
    # 恢复状态
    restored_state = state_manager.restore_table_state(table_name)
    if restored_state:
        print(f"   状态恢复: 成功")
        print(f"   表头数量: {len(restored_state.headers)}")
        print(f"   排序列: {restored_state.sort_column}")
    else:
        print(f"   状态恢复: 失败")
    
    # 4. 性能摘要
    print("\n📈 性能摘要")
    
    validator_summary = validator.get_performance_summary()
    state_summary = state_manager.get_state_summary()
    
    print(f"   验证器平均耗时: {validator_summary.get('average_validation_time_ms', 0):.2f}ms")
    print(f"   验证总次数: {validator_summary.get('total_validations', 0)}")
    print(f"   状态缓存数量: {state_summary.get('active_memory_states', 0)}")
    
    print("\n✅ 集成测试完成")
    print("="*60)


if __name__ == "__main__":
    print("🔧 薪资管理系统数据流修复测试")
    print("="*50)
    
    # 运行单元测试
    print("\n📋 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    run_integration_test()
