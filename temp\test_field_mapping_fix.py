#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射修复验证脚本
测试修复后的字段映射和排序功能
"""

import sys
import os
import json
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_mapping_loading():
    """测试字段映射加载"""
    print("🔍 测试字段映射加载...")
    
    # 检查字段映射文件
    mapping_file = project_root / "state" / "data" / "field_mappings.json"
    if not mapping_file.exists():
        print("❌ 字段映射文件不存在")
        return False
    
    # 加载字段映射
    with open(mapping_file, 'r', encoding='utf-8') as f:
        mapping_config = json.load(f)
    
    table_mappings = mapping_config.get("table_mappings", {})
    print(f"✅ 找到 {len(table_mappings)} 个表的映射配置")
    
    # 测试每个表的映射
    for table_name, table_config in table_mappings.items():
        field_mappings = table_config.get("field_mappings", {})
        print(f"📋 表 {table_name}: {len(field_mappings)} 个字段映射")
        
        # 显示前3个映射示例
        for i, (english_name, chinese_name) in enumerate(field_mappings.items()):
            if i < 3:
                print(f"   {english_name} -> {chinese_name}")
            elif i == 3:
                print(f"   ... 还有 {len(field_mappings) - 3} 个映射")
                break
    
    return True

def test_field_mapping_logic():
    """测试字段映射逻辑"""
    print("\n🔍 测试字段映射逻辑...")
    
    # 模拟DataFrame
    test_df = pd.DataFrame({
        'employee_id': ['19990089', '20161565'],
        'employee_name': ['张三', '李四'],
        'department': ['自动化学院', '计算机学院'],
        'total_salary': [5000.0, 6000.0]
    })
    
    # 模拟字段映射
    field_mapping = {
        'employee_id': '工号',
        'employee_name': '姓名', 
        'department': '部门名称',
        'total_salary': '应发工资'
    }
    
    print(f"原始DataFrame列名: {list(test_df.columns)}")
    print(f"字段映射: {field_mapping}")
    
    # 测试修复后的逻辑
    reverse_mapping = {}
    for english_name, chinese_name in field_mapping.items():
        if english_name in test_df.columns:
            reverse_mapping[english_name] = chinese_name
    
    print(f"反向映射: {reverse_mapping}")
    
    if reverse_mapping:
        df_mapped = test_df.rename(columns=reverse_mapping)
        print(f"✅ 映射后DataFrame列名: {list(df_mapped.columns)}")
        return True
    else:
        print("❌ 没有找到匹配的字段映射")
        return False

def test_cache_key_generation():
    """测试缓存键生成逻辑"""
    print("\n🔍 测试缓存键生成...")
    
    # 模拟缓存键生成
    def generate_cache_key(table_name: str, page: int, page_size: int, sort_state: str = "") -> str:
        if sort_state:
            return f"{table_name}:{page}:{page_size}:{sort_state}"
        return f"{table_name}:{page}:{page_size}"
    
    table_name = "salary_data_2025_07_active_employees"
    page = 1
    page_size = 50
    
    # 无排序状态
    key1 = generate_cache_key(table_name, page, page_size)
    print(f"无排序: {key1}")
    
    # 有排序状态
    sort_state = "grade_salary_2025:asc"
    key2 = generate_cache_key(table_name, page, page_size, sort_state)
    print(f"有排序: {key2}")
    
    print("✅ 缓存键生成逻辑正常")
    return True

def main():
    """主测试函数"""
    print("🚀 开始字段映射修复验证测试\n")
    
    tests = [
        ("字段映射加载", test_field_mapping_loading),
        ("字段映射逻辑", test_field_mapping_logic),
        ("缓存键生成", test_cache_key_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！修复应该生效了。")
    else:
        print("⚠️  部分测试失败，可能需要进一步调试。")

if __name__ == "__main__":
    main()
