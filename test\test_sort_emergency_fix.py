#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序功能紧急修复验证脚本

用于验证排序异常退出问题的修复效果。
此脚本会模拟快速点击表头的场景，确保系统不会异常退出。

创建时间: 2025-07-17
测试目标: 验证排序功能的稳定性和防护机制
"""

import sys
import os
import time
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.log_config import setup_logger
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

logger = setup_logger("sort_emergency_fix_test")

class SortEmergencyFixTester:
    """排序紧急修复测试器"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.test_results = []
        
    def setup_test_environment(self):
        """设置测试环境"""
        try:
            logger.info("🔧 [测试] 初始化测试环境")
            
            # 创建QApplication
            self.app = QApplication(sys.argv)
            
            # 创建主窗口
            self.main_window = PrototypeMainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTest.qWait(1000)
            
            logger.info("✅ 测试环境初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False
    
    def test_rapid_header_clicks(self):
        """测试快速点击表头"""
        try:
            logger.info("🔧 [测试] 开始快速表头点击测试")
            
            # 查找表格组件
            table_widget = None
            if hasattr(self.main_window, 'main_workspace'):
                workspace = self.main_window.main_workspace
                if hasattr(workspace, 'table_widget'):
                    table_widget = workspace.table_widget
            
            if not table_widget:
                logger.warning("⚠️ 没有找到表格组件，跳过测试")
                return False
            
            header = table_widget.horizontalHeader()
            
            # 测试快速点击第2列（避开序号列）
            test_column = 1
            click_count = 10
            
            logger.info(f"🔧 [测试] 对列{test_column}进行{click_count}次快速点击")
            
            start_time = time.time()
            success_count = 0
            
            for i in range(click_count):
                try:
                    # 模拟鼠标点击表头
                    header.sectionPressed.emit(test_column)
                    
                    # 极短间隔（模拟异常快速点击）
                    QTest.qWait(10)  # 10毫秒间隔
                    
                    success_count += 1
                    
                except Exception as click_error:
                    logger.warning(f"⚠️ 第{i+1}次点击失败: {click_error}")
            
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"✅ 快速点击测试完成: {success_count}/{click_count} 成功，耗时: {duration:.3f}秒")
            
            # 验证系统是否仍然响应
            self.verify_system_responsive()
            
            return success_count == click_count
            
        except Exception as e:
            logger.error(f"❌ 快速点击测试失败: {e}")
            return False
    
    def test_protection_mechanisms(self):
        """测试防护机制"""
        try:
            logger.info("🔧 [测试] 测试防护机制")
            
            # 查找表格组件
            table_widget = None
            if hasattr(self.main_window, 'main_workspace'):
                workspace = self.main_window.main_workspace
                if hasattr(workspace, 'table_widget'):
                    table_widget = workspace.table_widget
            
            if not table_widget:
                logger.warning("⚠️ 没有找到表格组件，跳过测试")
                return False
            
            # 检查防护标志是否正确初始化
            protection_flags = [
                '_processing_header_click',
                '_last_header_click_time',
                '_header_click_count'
            ]
            
            missing_flags = []
            for flag in protection_flags:
                if not hasattr(table_widget, flag):
                    missing_flags.append(flag)
            
            if missing_flags:
                logger.error(f"❌ 缺少防护标志: {missing_flags}")
                return False
            
            logger.info("✅ 所有防护机制正确初始化")
            return True
            
        except Exception as e:
            logger.error(f"❌ 防护机制测试失败: {e}")
            return False
    
    def verify_system_responsive(self):
        """验证系统响应性"""
        try:
            logger.info("🔧 [测试] 验证系统响应性")
            
            # 处理所有挂起的事件
            self.app.processEvents()
            QTest.qWait(500)
            
            # 检查主窗口是否仍然可见
            if self.main_window.isVisible():
                logger.info("✅ 系统响应正常，主窗口可见")
                return True
            else:
                logger.error("❌ 主窗口不可见，系统可能崩溃")
                return False
                
        except Exception as e:
            logger.error(f"❌ 系统响应性验证失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        try:
            logger.info("🚀 开始排序紧急修复验证测试")
            
            # 设置测试环境
            if not self.setup_test_environment():
                return False
            
            # 运行测试
            test_results = {
                "protection_mechanisms": self.test_protection_mechanisms(),
                "rapid_clicks": self.test_rapid_header_clicks(),
                "system_responsive": self.verify_system_responsive()
            }
            
            # 输出测试结果
            success_count = sum(test_results.values())
            total_tests = len(test_results)
            
            logger.info(f"📊 测试结果总结: {success_count}/{total_tests} 通过")
            
            for test_name, result in test_results.items():
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"  - {test_name}: {status}")
            
            if success_count == total_tests:
                logger.info("🎉 所有测试通过！排序紧急修复验证成功")
                return True
            else:
                logger.warning("⚠️ 部分测试失败，需要进一步检查")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
        
        finally:
            # 清理测试环境
            if self.main_window:
                self.main_window.close()
            if self.app:
                self.app.quit()

if __name__ == "__main__":
    tester = SortEmergencyFixTester()
    success = tester.run_all_tests()
    
    if success:
        print("✅ 排序紧急修复验证成功")
        sys.exit(0)
    else:
        print("❌ 排序紧急修复验证失败")
        sys.exit(1)