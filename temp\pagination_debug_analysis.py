#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页按钮变灰问题调试分析脚本

通过分析代码逻辑，找出分页按钮变灰的根本原因
"""

import sys
import os
import re
from pathlib import Path

def analyze_pagination_button_logic():
    """分析分页按钮逻辑"""
    print("\n=== 分析分页按钮enable/disable逻辑 ===")
    
    try:
        pagination_file = Path("src/gui/widgets/pagination_widget.py")
        if not pagination_file.exists():
            print("错误: pagination_widget.py文件不存在")
            return False
        
        with open(pagination_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到按钮状态设置逻辑
        if "self.next_page_btn.setEnabled(self.state.current_page < self.state.total_pages)" in content:
            print("✓ 找到下一页按钮控制逻辑: current_page < total_pages")
        
        # 检查total_pages计算逻辑
        total_pages_pattern = r"self\.total_pages\s*=\s*max\(1,\s*\(self\.total_records\s*\+\s*self\.page_size\s*-\s*1\)\s*//\s*self\.page_size\)"
        if re.search(total_pages_pattern, content):
            print("✓ 找到total_pages计算逻辑: max(1, (total_records + page_size - 1) // page_size)")
        
        return True
        
    except Exception as e:
        print(f"分析异常: {e}")
        return False

def analyze_total_records_updates():
    """分析total_records更新位置"""
    print("\n=== 分析total_records更新位置 ===")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("错误: prototype_main_window.py文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到所有set_total_records调用
        pattern = r"\.set_total_records\(([^)]+)\)"
        matches = re.finditer(pattern, content)
        
        print("发现的set_total_records调用:")
        for i, match in enumerate(matches, 1):
            param = match.group(1)
            # 查找行号
            lines = content[:match.start()].count('\n') + 1
            print(f"{i}. 行{lines}: set_total_records({param})")
            
            # 分析参数类型
            if "len(df_converted)" in param:
                print(f"   ⚠️  可能有问题: 使用len(df_converted)可能不是完整数据集大小")
            elif "total_records" in param:
                print(f"   ✓ 正常: 使用total_records变量")
            elif "len(df)" in param:
                print(f"   ⚠️  需检查: 使用len(df)，需确认df是完整数据集")
        
        return True
        
    except Exception as e:
        print(f"分析异常: {e}")
        return False

def analyze_pagination_data_flow():
    """分析分页数据流"""
    print("\n=== 分析分页数据流 ===")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查分页数据加载流程
        pagination_flow_markers = [
            "_on_pagination_data_loaded",
            "set_total_records",
            "set_current_page", 
            "set_page_size",
            "_update_ui_state"
        ]
        
        print("分页数据流关键节点:")
        for marker in pagination_flow_markers:
            if marker in content:
                print(f"✓ 找到: {marker}")
            else:
                print(f"✗ 缺失: {marker}")
        
        # 检查分页状态同步
        if "sync_with_data_source" in content:
            print("✓ 找到数据源同步方法")
        
        return True
        
    except Exception as e:
        print(f"分析异常: {e}")
        return False

def check_potential_race_conditions():
    """检查潜在的竞态条件"""
    print("\n=== 检查潜在竞态条件 ===")
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有多处设置total_records但值不同
        race_conditions = []
        
        # 检查非分页模式下的total_records设置
        if "len(df_converted)" in content and "set_total_records" in content:
            race_conditions.append("非分页模式使用len(df_converted)可能与分页模式冲突")
        
        # 检查分页数据加载时的状态更新顺序
        if "set_total_records" in content and "set_current_page" in content:
            race_conditions.append("total_records和current_page设置顺序可能有问题")
        
        if race_conditions:
            print("发现潜在竞态条件:")
            for i, condition in enumerate(race_conditions, 1):
                print(f"{i}. {condition}")
        else:
            print("未发现明显竞态条件")
        
        return True
        
    except Exception as e:
        print(f"分析异常: {e}")
        return False

def suggest_fix_strategy():
    """提出修复策略"""
    print("\n=== 修复策略建议 ===")
    
    strategies = [
        "1. 确保分页模式下total_records设置的一致性",
        "2. 添加分页状态同步的调试日志",
        "3. 检查set_total_records调用的时机和参数正确性",
        "4. 验证total_pages计算和UI更新的同步性",
        "5. 添加分页按钮状态的强制刷新机制"
    ]
    
    for strategy in strategies:
        print(strategy)
    
    print("\n重点修复区域:")
    print("- src/gui/prototype/prototype_main_window.py 第793行")
    print("- src/gui/prototype/prototype_main_window.py 第6618行") 
    print("- src/gui/widgets/pagination_widget.py 第400行")

def main():
    """主分析函数"""
    print("🔍 分页按钮变灰问题深度分析")
    print("=" * 60)
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    analyses = [
        ("分页按钮逻辑分析", analyze_pagination_button_logic),
        ("total_records更新分析", analyze_total_records_updates),
        ("分页数据流分析", analyze_pagination_data_flow),
        ("竞态条件检查", check_potential_race_conditions),
    ]
    
    results = []
    
    for analysis_name, analysis_func in analyses:
        print(f"\n>>> {analysis_name}")
        try:
            result = analysis_func()
            results.append((analysis_name, result))
        except Exception as e:
            print(f"分析异常: {e}")
            results.append((analysis_name, False))
    
    suggest_fix_strategy()
    
    print("\n" + "=" * 60)
    print("分析完成")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"成功分析: {passed}/{total} 项")

if __name__ == "__main__":
    main()