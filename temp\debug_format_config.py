#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试格式配置脚本
"""

import sys
import os
import pandas as pd
sys.path.append('.')

def debug_format_config():
    print("Debug format configuration...")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        # Initialize components
        config_manager = ConfigManager()
        db_manager = DynamicTableManager("data/salary_system.db")
        factory = get_architecture_factory(db_manager, config_manager)
        
        if not factory.initialize_architecture():
            print("FAILED: Architecture initialization")
            return False
        
        # Get unified format manager
        unified_format_manager = factory.get_unified_format_manager()
        print("SUCCESS: Got unified format manager")
        
        # Debug format config
        format_config = unified_format_manager.format_config
        
        # Test get_format_rules for different scenarios
        print("\n=== Testing format_rules ===")
        
        # Test default float config
        default_float = format_config.get_format_rules('float')
        print(f"Default float config: {default_float}")
        
        # Test float config for retired_employees
        retired_float = format_config.get_format_rules('float', 'retired_employees')
        print(f"Retired employees float config: {retired_float}")
        
        # Test retired staff config directly
        retired_staff_config = format_config.get_config('retired_staff_format_config.format_rules')
        print(f"Retired staff config direct: {retired_staff_config}")
        
        # Check all config keys
        all_config = format_config.get_config('')
        print(f"Available config keys: {list(all_config.keys()) if all_config else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_format_config()