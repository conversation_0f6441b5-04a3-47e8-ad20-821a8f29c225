#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入设置对话框

提供用户界面来配置数据导入的默认设置和智能匹配规则。

创建时间: 2025-01-20
"""

import json
from typing import Dict, List, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QGroupBox, QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QCheckBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QDialogButtonBox, QMessageBox, QTextEdit, QSplitter
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger
from src.modules.data_import.import_defaults_manager import ImportDefaultsManager


class ImportSettingsDialog(QDialog):
    """数据导入设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.defaults_manager = ImportDefaultsManager()
        self.current_settings = self.defaults_manager.load_settings()
        self.current_mapping = self.defaults_manager.get_category_sheet_mapping()
        
        self._init_ui()
        self._load_current_settings()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据导入设置")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 默认设置选项卡
        self._create_defaults_tab()
        
        # 匹配规则选项卡
        self._create_mapping_tab()
        
        # 高级设置选项卡
        self._create_advanced_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        self._create_button_area(layout)
        
        # 设置样式
        self._set_dialog_style()
    
    def _create_defaults_tab(self):
        """创建默认设置选项卡"""
        defaults_widget = QWidget()
        layout = QVBoxLayout(defaults_widget)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        
        # 起始行
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setRange(1, 100)
        self.start_row_spin.setToolTip("数据开始的行号")
        basic_layout.addRow("默认起始行:", self.start_row_spin)
        
        # 导入模式
        self.import_mode_combo = QComboBox()
        self.import_mode_combo.addItems(["single_sheet", "multi_sheet"])
        self.import_mode_combo.setToolTip("默认的导入模式")
        basic_layout.addRow("默认导入模式:", self.import_mode_combo)
        
        # 导入策略
        self.import_strategy_combo = QComboBox()
        self.import_strategy_combo.addItems(["merge_to_single_table", "separate_tables"])
        self.import_strategy_combo.setToolTip("多Sheet导入时的策略")
        basic_layout.addRow("导入策略:", self.import_strategy_combo)
        
        # 表创建模式
        self.table_mode_combo = QComboBox()
        self.table_mode_combo.addItems(["sheet_name", "custom_name", "preview_only"])
        self.table_mode_combo.setToolTip("表不存在时的创建模式")
        basic_layout.addRow("表创建模式:", self.table_mode_combo)
        
        layout.addWidget(basic_group)
        
        # 功能开关组
        features_group = QGroupBox("功能开关")
        features_layout = QFormLayout(features_group)
        
        # 自动匹配工作表
        self.auto_match_check = QCheckBox("启用智能工作表匹配")
        self.auto_match_check.setToolTip("根据人员类别自动匹配工作表")
        features_layout.addRow(self.auto_match_check)
        
        # 包含表头
        self.include_header_check = QCheckBox("默认包含表头")
        self.include_header_check.setToolTip("第一行作为表头处理")
        features_layout.addRow(self.include_header_check)
        
        # 跳过空行
        self.skip_empty_check = QCheckBox("默认跳过空行")
        self.skip_empty_check.setToolTip("导入时自动跳过空行")
        features_layout.addRow(self.skip_empty_check)
        
        layout.addWidget(features_group)
        
        # 类别特定设置组
        category_group = QGroupBox("类别特定设置")
        category_layout = QVBoxLayout(category_group)
        
        # 说明文本
        info_label = QLabel("为不同的人员类别配置特定的导入设置：")
        info_label.setWordWrap(True)
        category_layout.addWidget(info_label)
        
        # 类别设置表格
        self.category_settings_table = QTableWidget()
        self.category_settings_table.setColumnCount(3)
        self.category_settings_table.setHorizontalHeaderLabels(["人员类别", "导入模式", "表创建模式"])
        
        # 设置列宽
        header = self.category_settings_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        category_layout.addWidget(self.category_settings_table)
        
        layout.addWidget(category_group)
        
        self.tab_widget.addTab(defaults_widget, "默认设置")
    
    def _create_mapping_tab(self):
        """创建匹配规则选项卡"""
        mapping_widget = QWidget()
        layout = QVBoxLayout(mapping_widget)
        
        # 说明区域
        info_group = QGroupBox("匹配规则说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
智能匹配规则用于根据人员类别自动选择最合适的工作表。
您可以为每个人员类别定义多个匹配关键词，系统会按照以下优先级进行匹配：

1. 精确匹配：工作表名称与关键词完全一致
2. 模糊匹配：工作表名称包含关键词
3. 语义匹配：基于语义相似度的匹配

关键词越靠前，匹配优先级越高。
        """)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # 匹配规则编辑区域
        mapping_group = QGroupBox("匹配规则编辑")
        mapping_layout = QVBoxLayout(mapping_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.add_category_btn = QPushButton("添加类别")
        self.add_category_btn.clicked.connect(self._add_category)
        button_layout.addWidget(self.add_category_btn)
        
        self.remove_category_btn = QPushButton("删除类别")
        self.remove_category_btn.clicked.connect(self._remove_category)
        button_layout.addWidget(self.remove_category_btn)
        
        self.reset_mapping_btn = QPushButton("重置为默认")
        self.reset_mapping_btn.clicked.connect(self._reset_mapping)
        button_layout.addWidget(self.reset_mapping_btn)
        
        button_layout.addStretch()
        mapping_layout.addLayout(button_layout)
        
        # 匹配规则表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(2)
        self.mapping_table.setHorizontalHeaderLabels(["人员类别", "匹配关键词"])
        
        # 设置列宽
        header = self.mapping_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        mapping_layout.addWidget(self.mapping_table)
        
        layout.addWidget(mapping_group)
        
        self.tab_widget.addTab(mapping_widget, "匹配规则")
    
    def _create_advanced_tab(self):
        """创建高级设置选项卡"""
        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)
        
        # 性能设置组
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        # 匹配阈值
        self.match_threshold_spin = QSpinBox()
        self.match_threshold_spin.setRange(10, 90)
        self.match_threshold_spin.setSuffix("%")
        self.match_threshold_spin.setToolTip("最低匹配阈值，低于此值的匹配将被忽略")
        performance_layout.addRow("匹配阈值:", self.match_threshold_spin)
        
        # 建议数量
        self.suggestion_count_spin = QSpinBox()
        self.suggestion_count_spin.setRange(1, 10)
        self.suggestion_count_spin.setToolTip("显示匹配建议的最大数量")
        performance_layout.addRow("建议数量:", self.suggestion_count_spin)
        
        layout.addWidget(performance_group)
        
        # 用户体验设置组
        ux_group = QGroupBox("用户体验设置")
        ux_layout = QFormLayout(ux_group)
        
        # 状态消息持续时间
        self.message_duration_spin = QSpinBox()
        self.message_duration_spin.setRange(1000, 10000)
        self.message_duration_spin.setSuffix(" ms")
        self.message_duration_spin.setToolTip("状态消息的显示持续时间")
        ux_layout.addRow("消息持续时间:", self.message_duration_spin)
        
        # 自动建议
        self.auto_suggest_check = QCheckBox("启用自动建议")
        self.auto_suggest_check.setToolTip("当自动匹配失败时显示建议对话框")
        ux_layout.addRow(self.auto_suggest_check)
        
        layout.addWidget(ux_group)
        
        # 配置文件管理组
        config_group = QGroupBox("配置文件管理")
        config_layout = QVBoxLayout(config_group)
        
        # 操作按钮
        config_button_layout = QHBoxLayout()
        
        self.export_config_btn = QPushButton("导出配置")
        self.export_config_btn.clicked.connect(self._export_config)
        config_button_layout.addWidget(self.export_config_btn)
        
        self.import_config_btn = QPushButton("导入配置")
        self.import_config_btn.clicked.connect(self._import_config)
        config_button_layout.addWidget(self.import_config_btn)
        
        self.reset_all_btn = QPushButton("重置所有设置")
        self.reset_all_btn.clicked.connect(self._reset_all_settings)
        config_button_layout.addWidget(self.reset_all_btn)
        
        config_button_layout.addStretch()
        config_layout.addLayout(config_button_layout)
        
        layout.addWidget(config_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(advanced_widget, "高级设置")
    
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        
        button_box.accepted.connect(self._save_and_close)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_settings)
        
        parent_layout.addWidget(button_box)
    
    def _set_dialog_style(self):
        """设置对话框样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: #ffffff;
                border-bottom-color: #ffffff;
            }
        """)
    
    def _load_current_settings(self):
        """加载当前设置到界面"""
        try:
            # 加载基本设置
            self.start_row_spin.setValue(self.current_settings.get('start_row', 1))
            
            import_mode = self.current_settings.get('import_mode', 'multi_sheet')
            index = self.import_mode_combo.findText(import_mode)
            if index >= 0:
                self.import_mode_combo.setCurrentIndex(index)
            
            import_strategy = self.current_settings.get('import_strategy', 'merge_to_single_table')
            index = self.import_strategy_combo.findText(import_strategy)
            if index >= 0:
                self.import_strategy_combo.setCurrentIndex(index)
            
            table_mode = self.current_settings.get('create_table_mode', 'sheet_name')
            index = self.table_mode_combo.findText(table_mode)
            if index >= 0:
                self.table_mode_combo.setCurrentIndex(index)
            
            # 加载功能开关
            self.auto_match_check.setChecked(self.current_settings.get('auto_match_sheet', True))
            self.include_header_check.setChecked(self.current_settings.get('include_header', True))
            self.skip_empty_check.setChecked(self.current_settings.get('skip_empty_rows', True))
            
            # 加载高级设置
            self.match_threshold_spin.setValue(int(self.current_settings.get('match_threshold', 30)))
            self.suggestion_count_spin.setValue(self.current_settings.get('suggestion_count', 3))
            self.message_duration_spin.setValue(self.current_settings.get('message_duration', 3000))
            self.auto_suggest_check.setChecked(self.current_settings.get('auto_suggest', True))
            
            # 加载匹配规则
            self._load_mapping_table()
            
            self.logger.info("设置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            QMessageBox.warning(self, "警告", f"加载设置失败: {e}")
    
    def _load_mapping_table(self):
        """加载匹配规则表格"""
        try:
            self.mapping_table.setRowCount(len(self.current_mapping))
            
            for row, (category, keywords) in enumerate(self.current_mapping.items()):
                # 类别名称
                category_item = QTableWidgetItem(category)
                self.mapping_table.setItem(row, 0, category_item)
                
                # 关键词（用逗号分隔）
                keywords_text = ", ".join(keywords) if isinstance(keywords, list) else str(keywords)
                keywords_item = QTableWidgetItem(keywords_text)
                self.mapping_table.setItem(row, 1, keywords_item)
            
        except Exception as e:
            self.logger.error(f"加载匹配规则表格失败: {e}")
    
    def _add_category(self):
        """添加新类别"""
        # 这里可以添加新类别的逻辑
        pass
    
    def _remove_category(self):
        """删除选中的类别"""
        # 这里可以添加删除类别的逻辑
        pass
    
    def _reset_mapping(self):
        """重置匹配规则为默认值"""
        # 这里可以添加重置逻辑
        pass
    
    def _export_config(self):
        """导出配置"""
        # 这里可以添加导出逻辑
        pass
    
    def _import_config(self):
        """导入配置"""
        # 这里可以添加导入逻辑
        pass
    
    def _reset_all_settings(self):
        """重置所有设置"""
        # 这里可以添加重置所有设置的逻辑
        pass
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 收集设置
            settings = {
                'start_row': self.start_row_spin.value(),
                'import_mode': self.import_mode_combo.currentText(),
                'import_strategy': self.import_strategy_combo.currentText(),
                'create_table_mode': self.table_mode_combo.currentText(),
                'auto_match_sheet': self.auto_match_check.isChecked(),
                'include_header': self.include_header_check.isChecked(),
                'skip_empty_rows': self.skip_empty_check.isChecked(),
                'match_threshold': self.match_threshold_spin.value(),
                'suggestion_count': self.suggestion_count_spin.value(),
                'message_duration': self.message_duration_spin.value(),
                'auto_suggest': self.auto_suggest_check.isChecked()
            }
            
            # 保存设置
            success = self.defaults_manager.save_settings(settings)
            if success:
                QMessageBox.information(self, "成功", "设置已保存")
                self.logger.info("设置保存成功")
            else:
                QMessageBox.warning(self, "错误", "设置保存失败")
                
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
            QMessageBox.critical(self, "错误", f"应用设置失败: {e}")
    
    def _save_and_close(self):
        """保存设置并关闭"""
        self._apply_settings()
        self.accept()
