# VS Code SFTP 插件配置指南

本文档记录了如何配置 VS Code 的 `SFTP` 插件，以实现与远程服务器的高效文件同步。

## 1. 基础连接配置

根据用户提供的 SSH 配置文件 (`~/.ssh/config`)，我们将 `.vscode/sftp.json` 进行了如下基础配置，以便能成功连接到远程服务器。

### SSH 配置 (`~/.ssh/config`)
```
Host cc
  HostName *************
  User ubuntu
  IdentityFile ~/.ssh/id_rsa
```

### 初始 SFTP 配置 (`.vscode/sftp.json`)
```json
{
    "name": "CC Server",
    "host": "*************",
    "protocol": "sftp",
    "port": 22,
    "username": "ubuntu",
    "privateKeyPath": "~/.ssh/id_rsa",
    "remotePath": "/home/<USER>",
    "uploadOnSave": false,
    "useTempFile": false,
    "openSsh": true
}
```
**关键点**:
- `host`, `username`, `port` 直接从 SSH 配置中获取。
- `privateKeyPath` 指定了认证所需的私钥文件。
- `openSsh": true` 允许插件利用本地 SSH 配置。

## 2. 双向自动同步配置

为了实现本地工作区与服务器文件的自动同步，我们在基础配置上增加了高级同步功能。

### 最终 SFTP 配置 (`.vscode/sftp.json`)
```json
{
    "name": "CC Server",
    "host": "*************",
    "protocol": "sftp",
    "port": 22,
    "username": "ubuntu",
    "privateKeyPath": "~/.ssh/id_rsa",
    "remotePath": "/home/<USER>/salary_changes",
    "uploadOnSave": true,
    "downloadOnOpen": true,
    "syncMode": "update",
    "watcher": {
        "files": "**/*",
        "autoUpload": true,
        "autoDelete": true
    },
    "useTempFile": false,
    "openSsh": true,
    "ignore": [
        ".vscode/**",
        ".git/**",
        "node_modules/**",
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/__pycache__/**",
        "**/*.pyc",
        "temp/**",
        "output/**"
    ]
}
```

**新增功能**:
- **`uploadOnSave: true`**: 本地文件保存时自动上传。
- **`downloadOnOpen: true`**: 打开文件时自动从服务器下载。
- **`watcher`**: 文件监控器，可以实时监测文件变动并自动同步（包括删除操作）。
- **`ignore`**: 忽略列表，避免同步不必要的文件和目录（如 `.vscode`, `.git`, `temp` 等），提高同步效率。
- **`remotePath`**: 更新为项目特定的远程路径 `/home/<USER>/salary_changes`。

## 3. 使用方法

1.  **启用/禁用监控**:
    - 使用 `Ctrl+Shift+P` 打开命令面板。
    - 输入 `SFTP: Toggle Watcher` 来启动或停止文件变动监控。

2.  **手动同步**:
    - `SFTP: Sync Remote -> Local`: 将远程服务器的更新同步到本地。
    - `SFTP: Sync Local -> Remote`: 将本地的修改同步到远程服务器。

通过以上配置，可以极大地提升开发效率，确保本地和服务器代码的实时一致性。 