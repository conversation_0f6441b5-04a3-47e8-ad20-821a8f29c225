#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射配置修复脚本

直接修复existing_display_fields为空的问题
"""

import sys
import os
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.utils.log_config import setup_logger

logger = setup_logger("FieldMappingFix")

def fix_field_mappings():
    """修复字段映射配置"""
    try:
        mapping_file = "state/data/field_mappings.json"
        logger.info(f"开始修复字段映射配置文件: {mapping_file}")
        
        # 读取现有配置
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                field_mappings = json.load(f)
            logger.info("已读取现有字段映射配置")
        else:
            logger.warning("字段映射配置文件不存在，无法修复")
            return False
        
        # 检查table_mappings
        table_mappings = field_mappings.get("table_mappings", {})
        if not table_mappings:
            logger.warning("没有找到table_mappings配置")
            return False
        
        fixes_made = 0
        
        for table_key, table_config in table_mappings.items():
            logger.info(f"检查表: {table_key}")
            
            # 检查并修复existing_display_fields
            metadata = table_config.setdefault('metadata', {})
            existing_display_fields = metadata.get('existing_display_fields', [])
            
            if not existing_display_fields:
                # 从field_mappings生成existing_display_fields
                field_mappings_data = table_config.get('field_mappings', {})
                display_order = table_config.get('display_order', [])
                hidden_fields = set(table_config.get('hidden_fields', []))
                
                if field_mappings_data:
                    # 按display_order优先，然后添加其他字段
                    auto_display_fields = []
                    
                    # 添加display_order中的字段
                    for field in display_order:
                        if field in field_mappings_data and field not in hidden_fields:
                            auto_display_fields.append(field)
                    
                    # 添加其他字段
                    for field in field_mappings_data.keys():
                        if field not in auto_display_fields and field not in hidden_fields:
                            auto_display_fields.append(field)
                    
                    if auto_display_fields:
                        metadata['existing_display_fields'] = auto_display_fields
                        fixes_made += 1
                        logger.info(f"  ✅ 修复 {table_key}: 生成了 {len(auto_display_fields)} 个 existing_display_fields")
                        logger.debug(f"     字段: {auto_display_fields[:5]}...")
                else:
                    logger.warning(f"  ❌ {table_key}: 没有field_mappings，无法生成existing_display_fields")
            else:
                logger.info(f"  ✅ {table_key}: existing_display_fields已存在 ({len(existing_display_fields)}个字段)")
            
            # 检查并修复字段类型定义一致性
            field_mappings_data = table_config.get('field_mappings', {})
            field_types = table_config.setdefault('field_types', {})
            
            # 为缺失的字段添加默认类型
            for field in field_mappings_data.keys():
                if field not in field_types:
                    # 根据字段名推断类型
                    if 'salary' in field.lower() or 'amount' in field.lower() or 'money' in field.lower():
                        field_types[field] = 'currency'
                    elif 'date' in field.lower() or 'time' in field.lower():
                        field_types[field] = 'date'
                    elif 'id' in field.lower() or 'number' in field.lower():
                        field_types[field] = 'string'
                    else:
                        field_types[field] = 'string'
                    
                    fixes_made += 1
                    logger.info(f"  ✅ 为字段 {field} 添加默认类型: {field_types[field]}")
            
            # 移除多余的字段类型定义
            extra_fields = []
            for field in list(field_types.keys()):
                if field not in field_mappings_data:
                    extra_fields.append(field)
                    del field_types[field]
                    fixes_made += 1
            
            if extra_fields:
                logger.info(f"  ✅ 移除多余的字段类型定义: {extra_fields}")
        
        # 保存修复后的配置
        if fixes_made > 0:
            # 创建备份
            backup_file = f"{mapping_file}.backup"
            if os.path.exists(mapping_file):
                import shutil
                shutil.copy2(mapping_file, backup_file)
                logger.info(f"已创建备份文件: {backup_file}")
            
            # 保存修复后的配置
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(field_mappings, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 字段映射配置修复完成，总计修复: {fixes_made} 项")
            return True
        else:
            logger.info("✅ 字段映射配置正常，无需修复")
            return True
            
    except Exception as e:
        logger.error(f"字段映射配置修复失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔧 开始字段映射配置修复")
    logger.info("=" * 50)
    
    result = fix_field_mappings()
    
    logger.info("=" * 50)
    if result:
        logger.info("✅ 字段映射配置修复成功")
    else:
        logger.error("❌ 字段映射配置修复失败")
    
    return result

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("修复被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}")
        sys.exit(1)