<!-- Generated by SpecStory -->

# 系统字段显示异常问题 (2025-07-01 10:15Z)

_**User**_

按上面修改后，启动系统，在主界面列表展示区域，发现一个很奇怪的现象：
1、表“全部在职人员”和“A岗职工”的第一页达到了预期，但是，点击下一页后，发现那四个字段还是显示。
2、通过导航切换到表“离休人员”或“退休人员”，发现那四个字段（自增主键、序号、创建时间、更新时间）还是显示在列表区域。

---

_**Assistant**_

我需要先检查一下当前系统的分页和导航切换时的字段处理逻辑，以定位为什么系统字段过滤在某些情况下失效。

---

<details>
            <summary>Searched codebase "分页数据加载 _on_page_changed _on_pagination_data_loaded" • **23** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/prototype_main_window.py` | L3387-3410 |
| `src/gui/prototype/prototype_main_window.py` | L3343-3387 |
| `src/gui/widgets/pagination_widget.py` | L1-55 |
| `src/gui/prototype/prototype_main_window.py` | L233-264 |
| `src/gui/prototype/prototype_main_window.py` | L3410-3447 |
| `src/gui/prototype/prototype_main_window.py` | L105-142 |
| `src/gui/prototype/prototype_main_window.py` | L3313-3343 |
| `src/gui/prototype/prototype_main_window.py` | L3447-3476 |
| `src/gui/widgets/pagination_widget.py` | L172-191 |
| `src/gui/widgets/pagination_widget.py` | L285-305 |
| `src/gui/widgets/pagination_cache_manager.py` | L214-259 |
| `docs/prd/draft/design/main_interface_prototype.html` | L1438-1473 |
| `src/gui/widgets/pagination_widget.py` | L55-88 |
| `src/gui/prototype/prototype_main_window.py` | L142-166 |
| `src/gui/widgets/pagination_widget.py` | L231-285 |
| `src/gui/prototype/prototype_main_window.py` | L3476-3508 |
| `src/gui/widgets/pagination_widget.py` | L191-231 |
| `src/gui/prototype/prototype_main_window.py` | L686-724 |
| `src/gui/prototype/prototype_main_window.py` | L3288-3313 |
| `src/gui/prototype/prototype_main_window.py` | L3267-3288 |
| `src/gui/prototype/prototype_main_window.py` | L1180-1208 |
| `src/gui/prototype/prototype_main_window.py` | L2804-2833 |
| `src/gui/prototype/prototype_main_window.py` | L724-753 |

</details>

---

现在我需要查看`PaginationWorker`中的`apply_mapping_func`是如何应用的，以及`_on_page_changed`方法的具体实现：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我需要查看`_on_page_changed`方法和`MainWorkspaceArea`的相关方法：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我发现了问题所在！通过分析代码，我发现有两个问题：

1. **分页时的问题**：在`MainWorkspaceArea`的`_on_page_changed`方法中（第637-710行），分页数据加载时只进行了简单的字段映射，没有调用`_apply_unified_field_processing`来应用系统字段过滤。

2. **导航切换时的问题**：在主窗口的分页数据加载流程中，`PaginationWorker`虽然有`apply_mapping_func`参数，但在某些情况下可能没有正确传递或调用。

让我修复这两个问题：

---



---

现在我需要检查一下主窗口的`_load_data_with_pagination`方法，确保它正确地传递了统一字段处理函数给`PaginationWorker`：

---

Read file: src/gui/prototype/prototype_main_window.py

---

好的，我看到主窗口的`_load_data_with_pagination`方法已经正确地传递了`apply_mapping_func=self._apply_unified_field_processing`给`PaginationWorker`。现在我需要检查一下缓存处理部分和非分页模式的数据加载：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我需要修复非分页模式下的数据加载逻辑，确保它也使用统一字段处理：

---



---

现在我需要检查并清理分页缓存，因为缓存中可能还有旧的数据包含系统字段。我们需要添加一个缓存清理机制：

---



---

现在我需要检查`PaginationCacheManager`是否有`clear_table_cache`方法，如果没有就需要添加：

---

<details>
            <summary>Grep search for "def clear_table_cache" • **0** files</summary>
        
No results found
</details>

---

我需要给`PaginationCacheManager`添加`clear_table_cache`方法：

---

Read file: src/gui/widgets/pagination_cache_manager.py

---

我看到缓存管理器已经有了`clear_cache`方法，我可以直接使用它。我需要修改调用方式：

---



---

现在让我们测试修复后的系统：

---

```bash
cd /c%3A/test/salary_changes/salary_changes && python main.py
```

---

