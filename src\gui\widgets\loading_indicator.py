#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加载状态指示器
遵循CLAUDE.md要求：提升用户体验，无emoji字符
"""

import time
from typing import Optional
from PyQt5.QtWidgets import (
    QWidget, QLabel, QProgressBar, QVBoxLayout, QHBoxLayout, 
    QFrame, QGraphicsOpacityEffect
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt5.QtGui import QPainter, QPen, QColor, QFont

from src.utils.log_config import setup_logger


class LoadingIndicator(QWidget):
    """
    加载状态指示器
    
    提供多种加载状态的视觉反馈
    遵循CLAUDE.md原则：数据流追踪日志 + 用户体验优化
    """
    
    def __init__(self, parent=None):
        """初始化加载指示器"""
        super().__init__(parent)
        self.logger = setup_logger(self.__class__.__name__)
        
        # 状态变量
        self._is_loading = False
        self._current_message = ""
        self._animation_angle = 0
        
        # UI组件
        self._setup_ui()
        self._setup_animations()
        
        # 默认隐藏
        self.hide()
        
        self.logger.debug("加载状态指示器初始化完成")
    
    def _setup_ui(self):
        """设置UI界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 背景框架
        self._background_frame = QFrame()
        self._background_frame.setFrameShape(QFrame.Box)
        self._background_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 240);
                border: 2px solid #E0E0E0;
                border-radius: 8px;
            }
        """)
        
        # 框架内部布局
        frame_layout = QVBoxLayout(self._background_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(10)
        
        # 加载消息标签
        self._message_label = QLabel("数据加载中...")
        self._message_label.setAlignment(Qt.AlignCenter)
        self._message_label.setFont(QFont("微软雅黑", 10))
        self._message_label.setStyleSheet("color: #333333;")
        
        # 进度条
        self._progress_bar = QProgressBar()
        self._progress_bar.setRange(0, 0)  # 不确定进度
        self._progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                text-align: center;
                background-color: #F5F5F5;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        # 详细信息标签
        self._detail_label = QLabel("")
        self._detail_label.setAlignment(Qt.AlignCenter)
        self._detail_label.setFont(QFont("微软雅黑", 8))
        self._detail_label.setStyleSheet("color: #666666;")
        
        # 添加到框架布局
        frame_layout.addWidget(self._message_label)
        frame_layout.addWidget(self._progress_bar)
        frame_layout.addWidget(self._detail_label)
        
        # 添加框架到主布局
        layout.addWidget(self._background_frame)
        
        # 设置固定大小
        self.setFixedSize(300, 120)
    
    def _setup_animations(self):
        """设置动画效果"""
        # 透明度动画
        self._opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self._opacity_effect)
        
        self._fade_in_animation = QPropertyAnimation(self._opacity_effect, b"opacity")
        self._fade_in_animation.setDuration(200)
        self._fade_in_animation.setStartValue(0.0)
        self._fade_in_animation.setEndValue(1.0)
        self._fade_in_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self._fade_out_animation = QPropertyAnimation(self._opacity_effect, b"opacity")
        self._fade_out_animation.setDuration(150)
        self._fade_out_animation.setStartValue(1.0)
        self._fade_out_animation.setEndValue(0.0)
        self._fade_out_animation.setEasingCurve(QEasingCurve.InCubic)
        self._fade_out_animation.finished.connect(self._on_fade_out_finished)
        
        # 旋转动画定时器（用于自定义旋转指示器）
        self._rotation_timer = QTimer()
        self._rotation_timer.timeout.connect(self._update_rotation)
        self._rotation_timer.setInterval(50)  # 每50ms更新一次
    
    def show_loading(self, message: str = "数据加载中...", show_progress: bool = True):
        """
        显示加载状态
        
        Args:
            message: 加载消息
            show_progress: 是否显示进度条
        """
        try:
            self.logger.info(f"[数据流追踪] 显示加载状态: {message}")
            
            self._current_message = message
            self._is_loading = True
            
            # 更新UI
            self._message_label.setText(message)
            self._progress_bar.setVisible(show_progress)
            self._detail_label.setText("")
            
            # 重置进度条为不确定进度
            self._progress_bar.setRange(0, 0)
            
            # 显示并开始淡入动画
            self.show()
            self._fade_in_animation.start()
            
            # 开始旋转动画
            self._rotation_timer.start()
            
            # 居中显示
            if self.parent():
                self._center_in_parent()
            
        except Exception as e:
            self.logger.error(f"显示加载状态失败: {e}")
    
    def show_progress(self, current: int, total: int, message: str = ""):
        """
        显示进度条
        
        Args:
            current: 当前进度
            total: 总进度
            message: 进度消息
        """
        try:
            if not self._is_loading:
                self.show_loading(message or "处理中...", show_progress=True)
            
            # 更新进度条
            self._progress_bar.setRange(0, total)
            self._progress_bar.setValue(current)
            
            # 更新消息
            if message:
                self._message_label.setText(message)
            
            # 更新详细信息
            percentage = (current / total * 100) if total > 0 else 0
            self._detail_label.setText(f"进度: {current}/{total} ({percentage:.1f}%)")
            
            self.logger.debug(f"[数据流追踪] 更新进度: {current}/{total} ({percentage:.1f}%)")
            
        except Exception as e:
            self.logger.error(f"显示进度失败: {e}")
    
    def update_message(self, message: str, detail: str = ""):
        """
        更新加载消息
        
        Args:
            message: 主消息
            detail: 详细信息
        """
        try:
            if self._is_loading:
                self._message_label.setText(message)
                if detail:
                    self._detail_label.setText(detail)
                
                self.logger.debug(f"[数据流追踪] 更新加载消息: {message}")
            
        except Exception as e:
            self.logger.error(f"更新消息失败: {e}")
    
    def hide_loading(self):
        """隐藏加载状态"""
        try:
            if self._is_loading:
                self.logger.info("[数据流追踪] 隐藏加载状态")
                
                self._is_loading = False
                
                # 停止旋转动画
                self._rotation_timer.stop()
                
                # 开始淡出动画
                self._fade_out_animation.start()
                
        except Exception as e:
            self.logger.error(f"隐藏加载状态失败: {e}")
    
    def _on_fade_out_finished(self):
        """淡出动画完成时的回调"""
        self.hide()
    
    def _update_rotation(self):
        """更新旋转角度"""
        self._animation_angle = (self._animation_angle + 10) % 360
        # 这里可以触发自定义绘制的重绘
        self.update()
    
    def _center_in_parent(self):
        """在父组件中居中显示"""
        if self.parent():
            parent_rect = self.parent().rect()
            self.move(
                (parent_rect.width() - self.width()) // 2,
                (parent_rect.height() - self.height()) // 2
            )
    
    def is_loading(self) -> bool:
        """检查是否正在加载"""
        return self._is_loading
    
    def set_loading_style(self, style: str):
        """
        设置加载样式
        
        Args:
            style: 样式名称 ('default', 'minimal', 'detailed')
        """
        try:
            if style == 'minimal':
                # 最小化样式
                self._detail_label.hide()
                self.setFixedSize(250, 80)
            elif style == 'detailed':
                # 详细样式
                self._detail_label.show()
                self.setFixedSize(320, 140)
            else:
                # 默认样式
                self._detail_label.show()
                self.setFixedSize(300, 120)
            
            self.logger.debug(f"[数据流追踪] 设置加载样式: {style}")
            
        except Exception as e:
            self.logger.error(f"设置加载样式失败: {e}")


class SimpleLoadingIndicator(QWidget):
    """
    简单加载指示器
    
    更轻量级的加载提示
    """
    
    def __init__(self, parent=None):
        """初始化简单加载指示器"""
        super().__init__(parent)
        self.logger = setup_logger(self.__class__.__name__)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 120);
                border-radius: 4px;
            }
        """)
        
        # 创建标签
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        self._label = QLabel("加载中...")
        self._label.setStyleSheet("color: white; font-size: 12px;")
        layout.addWidget(self._label)
        
        # 默认隐藏
        self.hide()
        
        self.logger.debug("简单加载指示器初始化完成")
    
    def show_loading(self, message: str = "加载中..."):
        """显示加载状态"""
        self._label.setText(message)
        self.show()
        self.logger.debug(f"[数据流追踪] 简单指示器显示: {message}")
    
    def hide_loading(self):
        """隐藏加载状态"""
        self.hide()
        self.logger.debug("[数据流追踪] 简单指示器隐藏")


class LoadingIndicatorManager:
    """
    加载指示器管理器
    
    统一管理应用中的所有加载状态
    """
    
    def __init__(self):
        """初始化加载指示器管理器"""
        self.logger = setup_logger(self.__class__.__name__)
        self._indicators = {}  # 指示器实例缓存
        self._loading_states = {}  # 加载状态跟踪
        
        self.logger.info("加载指示器管理器初始化完成")
    
    def get_indicator(self, parent_widget, indicator_type: str = 'default') -> LoadingIndicator:
        """
        获取加载指示器实例
        
        Args:
            parent_widget: 父组件
            indicator_type: 指示器类型 ('default', 'simple')
            
        Returns:
            LoadingIndicator实例
        """
        widget_id = id(parent_widget)
        cache_key = f"{widget_id}_{indicator_type}"
        
        if cache_key not in self._indicators:
            if indicator_type == 'simple':
                indicator = SimpleLoadingIndicator(parent_widget)
            else:
                indicator = LoadingIndicator(parent_widget)
            
            self._indicators[cache_key] = indicator
            self.logger.debug(f"[数据流追踪] 创建新的加载指示器: {indicator_type}")
        
        return self._indicators[cache_key]
    
    def show_loading(self, parent_widget, message: str = "数据加载中...", 
                    indicator_type: str = 'default'):
        """显示加载状态"""
        indicator = self.get_indicator(parent_widget, indicator_type)
        indicator.show_loading(message)
        
        widget_id = id(parent_widget)
        self._loading_states[widget_id] = {
            'start_time': time.time(),
            'message': message,
            'indicator_type': indicator_type
        }
        
        self.logger.info(f"[数据流追踪] 管理器显示加载: {message}")
    
    def hide_loading(self, parent_widget):
        """隐藏加载状态"""
        widget_id = id(parent_widget)
        
        if widget_id in self._loading_states:
            # 计算加载时长
            loading_duration = time.time() - self._loading_states[widget_id]['start_time']
            
            # 找到并隐藏指示器
            for cache_key, indicator in self._indicators.items():
                if cache_key.startswith(str(widget_id)):
                    indicator.hide_loading()
                    break
            
            # 清除状态
            del self._loading_states[widget_id]
            
            self.logger.info(f"[数据流追踪] 管理器隐藏加载: 持续时间{loading_duration:.2f}秒")
    
    def get_loading_statistics(self) -> dict:
        """获取加载状态统计"""
        current_time = time.time()
        active_loadings = []
        
        for widget_id, state in self._loading_states.items():
            duration = current_time - state['start_time']
            active_loadings.append({
                'widget_id': widget_id,
                'message': state['message'],
                'duration_seconds': round(duration, 2),
                'indicator_type': state['indicator_type']
            })
        
        return {
            'active_loadings_count': len(active_loadings),
            'active_loadings': active_loadings,
            'total_indicators': len(self._indicators)
        }


# 全局加载指示器管理器
_loading_manager = None


def get_loading_indicator_manager() -> LoadingIndicatorManager:
    """获取全局加载指示器管理器"""
    global _loading_manager
    if _loading_manager is None:
        _loading_manager = LoadingIndicatorManager()
    return _loading_manager