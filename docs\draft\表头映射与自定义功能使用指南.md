# 表头映射与自定义功能使用指南

## 功能概述

本系统已实现了智能表头映射与自定义显示功能，可以：
- 自动将Excel表头映射为友好的中文显示名
- 允许用户自定义选择要显示的字段
- 按需加载数据，提升系统性能
- 持久化保存用户偏好设置

## 如何使用

### 1. 显示菜单栏

**重要提示**: 菜单栏默认是隐藏的，需要手动显示。

**操作步骤**:
1. 启动系统后，在界面右上角找到设置按钮（⚙️图标）
2. 点击设置按钮，菜单栏将会显示在界面顶部
3. 菜单栏包含：文件、数据、视图、工具、帮助等菜单

### 2. 数据导入时的自动映射

**操作步骤**:
1. 点击菜单栏中的"数据" → "导入数据"
2. 选择Excel文件进行导入
3. 系统会自动：
   - 检测Excel表头类型（中文/英文）
   - 生成智能字段映射
   - 保存映射配置到系统中
   - 显示友好的中文表头

**映射示例**:
- `employee_id` → `工号`
- `employee_name` → `姓名`
- `department` → `部门`
- `basic_salary` → `基本工资`
- `2026年岗位工资` → `2026年岗位工资`（中文保持不变）

### 3. 查看数据时的中文表头显示

**操作步骤**:
1. 在左侧导航栏中选择要查看的数据表
2. 系统会自动：
   - 应用已保存的字段映射
   - 显示统一的中文表头
   - 按需加载用户选择的字段（如果有自定义偏好）

### 4. 自定义表头显示

**操作步骤**:
1. 确保菜单栏已显示（点击右上角⚙️按钮）
2. 选择要自定义的数据表（在左侧导航栏中点击）
3. 点击菜单栏中的"数据" → "自定义表头"（快捷键：Ctrl+H）
4. 在弹出的对话框中：
   - 查看所有可用字段列表
   - 勾选要显示的字段
   - 使用搜索框快速查找字段
   - 点击"全选"或"取消全选"批量操作
5. 点击"确定"保存设置
6. 系统会自动重新加载数据，只显示选择的字段

**自定义对话框功能**:
- **字段列表**: 显示所有可用字段，格式为"显示名 (字段名)"
- **搜索功能**: 可以搜索字段名或显示名
- **批量操作**: 全选/取消全选按钮
- **统计信息**: 显示已选择字段数量

### 5. 重置表头为默认显示

**操作步骤**:
1. 确保菜单栏已显示
2. 选择要重置的数据表
3. 点击菜单栏中的"数据" → "重置表头"
4. 在确认对话框中点击"是"
5. 系统会删除用户自定义设置，恢复显示所有字段

### 6. 性能优化效果

**按需加载**:
- 当用户自定义了表头显示时，系统只从数据库加载选择的字段
- 减少数据传输量50-80%
- 提升界面响应速度
- 降低内存使用

**分页支持**:
- 自定义表头功能完全支持分页模式
- 在大数据量情况下仍能快速响应

## 配置文件位置

用户的自定义设置保存在：
```
项目根目录/config/field_mappings.json
```

配置文件结构：
```json
{
  "table_mappings": {
    "表名": {
      "字段名": "显示名"
    }
  },
  "user_preferences": {
    "header_preferences": {
      "表名": {
        "selected_fields": ["字段1", "字段2"],
        "updated_at": "2025-06-25T21:28:20",
        "field_count": 2
      }
    }
  }
}
```

## 常见问题

### Q1: 为什么看不到菜单栏？
**A**: 菜单栏默认隐藏，需要点击右上角的设置按钮（⚙️）来显示。

### Q2: 为什么表头还是英文？
**A**: 可能的原因：
1. 该表还没有导入过数据（没有生成字段映射）
2. 字段映射配置文件损坏
3. 表名不匹配

**解决方法**: 重新导入Excel数据，系统会自动生成新的字段映射。

### Q3: 自定义表头后数据加载很慢？
**A**: 这是正常现象，因为系统需要：
1. 重新查询数据库
2. 应用新的字段选择
3. 刷新表格显示

通常几秒钟内就会完成。

### Q4: 如何恢复默认显示？
**A**: 使用"数据" → "重置表头"功能，或者手动删除配置文件中对应表的用户偏好设置。

### Q5: 自定义设置会丢失吗？
**A**: 不会，所有自定义设置都保存在配置文件中，重启系统后会自动恢复。

## 技术支持

如果遇到问题，请检查：
1. 日志文件：`logs/salary_system.log`
2. 配置文件：`config/field_mappings.json`
3. 确保有足够的磁盘空间和内存

## 更新日志

**2025-06-25**:
- ✅ 实现智能字段映射生成器
- ✅ 添加表头自定义对话框
- ✅ 支持用户偏好持久化
- ✅ 实现按需字段加载
- ✅ 集成主界面菜单功能

**功能状态**: 已完成并测试通过
