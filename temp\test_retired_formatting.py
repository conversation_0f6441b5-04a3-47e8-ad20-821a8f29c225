#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试离休人员工资表格式化修复效果

验证用户的三个具体需求：
1. 增发一次性生活补贴、补发、借支 -> float类型，空值显示为"0.00"
2. 备注字段 -> string类型，空值显示为空白
3. 月份字段 -> 提取后两位并转换为字符串
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量，防止编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'

try:
    # 导入项目模块
    from src.modules.format_management.unified_format_manager import UnifiedFormatManager
    from src.modules.format_management.field_registry import FieldRegistry
    from src.modules.format_management.format_renderer import FormatRenderer
    from src.modules.format_management.format_config import FormatConfig
    from src.modules.data_storage.database_manager import DatabaseManager
    from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
    from src.modules.system_config.config_manager import ConfigManager
    
    print("[SUCCESS] 所有模块导入成功")
    
except ImportError as e:
    print(f"[ERROR] 模块导入失败: {e}")
    sys.exit(1)

def test_retired_employees_formatting():
    """测试离休人员表格式化"""
    try:
        print("\\n=== 离休人员工资表格式化测试 ===")
        
        # 1. 初始化核心组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 2. 获取离休人员表数据
        table_name = "salary_data_2025_08_retired_employees"
        df = table_manager.get_dataframe_paginated(table_name, page=1, page_size=50)
        
        if df is None or df.empty:
            print(f"[WARNING] 表 {table_name} 无数据或不存在")
            return False
            
        print(f"[SUCCESS] 获取到 {len(df)} 行数据")
        
        # 3. 初始化格式管理器
        format_manager = UnifiedFormatManager()
        
        # 4. 应用格式化
        formatted_df = format_manager.format_data(df, table_type="retired_employees")
        
        if formatted_df is None or formatted_df.empty:
            print("[ERROR] 格式化失败")
            return False
            
        print(f"[SUCCESS] 格式化完成，{len(formatted_df)} 行 x {len(formatted_df.columns)} 列")
        
        # 5. 验证用户需求
        test_results = []
        
        # 测试需求1：浮点字段格式化
        float_fields = ["增发一次性生活补贴", "补发", "借支"]
        for field in float_fields:
            if field in formatted_df.columns:
                sample_values = formatted_df[field].head(3).tolist()
                print(f"[CHECK] {field}: {sample_values}")
                # 检查是否为字符串格式的数字或"0.00"
                success = all(isinstance(v, str) and (v == "0.00" or v.replace(".", "").replace("-", "").isdigit()) for v in sample_values)
                test_results.append((f"{field}格式化", success))
            else:
                print(f"[WARNING] 字段 {field} 不存在")
                test_results.append((f"{field}字段存在", False))
        
        # 测试需求2：备注字段
        if "备注" in formatted_df.columns:
            remarks_values = formatted_df["备注"].head(3).tolist()
            print(f"[CHECK] 备注: {remarks_values}")
            # 检查空值是否显示为空字符串而不是"-"
            success = all(v != "-" for v in remarks_values)
            test_results.append(("备注字段空值处理", success))
        else:
            print("[WARNING] 备注字段不存在")
            test_results.append(("备注字段存在", False))
            
        # 测试需求3：月份字段
        if "月份" in formatted_df.columns:
            month_values = formatted_df["月份"].head(3).tolist()
            print(f"[CHECK] 月份: {month_values}")
            # 检查是否提取了后两位
            success = all(isinstance(v, str) and len(v) <= 2 for v in month_values)
            test_results.append(("月份提取后两位", success))
        else:
            print("[WARNING] 月份字段不存在")
            test_results.append(("月份字段存在", False))
        
        # 6. 汇总结果
        print("\\n=== 测试结果汇总 ===")
        success_count = 0
        for test_name, success in test_results:
            status = "[OK]" if success else "[FAIL]"
            print(f"{status} {test_name}")
            if success:
                success_count += 1
        
        total_tests = len(test_results)
        success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
        print(f"\\n成功: {success_count}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("[SUCCESS] 格式化修复效果良好！")
            return True
        else:
            print("[WARNING] 还有部分问题需要处理")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_retired_employees_formatting()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n[INFO] 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"[FATAL] 程序异常退出: {e}")
        sys.exit(1)