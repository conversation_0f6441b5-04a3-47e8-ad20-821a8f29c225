#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的离休人员表结构升级脚本

功能：
1. 检测所有使用通用16字段结构的离休人员表
2. 为这些表添加专用字段，特别是"增发一次性生活补贴"字段
3. 更新字段映射配置
"""

import sqlite3
import json
import sys
from pathlib import Path
from datetime import datetime

# 项目路径
project_root = Path(__file__).parent.parent
db_path = project_root / "data" / "db" / "salary_system.db"
field_mappings_path = project_root / "state" / "data" / "field_mappings.json"

def main():
    print("=== 离休人员表结构升级工具 ===")
    
    # 需要添加的字段
    additional_fields = [
        ("sequence_number", "INTEGER"),
        ("basic_retirement_salary", "REAL DEFAULT 0"),
        ("balance_allowance", "REAL DEFAULT 0"),
        ("living_allowance", "REAL DEFAULT 0"),
        ("housing_allowance", "REAL DEFAULT 0"),
        ("property_allowance", "REAL DEFAULT 0"),
        ("retirement_allowance", "REAL DEFAULT 0"),
        ("nursing_fee", "REAL DEFAULT 0"),
        ("one_time_living_allowance", "REAL DEFAULT 0"),  # 关键字段
        ("supplement", "REAL DEFAULT 0"),
        ("total", "REAL DEFAULT 0"),
        ("advance", "REAL DEFAULT 0"),
        ("remarks", "TEXT")
    ]
    
    # 字段映射
    field_mappings = {
        "sequence_number": "序号",
        "employee_id": "人员代码",
        "employee_name": "姓名",
        "department": "部门名称",
        "basic_retirement_salary": "基本离休费",
        "balance_allowance": "结余津贴",
        "living_allowance": "生活补贴",
        "housing_allowance": "住房补贴",
        "property_allowance": "物业补贴",
        "retirement_allowance": "离休补贴",
        "nursing_fee": "护理费",
        "one_time_living_allowance": "增发一次性生活补贴",
        "supplement": "补发",
        "total": "合计",
        "advance": "借支",
        "remarks": "备注"
    }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查找所有离休人员表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%retired_employees'")
        tables = cursor.fetchall()
        
        print(f"发现 {len(tables)} 个离休人员表")
        
        upgraded_count = 0
        
        for table_tuple in tables:
            table_name = table_tuple[0]
            print(f"\\n检查表: {table_name}")
            
            # 检查表结构
            cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 检查是否已有专用字段
            if "one_time_living_allowance" in column_names:
                print(f"  ✓ 表 {table_name} 已有完整结构，跳过")
                continue
            
            print(f"  需要升级表: {table_name} (当前有 {len(column_names)} 个字段)")
            
            # 预演模式检查
            if "--dry-run" in sys.argv:
                print(f"  [预演] 将添加 {len(additional_fields)} 个专用字段")
                continue
            
            # 执行升级
            try:
                # 备份表
                backup_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                cursor.execute(f"CREATE TABLE \"{backup_name}\" AS SELECT * FROM \"{table_name}\"")
                print(f"  ✓ 已备份到: {backup_name}")
                
                # 添加字段
                added_fields = []
                for field_name, field_type in additional_fields:
                    if field_name not in column_names:
                        try:
                            cursor.execute(f"ALTER TABLE \"{table_name}\" ADD COLUMN \"{field_name}\" {field_type}")
                            added_fields.append(field_name)
                        except sqlite3.OperationalError as e:
                            if "duplicate column name" not in str(e):
                                print(f"    警告: 添加字段 {field_name} 失败: {e}")
                
                print(f"  ✓ 添加了 {len(added_fields)} 个字段")
                
                # 验证升级
                cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
                new_columns = cursor.fetchall()
                new_column_names = [col[1] for col in new_columns]
                
                if "one_time_living_allowance" in new_column_names:
                    print(f"  ✓ 升级验证成功: 包含关键字段")
                    upgraded_count += 1
                else:
                    print(f"  ✗ 升级验证失败: 缺少关键字段")
                
            except Exception as e:
                print(f"  ✗ 升级失败: {e}")
        
        conn.commit()
        conn.close()
        
        if "--dry-run" not in sys.argv:
            print(f"\\n=== 升级完成 ===")
            print(f"成功升级 {upgraded_count} 个表")
            
            # 更新字段映射配置
            print("\\n更新字段映射配置...")
            try:
                if field_mappings_path.exists():
                    with open(field_mappings_path, 'r', encoding='utf-8') as f:
                        mappings = json.load(f)
                else:
                    mappings = {"version": "2.0", "table_mappings": {}, "field_templates": {}}
                
                # 添加离休人员字段模板
                mappings["field_templates"]["离休人员工资表"] = field_mappings
                mappings["last_updated"] = datetime.now().isoformat()
                
                with open(field_mappings_path, 'w', encoding='utf-8') as f:
                    json.dump(mappings, f, ensure_ascii=False, indent=2)
                
                print("✓ 字段映射配置已更新")
                
            except Exception as e:
                print(f"✗ 更新字段映射配置失败: {e}")
        else:
            print(f"\\n=== 预演完成 ===")
            print("运行 'python3 scripts/simple_upgrade_retired_tables.py' 执行实际升级")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())