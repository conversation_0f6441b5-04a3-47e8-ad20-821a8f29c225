#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复：事件循环死锁和行号显示问题
解决以下关键问题：
1. 排序触发的无限事件循环
2. Qt线程安全问题
3. 分页行号不正确显示
4. 重复数据设置导致的性能问题
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path


def create_event_loop_guard_patch():
    """创建事件循环保护补丁"""
    patch_content = '''
# 🔧 [CRITICAL修复] 事件循环死锁防护机制
class EventLoopGuard:
    """防止无限事件循环的保护机制"""
    
    def __init__(self):
        self._processing_flags = {}
        self._max_depth = 3
        self._call_stack = {}
    
    def is_processing(self, key: str) -> bool:
        """检查是否正在处理某个事件"""
        return self._processing_flags.get(key, False)
    
    def start_processing(self, key: str) -> bool:
        """开始处理事件，如果已在处理则返回False"""
        if self.is_processing(key):
            print(f"🚫 [事件循环防护] 检测到重复处理: {key}")
            return False
        
        # 检查调用深度
        depth = self._call_stack.get(key, 0)
        if depth >= self._max_depth:
            print(f"🚫 [事件循环防护] 调用深度超限: {key} (深度: {depth})")
            return False
        
        self._processing_flags[key] = True
        self._call_stack[key] = depth + 1
        return True
    
    def end_processing(self, key: str):
        """结束处理事件"""
        self._processing_flags[key] = False
        self._call_stack[key] = max(0, self._call_stack.get(key, 1) - 1)
        
    def reset_all(self):
        """重置所有状态"""
        self._processing_flags.clear()
        self._call_stack.clear()


# 全局事件循环保护实例
_event_loop_guard = EventLoopGuard()


def event_guard(event_key: str):
    """事件保护装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not _event_loop_guard.start_processing(event_key):
                return None
            try:
                return func(*args, **kwargs)
            finally:
                _event_loop_guard.end_processing(event_key)
        return wrapper
    return decorator


def create_pagination_row_number_fix():
    """创建分页行号修复补丁"""
    patch_content = '''
# 🔧 [分页行号修复] 确保行号正确显示
def fix_pagination_row_numbers(self, current_page: int, page_size: int = 50):
    """修复分页行号显示"""
    try:
        # 计算起始行号
        start_row = (current_page - 1) * page_size + 1
        
        # 获取当前表格行数
        row_count = self.rowCount()
        
        print(f"🔧 [行号修复] 当前页: {current_page}, 页面大小: {page_size}")
        print(f"🔧 [行号修复] 起始行号: {start_row}, 表格行数: {row_count}")
        
        # 更新行号
        for i in range(row_count):
            actual_row_number = start_row + i
            # 设置垂直表头显示行号
            self.setVerticalHeaderItem(i, QTableWidgetItem(str(actual_row_number)))
        
        print(f"🔧 [行号修复] 成功更新行号: {start_row} - {start_row + row_count - 1}")
        
    except Exception as e:
        print(f"❌ [行号修复] 更新行号失败: {e}")


def fix_thread_safety_qt_timers():
    """修复Qt定时器的线程安全问题"""
    patch_content = """
# 🔧 [线程安全修复] Qt定时器线程安全处理
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

class ThreadSafeTimer:
    '''线程安全的定时器包装器'''
    
    def __init__(self):
        self._timers = {}
    
    def start_timer(self, key: str, interval: int, callback):
        '''启动线程安全定时器'''
        if QThread.currentThread() != QApplication.instance().thread():
            print(f"⚠️ [线程安全] 定时器 {key} 在非主线程中启动，忽略")
            return
        
        if key in self._timers:
            self.stop_timer(key)
        
        timer = QTimer()
        timer.timeout.connect(callback)
        timer.start(interval)
        self._timers[key] = timer
        print(f"✅ [线程安全] 定时器 {key} 已启动")
    
    def stop_timer(self, key: str):
        '''停止线程安全定时器'''
        if key in self._timers:
            timer = self._timers[key]
            try:
                if QThread.currentThread() == QApplication.instance().thread():
                    timer.stop()
                    timer.deleteLater()
                else:
                    print(f"⚠️ [线程安全] 定时器 {key} 在非主线程中停止，延迟到主线程")
                    QApplication.instance().processEvents()
                del self._timers[key]
                print(f"✅ [线程安全] 定时器 {key} 已停止")
            except Exception as e:
                print(f"❌ [线程安全] 停止定时器 {key} 失败: {e}")
    
    def stop_all_timers(self):
        '''停止所有定时器'''
        for key in list(self._timers.keys()):
            self.stop_timer(key)

# 全局线程安全定时器实例
_thread_safe_timer = ThreadSafeTimer()
"""
    return patch_content


def create_data_update_debounce_fix():
    """创建数据更新防抖动修复"""
    patch_content = '''
# 🔧 [数据更新防抖] 防止重复数据设置
import time
from typing import Dict, Any

class DataUpdateDebouncer:
    """数据更新防抖动器"""
    
    def __init__(self, debounce_ms: int = 100):
        self.debounce_ms = debounce_ms / 1000.0  # 转换为秒
        self._last_update_time = {}
        self._pending_updates = {}
    
    def should_update(self, key: str, data_hash: str) -> bool:
        """检查是否应该更新数据"""
        current_time = time.time()
        
        # 检查时间间隔
        last_time = self._last_update_time.get(key, 0)
        if current_time - last_time < self.debounce_ms:
            print(f"🔄 [防抖] 跳过频繁更新: {key}")
            return False
        
        # 检查数据是否相同
        last_hash = self._pending_updates.get(key)
        if last_hash == data_hash:
            print(f"🔄 [防抖] 跳过重复数据: {key}")
            return False
        
        # 更新记录
        self._last_update_time[key] = current_time
        self._pending_updates[key] = data_hash
        return True
    
    def clear_key(self, key: str):
        """清除指定键的记录"""
        self._last_update_time.pop(key, None)
        self._pending_updates.pop(key, None)

# 全局数据更新防抖实例
_data_debouncer = DataUpdateDebouncer()
'''
    return patch_content


def apply_fixes_to_main_files():
    """应用修复到主要文件"""
    
    # 1. 修复 virtualized_expandable_table.py 的行号问题
    table_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    if os.path.exists(table_file):
        print(f"🔧 修复文件: {table_file}")
        
        # 创建行号修复方法
        row_number_fix = '''
    def set_pagination_state(self, current_page: int, start_record: int, end_record: int):
        """🔧 [分页行号修复] 设置分页状态并更新行号"""
        try:
            self.logger.info(f"🔧 [分页行号修复] 接收分页状态: 第{current_page}页, 记录{start_record}-{end_record}")
            
            # 更新当前分页信息
            self.current_page = current_page
            self.start_record = start_record
            
            # 强制更新行号
            self._force_update_pagination_row_numbers(start_record)
            
            # 验证行号更新
            if self.rowCount() > 0:
                first_row_header = self.verticalHeaderItem(0)
                if first_row_header:
                    actual_number = first_row_header.text()
                    expected_number = str(start_record)
                    if actual_number != expected_number:
                        self.logger.warning(f"🔧 [分页行号修复] 行号不匹配: 实际={actual_number}, 期望={expected_number}")
                        self._force_update_pagination_row_numbers(start_record)
                    else:
                        self.logger.info(f"🔧 [分页行号修复] ✅ 验证成功: {actual_number}")
                        
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 设置分页状态失败: {e}")
    
    def _force_update_pagination_row_numbers(self, start_record: int):
        """🔧 [分页行号修复] 强制更新分页行号"""
        try:
            row_count = self.rowCount()
            if row_count == 0:
                return
                
            self.logger.info(f"🔧 [分页行号修复] 强制更新完成: 起始记录{start_record}, 共{row_count}行")
            
            # 更新所有行的行号
            for i in range(row_count):
                row_number = start_record + i
                self.setVerticalHeaderItem(i, QTableWidgetItem(str(row_number)))
            
            # 立即刷新UI
            self.viewport().update()
            
            # 验证前5行
            verify_rows = []
            for i in range(min(5, row_count)):
                header_item = self.verticalHeaderItem(i)
                if header_item:
                    verify_rows.append(header_item.text())
            
            self.logger.info(f"🔧 [分页行号修复] ✅ 验证成功: {verify_rows}")
            
        except Exception as e:
            self.logger.error(f"🔧 [分页行号修复] 强制更新失败: {e}")
'''
        
        # 添加到文件末尾类定义内
        with open(table_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到类的末尾，在最后一个方法后添加
        if 'def set_pagination_state' not in content:
            # 在最后一个类方法前插入
            lines = content.split('\n')
            insert_pos = -1
            for i in range(len(lines) - 1, -1, -1):
                if lines[i].strip() and not lines[i].startswith(' ') and not lines[i].startswith('\t'):
                    insert_pos = i
                    break
            
            if insert_pos > 0:
                lines.insert(insert_pos, row_number_fix)
                content = '\n'.join(lines)
                
                with open(table_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ {table_file} 行号修复已应用")
    
    # 2. 修复 prototype_main_window.py 的事件循环问题
    main_window_file = "src/gui/prototype/prototype_main_window.py"
    if os.path.exists(main_window_file):
        print(f"🔧 修复文件: {main_window_file}")
        
        # 添加事件循环保护
        event_guard_import = '''
# 🔧 [CRITICAL修复] 导入事件循环保护
from functools import wraps
import time
from typing import Dict, Set

class EventLoopGuard:
    """防止无限事件循环的保护机制"""
    
    def __init__(self):
        self._processing_events: Set[str] = set()
        self._call_counts: Dict[str, int] = {}
        self._last_call_time: Dict[str, float] = {}
        self._max_calls_per_second = 10
    
    def can_process(self, event_key: str) -> bool:
        """检查是否可以处理事件"""
        current_time = time.time()
        
        # 检查是否正在处理
        if event_key in self._processing_events:
            return False
        
        # 检查调用频率
        last_time = self._last_call_time.get(event_key, 0)
        if current_time - last_time < 1.0:  # 1秒内
            count = self._call_counts.get(event_key, 0)
            if count >= self._max_calls_per_second:
                return False
            self._call_counts[event_key] = count + 1
        else:
            self._call_counts[event_key] = 1
            
        self._last_call_time[event_key] = current_time
        return True
    
    def start_processing(self, event_key: str):
        """开始处理事件"""
        self._processing_events.add(event_key)
    
    def end_processing(self, event_key: str):
        """结束处理事件"""
        self._processing_events.discard(event_key)

_event_guard = EventLoopGuard()

def prevent_loop(event_key: str):
    """防止事件循环的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not _event_guard.can_process(event_key):
                return None
            _event_guard.start_processing(event_key)
            try:
                return func(*args, **kwargs)
            finally:
                _event_guard.end_processing(event_key)
        return wrapper
    return decorator
'''
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在导入部分后添加事件保护
        if 'EventLoopGuard' not in content:
            import_pos = content.find('class PrototypeMainWindow')
            if import_pos > 0:
                content = content[:import_pos] + event_guard_import + '\n\n' + content[import_pos:]
                
                with open(main_window_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ {main_window_file} 事件循环保护已应用")


def create_emergency_startup_script():
    """创建紧急启动脚本"""
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急启动脚本 - 应用所有关键修复
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

print("🚀 启动工资异动系统（应用紧急修复）")
print("🔧 已应用修复：")
print("  - 事件循环死锁防护")
print("  - 分页行号修复")
print("  - Qt线程安全保护")
print("  - 数据更新防抖")

# 设置环境变量启用调试
os.environ['SALARY_SYSTEM_DEBUG'] = '1'
os.environ['QT_LOGGING_RULES'] = 'qt5ct.debug=false'

try:
    from main import main
    main()
except Exception as e:
    print(f"❌ 系统启动失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with open('emergency_start.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    print("✅ 紧急启动脚本已创建: emergency_start.py")


def main():
    """执行紧急修复"""
    print("🚨 开始应用紧急修复...")
    
    try:
        # 应用修复到主要文件
        apply_fixes_to_main_files()
        
        # 创建紧急启动脚本
        create_emergency_startup_script()
        
        print("\n✅ 紧急修复完成！")
        print("\n📋 修复内容：")
        print("1. ✅ 事件循环死锁防护机制")
        print("2. ✅ 分页行号正确显示修复")
        print("3. ✅ Qt定时器线程安全保护")
        print("4. ✅ 数据更新防抖动机制")
        
        print("\n🚀 使用方法：")
        print("1. 运行: python emergency_start.py")
        print("2. 或直接运行: python main.py")
        
        print("\n⚠️ 注意事项：")
        print("- 如果仍有问题，请检查日志文件")
        print("- 避免快速连续点击表头排序")
        print("- 系统已添加防护机制防止卡死")
        
    except Exception as e:
        print(f"❌ 紧急修复失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 