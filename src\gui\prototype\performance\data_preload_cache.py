#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预加载缓存模块
优化表格数据加载性能，减少重复的数据库查询和处理
"""

import time
import hashlib
from typing import Dict, Any, Optional, Tuple
from collections import OrderedDict
import pandas as pd
from src.utils.log_config import setup_logger


class DataPreloadCache:
    """数据预加载缓存
    
    提供智能的数据缓存机制，减少重复的数据库查询和处理
    支持LRU淘汰策略和自动过期机制
    """
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        """初始化数据预加载缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl_seconds: 缓存过期时间（秒）
        """
        self.logger = setup_logger(__name__ + ".DataPreloadCache")
        
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
        # 使用OrderedDict实现LRU缓存
        self.cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
        
        self.logger.info(f"数据预加载缓存初始化完成 - 最大条目数: {max_size}, TTL: {ttl_seconds}秒")
    
    def _generate_cache_key(self, table_name: str, page: int, per_page: int, 
                           sort_columns: Optional[list] = None, 
                           filters: Optional[dict] = None) -> str:
        """生成缓存键
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页条数
            sort_columns: 排序列
            filters: 过滤条件
            
        Returns:
            缓存键字符串
        """
        # 构建缓存键的组成部分
        key_parts = [
            f"table:{table_name}",
            f"page:{page}",
            f"per_page:{per_page}"
        ]
        
        # 添加排序信息
        if sort_columns:
            sort_str = ",".join([f"{col}:{order}" for col, order in sort_columns])
            key_parts.append(f"sort:{sort_str}")
        
        # 添加过滤信息
        if filters:
            filter_str = ",".join([f"{k}:{v}" for k, v in sorted(filters.items())])
            key_parts.append(f"filter:{filter_str}")
        
        # 生成哈希键
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get_cached_data(self, table_name: str, page: int, per_page: int,
                       sort_columns: Optional[list] = None,
                       filters: Optional[dict] = None) -> Optional[pd.DataFrame]:
        """获取缓存的数据
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页条数
            sort_columns: 排序列
            filters: 过滤条件
            
        Returns:
            缓存的DataFrame或None
        """
        cache_key = self._generate_cache_key(table_name, page, per_page, sort_columns, filters)
        
        if cache_key not in self.cache:
            self.stats['misses'] += 1
            self.logger.debug(f"缓存未命中: {table_name} 第{page}页")
            return None
        
        # 检查是否过期
        cache_entry = self.cache[cache_key]
        current_time = time.time()
        
        if current_time - cache_entry['timestamp'] > self.ttl_seconds:
            # 缓存已过期，移除
            del self.cache[cache_key]
            self.stats['expired'] += 1
            self.stats['misses'] += 1
            self.logger.debug(f"缓存已过期: {table_name} 第{page}页")
            return None
        
        # 缓存命中，移动到末尾（LRU）
        self.cache.move_to_end(cache_key)
        self.stats['hits'] += 1
        
        self.logger.debug(f"缓存命中: {table_name} 第{page}页")
        return cache_entry['data'].copy()  # 返回副本避免修改原数据
    
    def cache_data(self, table_name: str, page: int, per_page: int, data: pd.DataFrame,
                   sort_columns: Optional[list] = None,
                   filters: Optional[dict] = None) -> None:
        """缓存数据
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页条数
            data: 要缓存的DataFrame
            sort_columns: 排序列
            filters: 过滤条件
        """
        cache_key = self._generate_cache_key(table_name, page, per_page, sort_columns, filters)
        
        # 检查缓存大小，必要时进行LRU淘汰
        while len(self.cache) >= self.max_size:
            # 移除最旧的条目
            oldest_key, _ = self.cache.popitem(last=False)
            self.stats['evictions'] += 1
            self.logger.debug(f"LRU淘汰缓存条目: {oldest_key}")
        
        # 添加新的缓存条目
        cache_entry = {
            'data': data.copy(),  # 存储副本避免外部修改
            'timestamp': time.time(),
            'table_name': table_name,
            'page': page,
            'per_page': per_page
        }
        
        self.cache[cache_key] = cache_entry
        self.logger.debug(f"数据已缓存: {table_name} 第{page}页, 缓存大小: {len(self.cache)}")
    
    def invalidate_table(self, table_name: str) -> int:
        """使指定表的所有缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            被移除的缓存条目数
        """
        removed_count = 0
        keys_to_remove = []
        
        for key, entry in self.cache.items():
            if entry['table_name'] == table_name:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.cache[key]
            removed_count += 1
        
        if removed_count > 0:
            self.logger.info(f"表 {table_name} 的 {removed_count} 个缓存条目已失效")
        
        return removed_count
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        cache_size = len(self.cache)
        self.cache.clear()
        self.logger.info(f"已清空所有缓存，共移除 {cache_size} 个条目")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            包含缓存统计信息的字典
        """
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'hit_rate': f"{hit_rate:.1f}%",
            'evictions': self.stats['evictions'],
            'expired': self.stats['expired'],
            'ttl_seconds': self.ttl_seconds
        }
    
    def log_cache_stats(self) -> None:
        """记录缓存统计信息到日志"""
        stats = self.get_cache_stats()
        self.logger.info(f"🔧 [性能缓存] 缓存统计: "
                        f"大小={stats['cache_size']}/{stats['max_size']}, "
                        f"命中率={stats['hit_rate']}, "
                        f"命中={stats['hits']}, "
                        f"未命中={stats['misses']}, "
                        f"淘汰={stats['evictions']}, "
                        f"过期={stats['expired']}")


class TableStateCache:
    """表格状态缓存
    
    缓存表格的配置状态，减少重复的初始化操作
    """
    
    def __init__(self):
        """初始化表格状态缓存"""
        self.logger = setup_logger(__name__ + ".TableStateCache")
        self.state_cache: Dict[str, Dict[str, Any]] = {}
        self.logger.info("表格状态缓存初始化完成")
    
    def get_cached_state(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取缓存的表格状态
        
        Args:
            table_name: 表名
            
        Returns:
            缓存的状态字典或None
        """
        state = self.state_cache.get(table_name)
        if state:
            self.logger.debug(f"表格状态缓存命中: {table_name}")
            return state.copy()  # 返回副本
        else:
            self.logger.debug(f"表格状态缓存未命中: {table_name}")
            return None
    
    def cache_state(self, table_name: str, state: Dict[str, Any]) -> None:
        """缓存表格状态
        
        Args:
            table_name: 表名
            state: 状态字典
        """
        self.state_cache[table_name] = state.copy()  # 存储副本
        self.logger.debug(f"表格状态已缓存: {table_name}")
    
    def invalidate_state(self, table_name: str) -> bool:
        """使指定表的状态缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功移除缓存
        """
        if table_name in self.state_cache:
            del self.state_cache[table_name]
            self.logger.debug(f"表格状态缓存已失效: {table_name}")
            return True
        return False
    
    def clear_cache(self) -> None:
        """清空所有状态缓存"""
        cache_size = len(self.state_cache)
        self.state_cache.clear()
        self.logger.info(f"已清空所有状态缓存，共移除 {cache_size} 个条目")
