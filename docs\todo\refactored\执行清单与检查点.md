# 架构重构执行清单与检查点

## 文档信息

- **创建时间**: 2025-07-14
- **文档版本**: v1.0
- **文档类型**: 执行检查清单
- **预计执行时间**: 2周

## 🎯 总体目标

将双架构并存的系统清理为单一的新架构系统，消除技术债务，提升开发效率和系统稳定性。

## 📋 第一阶段：紧急止血 (Day 1-2)

### ✅ Day 1 检查清单

#### 🔍 系统诊断
- [ ] 运行 `python main.py --check` 检查系统状态
- [ ] 检查当前运行日志：`tail -100 logs/salary_system.log`
- [ ] 统计代码行数：`find src/ -name "*.py" | xargs wc -l`
- [ ] 识别架构标志：`grep -r "_new_architecture_enabled" src/`
- [ ] 检查降级日志：`grep -r "降级到旧架构" logs/`

#### 📦 创建备份
- [ ] 创建完整项目备份：
  ```bash
  tar -czf architecture_cleanup_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
      src/ docs/ *.py config.json requirements.txt
  ```
- [ ] 验证备份文件完整性：`tar -tzf architecture_cleanup_backup_*.tar.gz | head -20`
- [ ] 记录备份文件路径和时间

#### 📊 依赖关系分析
- [ ] 列出老架构相关文件：
  ```bash
  find src/ -name "*main_window*" -o -name "*windows.py" | grep -v prototype
  ```
- [ ] 检查导入关系：`grep -r "from.*main_window import" src/`
- [ ] 记录关键依赖组件

### ✅ Day 2 检查清单

#### 🗑️ 删除老架构文件
- [ ] 删除主窗口老架构：
  ```bash
  rm src/gui/main_window.py
  rm src/gui/main_window_refactored.py
  rm src/gui/windows.py
  rm src/gui/windows_refactored.py
  ```
- [ ] 删除备份文件：
  ```bash
  find src/ -name "*.backup" -delete
  find src/ -name "*.bak" -delete
  find . -name "*.py.backup" -delete
  ```
- [ ] 删除临时文件：`rm -rf temp/`

#### ✅ 验证删除结果
- [ ] 确认文件已删除：`ls -la src/gui/main_window*`
- [ ] 检查是否有遗漏：`find . -name "*main_window*" | grep -v prototype`
- [ ] 测试系统启动：`python main.py` (预期会有错误，记录错误信息)

## 📋 第二阶段：接口修复 (Day 3-7)

### ✅ Day 3 检查清单

#### 🔧 修复main.py入口
- [ ] 确保只导入新架构：
  ```python
  # 检查 main.py 中的导入语句
  grep "import.*main_window" main.py
  # 应该只有 PrototypeMainWindow 相关导入
  ```
- [ ] 移除架构选择逻辑：
  ```python
  # 删除类似代码
  # if new_architecture_available:
  #     return PrototypeMainWindow()
  # else:
  #     return MainWindow()
  ```
- [ ] 测试启动：`python main.py`

#### 🔗 修复import语句
- [ ] 查找所有老架构import：
  ```bash
  grep -r "from.*main_window import" src/
  grep -r "import.*main_window" src/
  ```
- [ ] 替换为新架构import：
  ```bash
  # 示例替换命令
  sed -i 's/from src.gui.main_window import MainWindow/from src.gui.prototype.prototype_main_window import PrototypeMainWindow as MainWindow/g' src/**/*.py
  ```

### ✅ Day 4-5 检查清单

#### 🎭 添加兼容性接口
- [ ] 在 `prototype_main_window.py` 中添加缺失属性：
  ```python
  def __init__(self, ...):
      # 添加兼容性属性
      self.field_mapping_cache = {}
  ```
- [ ] 添加缺失方法：
  ```python
  def _get_table_field_mapping(self, table_name):
      """兼容性方法"""
      return {}  # 临时实现
  ```
- [ ] 添加属性访问器：
  ```python
  @property
  def main_workspace_area(self):
      return self  # 临时自引用
  ```

#### 🧪 增量测试
- [ ] 测试系统启动：`python main.py`
- [ ] 测试基本功能：
  - [ ] 界面显示正常
  - [ ] 菜单可以点击
  - [ ] 没有AttributeError
- [ ] 记录剩余错误信息

### ✅ Day 6-7 检查清单

#### 🔍 运行时错误修复
- [ ] 运行系统并记录所有AttributeError
- [ ] 逐个修复缺失的方法和属性
- [ ] 测试修复后的功能是否正常

#### 📝 代码清理
- [ ] 移除无用的import语句
- [ ] 清理注释掉的代码
- [ ] 统一代码风格

## 📋 第三阶段：清理战场 (Day 8-10)

### ✅ Day 8 检查清单

#### 🚫 删除降级代码
- [ ] 搜索所有架构标志：
  ```bash
  grep -r "_new_architecture_enabled" src/ > architecture_flags.txt
  ```
- [ ] 删除条件判断代码：
  ```python
  # 删除类似代码
  if self._new_architecture_enabled and self.event_bus:
      # 新架构处理
  else:
      # 旧架构处理 <- 删除这部分
  ```
- [ ] 简化逻辑为只使用新架构

#### 🗂️ 配置文件清理
- [ ] 识别重复的配置文件：
  ```bash
  find . -name "config.json" -o -name "user_preferences.json"
  ```
- [ ] 只保留根目录的主配置文件
- [ ] 清理状态文件：`find . -name "*state*.json" -path "./backup/*" -delete`

### ✅ Day 9-10 检查清单

#### 📚 文档更新
- [ ] 删除过时的架构文档：
  ```bash
  rm docs/todo/工资系统新旧架构分析与迁移指南.md
  ```
- [ ] 更新README.md中的架构说明
- [ ] 更新CLAUDE.md中的构建命令

#### 🧹 最终清理
- [ ] 删除backup目录：`rm -rf backup/`
- [ ] 清理过时的测试文件
- [ ] 统一项目目录结构

## 📋 第四阶段：测试验证 (Day 11-14)

### ✅ Day 11-12 检查清单

#### 🔬 功能测试
- [ ] 系统启动测试：`python main.py`
- [ ] 数据导入测试：
  - [ ] Excel文件导入
  - [ ] 字段映射功能
  - [ ] 数据验证功能
- [ ] 界面交互测试：
  - [ ] 菜单功能
  - [ ] 表格操作
  - [ ] 分页功能
  - [ ] 排序功能

#### 🏃 性能测试
- [ ] 启动时间测试：`time python main.py --version`
- [ ] 大数据量测试：导入1000+行数据
- [ ] 内存使用监控：`ps aux | grep python`

### ✅ Day 13-14 检查清单

#### 🧪 自动化测试
- [ ] 运行单元测试：`python -m pytest test/ -v`
- [ ] 运行集成测试：`python main.py --test`
- [ ] 检查测试覆盖率：`pytest --cov=src test/`

#### 📊 质量评估
- [ ] 代码行数统计：`find src/ -name "*.py" | xargs wc -l`
- [ ] 复杂度检查：`flake8 src/`
- [ ] 导入依赖检查：`python -c "import src.gui.prototype.prototype_main_window"`

## 🎯 成功标准检查

### ✅ 技术指标
- [ ] 系统正常启动，无错误信息
- [ ] 核心功能（导入、导出、查询）正常工作
- [ ] 无AttributeError或ImportError
- [ ] 测试套件通过率 > 90%
- [ ] 代码行数减少 > 30%

### ✅ 代码质量指标
- [ ] 单一架构标准
- [ ] 清洁的import结构
- [ ] 统一的错误处理
- [ ] 完整的功能覆盖

### ✅ 用户体验指标
- [ ] 界面响应正常
- [ ] 功能操作流畅
- [ ] 错误提示清晰
- [ ] 性能无明显下降

## 🚨 风险检查点

### ⚠️ 每日风险检查
- [ ] 是否有新的运行时错误出现？
- [ ] 是否有功能完全不可用？
- [ ] 是否有数据丢失风险？
- [ ] 是否需要回滚到备份？

### 🔄 回滚准备
- [ ] 备份文件是否完整？
- [ ] 回滚步骤是否清晰？
- [ ] 回滚时间是否可接受？

## 📈 进度跟踪

### 阶段完成度
- [ ] 第一阶段 (紧急止血): ___% 
- [ ] 第二阶段 (接口修复): ___%
- [ ] 第三阶段 (清理战场): ___%
- [ ] 第四阶段 (测试验证): ___%

### 关键里程碑
- [ ] Day 2: 老架构文件删除完成
- [ ] Day 5: 系统能够正常启动
- [ ] Day 8: 降级代码删除完成
- [ ] Day 12: 核心功能测试通过
- [ ] Day 14: 项目重构完成

## 🎉 完成确认

### 最终检查清单
- [ ] 所有老架构文件已删除
- [ ] 所有降级代码已移除
- [ ] 系统功能正常运行
- [ ] 测试套件全部通过
- [ ] 文档已更新完整
- [ ] 备份已妥善保存

### 交付物确认
- [ ] 清理后的代码库
- [ ] 更新后的技术文档
- [ ] 完整的测试报告
- [ ] 架构重构总结报告

---

**注意事项:**
1. 每个检查点都必须确认完成后才能进入下一步
2. 如遇到无法解决的问题，立即记录并考虑回滚
3. 保持与团队的及时沟通
4. 定期更新进度状态

*本检查清单基于2025-07-14的架构分析，请严格按照顺序执行。*