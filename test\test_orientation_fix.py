#!/usr/bin/env python3
"""
测试Qt::Orientation类型注册修复
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qt_type_registration():
    """测试Qt类型注册"""
    print("🔧 开始测试Qt类型注册...")
    
    try:
        # 测试导入PyQt5
        from PyQt5.QtCore import Qt, qRegisterMetaType
        print("✅ PyQt5导入成功")
        
        # 测试类型注册
        try:
            qRegisterMetaType(Qt.Orientation)
            qRegisterMetaType(Qt.SortOrder)
            print("✅ Qt类型注册成功")
        except Exception as e:
            print(f"❌ Qt类型注册失败: {e}")
            return False
        
        # 测试Qt枚举类型
        orientation_value = Qt.Orientation.Horizontal
        sort_order_value = Qt.SortOrder.AscendingOrder
        print(f"✅ Qt枚举类型访问成功: {orientation_value}, {sort_order_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_signal_connection():
    """测试信号连接"""
    print("🔧 开始测试信号连接...")
    
    try:
        # 仅测试信号连接，不创建GUI
        from PyQt5.QtCore import Qt, QObject, pyqtSignal
        
        class TestObject(QObject):
            test_signal = pyqtSignal(int, Qt.SortOrder)
            
            def test_slot(self, index, order):
                print(f"✅ 信号槽测试成功: index={index}, order={order}")
        
        test_obj = TestObject()
        test_obj.test_signal.connect(test_obj.test_slot, Qt.DirectConnection)
        test_obj.test_signal.emit(0, Qt.SortOrder.AscendingOrder)
        
        print("✅ 信号连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 信号连接测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🔧 开始测试Qt::Orientation修复...")
    print()
    
    tests = [
        test_qt_type_registration,
        test_signal_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print(f"🎉 测试完成：{passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ Qt::Orientation修复测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)