#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化模块

提供表格数据加载性能优化功能，包括：
- 数据预加载缓存
- 表格状态缓存  
- 表头配置缓存
- 字段映射缓存
- 统一性能管理

主要组件：
- DataPreloadCache: 数据预加载缓存
- TableStateCache: 表格状态缓存
- HeaderConfigCache: 表头配置缓存
- FieldMappingCache: 字段映射缓存
- PerformanceManager: 统一性能管理器
"""

from .data_preload_cache import DataPreloadCache, TableStateCache
from .header_config_cache import HeaderConfigCache, FieldMappingCache
from .performance_manager import PerformanceManager, get_performance_manager, reset_performance_manager

__all__ = [
    'DataPreloadCache',
    'TableStateCache', 
    'HeaderConfigCache',
    'FieldMappingCache',
    'PerformanceManager',
    'get_performance_manager',
    'reset_performance_manager'
]
