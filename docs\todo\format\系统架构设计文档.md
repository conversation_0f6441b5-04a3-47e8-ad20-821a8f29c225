# 统一格式管理系统架构设计文档

## 🏗️ 系统架构概览

### 整体架构

```
统一格式管理系统 (UnifiedFormatManager)
├── 格式配置层 (FormatConfig)
│   ├── 表格类型定义
│   ├── 格式规则配置
│   └── 验证规则管理
├── 字段注册层 (FieldRegistry)
│   ├── 字段映射管理
│   ├── 字段类型定义
│   └── 字段验证规则
└── 数据渲染层 (FormatRenderer)
    ├── 货币格式渲染
    ├── 日期格式渲染
    ├── 数字格式渲染
    └── 字符串格式渲染
```

### 设计原则

1. **单一职责**: 每个组件专注于特定的格式化任务
2. **依赖倒置**: 通过接口和配置驱动，而非硬编码
3. **开闭原则**: 对扩展开放，对修改关闭
4. **配置驱动**: 通过配置文件控制系统行为

## 🔧 组件详细设计

### 1. 统一格式管理器 (UnifiedFormatManager)

**职责**: 系统的中央控制器，协调各子系统工作

**核心方法**:
```python
class UnifiedFormatManager:
    def __init__(self, config_path=None, field_mapping_path=None)
    def format_table_complete(self, data, table_type, data_source)
    def format_headers(self, raw_headers, table_type, data_source)
    def format_data(self, data, table_type, headers, data_source)
    def update_field_mapping(self, table_type, field_mappings)
    def update_format_config(self, config_key, config_value)
```

**依赖关系**:
- 依赖 `FormatConfig` 获取格式配置
- 依赖 `FieldRegistry` 获取字段映射
- 依赖 `FormatRenderer` 执行数据渲染

### 2. 格式配置管理 (FormatConfig)

**职责**: 管理系统的格式配置，提供配置的读取、保存和验证

**配置结构**:
```json
{
  "metadata": {
    "version": "1.0.0",
    "description": "格式配置文件"
  },
  "table_types": {
    "active_employees": {
      "display_name": "全部在职人员工资表",
      "priority": 1,
      "enabled": true
    }
  },
  "default_formats": {
    "currency": {
      "decimal_places": 2,
      "thousand_separator": ",",
      "symbol": "¥"
    }
  }
}
```

**核心方法**:
```python
class FormatConfig:
    def load_config(self) -> bool
    def save_config(self) -> bool
    def get_config(self, key_path: str, default=None) -> Any
    def set_config(self, key_path: str, value: Any)
    def get_table_types(self) -> Dict[str, Dict]
    def get_format_rules(self, format_type: str) -> Dict
```

### 3. 字段注册系统 (FieldRegistry)

**职责**: 管理字段映射关系和字段类型定义

**映射结构**:
```json
{
  "table_mappings": {
    "active_employees": {
      "field_mappings": {
        "employee_id": "工号",
        "employee_name": "姓名",
        "position_salary_2025": "2025年岗位工资"
      },
      "field_types": {
        "employee_id": "string",
        "position_salary_2025": "currency"
      }
    }
  }
}
```

**核心方法**:
```python
class FieldRegistry:
    def get_display_name(self, field_name: str, table_type: str) -> str
    def get_field_name(self, display_name: str, table_type: str) -> str
    def get_field_type(self, field_name: str, table_type: str) -> str
    def get_table_mapping(self, table_type: str) -> Dict[str, str]
    def update_table_mapping(self, table_type: str, field_mappings: Dict)
```

### 4. 数据渲染器 (FormatRenderer)

**职责**: 执行具体的数据格式化和渲染

**支持的格式类型**:
- `currency`: 货币格式
- `integer`: 整数格式
- `float`: 浮点数格式
- `percentage`: 百分比格式
- `date`: 日期格式
- `string`: 字符串格式

**核心方法**:
```python
class FormatRenderer:
    def render_dataframe(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame
    def render_column(self, column: pd.Series, field_type: str, field_name: str, table_type: str) -> pd.Series
    def render_value(self, value: Any, field_type: str, field_name: str) -> str
```

## 🔄 数据流设计

### 数据处理流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant UFS as UnifiedFormatManager
    participant FC as FormatConfig
    participant FR as FieldRegistry
    participant FRR as FormatRenderer
    participant Cache as 缓存系统

    App->>UFS: format_table_complete(data, table_type)
    UFS->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>UFS: 返回缓存数据
        UFS-->>App: 返回格式化结果
    else 缓存未命中
        UFS->>FC: 获取格式配置
        FC-->>UFS: 返回格式规则
        UFS->>FR: 获取字段映射
        FR-->>UFS: 返回字段映射
        UFS->>FRR: 执行数据渲染
        FRR-->>UFS: 返回渲染结果
        UFS->>Cache: 缓存结果
        UFS-->>App: 返回格式化结果
    end
```

### 配置更新流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant UFS as UnifiedFormatManager
    participant FC as FormatConfig
    participant FR as FieldRegistry
    participant Cache as 缓存系统
    participant File as 配置文件

    Admin->>UFS: update_format_config(key, value)
    UFS->>FC: set_config(key, value)
    FC->>File: 保存配置文件
    File-->>FC: 保存完成
    FC-->>UFS: 更新完成
    UFS->>Cache: 清除相关缓存
    Cache-->>UFS: 缓存清除完成
    UFS-->>Admin: 更新成功
```

## 🗂️ 文件组织结构

```
src/modules/format_management/
├── __init__.py                 # 模块初始化，导出主要类
├── unified_format_manager.py   # 统一格式管理器
├── format_config.py           # 格式配置管理
├── field_registry.py          # 字段注册系统
└── format_renderer.py         # 数据渲染器

state/data/
├── format_config.json         # 格式配置文件
├── field_mappings.json        # 字段映射文件
└── backups/                   # 配置备份目录
    ├── format_config_20250718_100000.json
    └── field_mappings_20250718_100000.json
```

## 🔌 集成接口设计

### 1. 服务层集成接口

**位置**: `src/services/table_data_service.py`

```python
class TableDataService:
    def __init__(self, db_manager, config_manager):
        self.format_manager = UnifiedFormatManager()
    
    def load_table_data(self, table_name, **kwargs):
        # 加载原始数据
        response = self.data_request_manager.request_table_data(request)
        
        # 格式化数据
        if response.success and response.data is not None:
            table_type = self._extract_table_type_from_name(table_name)
            formatted_headers, formatted_data = self.format_manager.format_table_complete(
                response.data, table_type, f"table_data_service:{table_name}"
            )
            response.data = formatted_data
        
        return response
```

### 2. 表格组件集成接口

**位置**: `src/gui/prototype/widgets/virtualized_expandable_table.py`

```python
class VirtualizedExpandableTable(QTableWidget):
    def __init__(self, parent=None, config_sync_manager=None):
        self.format_manager = UnifiedFormatManager()
    
    def set_data(self, data, headers, current_table_name=None):
        # 统一格式化处理
        if hasattr(self, 'format_manager') and self.format_manager:
            table_type = self._extract_table_type_from_name(current_table_name)
            formatted_headers, formatted_data = self.format_manager.format_table_complete(
                pd.DataFrame(data), table_type, f"virtualized_table:{current_table_name}"
            )
            data = formatted_data.to_dict('records')
            displayed_headers = formatted_headers
```

## 🔧 扩展点设计

### 1. 新格式类型扩展

**步骤**:
1. 在 `FormatRenderer` 中添加新的渲染方法
2. 在 `format_config.json` 中添加新的格式配置
3. 在 `field_mappings.json` 中定义字段类型

**示例**:
```python
# FormatRenderer 扩展
def _render_phone_column(self, column: pd.Series, format_config: Dict, field_name: str) -> pd.Series:
    def format_phone(value):
        # 电话号码格式化逻辑
        return formatted_phone
    return column.apply(format_phone)
```

### 2. 新表格类型扩展

**步骤**:
1. 在 `format_config.json` 的 `table_types` 中添加新类型
2. 在 `field_mappings.json` 的 `table_mappings` 中添加字段映射
3. 在表格类型识别方法中添加识别逻辑

**示例**:
```json
{
  "table_types": {
    "new_employee_type": {
      "display_name": "新员工类型工资表",
      "priority": 5,
      "enabled": true
    }
  }
}
```

### 3. 条件格式化扩展

**设计**:
```json
{
  "conditional_formatting": {
    "salary_ranges": {
      "low": {"max": 5000, "color": "#ffcdd2"},
      "medium": {"min": 5000, "max": 15000, "color": "#fff3e0"},
      "high": {"min": 15000, "color": "#e8f5e8"}
    }
  }
}
```

## 🚀 性能优化设计

### 1. 缓存策略

**多级缓存**:
```python
class UnifiedFormatManager:
    def __init__(self):
        self._format_cache = {}          # 格式化结果缓存
        self._cache_timestamps = {}      # 缓存时间戳
        self._config_cache = {}          # 配置缓存
```

**缓存失效**:
- 配置文件更新时清除相关缓存
- 基于时间的自动失效 (TTL)
- 基于版本的强制失效

### 2. 批量处理优化

**DataFrame 批量处理**:
```python
def render_dataframe(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame:
    # 批量处理整个DataFrame，而非逐行处理
    for column in df.columns:
        df[column] = self.render_column(df[column], field_type, column, table_type)
    return df
```

### 3. 懒加载设计

**配置懒加载**:
```python
def get_config(self, key_path: str, default=None):
    if not self._loaded:
        self.load_config()  # 懒加载配置
    return self._get_config_value(key_path, default)
```

## 🛡️ 错误处理设计

### 1. 异常处理策略

**分级处理**:
- **格式化异常**: 记录错误，返回原始数据
- **配置异常**: 使用默认配置，记录警告
- **系统异常**: 记录错误，抛出异常

**示例**:
```python
def format_table_complete(self, data, table_type, data_source):
    try:
        # 格式化处理
        return formatted_headers, formatted_data
    except Exception as e:
        self.logger.error(f"格式化失败: {e}")
        # 返回原始数据作为后备方案
        return list(data.columns), data
```

### 2. 降级方案

**多层降级**:
1. 格式化失败 → 使用原始数据
2. 配置缺失 → 使用默认配置
3. 组件初始化失败 → 使用Mock对象

## 📊 监控和日志设计

### 1. 日志分级

**日志级别**:
- `INFO`: 重要操作记录
- `DEBUG`: 详细调试信息
- `WARNING`: 非致命错误
- `ERROR`: 严重错误

**日志格式**:
```
2025-07-18 10:00:00 - format_management.unified_format_manager - INFO - 🎯 [统一格式管理] 数据格式化完成: table_name, 类型: table_type
```

### 2. 性能监控

**关键指标**:
- 格式化处理时间
- 缓存命中率
- 配置加载时间
- 错误发生率

**监控代码**:
```python
def format_table_complete(self, data, table_type, data_source):
    start_time = time.time()
    try:
        # 格式化处理
        result = self._do_format(data, table_type, data_source)
        processing_time = (time.time() - start_time) * 1000
        self.logger.info(f"格式化完成，耗时: {processing_time:.2f}ms")
        return result
    except Exception as e:
        self.logger.error(f"格式化失败，耗时: {(time.time() - start_time) * 1000:.2f}ms")
        raise
```

## 🧪 测试设计

### 1. 单元测试

**测试覆盖**:
- 每个组件的核心方法
- 边界条件和异常情况
- 配置文件加载和保存
- 数据格式化的正确性

### 2. 集成测试

**测试场景**:
- 完整的数据格式化流程
- 配置更新和缓存失效
- 多表格类型的格式化
- 错误恢复和降级

### 3. 性能测试

**性能指标**:
- 大数据量格式化性能
- 缓存效果验证
- 内存使用情况
- 并发处理能力

## 🔮 未来扩展规划

### 1. 高级功能

**计划功能**:
- 条件格式化支持
- 多语言本地化
- 自定义格式规则
- 实时配置更新

### 2. 性能优化

**优化方向**:
- 异步处理支持
- 分布式缓存
- 数据流优化
- 内存管理优化

### 3. 集成扩展

**扩展方向**:
- 导出格式统一
- 打印格式统一
- API接口格式统一
- 数据同步格式统一

---

这个架构设计确保了系统的**可扩展性**、**可维护性**和**高性能**，为统一格式管理提供了坚实的技术基础。