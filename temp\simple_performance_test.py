#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的分页性能测试脚本
"""

import sys
import os
import time

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def test_formatting_performance():
    """测试格式化性能"""
    print("[性能测试] 开始分页格式化性能测试...")
    
    try:
        # 导入模块
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        
        # 初始化格式化管理器
        start_time = time.time()
        unified_formatter = get_unified_format_manager()
        init_time = (time.time() - start_time) * 1000
        print(f"格式化管理器初始化: {init_time:.2f}ms")
        
        # 获取字段注册系统
        field_registry = unified_formatter.field_registry
        field_registry.clear_cache()
        
        # 测试字段类型查找性能
        print("\n测试字段类型查找...")
        
        # 首次查找
        start_time = time.time()
        for _ in range(5):
            field_types = field_registry.get_table_field_types("active_employees")
        first_time = (time.time() - start_time) * 1000
        
        # 缓存查找
        start_time = time.time()
        for _ in range(100):
            field_types = field_registry.get_table_field_types("active_employees")
        cached_time = (time.time() - start_time) * 1000
        
        cache_stats = field_registry.get_cache_stats()
        
        print(f"首次查找(5次): {first_time:.2f}ms")
        print(f"缓存查找(100次): {cached_time:.2f}ms") 
        print(f"缓存命中率: {cache_stats['hit_rate_percent']}%")
        
        # 测试单元格格式化
        print("\n测试单元格格式化...")
        
        test_cells = [
            (1234, "2025年基础性绩效", "active_employees"),
            (5678, "2025年奖励性绩效预发", "active_employees"),
            ("-", "车补", "active_employees"),
            (999, "代扣代存养老保险", "active_employees")
        ]
        
        # 批量格式化测试
        start_time = time.time()
        for _ in range(50):
            for value, column_name, table_type in test_cells:
                result = unified_formatter.format_display_value(value, column_name, table_type)
        format_time = (time.time() - start_time) * 1000
        
        cell_count = 50 * len(test_cells)
        avg_cell_time = format_time / cell_count
        
        print(f"批量格式化({cell_count}个单元格): {format_time:.2f}ms")
        print(f"平均每个单元格: {avg_cell_time:.3f}ms")
        
        # 模拟完整页面(50行24列)
        page_cells = 50 * 24
        estimated_page_time = avg_cell_time * page_cells
        
        print(f"\n页面格式化预估:")
        print(f"页面单元格数: {page_cells}")
        print(f"预估格式化时间: {estimated_page_time:.2f}ms")
        
        # 加上其他开销
        total_time = estimated_page_time + 50  # UI渲染等开销
        print(f"预估总响应时间: {total_time:.2f}ms")
        
        # 性能评级
        if total_time < 100:
            rating = "优秀"
        elif total_time < 300:
            rating = "良好"
        elif total_time < 800:
            rating = "一般"
        else:
            rating = "需要改进"
            
        print(f"性能评级: {rating}")
        
        # 优化效果估算
        if total_time < 200:
            improvement = 1200 / total_time  # 假设优化前1.2秒
            print(f"性能提升倍数: {improvement:.1f}x")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("分页性能优化验证测试")
    print("=" * 40)
    
    success = test_formatting_performance()
    
    if success:
        print("\n测试完成!")
    else:
        print("\n测试失败!")

if __name__ == "__main__":
    main()