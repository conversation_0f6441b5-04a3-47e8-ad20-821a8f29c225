#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序循环功能测试脚本
测试新架构中的完整排序循环：无排序 → 升序 → 降序 → 无排序
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SortTestWindow(QMainWindow):
    """排序测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("排序循环测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建表格
        self.table = VirtualizedExpandableTable()
        layout.addWidget(self.table)
        
        # 设置测试数据
        self.setup_test_data()
        
        logger.info("排序测试窗口初始化完成")
    
    def setup_test_data(self):
        """设置测试数据"""
        try:
            # 创建测试数据
            import pandas as pd
            
            test_data = pd.DataFrame({
                '序号': range(1, 11),
                '姓名': [f'员工{i}' for i in range(1, 11)],
                '2025年薪级工资': [3000, 2500, 3500, 2800, 3200, 2700, 3100, 2900, 3300, 2600],
                '部门': ['技术部', '人事部', '技术部', '财务部', '技术部', '人事部', '财务部', '技术部', '人事部', '财务部']
            })
            
            # 设置表格数据
            self.table.set_data(test_data, is_pagination_mode=False)
            
            logger.info("测试数据设置完成")
            
        except Exception as e:
            logger.error(f"设置测试数据失败: {e}")
    
    def test_sort_cycle(self, column_index: int):
        """测试排序循环"""
        try:
            logger.info(f"开始测试列{column_index}的排序循环")
            
            # 模拟点击表头
            self.table._on_header_clicked(column_index)
            
            # 获取当前排序状态
            current_state = self.table._get_column_sort_state(column_index)
            logger.info(f"第1次点击后状态: {current_state}")
            
            # 再次点击
            self.table._on_header_clicked(column_index)
            current_state = self.table._get_column_sort_state(column_index)
            logger.info(f"第2次点击后状态: {current_state}")
            
            # 第三次点击
            self.table._on_header_clicked(column_index)
            current_state = self.table._get_column_sort_state(column_index)
            logger.info(f"第3次点击后状态: {current_state}")
            
            # 第四次点击（应该回到初始状态）
            self.table._on_header_clicked(column_index)
            current_state = self.table._get_column_sort_state(column_index)
            logger.info(f"第4次点击后状态: {current_state}")
            
        except Exception as e:
            logger.error(f"测试排序循环失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = SortTestWindow()
    window.show()
    
    # 测试薪级工资列的排序循环（假设是第3列）
    window.test_sort_cycle(2)  # 2025年薪级工资列
    
    logger.info("排序循环测试完成，请查看日志输出")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
