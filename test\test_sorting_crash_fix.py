#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序功能崩溃修复测试脚本
测试防重复处理机制和信号连接修复
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_header_click_protection():
    """测试表头点击防重复处理机制"""
    print("🔧 开始测试表头点击防重复处理...")
    
    # 模拟表格组件
    class MockTable:
        def __init__(self):
            self._processing_header_click = False
            self.click_count = 0
        
        def _on_header_clicked(self, logical_index: int):
            """模拟修复后的表头点击处理"""
            try:
                # 🔧 [排序修复] 防止重复处理
                if hasattr(self, '_processing_header_click') and self._processing_header_click:
                    print(f"  ⚠️ 正在处理表头点击，跳过重复请求: 列{logical_index}")
                    return
                
                self._processing_header_click = True
                self.click_count += 1
                
                print(f"  ✅ 处理表头点击: 列{logical_index}, 处理次数: {self.click_count}")
                
                # 模拟处理逻辑
                import time
                time.sleep(0.01)  # 模拟处理时间
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
            finally:
                # 🔧 [排序修复] 确保处理标志被清除
                self._processing_header_click = False
    
    # 测试场景
    table = MockTable()
    
    # 模拟快速连续点击（在处理过程中）
    import threading
    import time

    def simulate_concurrent_clicks():
        """模拟并发点击"""
        for i in range(4):
            time.sleep(0.001)  # 很短的间隔
            table._on_header_clicked(6)

    # 启动并发点击线程
    thread = threading.Thread(target=simulate_concurrent_clicks)
    thread.start()

    # 主线程也点击一次
    table._on_header_clicked(6)

    # 等待线程完成
    thread.join()
    
    # 验证结果
    if table.click_count == 1:
        print("✅ 表头点击防重复处理测试通过")
        return True
    else:
        print(f"❌ 表头点击防重复处理测试失败: 期望1次处理，实际{table.click_count}次")
        return False

def test_data_update_protection():
    """测试数据更新事件防循环机制"""
    print("\n🔧 开始测试数据更新事件防循环...")
    
    # 模拟主窗口
    class MockMainWindow:
        def __init__(self):
            self._processing_data_update = False
            self.update_count = 0
        
        def _on_new_data_updated(self, event):
            """模拟修复后的数据更新处理"""
            try:
                # 🔧 [排序修复] 防止事件循环
                if hasattr(self, '_processing_data_update') and self._processing_data_update:
                    print("  ⚠️ 正在处理数据更新，跳过重复事件")
                    return
                
                self._processing_data_update = True
                self.update_count += 1
                
                print(f"  ✅ 处理数据更新事件, 处理次数: {self.update_count}")
                
                # 模拟处理逻辑
                import time
                time.sleep(0.01)  # 模拟处理时间
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
            finally:
                # 🔧 [排序修复] 确保处理标志被清除
                self._processing_data_update = False
    
    # 测试场景
    window = MockMainWindow()
    
    # 模拟事件对象
    class MockEvent:
        def __init__(self):
            self.table_name = "test_table"
            self.data = []
            self.metadata = {}
    
    # 模拟快速连续事件（在处理过程中）
    import threading
    import time

    def simulate_concurrent_updates():
        """模拟并发更新"""
        event = MockEvent()
        for i in range(4):
            time.sleep(0.001)  # 很短的间隔
            window._on_new_data_updated(event)

    # 启动并发更新线程
    thread = threading.Thread(target=simulate_concurrent_updates)
    thread.start()

    # 主线程也更新一次
    event = MockEvent()
    window._on_new_data_updated(event)

    # 等待线程完成
    thread.join()
    
    # 验证结果
    if window.update_count == 1:
        print("✅ 数据更新事件防循环测试通过")
        return True
    else:
        print(f"❌ 数据更新事件防循环测试失败: 期望1次处理，实际{window.update_count}次")
        return False

def test_sort_request_protection():
    """测试排序请求防重复机制"""
    print("\n🔧 开始测试排序请求防重复...")
    
    # 模拟主窗口
    class MockMainWindow:
        def __init__(self):
            self._processing_sort_request = False
            self.request_count = 0
        
        def _publish_sort_request_event(self, table_name: str, sort_columns: list, current_page: int = 1):
            """模拟修复后的排序请求发布"""
            try:
                # 🔧 [排序修复] 防止重复排序请求
                if hasattr(self, '_processing_sort_request') and self._processing_sort_request:
                    print("  ⚠️ 正在处理排序请求，跳过重复请求")
                    return
                
                self._processing_sort_request = True
                self.request_count += 1
                
                print(f"  ✅ 处理排序请求: {table_name}, 处理次数: {self.request_count}")
                
                # 模拟处理逻辑
                import time
                time.sleep(0.01)  # 模拟处理时间
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
            finally:
                # 🔧 [排序修复] 确保处理标志被清除
                self._processing_sort_request = False
    
    # 测试场景
    window = MockMainWindow()
    
    # 模拟快速连续排序请求（在处理过程中）
    import threading
    import time

    def simulate_concurrent_requests():
        """模拟并发请求"""
        sort_columns = [{'column_name': '2025年薪级工资', 'order': 'descending'}]
        for i in range(4):
            time.sleep(0.001)  # 很短的间隔
            window._publish_sort_request_event("test_table", sort_columns, 1)

    # 启动并发请求线程
    thread = threading.Thread(target=simulate_concurrent_requests)
    thread.start()

    # 主线程也请求一次
    sort_columns = [{'column_name': '2025年薪级工资', 'order': 'descending'}]
    window._publish_sort_request_event("test_table", sort_columns, 1)

    # 等待线程完成
    thread.join()
    
    # 验证结果
    if window.request_count == 1:
        print("✅ 排序请求防重复测试通过")
        return True
    else:
        print(f"❌ 排序请求防重复测试失败: 期望1次处理，实际{window.request_count}次")
        return False

def test_signal_disconnect():
    """测试信号断开逻辑"""
    print("\n🔧 开始测试信号断开逻辑...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QTableWidget
        from PyQt5.QtCore import Qt
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建测试表格
        table = QTableWidget(5, 3)
        table.setHorizontalHeaderLabels(['列1', '列2', '列3'])
        
        # 获取表头
        header = table.horizontalHeader()
        
        # 记录连接次数
        connection_count = 0
        
        def test_handler(index):
            nonlocal connection_count
            connection_count += 1
            print(f"  信号触发: 列{index}, 触发次数: {connection_count}")
        
        # 模拟修复后的信号连接逻辑
        def fix_header_signal_connections():
            nonlocal connection_count
            
            # 🔧 [排序修复] 断开所有现有的信号连接，防止重复连接
            try:
                header.sortIndicatorChanged.disconnect()
                header.sectionDoubleClicked.disconnect()
                header.sectionClicked.disconnect()  # 🔧 [关键修复] 断开sectionClicked信号
                print("  ✅ 成功断开所有表头信号")
            except Exception as disconnect_error:
                print(f"  ⚠️ 断开信号时的警告（正常）: {disconnect_error}")
                pass  # 忽略断开失败的错误
            
            # 重新连接信号
            header.sectionClicked.connect(test_handler, Qt.DirectConnection)
            print("  ✅ 重新连接sectionClicked信号")
        
        # 测试多次调用信号修复方法
        for i in range(3):
            fix_header_signal_connections()
        
        # 模拟点击表头
        header.sectionClicked.emit(1)
        
        # 验证结果
        if connection_count == 1:
            print("✅ 信号断开逻辑测试通过")
            return True
        else:
            print(f"❌ 信号断开逻辑测试失败: 期望1次触发，实际{connection_count}次")
            return False
        
    except Exception as e:
        print(f"❌ 信号断开逻辑测试失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始排序功能崩溃修复测试...")
    
    # 执行所有测试
    test1_success = test_header_click_protection()
    test2_success = test_data_update_protection()
    test3_success = test_sort_request_protection()
    test4_success = test_signal_disconnect()
    
    if test1_success and test2_success and test3_success and test4_success:
        print("\n🎉 所有排序功能崩溃修复测试通过！")
        print("✅ 表头点击防重复处理修复完成")
        print("✅ 数据更新事件防循环修复完成")
        print("✅ 排序请求防重复修复完成")
        print("✅ 信号连接重复问题修复完成")
        print("\n🔧 修复总结:")
        print("1. 添加了处理标志防止重复处理")
        print("2. 使用finally块确保标志正确清除")
        print("3. 修复了信号断开逻辑，防止重复连接")
        print("4. 增强了错误处理和日志记录")
        sys.exit(0)
    else:
        print("\n💥 排序功能崩溃修复测试失败！")
        sys.exit(1)
