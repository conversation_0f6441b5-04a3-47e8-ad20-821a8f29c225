#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案A修复效果验证脚本
验证字段类型配置是否正确加载和应用
"""

import sys
import os
import pandas as pd

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.modules.format_management.field_registry import FieldRegistry
from src.modules.format_management.format_config import FormatConfig
from src.modules.format_management.format_renderer import FormatRenderer

def verify_fix_A():
    """验证方案A的修复效果"""
    print("[方案A验证] 开始验证字段类型配置修复...")
    
    # 初始化组件
    field_registry = FieldRegistry("state/data/field_mappings.json")
    format_config = FormatConfig("src/modules/format_management/format_config.json")
    renderer = FormatRenderer(format_config, field_registry)
    
    # 测试1：验证字段类型配置加载
    print("\n[测试1] 字段类型配置加载测试:")
    
    # 测试具体表名
    table_name = "salary_data_2025_07_active_employees"
    field_types = field_registry.get_table_field_types(table_name)
    
    print(f"表名: {table_name}")
    print(f"字段类型总数: {len(field_types)}")
    
    # 检查关键字段类型
    critical_fields = {
        'basic_performance_2025': 'float',
        'performance_bonus_2025': 'float', 
        'car_allowance': 'float',
        'pension_insurance': 'float',
        'employee_type_code': 'string'
    }
    
    all_correct = True
    for field, expected_type in critical_fields.items():
        actual_type = field_types.get(field, 'NOT_FOUND')
        status = "OK" if actual_type == expected_type else "FAIL"
        print(f"  [{status}] {field}: {actual_type} (expected: {expected_type})")
        if actual_type != expected_type:
            all_correct = False
    
    if not all_correct:
        print("FAIL: Field type configuration verification failed!")
        return False
    
    # 测试2：验证数据格式化效果
    print("\n[测试2] 数据格式化效果测试:")
    
    # 创建测试数据
    test_data = {
        'basic_performance_2025': [1234, 5678.0, 0, None],
        'performance_bonus_2025': [2000, 1500.5, 0.0, None],
        'car_allowance': [100.5, 150, "-", None],
        'pension_insurance': [200.75, 250.5, 0.0, None],
        'employee_type_code': ["1", "02", "3.0", "1.0"]
    }
    
    df = pd.DataFrame(test_data)
    print(f"原始测试数据:\n{df}")
    
    # 格式化数据
    try:
        formatted_df = renderer.render_dataframe(df, table_name)
        print(f"\n格式化后数据:\n{formatted_df}")
        
        # 验证格式化结果
        print("\n[格式化结果验证]:")
        
        # 验证浮点数格式（应该显示两位小数）
        basic_perf_val = formatted_df.iloc[0]['basic_performance_2025']
        if basic_perf_val == "1234.00":
            print("[OK] basic_performance_2025 format correct: 1234 -> '1234.00'")
        else:
            print(f"[FAIL] basic_performance_2025 format wrong: 1234 -> '{basic_perf_val}'")  
            all_correct = False
        
        # 验证车补"-"字符处理
        car_val = formatted_df.iloc[2]['car_allowance']
        if car_val == "0.00":
            print("[OK] car_allowance '-' handled correctly: '-' -> '0.00'")
        else:
            print(f"[FAIL] car_allowance '-' handled wrong: '-' -> '{car_val}'")
            all_correct = False
        
        # 验证人员类别代码零填充
        type_code_val = formatted_df.iloc[0]['employee_type_code']
        if type_code_val == "01":
            print("[OK] employee_type_code zero-padding correct: '1' -> '01'")
        else:
            print(f"[FAIL] employee_type_code zero-padding wrong: '1' -> '{type_code_val}'")
            all_correct = False
        
    except Exception as e:
        print(f"FAIL: DataFrame formatting failed: {e}")
        return False
    
    # 测试3：验证通用表名匹配
    print("\n[Test 3] Generic table name matching test:")
    generic_types = field_registry.get_table_field_types("active_employees")
    print(f"Generic table name 'active_employees' field types count: {len(generic_types)}")
    
    if len(generic_types) > 0:
        print("[OK] Generic table name matching works")
    else:
        print("[FAIL] Generic table name matching failed")
        all_correct = False
    
    # 最终结果
    if all_correct:
        print("\nSUCCESS: Solution A verification passed! All tests passed.")
        print("  - Field type configuration loaded correctly")
        print("  - Float numbers display with two decimal places")
        print("  - Car allowance '-' character correctly converted to '0.00'")
        print("  - Employee type code correctly zero-padded")
        return True
    else:
        print("\nFAIL: Solution A verification failed! Issues need further fixing.")
        return False

if __name__ == "__main__":
    verify_fix_A()