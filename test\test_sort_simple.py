#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单排序功能测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_sort_cycle():
    """测试排序循环功能"""
    try:
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        from src.modules.data_import.config_sync_manager import ConfigSyncManager

        print("✅ 模块导入成功")

        # 创建QApplication
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 创建ConfigSyncManager实例
        config_sync = ConfigSyncManager()
        print("✅ ConfigSyncManager创建成功")
        
        # 创建表格实例
        table = VirtualizedExpandableTable(config_sync_manager=config_sync)
        print("✅ VirtualizedExpandableTable创建成功")

        # 设置测试数据
        import pandas as pd
        test_data = pd.DataFrame({
            '序号': range(1, 6),
            '姓名': [f'员工{i}' for i in range(1, 6)],
            '工资': [3000, 2500, 3500, 2800, 3200]
        })
        table.set_data(test_data)
        print("✅ 测试数据设置成功")
        
        # 测试排序状态方法
        if hasattr(table, '_get_column_sort_state'):
            print("✅ _get_column_sort_state方法存在")
        else:
            print("❌ _get_column_sort_state方法不存在")
            
        if hasattr(table, '_on_header_clicked'):
            print("✅ _on_header_clicked方法存在")
        else:
            print("❌ _on_header_clicked方法不存在")
            
        # 测试排序循环
        print("\n🔧 测试排序循环:")

        # 测试列1（姓名列）而不是列0（序号列，会被阻止排序）
        test_column = 1

        # 初始状态应该是none
        initial_state = table._get_column_sort_state(test_column)
        print(f"初始状态: {initial_state}")

        # 第一次点击 - 应该变为ascending
        table._on_header_clicked(test_column)
        state1 = table._get_column_sort_state(test_column)
        print(f"第1次点击后: {state1}")

        # 第二次点击 - 应该变为descending
        table._on_header_clicked(test_column)
        state2 = table._get_column_sort_state(test_column)
        print(f"第2次点击后: {state2}")

        # 第三次点击 - 应该变为none
        table._on_header_clicked(test_column)
        state3 = table._get_column_sort_state(test_column)
        print(f"第3次点击后: {state3}")
        
        # 验证循环
        expected_cycle = ['none', 'ascending', 'descending', 'none']
        actual_cycle = [initial_state, state1, state2, state3]
        
        if actual_cycle == expected_cycle:
            print("✅ 排序循环测试通过！")
            return True
        else:
            print(f"❌ 排序循环测试失败！")
            print(f"期望: {expected_cycle}")
            print(f"实际: {actual_cycle}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始排序功能测试...")
    success = test_sort_cycle()
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
