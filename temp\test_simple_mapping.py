#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试表类型映射修复
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_table_type_mapping():
    """测试表类型映射修复"""
    print("测试表类型映射修复")
    
    try:
        # 直接导入并测试映射函数
        sys.path.append(str(project_root / "src" / "services"))
        
        # 模拟映射逻辑
        def extract_table_type_from_name(table_name: str) -> str:
            """从表名中提取表格类型"""
            table_name_lower = table_name.lower()
            
            if 'active_employees' in table_name_lower or '全部在职人员' in table_name:
                return 'active_employees'
            elif 'retired_employees' in table_name_lower or '离休人员' in table_name:
                return 'retired_employees'
            elif 'pension_employees' in table_name_lower or '退休人员' in table_name:
                return 'retired_employees'
            elif 'part_time' in table_name_lower or '临时工' in table_name:
                return 'part_time_employees'
            elif 'contract' in table_name_lower or '合同工' in table_name:
                return 'contract_employees'
            elif 'a_grade' in table_name_lower or 'a岗' in table_name:
                return 'a_grade_employees'  # 修复后的映射
            else:
                return 'active_employees'
        
        # 测试A岗职工表类型映射
        test_table_name = "salary_data_2025_08_a_grade_employees"
        table_type = extract_table_type_from_name(test_table_name)
        
        print(f"表名: {test_table_name}")
        print(f"映射结果: {table_type}")
        
        if table_type == 'a_grade_employees':
            print("SUCCESS: A岗职工表类型映射修复成功!")
            return True
        else:
            print(f"FAILED: A岗职工表类型映射仍然错误: {table_type}")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试修复效果")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_table_type_mapping()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  表类型映射测试: {'通过' if test1_passed else '失败'}")
    
    if test1_passed:
        print("修复测试通过！表类型映射已正确修复")
    else:
        print("测试失败，需要进一步检查")