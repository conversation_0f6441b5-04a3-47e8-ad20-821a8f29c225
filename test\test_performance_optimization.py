#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化测试脚本
测试新的性能缓存机制是否有效提升性能
"""

import sys
import os
import time
import pandas as pd
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_performance_cache():
    """测试性能缓存功能"""
    try:
        # 导入性能管理器
        from src.gui.prototype.performance import get_performance_manager, reset_performance_manager
        
        print("🚀 开始性能缓存测试...")
        
        # 重置性能管理器确保干净的测试环境
        reset_performance_manager()
        perf_manager = get_performance_manager()
        
        print("✅ 性能管理器初始化成功")
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '工号': [f'2025{i:04d}' for i in range(1, 101)],
            '姓名': [f'员工{i}' for i in range(1, 101)],
            '部门': (['技术部', '人事部', '财务部'] * 34)[:100],  # 确保长度为100
            '工资': [3000 + i * 100 for i in range(100)]
        })
        
        table_name = "test_performance_table"
        page = 1
        per_page = 50
        
        print(f"📊 测试数据创建完成: {len(test_data)}行")
        
        # 测试1: 首次缓存存储
        print("\n🔧 测试1: 数据缓存存储")
        start_time = time.time()
        
        perf_manager.cache_data(table_name, page, per_page, test_data)
        
        cache_time = (time.time() - start_time) * 1000
        print(f"✅ 数据缓存存储耗时: {cache_time:.2f}ms")
        
        # 测试2: 缓存命中
        print("\n🔧 测试2: 缓存命中测试")
        start_time = time.time()
        
        cached_data = perf_manager.get_cached_data(table_name, page, per_page)
        
        hit_time = (time.time() - start_time) * 1000
        print(f"✅ 缓存命中耗时: {hit_time:.2f}ms")
        
        if cached_data is not None:
            print(f"✅ 缓存命中成功，数据行数: {len(cached_data)}")
            print(f"✅ 数据完整性验证: {'通过' if len(cached_data) == len(test_data) else '失败'}")
        else:
            print("❌ 缓存未命中")
            return False
        
        # 测试3: 缓存未命中
        print("\n🔧 测试3: 缓存未命中测试")
        start_time = time.time()
        
        missed_data = perf_manager.get_cached_data(table_name, page + 1, per_page)
        
        miss_time = (time.time() - start_time) * 1000
        print(f"✅ 缓存未命中耗时: {miss_time:.2f}ms")
        
        if missed_data is None:
            print("✅ 缓存未命中正确")
        else:
            print("❌ 缓存未命中测试失败")
            return False
        
        # 测试4: 排序缓存
        print("\n🔧 测试4: 排序缓存测试")
        sort_columns = [('工号', 'ascending')]
        
        # 存储排序数据
        sorted_data = test_data.sort_values('工号')
        perf_manager.cache_data(table_name, page, per_page, sorted_data, sort_columns)
        
        # 获取排序数据
        cached_sorted_data = perf_manager.get_cached_data(table_name, page, per_page, sort_columns)
        
        if cached_sorted_data is not None:
            print("✅ 排序缓存测试通过")
        else:
            print("❌ 排序缓存测试失败")
            return False
        
        # 测试5: 状态缓存
        print("\n🔧 测试5: 状态缓存测试")
        test_state = {
            'current_page': 1,
            'page_size': 50,
            'sort_column': '工号',
            'sort_order': 'ascending'
        }
        
        perf_manager.cache_table_state(table_name, test_state)
        cached_state = perf_manager.get_cached_table_state(table_name)
        
        if cached_state and cached_state['current_page'] == 1:
            print("✅ 状态缓存测试通过")
        else:
            print("❌ 状态缓存测试失败")
            return False
        
        # 测试6: 字段映射缓存
        print("\n🔧 测试6: 字段映射缓存测试")
        test_mapping = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门',
            'salary': '工资'
        }
        
        perf_manager.cache_field_mapping(table_name, test_mapping)
        cached_mapping = perf_manager.get_cached_field_mapping(table_name)
        
        if cached_mapping and cached_mapping.get('employee_id') == '工号':
            print("✅ 字段映射缓存测试通过")
        else:
            print("❌ 字段映射缓存测试失败")
            return False
        
        # 测试7: 性能统计
        print("\n🔧 测试7: 性能统计测试")
        
        # 记录一些加载时间
        for i in range(5):
            perf_manager.record_data_load_time(50 + i * 10)
        
        stats = perf_manager.get_comprehensive_stats()
        
        print(f"✅ 数据缓存统计: {stats['data_cache']}")
        print(f"✅ 性能统计: {stats['performance']}")
        
        # 测试8: 缓存失效
        print("\n🔧 测试8: 缓存失效测试")
        
        # 失效表缓存
        perf_manager.invalidate_all_table_cache(table_name)
        
        # 验证缓存已失效
        invalidated_data = perf_manager.get_cached_data(table_name, page, per_page)
        invalidated_state = perf_manager.get_cached_table_state(table_name)
        invalidated_mapping = perf_manager.get_cached_field_mapping(table_name)
        
        if all(x is None for x in [invalidated_data, invalidated_state, invalidated_mapping]):
            print("✅ 缓存失效测试通过")
        else:
            print("❌ 缓存失效测试失败")
            return False
        
        # 输出最终统计
        print("\n📊 最终性能统计:")
        perf_manager.log_comprehensive_stats()
        
        print("\n🎉 所有性能缓存测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 性能缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_improvement():
    """测试性能提升效果"""
    try:
        print("\n🚀 开始性能提升效果测试...")
        
        # 创建大量测试数据
        large_data = pd.DataFrame({
            '工号': [f'2025{i:06d}' for i in range(1, 1001)],
            '姓名': [f'员工{i}' for i in range(1, 1001)],
            '部门': ['技术部', '人事部', '财务部', '市场部', '运营部'] * 200,
            '工资': [3000 + i * 50 for i in range(1000)]
        })
        
        print(f"📊 大数据集创建完成: {len(large_data)}行")
        
        # 模拟数据处理时间
        def simulate_data_processing(data):
            """模拟数据处理耗时"""
            time.sleep(0.01)  # 模拟10ms的处理时间
            return data.copy()
        
        # 测试无缓存情况
        print("\n🔧 测试无缓存性能:")
        no_cache_times = []
        
        for i in range(3):
            start_time = time.time()
            processed_data = simulate_data_processing(large_data)
            end_time = time.time()
            
            elapsed = (end_time - start_time) * 1000
            no_cache_times.append(elapsed)
            print(f"  第{i+1}次无缓存处理耗时: {elapsed:.2f}ms")
        
        avg_no_cache = sum(no_cache_times) / len(no_cache_times)
        print(f"✅ 无缓存平均耗时: {avg_no_cache:.2f}ms")
        
        # 测试有缓存情况
        print("\n🔧 测试有缓存性能:")
        from src.gui.prototype.performance import get_performance_manager
        
        perf_manager = get_performance_manager()
        table_name = "large_test_table"
        
        # 首次缓存
        perf_manager.cache_data(table_name, 1, 1000, large_data)
        
        cache_times = []
        
        for i in range(3):
            start_time = time.time()
            cached_data = perf_manager.get_cached_data(table_name, 1, 1000)
            end_time = time.time()
            
            elapsed = (end_time - start_time) * 1000
            cache_times.append(elapsed)
            print(f"  第{i+1}次缓存命中耗时: {elapsed:.2f}ms")
        
        avg_cache = sum(cache_times) / len(cache_times)
        print(f"✅ 缓存命中平均耗时: {avg_cache:.2f}ms")
        
        # 计算性能提升
        improvement = ((avg_no_cache - avg_cache) / avg_no_cache) * 100
        print(f"\n🚀 性能提升: {improvement:.1f}%")
        print(f"🚀 速度提升: {avg_no_cache / avg_cache:.1f}倍")
        
        if improvement > 50:  # 期望至少50%的性能提升
            print("✅ 性能提升测试通过！")
            return True
        else:
            print("❌ 性能提升不够显著")
            return False
        
    except Exception as e:
        print(f"❌ 性能提升测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始性能优化测试...")
    
    # 测试缓存功能
    cache_success = test_performance_cache()
    
    # 测试性能提升
    improvement_success = test_performance_improvement()
    
    if cache_success and improvement_success:
        print("\n🎉 所有性能优化测试通过！")
        sys.exit(0)
    else:
        print("\n💥 性能优化测试失败！")
        sys.exit(1)
