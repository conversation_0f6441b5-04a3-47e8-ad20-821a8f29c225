"""
系统性修复验证 - 验证P0-P2级别修复效果
通过代码分析验证修复是否正确实现
"""

import os
import re

def read_file_safe(file_path):
    """安全读取文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败: {file_path}, 错误: {e}")
        return None

def test_column_width_manager_timer_fix():
    """验证ColumnWidthManager Timer方法修复"""
    print("验证ColumnWidthManager Timer方法...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    content = read_file_safe(file_path)
    
    if not content:
        return False
    
    # 检查关键修复
    checks = [
        ("_setup_save_timer_safe方法", r"def _setup_save_timer_safe\(self\):"),
        ("_cleanup_save_timer方法", r"def _cleanup_save_timer\(self\):"),
        ("Timer parent指定", r"self\._save_timer = QTimer\(self\)"),
        ("线程安全检查", r"QThread\.currentThread\(\) != QApplication\.instance\(\)\.thread\(\)"),
    ]
    
    results = []
    for name, pattern in checks:
        if re.search(pattern, content):
            print(f"  成功: {name}")
            results.append(True)
        else:
            print(f"  失败: {name}")
            results.append(False)
    
    return all(results)

def test_table_type_consistency_fix():
    """验证表类型识别一致性修复"""
    print("验证表类型识别一致性...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    content = read_file_safe(file_path)
    
    if not content:
        return False
    
    # 检查关键修复点
    checks = [
        ("退休人员映射修复", r"'pension_employees': 'pension_employees'"),
        ("中文映射修复", r"'退休人员': 'pension_employees'"),
        ("返回值修复", r"return 'pension_employees'.*退休人员使用独立配置"),
    ]
    
    results = []
    for name, pattern in checks:
        if re.search(pattern, content):
            print(f"  成功: {name}")
            results.append(True)
        else:
            print(f"  失败: {name}")
            results.append(False)
    
    return all(results)

def main():
    """执行静态修复验证"""
    print("开始静态修复验证")
    print("=" * 50)
    
    # 切换到项目根目录
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    tests = [
        ("P0-1: ColumnWidthManager Timer修复", test_column_width_manager_timer_fix),
        ("P0-2: 表类型识别一致性修复", test_table_type_consistency_fix),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        try:
            result = test_func()
            results.append(result)
            print(f"结果: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"测试异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    total_tests = len(results)
    passed_tests = sum(results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"验证结果:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate == 100 else 1)
