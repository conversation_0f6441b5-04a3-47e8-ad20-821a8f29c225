{"salary_data_2025_08_active_employees": {"column_widths": [100, 100, 100, 100, 100, 168, 205, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "column_names": ["工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险", "月份", "年份"], "timestamp": 1754378918.113941}, "salary_data_2025_08_a_grade_employees": {"column_widths": [100, 100, 100, 100, 100, 163, 172, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "column_names": ["工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险", "月份", "年份"], "timestamp": 1754378897.752599}, "salary_data_2025_08_pension_employees": {"column_widths": [100, 100, 100, 100, 174, 136, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "column_names": ["人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注", "月份", "年份"], "timestamp": 1754378879.487998}, "salary_data_2025_08_retired_employees": {"column_widths": [100, 100, 100, 154, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "column_names": ["人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注", "月份", "年份"], "timestamp": 1754378888.5447354}}