#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分页策略管理器
遵循CLAUDE.md要求：添加数据流追踪日志，完善新架构
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.utils.log_config import setup_logger


@dataclass
class PaginationDecision:
    """分页决策结果"""
    use_pagination: bool
    strategy: str  # 'full_display' | 'pagination'
    reason: str
    estimated_performance_ms: int
    recommended_page_size: Optional[int] = None


class SmartPaginationStrategy:
    """
    智能分页策略管理器
    
    根据数据量智能决定使用分页还是全量显示，优化用户体验
    遵循CLAUDE.md原则：数据流追踪日志 + 新架构完善
    """
    
    # 策略配置常量
    DEFAULT_PAGE_SIZE = 50
    SMALL_DATASET_THRESHOLD = 75  # 小于等于75条直接显示
    PERFORMANCE_THRESHOLD_MS = 500  # 性能阈值：500ms
    
    def __init__(self):
        """初始化智能分页策略管理器"""
        self.logger = setup_logger(self.__class__.__name__)
        self._strategy_stats = {
            'full_display_count': 0,
            'pagination_count': 0,
            'total_decisions': 0
        }
        
        self.logger.info("智能分页策略管理器初始化完成")
        self.logger.debug(f"配置参数: 默认页面大小={self.DEFAULT_PAGE_SIZE}, "
                         f"小数据集阈值={self.SMALL_DATASET_THRESHOLD}")
    
    def should_use_pagination(self, total_records: int, 
                            page_size: Optional[int] = None,
                            user_preference: Optional[str] = None) -> PaginationDecision:
        """
        智能分页决策核心逻辑
        
        Args:
            total_records: 总记录数
            page_size: 页面大小，None使用默认值
            user_preference: 用户偏好 'force_pagination' | 'auto' | None
            
        Returns:
            PaginationDecision: 决策结果
        """
        start_time = time.time()
        
        # 数据流追踪日志：输入参数
        self.logger.info(f"[数据流追踪] 分页决策输入: 总记录数={total_records}, "
                        f"页面大小={page_size}, 用户偏好={user_preference}")
        
        # 使用默认页面大小
        effective_page_size = page_size or self.DEFAULT_PAGE_SIZE
        
        # 处理用户强制偏好
        if user_preference == 'force_pagination':
            decision = self._create_pagination_decision(
                total_records, effective_page_size, 
                "用户强制要求分页显示"
            )
            self.logger.info(f"[数据流追踪] 用户强制分页: {decision.reason}")
            return decision
        
        # 核心决策逻辑
        decision = self._execute_smart_decision(total_records, effective_page_size)
        
        # 更新统计信息
        self._update_statistics(decision)
        
        # 性能度量
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        # 数据流追踪日志：输出结果
        self.logger.info(f"[数据流追踪] 分页决策输出: 策略={decision.strategy}, "
                        f"原因={decision.reason}, 预期性能={decision.estimated_performance_ms}ms, "
                        f"决策耗时={elapsed_ms}ms")
        
        return decision
    
    def _execute_smart_decision(self, total_records: int, page_size: int) -> PaginationDecision:
        """执行智能决策逻辑"""
        
        # 策略1: 空数据或极小数据集
        if total_records <= 0:
            return PaginationDecision(
                use_pagination=False,
                strategy='full_display',
                reason=f"空数据集({total_records}条)",
                estimated_performance_ms=50
            )
        
        # 策略2: 记录数少于等于页面大小
        if total_records <= page_size:
            return PaginationDecision(
                use_pagination=False,
                strategy='full_display',
                reason=f"数据量({total_records}条) <= 页面大小({page_size}条)",
                estimated_performance_ms=self._estimate_render_time(total_records, 'small')
            )
        
        # 策略3: 小数据集优化 - 避免只有51条记录却要分成2页的尴尬
        if total_records <= self.SMALL_DATASET_THRESHOLD:
            return PaginationDecision(
                use_pagination=False,
                strategy='full_display',
                reason=f"小数据集({total_records}条) <= 阈值({self.SMALL_DATASET_THRESHOLD}条)",
                estimated_performance_ms=self._estimate_render_time(total_records, 'small')
            )
        
        # 策略4: 中等数据集 - 考虑性能平衡
        if total_records <= 200:
            # 预估全量显示性能
            full_display_time = self._estimate_render_time(total_records, 'medium')
            if full_display_time <= self.PERFORMANCE_THRESHOLD_MS:
                return PaginationDecision(
                    use_pagination=False,
                    strategy='full_display',
                    reason=f"中等数据集({total_records}条)性能可接受(预计{full_display_time}ms)",
                    estimated_performance_ms=full_display_time
                )
        
        # 策略5: 大数据集 - 启用分页
        pages_count = (total_records + page_size - 1) // page_size
        return PaginationDecision(
            use_pagination=True,
            strategy='pagination',
            reason=f"大数据集({total_records}条)分{pages_count}页显示",
            estimated_performance_ms=self._estimate_render_time(page_size, 'paginated'),
            recommended_page_size=page_size
        )
    
    def _create_pagination_decision(self, total_records: int, page_size: int, 
                                  reason: str) -> PaginationDecision:
        """创建强制分页决策"""
        pages_count = max(1, (total_records + page_size - 1) // page_size)
        return PaginationDecision(
            use_pagination=True,
            strategy='pagination',
            reason=reason,
            estimated_performance_ms=self._estimate_render_time(page_size, 'paginated'),
            recommended_page_size=page_size
        )
    
    def _estimate_render_time(self, record_count: int, dataset_type: str) -> int:
        """
        估算渲染时间
        
        Args:
            record_count: 记录数量
            dataset_type: 'small' | 'medium' | 'paginated'
            
        Returns:
            预估时间(毫秒)
        """
        # 基于实际测试的性能模型
        if dataset_type == 'small':
            # 小数据集：每行约5ms
            return min(300, record_count * 5)
        elif dataset_type == 'medium':
            # 中等数据集：每行约6ms，有基础开销
            return min(600, 100 + record_count * 6)
        elif dataset_type == 'paginated':
            # 分页数据集：固定基础时间 + 每行7ms
            return min(1000, 200 + record_count * 7)
        else:
            # 默认估算
            return record_count * 10
    
    def _update_statistics(self, decision: PaginationDecision):
        """更新决策统计信息"""
        self._strategy_stats['total_decisions'] += 1
        
        if decision.use_pagination:
            self._strategy_stats['pagination_count'] += 1
        else:
            self._strategy_stats['full_display_count'] += 1
        
        # 每100次决策记录一次统计
        if self._strategy_stats['total_decisions'] % 100 == 0:
            self._log_statistics()
    
    def _log_statistics(self):
        """记录统计信息"""
        stats = self._strategy_stats
        total = stats['total_decisions']
        
        if total > 0:
            full_display_pct = (stats['full_display_count'] / total) * 100
            pagination_pct = (stats['pagination_count'] / total) * 100
            
            self.logger.info(f"[统计信息] 分页策略使用情况: "
                           f"全量显示={stats['full_display_count']}次({full_display_pct:.1f}%), "
                           f"分页显示={stats['pagination_count']}次({pagination_pct:.1f}%), "
                           f"总决策={total}次")
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略使用统计"""
        stats = self._strategy_stats.copy()
        total = stats['total_decisions']
        
        if total > 0:
            stats['full_display_percentage'] = (stats['full_display_count'] / total) * 100
            stats['pagination_percentage'] = (stats['pagination_count'] / total) * 100
        else:
            stats['full_display_percentage'] = 0.0
            stats['pagination_percentage'] = 0.0
        
        return stats
    
    def configure_thresholds(self, small_dataset_threshold: Optional[int] = None,
                           performance_threshold_ms: Optional[int] = None):
        """
        配置策略阈值
        
        Args:
            small_dataset_threshold: 小数据集阈值
            performance_threshold_ms: 性能阈值(毫秒)
        """
        if small_dataset_threshold is not None:
            old_threshold = self.SMALL_DATASET_THRESHOLD
            self.SMALL_DATASET_THRESHOLD = small_dataset_threshold
            self.logger.info(f"[配置更新] 小数据集阈值: {old_threshold} -> {small_dataset_threshold}")
        
        if performance_threshold_ms is not None:
            old_threshold = self.PERFORMANCE_THRESHOLD_MS
            self.PERFORMANCE_THRESHOLD_MS = performance_threshold_ms
            self.logger.info(f"[配置更新] 性能阈值: {old_threshold}ms -> {performance_threshold_ms}ms")


# 全局单例实例
_smart_pagination_strategy = None


def get_smart_pagination_strategy() -> SmartPaginationStrategy:
    """获取智能分页策略管理器单例实例"""
    global _smart_pagination_strategy
    if _smart_pagination_strategy is None:
        _smart_pagination_strategy = SmartPaginationStrategy()
    return _smart_pagination_strategy