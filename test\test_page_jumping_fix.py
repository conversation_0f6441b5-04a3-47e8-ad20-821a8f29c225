#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面跳动问题修复验证测试

测试修复后的系统是否解决了页面跳动问题
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_page_jumping_fix.log')
        ]
    )
    return logging.getLogger(__name__)

def test_preload_threshold():
    """测试预加载阈值修复"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试预加载阈值修复...")
    
    try:
        # 检查源代码中的预加载阈值
        service_file = project_root / "src" / "services" / "table_data_service.py"
        
        if not service_file.exists():
            logger.error(f"❌ 服务文件不存在: {service_file}")
            return False
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查预加载阈值是否已修改
        if ">= 8" in content and "提高预加载阈值到8" in content:
            logger.info("✅ 预加载阈值已正确修改为8")
        else:
            logger.error("❌ 预加载阈值未正确修改")
            return False
        
        # 检查预加载范围限制
        if "access_count >= 15" in content and "极高频访问页面" in content:
            logger.info("✅ 预加载范围已正确限制")
        else:
            logger.error("❌ 预加载范围未正确限制")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 预加载阈值测试失败: {e}")
        return False

def test_access_type_distinction():
    """测试访问类型区分修复"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试访问类型区分修复...")
    
    try:
        service_file = project_root / "src" / "services" / "table_data_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查is_preload参数
        if "is_preload: bool = False" in content:
            logger.info("✅ is_preload参数已添加")
        else:
            logger.error("❌ is_preload参数未添加")
            return False
        
        # 检查预加载访问不计入统计
        if "if not is_preload:" in content and "只有用户主动访问才计入统计" in content:
            logger.info("✅ 预加载访问区分逻辑已添加")
        else:
            logger.error("❌ 预加载访问区分逻辑未添加")
            return False
        
        # 检查预加载调用传递is_preload=True
        if "is_preload=True" in content:
            logger.info("✅ 预加载调用已正确传递is_preload参数")
        else:
            logger.error("❌ 预加载调用未正确传递is_preload参数")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 访问类型区分测试失败: {e}")
        return False

def test_event_publishing_fix():
    """测试事件发布修复"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试事件发布修复...")
    
    try:
        service_file = project_root / "src" / "services" / "table_data_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查预加载时不发布事件
        if "预加载时不发布数据更新事件，避免UI跳转" in content:
            logger.info("✅ 预加载事件发布控制已添加")
        else:
            logger.error("❌ 预加载事件发布控制未添加")
            return False
        
        # 检查缓存事件发布控制
        if "预加载缓存命中，不发布UI更新事件" in content:
            logger.info("✅ 预加载缓存事件发布控制已添加")
        else:
            logger.error("❌ 预加载缓存事件发布控制未添加")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 事件发布修复测试失败: {e}")
        return False

def test_thread_pool_limit():
    """测试线程池限制修复"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试线程池限制修复...")
    
    try:
        service_file = project_root / "src" / "services" / "table_data_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查线程池大小限制
        if "max_workers=1" in content and "限制为1个线程避免并发预加载" in content:
            logger.info("✅ 线程池大小已正确限制为1")
        else:
            logger.error("❌ 线程池大小未正确限制")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 线程池限制测试失败: {e}")
        return False

def test_code_consistency():
    """测试代码一致性"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 [测试] 开始测试代码一致性...")
    
    try:
        service_file = project_root / "src" / "services" / "table_data_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查P0-CRITICAL修复标记
        critical_fixes = content.count("P0-CRITICAL修复")
        if critical_fixes >= 5:
            logger.info(f"✅ 发现 {critical_fixes} 个P0-CRITICAL修复标记")
        else:
            logger.warning(f"⚠️ 只发现 {critical_fixes} 个P0-CRITICAL修复标记，可能有遗漏")
        
        # 检查语法错误
        try:
            compile(content, service_file, 'exec')
            logger.info("✅ 代码语法检查通过")
        except SyntaxError as e:
            logger.error(f"❌ 代码语法错误: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 代码一致性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_test_logging()
    logger.info("🔧 [开始] 页面跳动问题修复验证测试")
    logger.info("=" * 60)
    
    tests = [
        ("预加载阈值修复测试", test_preload_threshold),
        ("访问类型区分修复测试", test_access_type_distinction),
        ("事件发布修复测试", test_event_publishing_fix),
        ("线程池限制修复测试", test_thread_pool_limit),
        ("代码一致性测试", test_code_consistency)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n🔧 [执行] {test_name}")
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 失败")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            failed += 1
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🔧 [结果] 通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        logger.info("🎉 [成功] 所有P0修复验证都通过！")
        logger.info("\n📋 [建议] 现在可以启动系统测试页面跳动问题：")
        logger.info("  1. 启动系统: python src/main.py")
        logger.info("  2. 切换到'全部在职人员工资表'")
        logger.info("  3. 验证页面不再跳动")
        logger.info("  4. 测试正常的分页功能")
        return True
    else:
        logger.error(f"⚠️ [警告] 有 {failed} 个验证失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
