# 统一格式管理系统配置文件说明

## 📋 配置文件概览

统一格式管理系统使用两个核心配置文件：

1. **格式配置文件**: `state/data/format_config.json`
2. **字段映射文件**: `state/data/field_mappings.json`

这两个文件共同定义了整个系统的格式化规则和字段映射关系。

## 🔧 格式配置文件 (format_config.json)

### 文件结构

```json
{
  "metadata": {
    "version": "1.0.0",
    "created_at": "2025-07-18T10:00:00.000000",
    "description": "统一格式管理系统配置文件",
    "last_modified": "2025-07-18T10:00:00.000000"
  },
  "table_types": { ... },
  "default_formats": { ... },
  "field_type_rules": { ... },
  "validation_rules": { ... },
  "display_rules": { ... },
  "export_settings": { ... },
  "localization": { ... },
  "performance_settings": { ... },
  "advanced_rules": { ... }
}
```

### 详细配置说明

#### 1. 元数据配置 (metadata)

```json
{
  "metadata": {
    "version": "1.0.0",                                    // 配置文件版本
    "created_at": "2025-07-18T10:00:00.000000",           // 创建时间
    "description": "统一格式管理系统配置文件",              // 描述
    "last_modified": "2025-07-18T10:00:00.000000"         // 最后修改时间
  }
}
```

#### 2. 表格类型配置 (table_types)

```json
{
  "table_types": {
    "active_employees": {
      "display_name": "全部在职人员工资表",                 // 显示名称
      "description": "包含所有在职员工的工资数据",           // 描述
      "priority": 1,                                      // 优先级
      "enabled": true,                                    // 是否启用
      "default_sort": ["employee_id", "asc"],             // 默认排序
      "export_template": "active_employees_template.xlsx"  // 导出模板
    },
    "retired_employees": {
      "display_name": "离休人员工资表",
      "description": "离休员工的工资数据",
      "priority": 2,
      "enabled": true,
      "default_sort": ["employee_id", "asc"],
      "export_template": "retired_employees_template.xlsx"
    }
  }
}
```

#### 3. 默认格式配置 (default_formats)

```json
{
  "default_formats": {
    "currency": {
      "decimal_places": 2,                    // 小数位数
      "thousand_separator": ",",              // 千分位分隔符
      "symbol": "¥",                         // 货币符号
      "symbol_position": "prefix",            // 符号位置 (prefix/suffix)
      "negative_format": "-{symbol}{value}",  // 负数格式
      "zero_display": "0.00",                // 零值显示
      "description": "货币格式，用于工资、津贴、奖金等金额字段"
    },
    "percentage": {
      "decimal_places": 1,                    // 小数位数
      "symbol": "%",                          // 百分比符号
      "symbol_position": "suffix",            // 符号位置
      "multiply_by_100": false,               // 是否乘以100
      "zero_display": "0.0%",                // 零值显示
      "description": "百分比格式，用于增长率、完成率等"
    },
    "date": {
      "input_format": "%Y-%m-%d",            // 输入格式
      "display_format": "%Y年%m月%d日",       // 显示格式
      "short_format": "%Y-%m-%d",            // 简短格式
      "zero_display": "",                     // 空值显示
      "description": "日期格式，用于入职日期、生日等"
    }
  }
}
```

#### 4. 字段类型规则 (field_type_rules)

```json
{
  "field_type_rules": {
    "currency_fields": [
      "position_salary_2025",
      "grade_salary_2025",
      "allowance",
      "balance_allowance",
      "basic_performance_2025",
      "performance_bonus_2025",
      "total_salary"
    ],
    "integer_fields": [
      "employee_id",
      "year",
      "month",
      "employee_type_code"
    ],
    "string_fields": [
      "employee_name",
      "department",
      "employee_type",
      "position",
      "remarks"
    ],
    "date_fields": [
      "entry_date",
      "retirement_date",
      "birth_date"
    ]
  }
}
```

#### 5. 验证规则 (validation_rules)

```json
{
  "validation_rules": {
    "required_fields": {
      "active_employees": ["employee_id", "employee_name"],
      "retired_employees": ["employee_id", "employee_name"]
    },
    "data_ranges": {
      "salary_min": 0,
      "salary_max": 1000000,
      "year_min": 2020,
      "year_max": 2030
    },
    "string_patterns": {
      "employee_id": "^[A-Za-z0-9]{6,20}$",
      "employee_name": "^[\\u4e00-\\u9fa5A-Za-z\\s]{2,50}$"
    }
  }
}
```

#### 6. 显示规则 (display_rules)

```json
{
  "display_rules": {
    "column_width": {
      "min_width": 80,                        // 最小宽度
      "max_width": 300,                       // 最大宽度
      "auto_resize": true,                    // 自动调整
      "default_widths": {
        "employee_id": 100,
        "employee_name": 120,
        "department": 150,
        "salary_fields": 120
      }
    },
    "color_scheme": {
      "header_bg": "#f0f0f0",               // 表头背景色
      "row_alternate": "#f9f9f9",           // 交替行背景色
      "currency_positive": "#000000",        // 正数颜色
      "currency_negative": "#d32f2f"         // 负数颜色
    },
    "font_settings": {
      "family": "微软雅黑",                  // 字体家族
      "size": 9,                            // 字体大小
      "bold_headers": true                   // 表头加粗
    }
  }
}
```

### 配置文件使用示例

#### 修改货币格式小数位数

```json
{
  "default_formats": {
    "currency": {
      "decimal_places": 3,  // 改为3位小数
      "thousand_separator": ",",
      "symbol": "¥"
    }
  }
}
```

#### 添加新的表格类型

```json
{
  "table_types": {
    "intern_employees": {
      "display_name": "实习生工资表",
      "description": "实习生的工资数据",
      "priority": 5,
      "enabled": true,
      "default_sort": ["employee_id", "asc"]
    }
  }
}
```

## 🗂️ 字段映射文件 (field_mappings.json)

### 文件结构

```json
{
  "metadata": {
    "version": "1.0.0",
    "created_at": "2025-07-18T10:00:00.000000",
    "description": "字段映射配置文件",
    "last_modified": "2025-07-18T10:00:00.000000"
  },
  "table_mappings": { ... },
  "global_aliases": { ... },
  "field_patterns": { ... },
  "reverse_mappings": { ... },
  "validation_templates": { ... },
  "import_templates": { ... }
}
```

### 详细配置说明

#### 1. 表格映射配置 (table_mappings)

```json
{
  "table_mappings": {
    "active_employees": {
      "display_name": "全部在职人员工资表",
      "description": "在职员工工资数据表",
      "field_mappings": {
        "employee_id": "工号",                        // 数据库字段 → 显示名称
        "employee_name": "姓名",
        "department": "部门名称",
        "position_salary_2025": "2025年岗位工资",
        "grade_salary_2025": "2025年薪级工资",
        "total_salary": "应发工资"
      },
      "field_types": {
        "employee_id": "string",                     // 字段类型定义
        "employee_name": "string",
        "position_salary_2025": "currency",
        "grade_salary_2025": "currency",
        "total_salary": "currency"
      },
      "required_fields": ["employee_id", "employee_name"],  // 必需字段
      "primary_key": "employee_id",                          // 主键
      "sort_fields": ["employee_id", "employee_name"],       // 可排序字段
      "searchable_fields": ["employee_id", "employee_name"]  // 可搜索字段
    }
  }
}
```

#### 2. 全局别名配置 (global_aliases)

```json
{
  "global_aliases": {
    "id": "employee_id",          // 别名 → 标准字段名
    "name": "employee_name",
    "dept": "department",
    "salary": "total_salary",
    "工资": "total_salary",
    "工号": "employee_id",
    "姓名": "employee_name",
    "部门": "department"
  }
}
```

#### 3. 字段模式配置 (field_patterns)

```json
{
  "field_patterns": {
    "salary_patterns": [
      ".*salary.*",
      ".*工资.*",
      ".*薪.*",
      ".*wage.*",
      ".*pay.*"
    ],
    "allowance_patterns": [
      ".*allowance.*",
      ".*津贴.*",
      ".*补贴.*",
      ".*subsidy.*"
    ],
    "bonus_patterns": [
      ".*bonus.*",
      ".*奖金.*",
      ".*绩效.*",
      ".*incentive.*"
    ]
  }
}
```

#### 4. 反向映射配置 (reverse_mappings)

```json
{
  "reverse_mappings": {
    "active_employees": {
      "工号": "employee_id",          // 显示名称 → 数据库字段
      "姓名": "employee_name",
      "部门名称": "department",
      "2025年岗位工资": "position_salary_2025",
      "应发工资": "total_salary"
    }
  }
}
```

### 字段映射使用示例

#### 添加新字段映射

```json
{
  "table_mappings": {
    "active_employees": {
      "field_mappings": {
        "new_field": "新字段显示名",
        "bonus_2025": "2025年奖金"
      },
      "field_types": {
        "new_field": "string",
        "bonus_2025": "currency"
      }
    }
  }
}
```

#### 修改字段显示名称

```json
{
  "table_mappings": {
    "active_employees": {
      "field_mappings": {
        "employee_id": "员工编号",     // 从"工号"改为"员工编号"
        "total_salary": "总工资"      // 从"应发工资"改为"总工资"
      }
    }
  }
}
```

## 🔄 配置文件管理

### 1. 配置文件备份

系统会自动备份配置文件：

```
state/data/backups/
├── format_config_20250718_100000.json
├── format_config_20250718_110000.json
├── field_mappings_20250718_100000.json
└── field_mappings_20250718_110000.json
```

### 2. 配置文件验证

**格式验证**:
- JSON格式正确性
- 必需字段完整性
- 数据类型正确性
- 引用关系一致性

**验证错误处理**:
```json
{
  "validation_errors": [
    "缺少必需字段: table_types",
    "字段类型错误: default_formats.currency.decimal_places 应为整数",
    "引用不存在: field_mappings.unknown_table"
  ]
}
```

### 3. 配置文件同步

**更新流程**:
1. 修改配置文件
2. 系统自动检测文件变化
3. 重新加载配置
4. 清除相关缓存
5. 通知相关组件更新

## 🔧 常见配置场景

### 1. 添加新的货币格式

```json
{
  "default_formats": {
    "usd_currency": {
      "decimal_places": 2,
      "thousand_separator": ",",
      "symbol": "$",
      "symbol_position": "prefix",
      "negative_format": "-{symbol}{value}",
      "zero_display": "$0.00"
    }
  }
}
```

### 2. 支持多语言显示

```json
{
  "localization": {
    "zh_CN": {
      "currency_symbol": "¥",
      "date_format": "%Y年%m月%d日",
      "decimal_separator": ".",
      "thousands_separator": ","
    },
    "en_US": {
      "currency_symbol": "$",
      "date_format": "%Y-%m-%d",
      "decimal_separator": ".",
      "thousands_separator": ","
    }
  }
}
```

### 3. 条件格式化配置

```json
{
  "conditional_formatting": {
    "salary_ranges": {
      "low": {"max": 5000, "color": "#ffcdd2", "font_weight": "normal"},
      "medium": {"min": 5000, "max": 15000, "color": "#fff3e0", "font_weight": "normal"},
      "high": {"min": 15000, "color": "#e8f5e8", "font_weight": "bold"}
    },
    "warning_rules": {
      "missing_data": {"color": "#fff3cd", "background": "#ffeaa7"},
      "invalid_data": {"color": "#f8d7da", "background": "#f5c6cb"}
    }
  }
}
```

### 4. 自定义验证规则

```json
{
  "validation_rules": {
    "custom_validations": {
      "department_codes": {
        "pattern": "^[A-Z]{2}\\d{2}$",
        "message": "部门代码格式不正确，应为2个大写字母+2个数字"
      },
      "salary_ranges": {
        "min_value": 1000,
        "max_value": 50000,
        "message": "工资范围应在1000-50000之间"
      }
    }
  }
}
```

## ⚠️ 配置注意事项

### 1. 格式兼容性

- 修改配置时保持向后兼容
- 重要配置变更需要版本升级
- 删除配置项前确保无组件依赖

### 2. 性能影响

- 复杂的格式化规则会影响性能
- 过多的字段映射会增加内存占用
- 频繁的配置更新会影响缓存效率

### 3. 数据一致性

- 字段映射与数据库字段保持一致
- 字段类型与实际数据类型匹配
- 验证规则与业务逻辑一致

### 4. 错误处理

- 配置文件损坏时使用备份
- 格式化失败时降级到默认格式
- 字段映射缺失时保持原始名称

## 🚀 最佳实践

### 1. 配置管理

- 定期备份配置文件
- 使用版本控制管理配置变更
- 测试环境验证配置正确性
- 生产环境谨慎更新配置

### 2. 性能优化

- 合理使用缓存机制
- 避免过度复杂的格式化规则
- 定期清理无用的配置项
- 监控配置文件大小

### 3. 维护建议

- 定期审查配置文件内容
- 及时更新过期的配置项
- 保持配置文件的清晰结构
- 添加必要的注释和说明

---

通过合理配置这些文件，可以实现**统一、灵活、高效**的格式管理，满足各种业务需求的同时保持系统的稳定性和可维护性。