---
description: 
globs: 
alwaysApply: true
---
# python编码规范

## 源码位置
- 所有源码按其功能划分，统一放置在项目根目录下的src目录及其子目录中。
- 通用公共功能模块代码文件，统一放置在项目根目录下的src目录中的utils子目录。


## 日志规范
- 实现具体功能模块代码时，要按照项目日志规范统一管理。
- 日志统一存放在项目根目录下的logs目录中。
- 具体日志配置代码在项目根目录中src目录下utils子目录中的文件log_config.py里。（ @log_config.py ）
- 函数中涉及输入文件的，日志中要体现从哪个位置的文件读取。
- 函数中涉及输出保存文件的，日志中要体现文件被保存到哪个位置。


## 重要原则
### 真实性原则
- 要以项目现状为基础，进行功能模块的具体实现。
- 实现具体功能模块前，要先在项目根目录中查找并阅读文档 README.md 或 CHANGELOG.md ，如果不存在上述文档，要自行遍历阅读根目录下src目录及其子目录中源码文件，了解项目各个功能模块现状。理解这个项目的目标、架构、实现方式等。

### 严谨性原则
- 在没有明确提示的情况下，请勿更改代码。
- 每次对代码进行更改时，都必须充分了解其功能和上下文，以确保更改不会破坏现有逻辑。
- 对于不确定的部分，主动询问用户，而不是假设或猜测。

### 全局性原则
- 在编写或优化代码时，对项目逻辑有全面的了解非常重要。
- 确保代码与项目的整体架构、设计模式和业务逻辑一致。
- 避免局部优化导致全局问题。

### 优化意识
- 每次优化代码时，都必须考虑它对整个项目的影响。
- 优先解决性能瓶颈、代码冗余和潜在风险，而不是不必要的“完美”。
- 优化后，代码的可读性、可维护性和可扩展性不受影响。

### 代码质量
- 遵循最佳实践以确保一致的代码样式、命名约定和清晰的注释。
- 避免重复代码并明智地使用函数、类和模块。
- 对于关键逻辑和复杂部分，请添加必要的注释和文档。


## 功能模块编写要求
- 功能模块尽可能采用函数形式来实现。
- 功能模块的实现要一步一步来，一个功能模块完全完成，再开始下一个功能模块，避免生成很多没有完成的功能模块代码文件。
- 尽可能以功能相对独立的函数来实现，函数代码量尽可能不超过300行。
- 函数注释要规范，清晰准确，简明扼要，函数功能和参数说明不能有遗漏。
- 函数需要保存文件时，要在函数中添加开关选项，可以通过函数传参来进行控制。
- 函数如果涉及输入、输出目录，要给出参数默认值，目录要统一在函数头部设置，或通过参数传入，同时进行存在性验证，及跨系统的目录规范化处理。
- 在功能模块代码文件顶部要说明模块及其相关函数的功能。
- 统一管理日志，具体日志配置代码在项目根目录中src目录下utils子目录中的文件log_config.py里。
- 实现函数时，不要在函数中再定义子函数，要考虑未来的可扩展与可重用。
- 在实现某些功能时，可以根据实际情况，尝试采用装饰器来辅助增强。
- 引入自定义功能模块的包路径尽可能采用绝对路径。
