#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单最终测试 - 无Unicode字符版本
"""

import sys
import os
import pandas as pd
import numpy as np
sys.path.append('.')

def test_format_fixes():
    print("=== Final Format Verification Test ===")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.core.architecture_factory import get_architecture_factory
        
        # Initialize components
        config_manager = ConfigManager()
        db_manager = DynamicTableManager("data/salary_system.db")
        factory = get_architecture_factory(db_manager, config_manager)
        
        if not factory.initialize_architecture():
            print("FAILED: Architecture initialization")
            return False
        
        # Get unified format manager
        unified_format_manager = factory.get_unified_format_manager()
        print("SUCCESS: Got unified format manager")
        
        # Get format renderer
        format_renderer = unified_format_manager.format_renderer
        
        print("\n=== Problem 1: Employee ID Format ===")
        # Test removing .0 suffix from employee IDs
        test_ids = [19990089.0, 19990090.0]
        for emp_id in test_ids:
            result = format_renderer.render_value(emp_id, 'string', 'employee_id')
            print(f"Employee ID {emp_id} -> '{result}'")
            if ".0" not in str(result):
                print("OK: No .0 suffix found")
            else:
                print("PROBLEM: Still has .0 suffix")
        
        print("\n=== Problem 2: Null Values for Retired Employees ===")
        # Test null value display for retired employees
        test_values = [None, np.nan, "", 0, 0.0]
        
        for value in test_values:
            result = format_renderer.render_value(value, 'float', 'allowance_field', 'retired_employees')
            print(f"Retired employee null value {repr(value)} -> '{result}'")
            if result == "0.00":
                print("OK: Shows '0.00' for null value")
            elif result == "-":
                print("PROBLEM: Still shows '-' instead of '0.00'")
            else:
                print(f"INFO: Shows '{result}'")
        
        print("\n=== Problem 3: Month Field Extraction ===")
        # Test month string extraction
        test_months = ["202507", "202512", "25"]
        
        for month in test_months:
            result = format_renderer.render_value(month, 'month_string', 'month_field')
            expected = month[-2:] if len(month) >= 2 else month.zfill(2)
            print(f"Month '{month}' -> '{result}', expected: '{expected}'")
            if result == expected:
                print("OK: Month extraction correct")
            else:
                print("PROBLEM: Month extraction incorrect")
        
        print("\n=== Configuration Check ===")
        # Check retired employee configuration
        format_config = unified_format_manager.format_config
        retired_config = format_config.get_format_rules('float', 'retired_employees')
        
        print(f"Retired employee float config: {retired_config}")
        if retired_config and retired_config.get('null_display') == '0.00':
            print("OK: Configuration shows null_display as '0.00'")
        else:
            print("PROBLEM: Configuration null_display not set to '0.00'")
        
        print("\n=== Test Summary ===")
        print("All three problems tested:")
        print("1. Employee ID formatting (remove .0)")
        print("2. Retired employee null values (show 0.00)")
        print("3. Month field extraction (last 2 digits)")
        
        return True
        
    except Exception as e:
        print(f"FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_format_fixes()