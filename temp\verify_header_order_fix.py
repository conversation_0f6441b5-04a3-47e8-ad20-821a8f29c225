#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证表头顺序修复效果

检查修复后的配置文件中"人员类别"和"人员类别代码"的顺序是否正确
"""

import json
import sys
from pathlib import Path

def check_config_files():
    """检查配置文件中的字段顺序"""
    
    config_files = [
        "state/format_config.json",
        "state/data/format_config.json"
    ]
    
    print("[表头顺序修复验证]")
    print("=" * 50)
    
    for config_file in config_files:
        config_path = Path(config_file)
        if not config_path.exists():
            print(f"ERROR 配置文件不存在: {config_file}")
            continue
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查 active_employees_format_config.field_formats.string_fields
            string_fields = config.get("active_employees_format_config", {}).get("field_formats", {}).get("string_fields", [])
            
            print(f"\n配置文件: {config_file}")
            print(f"   string_fields 字段顺序:")
            
            for i, field in enumerate(string_fields):
                if "人员类别" in field:
                    print(f"   {i+1}. {field}")
            
            # 检查顺序是否正确
            if "人员类别" in string_fields and "人员类别代码" in string_fields:
                type_index = string_fields.index("人员类别")
                code_index = string_fields.index("人员类别代码")
                
                if type_index < code_index:
                    print(f"   PASS 顺序正确: 人员类别(位置{type_index+1}) -> 人员类别代码(位置{code_index+1})")
                else:
                    print(f"   FAIL 顺序错误: 人员类别代码(位置{code_index+1}) -> 人员类别(位置{type_index+1})")
            else:
                print(f"   WARNING 未找到相关字段")
                
        except Exception as e:
            print(f"ERROR 读取配置文件失败: {config_file}, 错误: {e}")
    
    print("\n[源代码验证]")
    print("检查 field_registry.py 中的 display_order 定义:")
    
    # 检查源代码中的定义
    registry_file = Path("src/modules/format_management/field_registry.py")
    if registry_file.exists():
        try:
            with open(registry_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找 active_employees 的 display_order
            import re
            pattern = r'"active_employees".*?"display_order":\s*\[(.*?)\]'
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                order_content = match.group(1)
                # 提取 employee_type 相关字段
                type_fields = []
                lines = order_content.split('\n')
                for line in lines:
                    if 'employee_type' in line:
                        fields = re.findall(r'"(employee_type[^"]*)"', line)
                        type_fields.extend(fields)
                
                print(f"   源代码中的顺序: {' -> '.join(type_fields)}")
                
                if type_fields == ['employee_type', 'employee_type_code']:
                    print(f"   PASS 源代码定义正确")
                else:
                    print(f"   FAIL 源代码定义可能有问题")
                    
        except Exception as e:
            print(f"ERROR 读取源代码失败: {e}")
    else:
        print(f"ERROR 源代码文件不存在")

if __name__ == "__main__":
    check_config_files()