#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版表头重影修复验证脚本
"""

import os
import sys
from pathlib import Path

def check_code_exists(file_path: str, search_text: str, description: str) -> bool:
    """检查代码中是否包含特定文本"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if search_text in content:
                print(f"[OK] {description}")
                return True
            else:
                print(f"[FAIL] {description}")
                return False
    except Exception as e:
        print(f"[ERROR] {description} - {e}")
        return False

def main():
    """主验证函数"""
    print("表头重影修复验证")
    print("=" * 50)
    
    project_root = Path(__file__).parent.parent
    
    # 检查关键修复点
    checks = [
        # P0修复
        (project_root / "src/gui/prototype/widgets/header_update_manager.py", 
         "智能强制更新(仅行号): 跳过空表头", 
         "P0: 空表头修复"),
        
        # P1修复  
        (project_root / "src/gui/prototype/widgets/virtualized_expandable_table.py",
         "P1-统一更新",
         "P1: 统一更新机制"),
        
        # P1重绘简化
        (project_root / "src/gui/table_header_manager.py",
         "P1-简化重绘",
         "P1: 重绘机制简化"),
        
        # P2信号修复
        (project_root / "src/gui/prototype/widgets/virtualized_expandable_table.py",
         "QueuedConnection",
         "P2: 信号连接修复"),
        
        # P3架构简化
        (project_root / "src/gui/table_header_manager.py",
         "P3-架构简化",
         "P3: 架构简化")
    ]
    
    passed = 0
    total = len(checks)
    
    for file_path, search_text, description in checks:
        if check_code_exists(str(file_path), search_text, description):
            passed += 1
    
    print("=" * 50)
    print(f"验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("所有关键修复点已实施!")
        print("建议运行实际应用测试重影问题是否解决")
        return True
    else:
        print("部分修复点需要检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)