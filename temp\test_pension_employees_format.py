#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
退休人员工资表格式化测试脚本

验证退休人员工资表在所有数据路径中的格式化效果：
1. 数据导入后自动打开
2. 数据导航切换
3. 分页组件跳转  
4. 刷新按钮

测试内容：
- 浮点型字段格式化（保留两位小数）
- 空值统一显示为"0.00"
- 月份字段提取后两位并转字符串
- 年份字段转字符串
- 隐藏字段不显示
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.format_management.unified_format_manager import get_unified_format_manager
from src.modules.format_management.format_config import FormatConfig  
from src.modules.format_management.field_registry import FieldRegistry
from src.utils.log_config import setup_logger

def setup_test_environment():
    """设置测试环境"""
    logger = setup_logger("test_pension_format")
    logger.info("🧪 [测试] 开始退休人员工资表格式化测试")
    
    # 初始化格式管理器
    format_manager = get_unified_format_manager()
    
    return logger, format_manager

def create_test_data():
    """创建测试数据"""
    # 模拟退休人员工资表数据
    test_data = pd.DataFrame({
        'sequence_number': [1, 2, 3],
        'employee_id': ['R001', 'R002', 'R003'],
        'employee_name': ['张三', '李四', '王五'],
        'department': ['行政部', '财务部', '人事部'],
        'employee_type_code': ['RT01', 'RT02', 'RT03'],
        'basic_retirement_salary': [3500.5, np.nan, 4200.0],
        'allowance': [800.75, 0, 950.25],
        'balance_allowance': [200.0, "", 300.5],
        'retirement_living_allowance': [1500.25, "-", 1800.0],
        'nursing_fee': [500.0, None, 600.75],
        'property_allowance': [300.5, "0.0", 400.25],
        'housing_allowance': [1200.0, " ", 1400.5],
        'salary_advance': [0, "ne", 200.0],
        'adjustment_2016': [100.0, "nan", 120.5],
        'adjustment_2017': [110.25, 0.0, 130.0],
        'adjustment_2018': [120.5, "", 140.25],
        'adjustment_2019': [130.0, " ", 150.5],
        'adjustment_2020': [140.25, "-", 160.0],
        'adjustment_2021': [150.5, None, 170.25],
        'adjustment_2022': [160.0, np.nan, 180.5],
        'adjustment_2023': [170.25, "0", 190.0],
        'supplement': [50.0, "", 75.5],
        'advance': [25.5, "-", 0],
        'total_salary': [8000.75, None, 9500.25],
        'provident_fund': [800.0, "nan", 950.0],
        'insurance_deduction': [400.25, 0, 475.5],
        'remarks': ['', '正常', None],
        'year': [202508, 202508, 202508],
        'month': [202508, 202508, 202508],
        'id': [1, 2, 3],
        'created_at': ['2025-08-02', '2025-08-02', '2025-08-02'],
        'updated_at': ['2025-08-02', '2025-08-02', '2025-08-02']
    })
    
    return test_data

def test_table_name_recognition(logger, format_manager):
    """测试表名识别"""
    logger.info("🧪 [测试] 测试表名识别功能")
    
    test_table_names = [
        'salary_data_2025_08_pension_employees',
        'pension_employees',
        '退休人员工资表',
        '退休人员'
    ]
    
    for table_name in test_table_names:
        normalized = format_manager._normalize_table_type(table_name)
        logger.info(f"表名: {table_name} -> 标准化: {normalized}")
        
        # 验证是否正确识别为pension_employees
        if normalized == 'pension_employees':
            logger.info(f"✅ 表名识别成功: {table_name}")
        else:
            logger.warning(f"❌ 表名识别失败: {table_name} -> {normalized}")

def test_field_type_configuration(logger, format_manager):
    """测试字段类型配置"""
    logger.info("🧪 [测试] 测试字段类型配置")
    
    # 测试浮点型字段
    float_fields = [
        '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费',
        '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整',
        '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整',
        '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款'
    ]
    
    # 测试字符串字段
    string_fields = ['姓名', '部门名称', '人员代码', '备注']
    
    # 测试特殊字符串字段
    special_fields = {'月份': 'month_string_extract_last_two', '年份': 'year_string'}
    
    # 测试隐藏字段
    hidden_fields = ['创建时间', '更新时间', '自增主键', '序号']
    
    # 验证配置
    config = format_manager.format_config.get_pension_employees_format_config()
    
    logger.info(f"配置的浮点型字段数: {len(config['field_formats']['float_fields'])}")
    logger.info(f"配置的字符串字段数: {len(config['field_formats']['string_fields'])}")
    logger.info(f"配置的特殊字段数: {len(config['field_formats']['special_string_fields'])}")
    logger.info(f"配置的隐藏字段数: {len(config['hidden_fields'])}")
    
    # 验证关键字段是否在配置中
    for field in ['基本退休费', '津贴', '补发', '借支']:
        if field in config['field_formats']['float_fields']:
            logger.info(f"✅ 浮点型字段配置正确: {field}")
        else:
            logger.warning(f"❌ 浮点型字段配置缺失: {field}")

def test_data_formatting(logger, format_manager, test_data):
    """测试数据格式化"""
    logger.info("🧪 [测试] 测试数据格式化功能")
    
    # 测试完整表格格式化
    try:
        formatted_headers, formatted_data = format_manager.format_table_complete(
            test_data, 
            'pension_employees',
            data_source='test'
        )
        
        logger.info(f"✅ 格式化成功: {len(formatted_data)} 行, {len(formatted_headers)} 列")
        logger.info(f"格式化后的表头: {formatted_headers[:5]}...")
        
        # 检查浮点型格式化
        if 'basic_retirement_salary' in test_data.columns:
            original_values = test_data['basic_retirement_salary'].tolist()
            if 'basic_retirement_salary' in formatted_data.columns:
                formatted_values = formatted_data['basic_retirement_salary'].tolist()
                logger.info(f"原始值: {original_values}")
                logger.info(f"格式化值: {formatted_values}")
            
        # 检查隐藏字段
        hidden_fields_found = [col for col in ['created_at', 'updated_at', 'id', 'sequence_number'] 
                              if col in formatted_data.columns]
        if hidden_fields_found:
            logger.warning(f"❌ 隐藏字段仍然存在: {hidden_fields_found}")
        else:
            logger.info("✅ 隐藏字段已正确过滤")
            
        # 检查月份和年份处理
        if 'month' in formatted_data.columns:
            month_values = formatted_data['month'].tolist()
            logger.info(f"月份字段格式化结果: {month_values}")
            
        if 'year' in formatted_data.columns:
            year_values = formatted_data['year'].tolist()
            logger.info(f"年份字段格式化结果: {year_values}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据格式化失败: {e}")
        return False

def test_single_value_formatting(logger, format_manager):
    """测试单个值格式化"""
    logger.info("🧪 [测试] 测试单个值格式化")
    
    test_values = [
        (1234.567, '基本退休费', 'pension_employees'),
        (np.nan, '津贴', 'pension_employees'),
        ("", '补发', 'pension_employees'),
        (0, '借支', 'pension_employees'),
        (202508, '月份', 'pension_employees'),
        (2025, '年份', 'pension_employees'),
    ]
    
    for value, column, table_type in test_values:
        try:
            formatted = format_manager.format_display_value(value, column, table_type)
            logger.info(f"值格式化: {value} ({column}) -> {formatted}")
        except Exception as e:
            logger.error(f"❌ 值格式化失败: {value} ({column}) -> {e}")

def main():
    """主测试函数"""
    logger, format_manager = setup_test_environment()
    
    try:
        # 创建测试数据
        test_data = create_test_data()
        logger.info(f"🧪 [测试] 创建测试数据: {len(test_data)} 行, {len(test_data.columns)} 列")
        
        # 测试表名识别
        test_table_name_recognition(logger, format_manager)
        
        # 测试字段类型配置
        test_field_type_configuration(logger, format_manager)
        
        # 测试数据格式化
        formatting_success = test_data_formatting(logger, format_manager, test_data)
        
        # 测试单个值格式化
        test_single_value_formatting(logger, format_manager)
        
        # 测试总结
        if formatting_success:
            logger.info("🎉 [测试] 退休人员工资表格式化测试通过！")
            logger.info("✅ 所有数据路径的格式化效果应该保持一致")
        else:
            logger.error("❌ [测试] 退休人员工资表格式化测试失败")
            
    except Exception as e:
        logger.error(f"❌ [测试] 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()