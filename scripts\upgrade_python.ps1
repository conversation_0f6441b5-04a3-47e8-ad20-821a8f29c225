# Python版本升级自动化脚本
# 用途: 自动化升级Python 3.9到Python 3.12的过程
# 作者: 系统管理员
# 日期: 2025-01-22

param(
    [string]$BackupPath = "salary_changes_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')",
    [switch]$SkipBackup = $false,
    [switch]$Force = $false
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 错误处理函数
function Handle-Error {
    param([string]$Message)
    Write-ColorOutput "错误: $Message" "Red"
    exit 1
}

# 成功提示函数
function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

# 警告提示函数
function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

Write-ColorOutput "=== Python版本升级脚本 ===" "Cyan"
Write-ColorOutput "升级目标: Python 3.9 → Python 3.12" "Cyan"
Write-ColorOutput "项目: 月度工资异动处理系统" "Cyan"
Write-ColorOutput ""

# 步骤1: 环境检查
Write-ColorOutput "步骤1: 环境检查" "Yellow"

# 检查当前Python版本
try {
    $currentPython = python --version 2>&1
    Write-ColorOutput "当前Python版本: $currentPython" "White"
} catch {
    Handle-Error "未找到Python安装"
}

# 检查是否在项目根目录
if (-not (Test-Path "main.py")) {
    Handle-Error "请在项目根目录运行此脚本"
}

# 步骤2: 备份项目
if (-not $SkipBackup) {
    Write-ColorOutput "步骤2: 备份项目" "Yellow"
    
    if (Test-Path $BackupPath) {
        if (-not $Force) {
            $response = Read-Host "备份目录已存在，是否覆盖? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                Handle-Error "用户取消备份"
            }
        }
        Remove-Item -Path $BackupPath -Recurse -Force
    }
    
    try {
        Copy-Item -Path "." -Destination $BackupPath -Recurse -Exclude @("*.pyc", "__pycache__", ".git", "dist", "build", "*.egg-info", "salary_system_env*")
        Write-Success "项目备份完成: $BackupPath"
    } catch {
        Handle-Error "项目备份失败: $_"
    }
} else {
    Write-Warning "跳过备份步骤"
}

# 步骤3: 导出当前依赖
Write-ColorOutput "步骤3: 导出当前依赖" "Yellow"
try {
    pip freeze > requirements_old_python39.txt
    Write-Success "依赖包列表已导出到 requirements_old_python39.txt"
} catch {
    Write-Warning "导出依赖失败，但继续执行"
}

# 步骤4: 清理旧环境
Write-ColorOutput "步骤4: 清理旧环境" "Yellow"

# 清理虚拟环境
$venvPaths = @("salary_system_env", "salary_system_env_py39", "venv", "env")
foreach ($venvPath in $venvPaths) {
    if (Test-Path $venvPath) {
        try {
            Remove-Item -Path $venvPath -Recurse -Force
            Write-Success "已删除虚拟环境: $venvPath"
        } catch {
            Write-Warning "删除虚拟环境失败: $venvPath"
        }
    }
}

# 清理构建文件
$buildPaths = @("build", "dist", "__pycache__", "*.egg-info")
foreach ($buildPath in $buildPaths) {
    if (Test-Path $buildPath) {
        try {
            Remove-Item -Path $buildPath -Recurse -Force
            Write-Success "已清理构建文件: $buildPath"
        } catch {
            Write-Warning "清理构建文件失败: $buildPath"
        }
    }
}

# 步骤5: 检查Python 3.12安装
Write-ColorOutput "步骤5: 检查Python 3.12安装" "Yellow"

# 检查Python 3.12是否已安装
$python312Found = $false
$pythonPaths = @("python", "python3", "python3.12", "py -3.12")

foreach ($pythonPath in $pythonPaths) {
    try {
        $version = & $pythonPath --version 2>&1
        if ($version -match "Python 3\.12") {
            Write-Success "找到Python 3.12: $version"
            $pythonExe = $pythonPath
            $python312Found = $true
            break
        }
    } catch {
        # 忽略错误，继续检查下一个路径
    }
}

if (-not $python312Found) {
    Write-Warning "未找到Python 3.12安装"
    Write-ColorOutput "请先安装Python 3.12，然后重新运行此脚本" "Yellow"
    Write-ColorOutput "下载地址: https://www.python.org/downloads/" "Yellow"
    
    $response = Read-Host "是否已经安装Python 3.12但未在PATH中? (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        $customPath = Read-Host "请输入Python 3.12可执行文件路径"
        if (Test-Path $customPath) {
            $pythonExe = $customPath
            $python312Found = $true
        } else {
            Handle-Error "指定的Python路径不存在"
        }
    } else {
        exit 1
    }
}

# 步骤6: 创建新虚拟环境
Write-ColorOutput "步骤6: 创建新虚拟环境" "Yellow"
try {
    & $pythonExe -m venv salary_system_env_py312
    Write-Success "虚拟环境创建成功"
} catch {
    Handle-Error "虚拟环境创建失败: $_"
}

# 步骤7: 激活虚拟环境并安装依赖
Write-ColorOutput "步骤7: 安装依赖包" "Yellow"

$activateScript = "salary_system_env_py312\Scripts\Activate.ps1"
if (-not (Test-Path $activateScript)) {
    Handle-Error "虚拟环境激活脚本不存在"
}

# 在虚拟环境中执行命令
$commands = @(
    "& '$activateScript'",
    "python -m pip install --upgrade pip",
    "pip install -r requirements.txt"
)

foreach ($command in $commands) {
    try {
        Write-ColorOutput "执行: $command" "Gray"
        Invoke-Expression $command
        Write-Success "命令执行成功"
    } catch {
        Handle-Error "命令执行失败: $command - $_"
    }
}

# 步骤8: 验证安装
Write-ColorOutput "步骤8: 验证安装" "Yellow"

$verifyScript = @"
& '$activateScript'
python -c "
import sys
print(f'Python版本: {sys.version}')
try:
    import PyQt5
    print('✓ PyQt5导入成功')
except ImportError as e:
    print(f'✗ PyQt5导入失败: {e}')

try:
    import pandas
    print('✓ pandas导入成功')
except ImportError as e:
    print(f'✗ pandas导入失败: {e}')

try:
    import openpyxl
    print('✓ openpyxl导入成功')
except ImportError as e:
    print(f'✗ openpyxl导入失败: {e}')

try:
    import loguru
    print('✓ loguru导入成功')
except ImportError as e:
    print(f'✗ loguru导入失败: {e}')

print('所有关键依赖检查完成')
"
"@

try {
    Invoke-Expression $verifyScript
    Write-Success "依赖验证完成"
} catch {
    Write-Warning "依赖验证出现问题: $_"
}

# 步骤9: 清理和总结
Write-ColorOutput "步骤9: 清理和总结" "Yellow"

Write-ColorOutput ""
Write-ColorOutput "=== 升级完成 ===" "Green"
Write-ColorOutput "Python版本已升级到3.12" "Green"
Write-ColorOutput "虚拟环境: salary_system_env_py312" "Green"
if (-not $SkipBackup) {
    Write-ColorOutput "项目备份: $BackupPath" "Green"
}
Write-ColorOutput ""

Write-ColorOutput "后续步骤:" "Yellow"
Write-ColorOutput "1. 激活虚拟环境: salary_system_env_py312\Scripts\activate" "White"
Write-ColorOutput "2. 运行主程序测试: python main.py" "White"
Write-ColorOutput "3. 运行单元测试: python -m pytest test/ -v" "White"
Write-ColorOutput "4. 测试PyInstaller打包: pyinstaller salary_changes_system.spec" "White"
Write-ColorOutput ""

Write-ColorOutput "如果遇到问题，请参考升级文档: docs/python_version_upgrade_guide.md" "Cyan"
Write-ColorOutput "升级脚本执行完成！" "Green" 