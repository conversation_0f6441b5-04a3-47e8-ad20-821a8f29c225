# 统一格式管理系统文档

## 📚 文档概览

本目录包含统一格式管理系统的完整文档，为系统的使用、维护和扩展提供详细指导。

## 📁 文档结构

| 文档名称 | 描述 | 主要内容 |
|---------|------|----------|
| [统一格式管理系统实现方案.md](./统一格式管理系统实现方案.md) | 🎯 **核心实现方案** | 需求分析、解决方案、核心组件、集成点、使用效果 |
| [系统架构设计文档.md](./系统架构设计文档.md) | 🏗️ **架构设计文档** | 系统架构、组件设计、数据流、扩展点、性能优化 |
| [配置文件说明文档.md](./配置文件说明文档.md) | 🔧 **配置文件说明** | 配置结构、参数说明、使用示例、最佳实践 |
| [使用指南和示例.md](./使用指南和示例.md) | 📖 **使用指南** | 快速开始、API说明、实际场景、常见问题 |

## 🎯 核心特性

### ✅ 已实现功能

- **🔄 统一格式化**: 所有数据路径统一格式化处理
- **📝 配置驱动**: 通过配置文件控制格式规则
- **🎨 多格式支持**: 支持货币、日期、数字、字符串等格式
- **🗂️ 字段映射**: 数据库字段名与显示名称智能映射
- **⚡ 性能优化**: 缓存机制、批量处理、懒加载
- **🛡️ 错误处理**: 自动降级、详细日志、异常恢复

### 🚀 核心优势

1. **一次配置，全局生效** - 格式变更只需修改配置文件
2. **完全向后兼容** - 与现有系统无缝集成
3. **高度可扩展** - 支持新表格类型和格式规则
4. **性能优化** - 智能缓存和批量处理
5. **错误容错** - 格式化失败时自动降级

## 🏗️ 系统架构

```
统一格式管理系统 (UnifiedFormatManager)
├── 格式配置层 (FormatConfig)
│   ├── 表格类型定义
│   ├── 格式规则配置
│   └── 验证规则管理
├── 字段注册层 (FieldRegistry)
│   ├── 字段映射管理
│   ├── 字段类型定义
│   └── 字段验证规则
└── 数据渲染层 (FormatRenderer)
    ├── 货币格式渲染
    ├── 日期格式渲染
    ├── 数字格式渲染
    └── 字符串格式渲染
```

## 🔧 快速开始

### 1. 基本使用

```python
from src.modules.format_management import UnifiedFormatManager

# 初始化格式管理器
format_manager = UnifiedFormatManager()

# 格式化完整表格
formatted_headers, formatted_data = format_manager.format_table_complete(
    data, 'active_employees'
)
```

### 2. 格式化效果

**输入数据**:
```python
data = [
    {'employee_id': 'E001', 'employee_name': '张三', 'total_salary': 12345.67},
    {'employee_id': 'E002', 'employee_name': '李四', 'total_salary': 23456.78}
]
```

**输出结果**:
```python
formatted_headers = ['工号', '姓名', '应发工资']
formatted_data = [
    {'工号': 'E001', '姓名': '张三', '应发工资': '¥12,345.67'},
    {'工号': 'E002', '姓名': '李四', '应发工资': '¥23,456.78'}
]
```

## 📊 支持的格式类型

| 格式类型 | 示例输入 | 示例输出 | 说明 |
|---------|----------|----------|------|
| **currency** | `12345.67` | `¥12,345.67` | 货币格式，支持千分位分隔符 |
| **date** | `2025-07-18` | `2025年07月18日` | 日期格式，支持中文显示 |
| **integer** | `1234567` | `1,234,567` | 整数格式，支持千分位分隔符 |
| **float** | `123.456` | `123.46` | 浮点数格式，支持小数位控制 |
| **percentage** | `0.1234` | `12.3%` | 百分比格式，支持自动转换 |
| **string** | `"  张三  "` | `张三` | 字符串格式，支持空白处理 |

## 🗂️ 支持的表格类型

| 表格类型 | 显示名称 | 主要字段 |
|---------|----------|----------|
| `active_employees` | 全部在职人员工资表 | 工号、姓名、岗位工资、薪级工资、应发工资 |
| `retired_employees` | 离休人员工资表 | 人员代码、姓名、基本工资、津贴、应发工资 |
| `part_time_employees` | 临时工工资表 | 工号、姓名、小时工资、工作小时、应发工资 |
| `contract_employees` | 合同工工资表 | 工号、姓名、基本工资、绩效奖金、应发工资 |

## 📁 文件结构

```
src/modules/format_management/
├── __init__.py                 # 模块初始化
├── unified_format_manager.py   # 统一格式管理器
├── format_config.py           # 格式配置管理
├── field_registry.py          # 字段注册系统
└── format_renderer.py         # 数据渲染器

state/data/
├── format_config.json         # 格式配置文件
├── field_mappings.json        # 字段映射文件
└── backups/                   # 配置备份目录
```

## 🔄 数据流向

```mermaid
graph TB
    A[原始数据] --> B[TableDataService]
    B --> C[UnifiedFormatManager]
    C --> D[FormatConfig]
    C --> E[FieldRegistry]
    C --> F[FormatRenderer]
    D --> G[格式规则]
    E --> H[字段映射]
    F --> I[数据渲染]
    G --> I
    H --> I
    I --> J[格式化数据]
    J --> K[VirtualizedExpandableTable]
    K --> L[统一显示]
```

## 🛠️ 配置管理

### 格式配置文件 (format_config.json)

```json
{
  "default_formats": {
    "currency": {
      "decimal_places": 2,
      "thousand_separator": ",",
      "symbol": "¥",
      "symbol_position": "prefix"
    },
    "date": {
      "display_format": "%Y年%m月%d日"
    }
  }
}
```

### 字段映射文件 (field_mappings.json)

```json
{
  "table_mappings": {
    "active_employees": {
      "field_mappings": {
        "employee_id": "工号",
        "employee_name": "姓名",
        "total_salary": "应发工资"
      },
      "field_types": {
        "employee_id": "string",
        "total_salary": "currency"
      }
    }
  }
}
```

## 🔧 常见操作

### 1. 修改格式配置

```python
# 修改货币格式小数位数
format_manager.update_format_config(
    'default_formats.currency.decimal_places', 
    3
)

# 保存配置
format_manager.format_config.save_config()
```

### 2. 更新字段映射

```python
# 更新字段映射
format_manager.update_field_mapping(
    'active_employees',
    {'new_field': '新字段显示名'}
)

# 保存映射
format_manager.field_registry.save_mappings()
```

### 3. 添加新表格类型

```python
# 1. 在配置文件中添加表格类型定义
# 2. 在字段映射文件中添加字段映射
# 3. 使用新的表格类型进行格式化

formatted_headers, formatted_data = format_manager.format_table_complete(
    data, 'new_table_type'
)
```

## 🚀 最佳实践

### 1. 性能优化

- **使用缓存**: 系统自动缓存格式化结果
- **批量处理**: 对大数据量使用批量处理
- **异步处理**: 对耗时操作使用异步处理

### 2. 错误处理

- **自动降级**: 格式化失败时自动使用原始数据
- **详细日志**: 记录所有格式化操作和错误
- **配置验证**: 定期验证配置文件的正确性

### 3. 维护建议

- **定期备份**: 系统自动备份配置文件
- **版本控制**: 使用版本控制管理配置变更
- **测试验证**: 在测试环境验证配置变更

## 📋 集成点

### 1. 数据服务层

**位置**: `src/services/table_data_service.py`

```python
# 在数据加载时统一格式化
response = self.data_request_manager.request_table_data(request)
if response.success:
    formatted_headers, formatted_data = self.format_manager.format_table_complete(
        response.data, table_type
    )
    response.data = formatted_data
```

### 2. 表格组件层

**位置**: `src/gui/prototype/widgets/virtualized_expandable_table.py`

```python
# 在表格显示时统一格式化
def set_data(self, data, headers, table_name):
    table_type = self._extract_table_type_from_name(table_name)
    formatted_headers, formatted_data = self.format_manager.format_table_complete(
        pd.DataFrame(data), table_type
    )
    # 设置格式化后的数据
```

## 🎉 实现效果

### ✅ 解决的问题

1. **格式一致性** - 所有数据源的显示格式完全统一
2. **集中管理** - 格式变更只需在配置文件中修改一次
3. **向后兼容** - 与现有系统完全兼容，无需重构
4. **易于维护** - 新增格式规则只需一次配置

### 🎯 用户需求满足

> **"应该采用统一的格式管理（表头及其数据格式化），不管主界面列表展示区域的数据来自那条路径，展示出来的表头及其数据格式都是统一的，日后如果有新的格式变动，更改一次就能够统一所有路径来源数据的显示格式。"**

**✅ 完全满足**: 
- 🔄 统一入口: 所有数据路径都经过统一格式化
- 🎨 一致显示: 货币、日期、数字等格式完全统一
- 📝 配置驱动: 修改配置文件即可调整全系统格式
- 🔧 易于维护: 新增格式规则只需一次配置

## 📞 技术支持

如有技术问题或需要扩展功能，请参考：

1. 📖 [使用指南和示例](./使用指南和示例.md) - 详细的使用方法和示例
2. 🏗️ [系统架构设计文档](./系统架构设计文档.md) - 深入了解系统架构
3. 🔧 [配置文件说明文档](./配置文件说明文档.md) - 配置文件的详细说明
4. 🎯 [统一格式管理系统实现方案](./统一格式管理系统实现方案.md) - 完整的实现方案

---

**统一格式管理系统** 为工资数据管理提供了强大而灵活的格式化支持，确保了数据展示的专业性和一致性。通过配置驱动的架构设计，真正实现了**一次配置，全局生效**的目标。