"""
字段映射器

功能说明:
- 数据字段自动映射
- 字段名称标准化
- 字段类型转换
- 映射规则管理
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import re

from src.utils.log_config import setup_logger

class FieldMapper:
    """字段映射器
    
    提供数据字段的映射和转换功能
    """
    
    def __init__(self, mapping_rules: Optional[Dict[str, str]] = None):
        """初始化字段映射器
        
        Args:
            mapping_rules: 字段映射规则
        """
        self.logger = setup_logger(__name__)
        self.mapping_rules = mapping_rules or {}
        
        # 默认映射规则
        self.default_mappings = {
            '工号': ['employee_id', 'emp_id', '员工号'],
            '姓名': ['name', 'emp_name', '员工姓名'],
            '身份证号': ['id_card', 'identity_card', '身份证'],
            '基本工资': ['basic_salary', 'base_salary', '基础工资'],
            '手机号': ['phone', 'mobile', '联系电话']
        }
        
    def map_fields(self, df: pd.DataFrame, 
                   target_fields: Optional[List[str]] = None) -> pd.DataFrame:
        """映射字段
        
        Args:
            df: 原始DataFrame
            target_fields: 目标字段列表
            
        Returns:
            映射后的DataFrame
        """
        if df.empty:
            return df
            
        mapped_df = df.copy()
        
        # 标准化列名
        mapped_df.columns = self._normalize_column_names(df.columns)
        
        self.logger.info(f"字段映射完成: {len(mapped_df.columns)}个字段")
        return mapped_df
        
    def _normalize_column_names(self, columns: List[str]) -> List[str]:
        """标准化列名
        
        Args:
            columns: 原始列名列表
            
        Returns:
            标准化后的列名列表
        """
        normalized = []
        
        for col in columns:
            # 清理空白字符
            clean_col = str(col).strip()
            
            # 查找映射
            mapped_col = self._find_mapping(clean_col)
            normalized.append(mapped_col or clean_col)
            
        return normalized
        
    def _find_mapping(self, column_name: str) -> Optional[str]:
        """查找字段映射
        
        Args:
            column_name: 原始字段名
            
        Returns:
            映射后的字段名，如果没有找到则返回None
        """
        column_lower = column_name.lower()
        
        # 在默认映射中查找
        for target, aliases in self.default_mappings.items():
            if column_name in aliases or column_lower in [alias.lower() for alias in aliases]:
                return target
                
        # 在自定义映射中查找
        if column_name in self.mapping_rules:
            return self.mapping_rules[column_name]
            
        return None 