#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离休人员表结构升级脚本

功能：
1. 检测所有使用通用16字段结构的离休人员表
2. 为这些表添加专用字段，特别是"增发一次性生活补贴"字段
3. 更新字段映射配置
4. 保持数据完整性

使用方法：
python scripts/upgrade_retired_employee_tables.py
"""

import sys
import os
import sqlite3
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates

class RetiredEmployeeTableUpgrader:
    """离休人员表结构升级器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.db_path = project_root / "data" / "db" / "salary_system.db"
        self.field_mappings_path = project_root / "state" / "data" / "field_mappings.json"
        self.templates = SpecializedTableTemplates()
        
        # 获取标准离休人员表模板
        self.retired_template = self.templates.get_template("retired_employees")
        
        # 需要添加的专用字段（相对于通用16字段结构）
        self.additional_fields = [
            ("sequence_number", "INTEGER", "序号"),
            ("basic_retirement_salary", "REAL", "基本离休费"), 
            ("balance_allowance", "REAL", "结余津贴"),
            ("living_allowance", "REAL", "生活补贴"),
            ("housing_allowance", "REAL", "住房补贴"),
            ("property_allowance", "REAL", "物业补贴"),
            ("retirement_allowance", "REAL", "离休补贴"),
            ("nursing_fee", "REAL", "护理费"),
            ("one_time_living_allowance", "REAL", "增发一次性生活补贴"),  # 关键字段
            ("supplement", "REAL", "补发"),
            ("total", "REAL", "合计"),
            ("advance", "REAL", "借支"),
            ("remarks", "TEXT", "备注")
        ]
    
    def identify_retired_tables_to_upgrade(self):
        """识别需要升级的离休人员表"""
        retired_tables = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%retired_employees'")
            tables = cursor.fetchall()
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                # 检查表结构
                cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                # 检查是否缺少关键字段
                missing_key_fields = []
                for field_name, _, chinese_name in self.additional_fields:
                    if field_name not in column_names:
                        missing_key_fields.append((field_name, chinese_name))
                
                if missing_key_fields:
                    self.logger.info(f"发现需要升级的表: {table_name}")
                    self.logger.info(f"  缺少字段: {[f[1] for f in missing_key_fields]}")
                    
                    # 获取记录数
                    cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
                    record_count = cursor.fetchone()[0]
                    
                    retired_tables.append({
                        'table_name': table_name,
                        'current_columns': column_names,
                        'missing_fields': missing_key_fields,
                        'record_count': record_count
                    })
                else:
                    self.logger.info(f"表 {table_name} 已有完整的专用字段结构")
            
            conn.close()
            return retired_tables
            
        except Exception as e:
            self.logger.error(f"识别需要升级的表时发生错误: {e}")
            return []
    
    def backup_table(self, table_name: str) -> bool:
        """备份表数据"""
        try:
            backup_table_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建备份表
            cursor.execute(f"CREATE TABLE \"{backup_table_name}\" AS SELECT * FROM \"{table_name}\"")
            conn.commit()
            
            # 验证备份
            cursor.execute(f"SELECT COUNT(*) FROM \"{backup_table_name}\"")
            backup_count = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
            original_count = cursor.fetchone()[0]
            
            conn.close()
            
            if backup_count == original_count:
                self.logger.info(f"表 {table_name} 备份成功: {backup_table_name} ({backup_count} 条记录)")
                return True
            else:
                self.logger.error(f"表 {table_name} 备份验证失败: 原始{original_count}条，备份{backup_count}条")
                return False
                
        except Exception as e:
            self.logger.error(f"备份表 {table_name} 失败: {e}")
            return False
    
    def upgrade_table_structure(self, table_info: dict) -> bool:
        """升级表结构"""
        table_name = table_info['table_name']
        missing_fields = table_info['missing_fields']
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 为每个缺少的字段添加列
            for field_name, chinese_name in missing_fields:
                # 从模板中找到对应的字段定义
                field_def = None
                for template_field in self.retired_template:
                    if template_field.name == field_name:
                        field_def = template_field
                        break
                
                if field_def:
                    # 构建ALTER TABLE语句
                    sql = f"ALTER TABLE \"{table_name}\" ADD COLUMN \"{field_name}\" {field_def.type}"
                    if field_def.default is not None:
                        sql += f" DEFAULT {field_def.default}"
                    
                    cursor.execute(sql)
                    self.logger.info(f"为表 {table_name} 添加字段: {field_name} ({chinese_name})")
                else:
                    self.logger.warning(f"在模板中未找到字段定义: {field_name}")
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"升级表结构 {table_name} 失败: {e}")
            return False
    
    def update_field_mappings(self, table_name: str) -> bool:
        """更新字段映射配置"""
        try:
            # 读取当前字段映射配置
            if self.field_mappings_path.exists():
                with open(self.field_mappings_path, 'r', encoding='utf-8') as f:
                    mappings = json.load(f)
            else:
                mappings = {
                    "version": "2.0",
                    "last_updated": datetime.now().isoformat(),
                    "global_settings": {
                        "auto_generate_mappings": True,
                        "enable_smart_suggestions": True,
                        "save_edit_history": True,
                        "preserve_chinese_headers": True
                    },
                    "table_mappings": {},
                    "field_templates": {},
                    "user_preferences": {
                        "default_field_patterns": {},
                        "recent_edits": [],
                        "favorite_mappings": []
                    }
                }
            
            # 为表生成完整的字段映射
            field_mapping = {}
            for field_def in self.retired_template:
                # 根据字段名生成中文映射
                if field_def.name == "sequence_number":
                    field_mapping[field_def.name] = "序号"
                elif field_def.name == "employee_id":
                    field_mapping[field_def.name] = "人员代码"
                elif field_def.name == "employee_name":
                    field_mapping[field_def.name] = "姓名"
                elif field_def.name == "department":
                    field_mapping[field_def.name] = "部门名称"
                elif field_def.name == "basic_retirement_salary":
                    field_mapping[field_def.name] = "基本离休费"
                elif field_def.name == "balance_allowance":
                    field_mapping[field_def.name] = "结余津贴"
                elif field_def.name == "living_allowance":
                    field_mapping[field_def.name] = "生活补贴"
                elif field_def.name == "housing_allowance":
                    field_mapping[field_def.name] = "住房补贴"
                elif field_def.name == "property_allowance":
                    field_mapping[field_def.name] = "物业补贴"
                elif field_def.name == "retirement_allowance":
                    field_mapping[field_def.name] = "离休补贴"
                elif field_def.name == "nursing_fee":
                    field_mapping[field_def.name] = "护理费"
                elif field_def.name == "one_time_living_allowance":
                    field_mapping[field_def.name] = "增发一次性生活补贴"
                elif field_def.name == "supplement":
                    field_mapping[field_def.name] = "补发"
                elif field_def.name == "total":
                    field_mapping[field_def.name] = "合计"
                elif field_def.name == "advance":
                    field_mapping[field_def.name] = "借支"
                elif field_def.name == "remarks":
                    field_mapping[field_def.name] = "备注"
                else:
                    # 其他系统字段保持英文
                    field_mapping[field_def.name] = field_def.name
            
            # 更新表映射
            mappings["table_mappings"][table_name] = {
                "field_mappings": field_mapping,
                "original_excel_headers": field_mapping.copy(),  # 简化处理
                "metadata": {
                    "source": "table_structure_upgrade",
                    "auto_generated": True,
                    "user_modified": False,
                    "created_at": datetime.now().isoformat(),
                    "sheet_name": "离休人员工资表",
                    "has_chinese_headers": True,
                    "upgraded_from_generic": True
                }
            }
            
            # 更新模板
            if "离休人员工资表" not in mappings["field_templates"]:
                mappings["field_templates"]["离休人员工资表"] = {
                    k: v for k, v in field_mapping.items() 
                    if not k.startswith(('id', 'month', 'year', 'created_at', 'updated_at'))
                }
            
            # 保存配置
            mappings["last_updated"] = datetime.now().isoformat()
            
            with open(self.field_mappings_path, 'w', encoding='utf-8') as f:
                json.dump(mappings, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"字段映射配置已更新: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新字段映射配置失败: {e}")
            return False
    
    def verify_upgrade(self, table_name: str) -> bool:
        """验证升级结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 检查关键字段是否存在
            if "one_time_living_allowance" in column_names:
                self.logger.info(f"✓ 表 {table_name} 升级验证成功: 包含'增发一次性生活补贴'字段")
                
                # 检查记录数是否保持不变
                cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
                record_count = cursor.fetchone()[0]
                self.logger.info(f"✓ 表 {table_name} 数据完整性验证: {record_count} 条记录")
                
                conn.close()
                return True
            else:
                self.logger.error(f"✗ 表 {table_name} 升级验证失败: 缺少关键字段")
                conn.close()
                return False
                
        except Exception as e:
            self.logger.error(f"验证升级结果失败: {e}")
            return False
    
    def run_upgrade(self, dry_run: bool = False) -> dict:
        """执行升级过程"""
        results = {
            "total_tables": 0,
            "identified_tables": [],
            "upgraded_tables": [],
            "failed_tables": [],
            "skipped_tables": []
        }
        
        self.logger.info("=== 离休人员表结构升级开始 ===")
        
        # 1. 识别需要升级的表
        tables_to_upgrade = self.identify_retired_tables_to_upgrade()
        results["total_tables"] = len(tables_to_upgrade)
        results["identified_tables"] = [t['table_name'] for t in tables_to_upgrade]
        
        if not tables_to_upgrade:
            self.logger.info("所有离休人员表都已有完整的专用字段结构，无需升级")
            return results
        
        self.logger.info(f"发现 {len(tables_to_upgrade)} 个表需要升级")
        
        if dry_run:
            self.logger.info("*** 预演模式：只检查，不执行实际升级 ***")
            for table_info in tables_to_upgrade:
                self.logger.info(f"[预演] 将升级表: {table_info['table_name']}")
                self.logger.info(f"[预演] 将添加字段: {[f[1] for f in table_info['missing_fields']]}")
            return results
        
        # 2. 执行升级
        for table_info in tables_to_upgrade:
            table_name = table_info['table_name']
            self.logger.info(f"正在升级表: {table_name}")
            
            try:
                # 备份表
                if not self.backup_table(table_name):
                    self.logger.error(f"表 {table_name} 备份失败，跳过升级")
                    results["failed_tables"].append(table_name)
                    continue
                
                # 升级表结构
                if not self.upgrade_table_structure(table_info):
                    self.logger.error(f"表 {table_name} 结构升级失败")
                    results["failed_tables"].append(table_name)
                    continue
                
                # 更新字段映射
                if not self.update_field_mappings(table_name):
                    self.logger.warning(f"表 {table_name} 字段映射更新失败，但表结构升级成功")
                
                # 验证升级
                if self.verify_upgrade(table_name):
                    results["upgraded_tables"].append(table_name)
                    self.logger.info(f"✓ 表 {table_name} 升级成功")
                else:
                    results["failed_tables"].append(table_name)
                    self.logger.error(f"✗ 表 {table_name} 升级验证失败")
                
            except Exception as e:
                self.logger.error(f"升级表 {table_name} 时发生异常: {e}")
                results["failed_tables"].append(table_name)
        
        # 3. 输出升级结果
        self.logger.info("=== 离休人员表结构升级完成 ===")
        self.logger.info(f"总计识别表数: {results['total_tables']}")
        self.logger.info(f"成功升级: {len(results['upgraded_tables'])} 个")
        self.logger.info(f"升级失败: {len(results['failed_tables'])} 个")
        
        if results["upgraded_tables"]:
            self.logger.info(f"升级成功的表: {results['upgraded_tables']}")
        
        if results["failed_tables"]:
            self.logger.error(f"升级失败的表: {results['failed_tables']}")
        
        return results

def main():
    """主函数"""
    upgrader = RetiredEmployeeTableUpgrader()
    
    # 检查参数
    dry_run = "--dry-run" in sys.argv
    
    if dry_run:
        print("执行预演模式...")
    else:
        print("执行实际升级...")
    
    results = upgrader.run_upgrade(dry_run=dry_run)
    
    # 输出结果摘要
    print(f"\n=== 升级结果摘要 ===")
    print(f"总计表数: {results['total_tables']}")
    print(f"成功升级: {len(results['upgraded_tables'])}")
    print(f"升级失败: {len(results['failed_tables'])}")
    
    if results['upgraded_tables']:
        print(f"成功升级的表:")
        for table in results['upgraded_tables']:
            print(f"  - {table}")
    
    if results['failed_tables']:
        print(f"升级失败的表:")
        for table in results['failed_tables']:
            print(f"  - {table}")

if __name__ == "__main__":
    main()