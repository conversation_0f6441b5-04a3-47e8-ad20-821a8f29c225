#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段映射配置修复

验证字段映射配置不一致问题的修复
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.utils.log_config import setup_logger
from src.modules.format_management.field_registry import FieldRegistry
from src.core.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

logger = setup_logger("FieldMappingRepairTest")

def test_field_mapping_repair():
    """测试字段映射配置修复"""
    try:
        logger.info("开始测试字段映射配置修复...")
        
        # 初始化组件
        config_manager = ConfigManager()
        db_path = config_manager.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        dynamic_table_manager = DynamicTableManager(db_manager)
        
        # 初始化字段注册器
        mapping_path = "state/data/field_mappings.json"
        field_registry = FieldRegistry(mapping_path)
        
        logger.info("字段注册器初始化完成")
        
        # 获取字段映射（这会触发默认映射合并和验证）
        field_mappings = field_registry.get_field_mappings()
        
        # 检查table_mappings
        table_mappings = field_mappings.get("table_mappings", {})
        logger.info(f"找到表映射: {len(table_mappings)}个")
        
        # 检查每个表的配置
        issues_found = []
        issues_fixed = []
        
        for table_key, table_config in table_mappings.items():
            logger.info(f"检查表: {table_key}")
            
            # 检查existing_display_fields
            metadata = table_config.get('metadata', {})
            existing_display_fields = metadata.get('existing_display_fields', [])
            
            if not existing_display_fields:
                issues_found.append(f"表 {table_key} existing_display_fields为空")
                logger.warning(f"  ❌ existing_display_fields为空")
            else:
                issues_fixed.append(f"表 {table_key} existing_display_fields已正常: {len(existing_display_fields)}个字段")
                logger.info(f"  ✅ existing_display_fields: {len(existing_display_fields)}个字段")
                logger.debug(f"     字段列表: {existing_display_fields[:5]}...")
            
            # 检查字段类型定义一致性
            field_mappings_data = table_config.get('field_mappings', {})
            field_types = table_config.get('field_types', {})
            
            # 检查缺失的类型定义
            missing_types = set(field_mappings_data.keys()) - set(field_types.keys())
            if missing_types:
                issues_found.append(f"表 {table_key} 字段缺少类型定义: {missing_types}")
                logger.warning(f"  ❌ 缺少类型定义: {missing_types}")
            
            # 检查多余的类型定义
            extra_types = set(field_types.keys()) - set(field_mappings_data.keys())
            if extra_types:
                issues_found.append(f"表 {table_key} 多余的字段类型定义: {extra_types}")
                logger.warning(f"  ❌ 多余类型定义: {extra_types}")
            
            if not missing_types and not extra_types:
                issues_fixed.append(f"表 {table_key} 字段类型定义一致")
                logger.info(f"  ✅ 字段类型定义一致: {len(field_types)}个字段")
        
        # 总结结果
        logger.info("=" * 50)
        logger.info("字段映射配置检查结果:")
        logger.info(f"  发现问题: {len(issues_found)}个")
        logger.info(f"  已修复问题: {len(issues_fixed)}个")
        
        if issues_found:
            logger.warning("尚未修复的问题:")
            for issue in issues_found[:10]:  # 只显示前10个
                logger.warning(f"  - {issue}")
        
        if issues_fixed:
            logger.info("已修复的配置:")
            for fix in issues_fixed[:10]:  # 只显示前10个
                logger.info(f"  + {fix}")
        
        # 触发智能修复
        logger.info("触发智能配置修复...")
        field_registry._apply_smart_config_repair_strategy()
        
        return len(issues_found) == 0
        
    except Exception as e:
        logger.error(f"字段映射配置修复测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔧 开始字段映射配置修复测试")
    logger.info("=" * 60)
    
    result = test_field_mapping_repair()
    
    logger.info("=" * 60)
    logger.info("测试结果:")
    if result:
        logger.info("✅ 字段映射配置检查通过")
    else:
        logger.warning("❌ 发现字段映射配置问题")
        logger.info("建议:")
        logger.info("  1. 检查字段映射配置文件")
        logger.info("  2. 运行智能修复策略")
        logger.info("  3. 重新启动系统")
    
    return result

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)