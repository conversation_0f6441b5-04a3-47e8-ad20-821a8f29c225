"""
月度工资异动处理系统 - 异动识别模块

本模块是系统的核心业务逻辑，负责：
- 基准工资数据建立和维护
- 异动类型识别算法实现
- 异动金额计算和验证  
- 异动原因分析和记录

核心组件：
- baseline_manager: 基准工资数据管理器
- change_detector: 异动识别器
- amount_calculator: 金额计算器
- reason_analyzer: 原因分析器

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

from .baseline_manager import BaselineManager
from .change_detector import ChangeDetector  
from .amount_calculator import AmountCalculator
from .reason_analyzer import ReasonAnalyzer

__all__ = [
    'BaselineManager',
    'ChangeDetector', 
    'AmountCalculator',
    'ReasonAnalyzer'
] 