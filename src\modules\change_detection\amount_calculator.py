"""
月度工资异动处理系统 - 异动金额计算器

本模块负责：
- 异动金额的精确计算
- 多种计算规则的支持
- 计算结果的验证
- 计算过程的审计追踪

支持的计算场景：
1. 工资调整金额计算
2. 津贴变动金额计算
3. 补发工资金额计算
4. 扣款金额计算
5. 复合异动金额计算

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, date
from decimal import Decimal, ROUND_HALF_UP
import math

from src.utils.log_config import get_module_logger
from src.core.config_manager import ConfigManager

logger = get_module_logger(__name__)


class CalculationRule:
    """计算规则类"""
    
    def __init__(self, name: str, formula: str, description: str):
        self.name = name
        self.formula = formula
        self.description = description
        self.created_time = datetime.now()


class AmountCalculator:
    """异动金额计算器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化异动金额计算器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.calculation_rules: Dict[str, CalculationRule] = {}
        self.calculation_history: List[Dict[str, Any]] = []
        
        # 计算精度设置
        self.decimal_places = 2  # 保留小数位数
        self.rounding_method = ROUND_HALF_UP  # 四舍五入方法
        
        # 初始化默认计算规则
        self._init_default_rules()
        
        logger.info("异动金额计算器初始化完成")
    
    def calculate_change_amount(
        self,
        change_record: Dict[str, Any],
        baseline_data: Optional[Dict[str, Any]] = None,
        current_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        计算异动金额
        
        Args:
            change_record: 异动记录
            baseline_data: 基准数据
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            change_type = change_record.get('change_type', '')
            employee_id = change_record.get('employee_id', '')
            
            logger.info(f"开始计算异动金额，员工: {employee_id}, 异动类型: {change_type}")
            
            # 根据异动类型选择计算方法
            if change_type == "工资调整":
                result = self._calculate_salary_adjustment(change_record, baseline_data, current_data)
            elif change_type == "津贴变动":
                result = self._calculate_allowance_change(change_record, baseline_data, current_data)
            elif change_type == "新进人员":
                result = self._calculate_new_employee_amount(change_record, current_data)
            elif change_type == "补发工资":
                result = self._calculate_supplementary_amount(change_record, current_data)
            elif change_type == "扣款异动":
                result = self._calculate_deduction_amount(change_record, current_data)
            elif change_type == "职务变动":
                result = self._calculate_position_change_amount(change_record, baseline_data, current_data)
            else:
                result = self._calculate_other_change_amount(change_record, baseline_data, current_data)
            
            # 记录计算历史
            self._record_calculation(change_record, result)
            
            logger.info(f"异动金额计算完成，结果: {result.get('total_amount', 0):.2f}")
            return result
            
        except Exception as e:
            logger.error(f"异动金额计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def batch_calculate_amounts(
        self,
        change_records: List[Dict[str, Any]],
        baseline_data_dict: Optional[Dict[str, Dict[str, Any]]] = None,
        current_data_dict: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> List[Dict[str, Any]]:
        """
        批量计算异动金额
        
        Args:
            change_records: 异动记录列表
            baseline_data_dict: 基准数据字典 {employee_id: data}
            current_data_dict: 当前数据字典 {employee_id: data}
            
        Returns:
            List[Dict[str, Any]]: 计算结果列表
        """
        try:
            logger.info(f"开始批量计算异动金额，共 {len(change_records)} 条记录")
            
            results = []
            for i, change_record in enumerate(change_records):
                employee_id = change_record.get('employee_id', '')
                
                # 获取对应的基准数据和当前数据
                baseline_data = baseline_data_dict.get(employee_id) if baseline_data_dict else None
                current_data = current_data_dict.get(employee_id) if current_data_dict else None
                
                # 计算单个异动金额
                result = self.calculate_change_amount(change_record, baseline_data, current_data)
                results.append(result)
                
                # 记录进度
                if (i + 1) % 100 == 0:
                    logger.info(f"批量计算进度: {i + 1}/{len(change_records)}")
            
            logger.info(f"批量计算完成，成功计算 {len(results)} 条记录")
            return results
            
        except Exception as e:
            logger.error(f"批量计算异动金额失败: {str(e)}")
            return []
    
    def validate_calculation_result(
        self,
        calculation_result: Dict[str, Any],
        validation_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        验证计算结果
        
        Args:
            calculation_result: 计算结果
            validation_rules: 验证规则
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            validation_result = {
                "is_valid": True,
                "validation_errors": [],
                "validation_warnings": [],
                "validated_time": datetime.now()
            }
            
            total_amount = calculation_result.get('total_amount', 0)
            
            # 基本数值验证
            if not isinstance(total_amount, (int, float, Decimal)):
                validation_result["is_valid"] = False
                validation_result["validation_errors"].append("总金额不是有效数值")
            
            # 金额范围验证
            if abs(total_amount) > 100000:  # 10万元
                validation_result["validation_warnings"].append(f"异动金额较大: {total_amount:.2f}")
            
            # 精度验证
            decimal_amount = Decimal(str(total_amount))
            if decimal_amount.as_tuple().exponent < -self.decimal_places:
                validation_result["validation_warnings"].append("金额精度超过设定的小数位数")
            
            # 自定义验证规则
            if validation_rules:
                custom_validation = self._apply_custom_validation(calculation_result, validation_rules)
                validation_result.update(custom_validation)
            
            logger.info(f"计算结果验证完成，有效性: {validation_result['is_valid']}")
            return validation_result
            
        except Exception as e:
            logger.error(f"验证计算结果失败: {str(e)}")
            return {"is_valid": False, "validation_errors": [str(e)]}
    
    def get_calculation_summary(self) -> Dict[str, Any]:
        """
        获取计算统计摘要
        
        Returns:
            Dict[str, Any]: 计算统计信息
        """
        try:
            if not self.calculation_history:
                return {"message": "无计算历史记录"}
            
            # 统计计算次数
            total_calculations = len(self.calculation_history)
            
            # 按异动类型统计
            type_counts = {}
            total_amounts = {}
            for record in self.calculation_history:
                change_type = record.get('change_type', '未知')
                amount = record.get('total_amount', 0)
                
                type_counts[change_type] = type_counts.get(change_type, 0) + 1
                total_amounts[change_type] = total_amounts.get(change_type, 0) + amount
            
            # 计算总金额统计
            all_amounts = [record.get('total_amount', 0) for record in self.calculation_history]
            amount_stats = {
                "总计算次数": total_calculations,
                "总金额": round(sum(all_amounts), 2),
                "平均金额": round(sum(all_amounts) / len(all_amounts), 2) if all_amounts else 0,
                "最大金额": round(max(all_amounts), 2) if all_amounts else 0,
                "最小金额": round(min(all_amounts), 2) if all_amounts else 0
            }
            
            summary = {
                "计算统计": amount_stats,
                "异动类型分布": type_counts,
                "类型金额汇总": {k: round(v, 2) for k, v in total_amounts.items()},
                "统计时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            logger.info("计算统计摘要生成完成")
            return summary
            
        except Exception as e:
            logger.error(f"生成计算统计摘要失败: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_salary_adjustment(
        self,
        change_record: Dict[str, Any],
        baseline_data: Optional[Dict[str, Any]],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算工资调整金额
        
        Args:
            change_record: 异动记录
            baseline_data: 基准数据
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            details = change_record.get('details', {})
            field = details.get('field', '')
            baseline_value = details.get('baseline_value', 0)
            current_value = details.get('current_value', 0)
            
            # 计算变动金额
            change_amount = current_value - baseline_value
            
            # 计算变动比例
            if baseline_value != 0:
                change_rate = change_amount / baseline_value
            else:
                change_rate = 1.0 if current_value != 0 else 0.0
            
            result = {
                "calculation_type": "工资调整",
                "field": field,
                "baseline_amount": self._round_amount(baseline_value),
                "current_amount": self._round_amount(current_value),
                "change_amount": self._round_amount(change_amount),
                "change_rate": round(change_rate, 4),
                "total_amount": self._round_amount(change_amount),
                "calculation_method": "当前金额 - 基准金额",
                "calculation_time": datetime.now(),
                "is_increase": change_amount > 0,
                "confidence": change_record.get('confidence', 0.8)
            }
            
            logger.debug(f"工资调整计算: {field} = {change_amount:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"工资调整计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_allowance_change(
        self,
        change_record: Dict[str, Any],
        baseline_data: Optional[Dict[str, Any]],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算津贴变动金额
        
        Args:
            change_record: 异动记录
            baseline_data: 基准数据
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            details = change_record.get('details', {})
            field = details.get('field', '')
            baseline_value = details.get('baseline_value', 0)
            current_value = details.get('current_value', 0)
            
            change_amount = current_value - baseline_value
            
            if baseline_value != 0:
                change_rate = change_amount / baseline_value
            else:
                change_rate = 1.0 if current_value != 0 else 0.0
            
            result = {
                "calculation_type": "津贴变动",
                "field": field,
                "baseline_amount": self._round_amount(baseline_value),
                "current_amount": self._round_amount(current_value),
                "change_amount": self._round_amount(change_amount),
                "change_rate": round(change_rate, 4),
                "total_amount": self._round_amount(change_amount),
                "calculation_method": "当前津贴 - 基准津贴",
                "calculation_time": datetime.now(),
                "is_increase": change_amount > 0,
                "confidence": change_record.get('confidence', 0.8)
            }
            
            logger.debug(f"津贴变动计算: {field} = {change_amount:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"津贴变动计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_new_employee_amount(
        self,
        change_record: Dict[str, Any],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算新进人员金额
        
        Args:
            change_record: 异动记录
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            details = change_record.get('details', {})
            employee_data = details.get('employee_data', {})
            
            # 计算新员工的总收入
            total_income = 0
            income_breakdown = {}
            
            # 查找工资相关字段
            for key, value in employee_data.items():
                if any(keyword in key for keyword in ['工资', '薪', '津贴', '补贴', '奖金']):
                    try:
                        amount = float(value) if value else 0
                        if amount > 0:
                            total_income += amount
                            income_breakdown[key] = self._round_amount(amount)
                    except (ValueError, TypeError):
                        continue
            
            result = {
                "calculation_type": "新进人员",
                "total_amount": self._round_amount(total_income),
                "income_breakdown": income_breakdown,
                "calculation_method": "新员工当月所有收入项目之和",
                "calculation_time": datetime.now(),
                "is_new_employee": True,
                "confidence": change_record.get('confidence', 0.95)
            }
            
            logger.debug(f"新进人员计算: 总收入 = {total_income:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"新进人员计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_supplementary_amount(
        self,
        change_record: Dict[str, Any],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算补发工资金额
        
        Args:
            change_record: 异动记录
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            details = change_record.get('details', {})
            field = details.get('field', '')
            supplement_amount = details.get('supplement_amount', 0)
            
            result = {
                "calculation_type": "补发工资",
                "field": field,
                "supplement_amount": self._round_amount(supplement_amount),
                "total_amount": self._round_amount(supplement_amount),
                "calculation_method": "补发字段金额",
                "calculation_time": datetime.now(),
                "is_supplement": True,
                "confidence": change_record.get('confidence', 0.90)
            }
            
            logger.debug(f"补发工资计算: {field} = {supplement_amount:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"补发工资计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_deduction_amount(
        self,
        change_record: Dict[str, Any],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算扣款金额
        
        Args:
            change_record: 异动记录
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            details = change_record.get('details', {})
            field = details.get('field', '')
            deduction_amount = details.get('deduction_amount', 0)
            
            result = {
                "calculation_type": "扣款异动",
                "field": field,
                "deduction_amount": self._round_amount(deduction_amount),
                "total_amount": self._round_amount(-deduction_amount),  # 扣款为负数
                "calculation_method": "扣款字段金额（负值）",
                "calculation_time": datetime.now(),
                "is_deduction": True,
                "confidence": change_record.get('confidence', 0.85)
            }
            
            logger.debug(f"扣款计算: {field} = -{deduction_amount:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"扣款计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_position_change_amount(
        self,
        change_record: Dict[str, Any],
        baseline_data: Optional[Dict[str, Any]],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算职务变动金额
        
        Args:
            change_record: 异动记录
            baseline_data: 基准数据
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            # 职务变动通常需要结合工资变动来计算
            # 这里简化处理，返回基本信息
            details = change_record.get('details', {})
            
            result = {
                "calculation_type": "职务变动",
                "field": details.get('field', ''),
                "old_position": details.get('old_position', ''),
                "new_position": details.get('new_position', ''),
                "total_amount": self._round_amount(0),  # 职务变动本身无直接金额
                "calculation_method": "职务变动（需结合工资调整）",
                "calculation_time": datetime.now(),
                "is_position_change": True,
                "confidence": change_record.get('confidence', 0.80),
                "note": "职务变动可能伴随工资调整，请查看相关工资异动记录"
            }
            
            logger.debug(f"职务变动计算: {details.get('old_position', '')} → {details.get('new_position', '')}")
            return result
            
        except Exception as e:
            logger.error(f"职务变动计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _calculate_other_change_amount(
        self,
        change_record: Dict[str, Any],
        baseline_data: Optional[Dict[str, Any]],
        current_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算其他类型异动金额
        
        Args:
            change_record: 异动记录
            baseline_data: 基准数据
            current_data: 当前数据
            
        Returns:
            Dict[str, Any]: 计算结果
        """
        try:
            result = {
                "calculation_type": "其他异动",
                "total_amount": self._round_amount(0),
                "calculation_method": "其他类型异动，需人工确认",
                "calculation_time": datetime.now(),
                "confidence": change_record.get('confidence', 0.5),
                "note": "未识别的异动类型，建议人工核实"
            }
            
            logger.debug("其他异动计算: 需人工确认")
            return result
            
        except Exception as e:
            logger.error(f"其他异动计算失败: {str(e)}")
            return self._create_error_result(str(e))
    
    def _round_amount(self, amount: Union[int, float, Decimal]) -> float:
        """
        金额四舍五入
        
        Args:
            amount: 原始金额
            
        Returns:
            float: 四舍五入后的金额
        """
        try:
            decimal_amount = Decimal(str(amount))
            rounded_amount = decimal_amount.quantize(
                Decimal('0.' + '0' * self.decimal_places),
                rounding=self.rounding_method
            )
            return float(rounded_amount)
        except Exception:
            return 0.0
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """
        创建错误结果
        
        Args:
            error_message: 错误信息
            
        Returns:
            Dict[str, Any]: 错误结果
        """
        return {
            "calculation_type": "错误",
            "total_amount": 0.0,
            "error": error_message,
            "calculation_time": datetime.now(),
            "is_error": True
        }
    
    def _record_calculation(self, change_record: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        记录计算历史
        
        Args:
            change_record: 异动记录
            result: 计算结果
        """
        try:
            history_record = {
                "employee_id": change_record.get('employee_id', ''),
                "change_type": change_record.get('change_type', ''),
                "total_amount": result.get('total_amount', 0),
                "calculation_type": result.get('calculation_type', ''),
                "calculation_time": datetime.now(),
                "confidence": result.get('confidence', 0)
            }
            
            self.calculation_history.append(history_record)
            
            # 限制历史记录数量，避免内存过度使用
            if len(self.calculation_history) > 10000:
                self.calculation_history = self.calculation_history[-5000:]
                logger.info("清理计算历史记录，保留最近5000条")
            
        except Exception as e:
            logger.error(f"记录计算历史失败: {str(e)}")
    
    def _init_default_rules(self) -> None:
        """
        初始化默认计算规则
        """
        try:
            # 基本计算规则
            rules = [
                CalculationRule(
                    "工资调整",
                    "current_amount - baseline_amount",
                    "工资调整金额 = 当前工资 - 基准工资"
                ),
                CalculationRule(
                    "津贴变动",
                    "current_allowance - baseline_allowance",
                    "津贴变动金额 = 当前津贴 - 基准津贴"
                ),
                CalculationRule(
                    "新进人员",
                    "sum(all_income_fields)",
                    "新进人员金额 = 所有收入项目之和"
                )
            ]
            
            for rule in rules:
                self.calculation_rules[rule.name] = rule
            
            logger.info(f"初始化 {len(rules)} 个默认计算规则")
            
        except Exception as e:
            logger.error(f"初始化默认计算规则失败: {str(e)}")
    
    def _apply_custom_validation(
        self,
        calculation_result: Dict[str, Any],
        validation_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        应用自定义验证规则
        
        Args:
            calculation_result: 计算结果
            validation_rules: 验证规则
            
        Returns:
            Dict[str, Any]: 验证结果补充
        """
        custom_result = {}
        
        try:
            # 这里可以扩展更多自定义验证逻辑
            max_amount = validation_rules.get('max_amount')
            if max_amount and abs(calculation_result.get('total_amount', 0)) > max_amount:
                custom_result["validation_errors"] = custom_result.get("validation_errors", [])
                custom_result["validation_errors"].append(f"金额超过最大限制: {max_amount}")
                custom_result["is_valid"] = False
            
        except Exception as e:
            logger.error(f"应用自定义验证规则失败: {str(e)}")
        
        return custom_result 