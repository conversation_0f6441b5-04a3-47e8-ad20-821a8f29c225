#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DataUpdatedEvent修复验证脚本

快速验证DataUpdatedEvent创建和发布是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_data_updated_event_creation():
    """测试DataUpdatedEvent的正确创建"""
    print("🔧 [验证] 测试DataUpdatedEvent创建...")
    
    try:
        from src.core.event_bus import DataUpdatedEvent, EventBus
        
        # 测试1：正确创建DataUpdatedEvent
        event = DataUpdatedEvent(
            event_type="data_updated",
            table_name="test_table",
            data=[{"id": 1, "name": "测试"}],
            metadata={"request_type": "sort_change"}
        )
        
        print(f"✅ DataUpdatedEvent创建成功: {event.event_type}")
        print(f"   - 表名: {event.table_name}")
        print(f"   - 元数据: {event.metadata}")
        
        # 测试2：事件总线发布
        event_bus = EventBus()
        
        received_events = []
        def event_handler(event):
            received_events.append(event)
            print(f"📨 接收到事件: {event.event_type}")
        
        # 订阅事件
        event_bus.subscribe("data_updated", event_handler)
        
        # 发布事件
        success = event_bus.publish(event)
        
        if success and received_events:
            print("✅ 事件发布和接收成功")
            return True
        else:
            print("❌ 事件发布或接收失败")
            return False
            
    except Exception as e:
        print(f"❌ DataUpdatedEvent测试失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_table_data_service_integration():
    """测试TableDataService的事件发布是否正常"""
    print("\n🔧 [验证] 测试TableDataService事件发布...")
    
    try:
        from src.services.table_data_service import TableDataService
        from src.core.event_bus import EventBus
        from src.core.unified_state_manager import UnifiedStateManager
        from src.core.unified_data_request_manager import UnifiedDataRequestManager
        
        print("✅ 所有必需模块导入成功")
        
        # 简单验证是否存在关键方法
        service_methods = [
            '_handle_sort_request',
            '_handle_page_request', 
            'load_table_data'
        ]
        
        for method in service_methods:
            if hasattr(TableDataService, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                
        return True
        
    except Exception as e:
        print(f"❌ TableDataService测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始验证DataUpdatedEvent修复效果\n")
    
    # 测试1：DataUpdatedEvent创建和发布
    test1_pass = test_data_updated_event_creation()
    
    # 测试2：TableDataService集成
    test2_pass = test_table_data_service_integration()
    
    print(f"\n📊 验证结果:")
    print(f"   - DataUpdatedEvent创建和发布: {'✅ 通过' if test1_pass else '❌ 失败'}")
    print(f"   - TableDataService集成: {'✅ 通过' if test2_pass else '❌ 失败'}")
    
    if test1_pass and test2_pass:
        print("\n🎉 验证完成：DataUpdatedEvent修复成功！")
        print("\n💡 现在可以重启系统，测试排序和分页功能是否正常工作。")
    else:
        print("\n❌ 验证失败：仍需要进一步修复") 