"""
月度工资异动处理系统 - 应用服务层

本模块实现应用服务层，作为GUI层和业务逻辑层之间的抽象层，
统一管理所有业务操作，降低模块间耦合度。

主要功能:
1. 数据导入服务
2. 异动检测服务  
3. 报告生成服务
4. 配置管理服务
5. 系统操作服务
"""

import os
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass

from src.utils.log_config import setup_logger
from src.core.config_manager import ConfigManager
from src.modules.system_config import UserPreferences
from src.modules.data_import import ExcelImporter, DataValidator
from src.modules.change_detection import ChangeDetector, BaselineManager
from src.modules.report_generation import ReportManager


@dataclass
class ServiceResult:
    """服务操作结果"""
    success: bool
    message: str
    data: Optional[Any] = None
    error: Optional[Exception] = None


class ApplicationService:
    """
    应用服务层主类
    
    负责协调各个业务模块，为GUI层提供统一的业务接口，
    实现业务逻辑与UI逻辑的解耦。
    """
    
    def __init__(self):
        """初始化应用服务"""
        self.logger = setup_logger(__name__)
        self._config_manager: Optional[ConfigManager] = None
        self._user_preferences: Optional[UserPreferences] = None
        self._excel_importer: Optional[ExcelImporter] = None
        self._data_validator: Optional[DataValidator] = None
        self._baseline_manager: Optional[BaselineManager] = None
        self._change_detector: Optional[ChangeDetector] = None
        self._report_manager: Optional[ReportManager] = None
        
        # 初始化标志
        self._initialized = False
    
    def initialize(self) -> ServiceResult:
        """
        初始化所有业务模块
        
        Returns:
            ServiceResult: 初始化结果
        """
        try:
            # 初始化配置管理器
            self._config_manager = ConfigManager()
            self._user_preferences = UserPreferences()
            
            # 初始化数据处理模块
            self._excel_importer = ExcelImporter()
            self._data_validator = DataValidator()
            
            # 初始化异动检测模块
            self._baseline_manager = BaselineManager(self._config_manager)
            self._change_detector = ChangeDetector(self._config_manager, self._baseline_manager)
            
            # 初始化报告生成模块
            self._report_manager = ReportManager()
            
            self._initialized = True
            self.logger.info("应用服务初始化成功")
            
            return ServiceResult(
                success=True,
                message="应用服务初始化成功"
            )
            
        except Exception as e:
            self.logger.error(f"应用服务初始化失败: {e}")
            return ServiceResult(
                success=False,
                message=f"应用服务初始化失败: {e}",
                error=e
            )
    
    def _ensure_initialized(self) -> None:
        """确保服务已初始化"""
        if not self._initialized:
            raise RuntimeError("应用服务未初始化，请先调用initialize()方法")
    
    # ==================== 数据导入服务 ====================
    
    def import_excel_data(self, file_path: str) -> ServiceResult:
        """
        导入Excel数据
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            ServiceResult: 导入结果
        """
        try:
            self._ensure_initialized()
            
            if not os.path.exists(file_path):
                return ServiceResult(
                    success=False,
                    message=f"文件不存在: {file_path}"
                )
            
            # 执行数据导入
            result = self._excel_importer.import_file(file_path)
            
            self.logger.info(f"Excel数据导入成功: {os.path.basename(file_path)}")
            return ServiceResult(
                success=True,
                message=f"Excel数据导入成功: {os.path.basename(file_path)}",
                data=result
            )
            
        except Exception as e:
            self.logger.error(f"Excel数据导入失败: {e}")
            return ServiceResult(
                success=False,
                message=f"数据导入失败: {e}",
                error=e
            )
    
    def import_baseline_data(self, file_path: str) -> ServiceResult:
        """
        导入基准数据
        
        Args:
            file_path: 基准数据文件路径
            
        Returns:
            ServiceResult: 导入结果
        """
        try:
            self._ensure_initialized()
            
            if not os.path.exists(file_path):
                return ServiceResult(
                    success=False,
                    message=f"基准数据文件不存在: {file_path}"
                )
            
            # 执行基准数据导入
            result = self._baseline_manager.load_baseline(file_path)
            
            self.logger.info(f"基准数据导入成功: {os.path.basename(file_path)}")
            return ServiceResult(
                success=True,
                message=f"基准数据导入成功: {os.path.basename(file_path)}",
                data=result
            )
            
        except Exception as e:
            self.logger.error(f"基准数据导入失败: {e}")
            return ServiceResult(
                success=False,
                message=f"基准数据导入失败: {e}",
                error=e
            )
    
    def validate_data(self) -> ServiceResult:
        """
        验证数据完整性
        
        Returns:
            ServiceResult: 验证结果
        """
        try:
            self._ensure_initialized()
            
            # 执行数据验证
            validation_result = self._data_validator.validate_all()
            
            if validation_result.is_valid:
                self.logger.info("数据验证通过")
                return ServiceResult(
                    success=True,
                    message="数据验证通过",
                    data=validation_result
                )
            else:
                self.logger.warning(f"数据验证发现问题: {validation_result.error_summary}")
                return ServiceResult(
                    success=False,
                    message=f"数据验证发现问题: {validation_result.error_summary}",
                    data=validation_result
                )
                
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return ServiceResult(
                success=False,
                message=f"数据验证失败: {e}",
                error=e
            )
    
    # ==================== 异动检测服务 ====================
    
    def detect_changes(self) -> ServiceResult:
        """
        执行异动检测
        
        Returns:
            ServiceResult: 检测结果
        """
        try:
            self._ensure_initialized()
            
            # 执行异动检测
            detection_result = self._change_detector.detect_all_changes()
            
            change_count = len(detection_result.changes) if detection_result.changes else 0
            
            self.logger.info(f"异动检测完成，发现 {change_count} 个异动")
            return ServiceResult(
                success=True,
                message=f"异动检测完成，发现 {change_count} 个异动",
                data=detection_result
            )
            
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            return ServiceResult(
                success=False,
                message=f"异动检测失败: {e}",
                error=e
            )
    
    def get_detection_results(self) -> ServiceResult:
        """
        获取异动检测结果
        
        Returns:
            ServiceResult: 检测结果
        """
        try:
            self._ensure_initialized()
            
            # 获取检测结果
            results = self._change_detector.get_latest_results()
            
            return ServiceResult(
                success=True,
                message="获取检测结果成功",
                data=results
            )
            
        except Exception as e:
            self.logger.error(f"获取检测结果失败: {e}")
            return ServiceResult(
                success=False,
                message=f"获取检测结果失败: {e}",
                error=e
            )
    
    def export_change_data(self, file_path: str) -> ServiceResult:
        """
        导出异动数据
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            ServiceResult: 导出结果
        """
        try:
            self._ensure_initialized()
            
            # 获取异动数据并导出
            results = self._change_detector.get_latest_results()
            if not results or not results.changes:
                return ServiceResult(
                    success=False,
                    message="没有可导出的异动数据"
                )
            
            # 执行导出操作
            self._excel_importer.export_changes(results.changes, file_path)
            
            self.logger.info(f"异动数据导出成功: {os.path.basename(file_path)}")
            return ServiceResult(
                success=True,
                message=f"异动数据导出成功: {os.path.basename(file_path)}"
            )
            
        except Exception as e:
            self.logger.error(f"异动数据导出失败: {e}")
            return ServiceResult(
                success=False,
                message=f"异动数据导出失败: {e}",
                error=e
            )
    
    # ==================== 报告生成服务 ====================
    
    def generate_word_report(self, output_path: Optional[str] = None) -> ServiceResult:
        """
        生成Word报告
        
        Args:
            output_path: 输出文件路径，为None时使用默认路径
            
        Returns:
            ServiceResult: 生成结果
        """
        try:
            self._ensure_initialized()
            
            # 获取异动数据
            detection_results = self._change_detector.get_latest_results()
            if not detection_results or not detection_results.changes:
                return ServiceResult(
                    success=False,
                    message="没有可生成报告的异动数据"
                )
            
            # 生成Word报告
            report_path = self._report_manager.generate_word_report(
                detection_results, output_path
            )
            
            self.logger.info(f"Word报告生成成功: {os.path.basename(report_path)}")
            return ServiceResult(
                success=True,
                message=f"Word报告生成成功: {os.path.basename(report_path)}",
                data={"report_path": report_path}
            )
            
        except Exception as e:
            self.logger.error(f"Word报告生成失败: {e}")
            return ServiceResult(
                success=False,
                message=f"Word报告生成失败: {e}",
                error=e
            )
    
    def generate_excel_report(self, output_path: Optional[str] = None) -> ServiceResult:
        """
        生成Excel报告
        
        Args:
            output_path: 输出文件路径，为None时使用默认路径
            
        Returns:
            ServiceResult: 生成结果
        """
        try:
            self._ensure_initialized()
            
            # 获取异动数据
            detection_results = self._change_detector.get_latest_results()
            if not detection_results or not detection_results.changes:
                return ServiceResult(
                    success=False,
                    message="没有可生成报告的异动数据"
                )
            
            # 生成Excel报告
            report_path = self._report_manager.generate_excel_report(
                detection_results, output_path
            )
            
            self.logger.info(f"Excel报告生成成功: {os.path.basename(report_path)}")
            return ServiceResult(
                success=True,
                message=f"Excel报告生成成功: {os.path.basename(report_path)}",
                data={"report_path": report_path}
            )
            
        except Exception as e:
            self.logger.error(f"Excel报告生成失败: {e}")
            return ServiceResult(
                success=False,
                message=f"Excel报告生成失败: {e}",
                error=e
            )
    
    # ==================== 配置管理服务 ====================
    
    def get_user_preferences(self) -> ServiceResult:
        """
        获取用户偏好设置
        
        Returns:
            ServiceResult: 偏好设置
        """
        try:
            self._ensure_initialized()
            
            preferences = self._user_preferences.get_all_preferences()
            
            return ServiceResult(
                success=True,
                message="获取用户偏好设置成功",
                data=preferences
            )
            
        except Exception as e:
            self.logger.error(f"获取用户偏好设置失败: {e}")
            return ServiceResult(
                success=False,
                message=f"获取用户偏好设置失败: {e}",
                error=e
            )
    
    def save_user_preferences(self, preferences: Dict[str, Any]) -> ServiceResult:
        """
        保存用户偏好设置
        
        Args:
            preferences: 偏好设置字典
            
        Returns:
            ServiceResult: 保存结果
        """
        try:
            self._ensure_initialized()
            
            for key, value in preferences.items():
                self._user_preferences.set_preference(key, value)
            
            self.logger.info("用户偏好设置保存成功")
            return ServiceResult(
                success=True,
                message="用户偏好设置保存成功"
            )
            
        except Exception as e:
            self.logger.error(f"用户偏好设置保存失败: {e}")
            return ServiceResult(
                success=False,
                message=f"用户偏好设置保存失败: {e}",
                error=e
            )
    
    def get_system_config(self) -> ServiceResult:
        """
        获取系统配置
        
        Returns:
            ServiceResult: 系统配置
        """
        try:
            self._ensure_initialized()
            
            config = self._config_manager.get_all_config()
            
            return ServiceResult(
                success=True,
                message="获取系统配置成功",
                data=config
            )
            
        except Exception as e:
            self.logger.error(f"获取系统配置失败: {e}")
            return ServiceResult(
                success=False,
                message=f"获取系统配置失败: {e}",
                error=e
            )
    
    # ==================== 系统操作服务 ====================
    
    def backup_data(self, backup_path: str) -> ServiceResult:
        """
        数据备份
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            ServiceResult: 备份结果
        """
        try:
            self._ensure_initialized()
            
            # 这里实现数据备份逻辑
            # 备份数据库、配置文件等
            
            self.logger.info(f"数据备份成功: {os.path.basename(backup_path)}")
            return ServiceResult(
                success=True,
                message=f"数据备份成功: {os.path.basename(backup_path)}"
            )
            
        except Exception as e:
            self.logger.error(f"数据备份失败: {e}")
            return ServiceResult(
                success=False,
                message=f"数据备份失败: {e}",
                error=e
            )
    
    def get_system_status(self) -> ServiceResult:
        """
        获取系统状态信息
        
        Returns:
            ServiceResult: 系统状态
        """
        try:
            self._ensure_initialized()
            
            status = {
                "initialized": self._initialized,
                "config_loaded": self._config_manager is not None,
                "modules_ready": all([
                    self._excel_importer is not None,
                    self._data_validator is not None,
                    self._change_detector is not None,
                    self._report_manager is not None
                ])
            }
            
            return ServiceResult(
                success=True,
                message="获取系统状态成功",
                data=status
            )
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return ServiceResult(
                success=False,
                message=f"获取系统状态失败: {e}",
                error=e
            )
    
    # ==================== 资源管理 ====================
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理各模块资源
            if self._change_detector:
                self._change_detector.cleanup()
            if self._baseline_manager:
                self._baseline_manager.cleanup()
            if self._excel_importer:
                self._excel_importer.cleanup()
            if self._report_manager:
                self._report_manager.cleanup()
            
            self._initialized = False
            self.logger.info("应用服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"应用服务资源清理失败: {e}") 