# 统一格式管理系统使用指南

## 🚀 快速开始

### 1. 基本使用

```python
from src.modules.format_management import UnifiedFormatManager

# 初始化格式管理器
format_manager = UnifiedFormatManager()

# 格式化完整表格
headers = ['employee_id', 'employee_name', 'total_salary']
data = [
    {'employee_id': 'E001', 'employee_name': '张三', 'total_salary': 12345.67},
    {'employee_id': 'E002', 'employee_name': '李四', 'total_salary': 23456.78}
]

formatted_headers, formatted_data = format_manager.format_table_complete(
    pd.DataFrame(data), 
    'active_employees'
)
```

### 2. 格式化结果

**输入**:
```python
data = [
    {'employee_id': 'E001', 'employee_name': '张三', 'total_salary': 12345.67},
    {'employee_id': 'E002', 'employee_name': '李四', 'total_salary': 23456.78}
]
headers = ['employee_id', 'employee_name', 'total_salary']
```

**输出**:
```python
formatted_headers = ['工号', '姓名', '应发工资']
formatted_data = [
    {'工号': 'E001', '姓名': '张三', '应发工资': '¥12,345.67'},
    {'工号': 'E002', '姓名': '李四', '应发工资': '¥23,456.78'}
]
```

## 📋 支持的格式类型

### 1. 货币格式 (currency)

**配置**:
```json
{
  "currency": {
    "decimal_places": 2,
    "thousand_separator": ",",
    "symbol": "¥",
    "symbol_position": "prefix",
    "negative_format": "-{symbol}{value}",
    "zero_display": "0.00"
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
12345.67   → "¥12,345.67"
0          → "0.00"
-1000      → "-¥1,000.00"
1234567.89 → "¥1,234,567.89"
```

### 2. 日期格式 (date)

**配置**:
```json
{
  "date": {
    "input_format": "%Y-%m-%d",
    "display_format": "%Y年%m月%d日",
    "short_format": "%Y-%m-%d",
    "zero_display": ""
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
"2025-07-18" → "2025年07月18日"
"2025-01-01" → "2025年01月01日"
None         → ""
```

### 3. 整数格式 (integer)

**配置**:
```json
{
  "integer": {
    "thousand_separator": ",",
    "zero_display": "0"
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
1234567 → "1,234,567"
0       → "0"
-1000   → "-1,000"
```

### 4. 浮点数格式 (float)

**配置**:
```json
{
  "float": {
    "decimal_places": 2,
    "thousand_separator": ",",
    "zero_display": "0.00"
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
123.456    → "123.46"
1234.5     → "1,234.50"
0          → "0.00"
```

### 5. 百分比格式 (percentage)

**配置**:
```json
{
  "percentage": {
    "decimal_places": 1,
    "symbol": "%",
    "symbol_position": "suffix",
    "multiply_by_100": true
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
0.1234 → "12.3%"
0.5    → "50.0%"
1.0    → "100.0%"
```

### 6. 字符串格式 (string)

**配置**:
```json
{
  "string": {
    "max_length": 100,
    "trim_whitespace": true,
    "empty_display": "-"
  }
}
```

**格式化示例**:
```python
# 输入值 → 输出格式
"  张三  " → "张三"
""        → "-"
None      → "-"
"很长的文本..."  → "很长的文本..."
```

## 🗂️ 表格类型配置

### 1. 在职人员工资表 (active_employees)

**字段映射**:
```json
{
  "field_mappings": {
    "employee_id": "工号",
    "employee_name": "姓名",
    "department": "部门名称",
    "position_salary_2025": "2025年岗位工资",
    "grade_salary_2025": "2025年薪级工资",
    "total_salary": "应发工资"
  }
}
```

**使用示例**:
```python
# 格式化在职人员数据
formatted_headers, formatted_data = format_manager.format_table_complete(
    employee_data, 'active_employees'
)
```

### 2. 离休人员工资表 (retired_employees)

**字段映射**:
```json
{
  "field_mappings": {
    "employee_id": "人员代码",
    "employee_name": "姓名",
    "department": "部门名称",
    "basic_salary": "基本工资",
    "total_salary": "应发工资"
  }
}
```

**使用示例**:
```python
# 格式化离休人员数据
formatted_headers, formatted_data = format_manager.format_table_complete(
    retired_data, 'retired_employees'
)
```

### 3. 临时工工资表 (part_time_employees)

**字段映射**:
```json
{
  "field_mappings": {
    "employee_id": "工号",
    "employee_name": "姓名",
    "hourly_rate": "小时工资",
    "hours_worked": "工作小时",
    "total_salary": "应发工资"
  }
}
```

**使用示例**:
```python
# 格式化临时工数据
formatted_headers, formatted_data = format_manager.format_table_complete(
    parttime_data, 'part_time_employees'
)
```

## 🛠️ 高级使用

### 1. 单独格式化表头

```python
# 只格式化表头
raw_headers = ['employee_id', 'employee_name', 'total_salary']
formatted_headers = format_manager.format_headers(
    raw_headers, 
    'active_employees'
)
# 输出: ['工号', '姓名', '应发工资']
```

### 2. 单独格式化数据

```python
# 只格式化数据
raw_data = pd.DataFrame([
    {'employee_id': 'E001', 'total_salary': 12345.67}
])
formatted_data = format_manager.format_data(
    raw_data, 
    'active_employees'
)
# 输出: DataFrame with formatted values
```

### 3. 单个值格式化

```python
# 格式化单个值
formatted_value = format_manager.format_renderer.render_value(
    12345.67, 
    'currency'
)
# 输出: "¥12,345.67"
```

### 4. 批量格式化

```python
# 批量格式化多个表格
tables = [
    ('active_employees', active_data),
    ('retired_employees', retired_data),
    ('part_time_employees', parttime_data)
]

formatted_results = []
for table_type, data in tables:
    headers, formatted_data = format_manager.format_table_complete(
        data, table_type
    )
    formatted_results.append((table_type, headers, formatted_data))
```

## 🔧 配置管理

### 1. 更新格式配置

```python
# 更新货币格式的小数位数
format_manager.update_format_config(
    'default_formats.currency.decimal_places', 
    3
)

# 更新货币符号
format_manager.update_format_config(
    'default_formats.currency.symbol', 
    '$'
)

# 保存配置
format_manager.format_config.save_config()
```

### 2. 更新字段映射

```python
# 更新字段映射
new_mappings = {
    'bonus_2025': '2025年奖金',
    'overtime_pay': '加班费'
}

format_manager.update_field_mapping('active_employees', new_mappings)

# 保存映射
format_manager.field_registry.save_mappings()
```

### 3. 添加新表格类型

**1. 更新格式配置**:
```json
{
  "table_types": {
    "intern_employees": {
      "display_name": "实习生工资表",
      "description": "实习生的工资数据",
      "priority": 5,
      "enabled": true
    }
  }
}
```

**2. 更新字段映射**:
```json
{
  "table_mappings": {
    "intern_employees": {
      "field_mappings": {
        "intern_id": "实习生编号",
        "intern_name": "姓名",
        "hourly_rate": "小时工资",
        "total_hours": "总工时",
        "total_pay": "总工资"
      },
      "field_types": {
        "intern_id": "string",
        "intern_name": "string",
        "hourly_rate": "currency",
        "total_hours": "float",
        "total_pay": "currency"
      }
    }
  }
}
```

**3. 使用新表格类型**:
```python
# 格式化实习生数据
formatted_headers, formatted_data = format_manager.format_table_complete(
    intern_data, 'intern_employees'
)
```

## 🎯 实际应用场景

### 1. 数据导入格式化

```python
# 数据导入时的格式化
def import_salary_data(file_path, table_type):
    # 读取原始数据
    raw_data = pd.read_excel(file_path)
    
    # 格式化数据
    formatted_headers, formatted_data = format_manager.format_table_complete(
        raw_data, table_type, f"import:{file_path}"
    )
    
    # 保存格式化后的数据
    return formatted_headers, formatted_data

# 使用示例
headers, data = import_salary_data('salary_2025_07.xlsx', 'active_employees')
```

### 2. 数据导出格式化

```python
# 数据导出时的格式化
def export_salary_data(data, table_type, output_file):
    # 格式化数据
    formatted_headers, formatted_data = format_manager.format_table_complete(
        data, table_type, f"export:{output_file}"
    )
    
    # 导出到Excel
    formatted_data.to_excel(output_file, index=False)
    
    return output_file

# 使用示例
export_salary_data(employee_data, 'active_employees', 'formatted_salary.xlsx')
```

### 3. 表格显示格式化

```python
# 表格显示时的格式化
class SalaryTableWidget(QTableWidget):
    def __init__(self):
        super().__init__()
        self.format_manager = UnifiedFormatManager()
    
    def set_data(self, data, table_type):
        # 格式化数据
        headers, formatted_data = self.format_manager.format_table_complete(
            data, table_type, "table_display"
        )
        
        # 设置表格数据
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 填充数据
        for row, record in enumerate(formatted_data.to_dict('records')):
            self.insertRow(row)
            for col, (key, value) in enumerate(record.items()):
                self.setItem(row, col, QTableWidgetItem(str(value)))
```

### 4. 数据验证格式化

```python
# 数据验证时的格式化
def validate_salary_data(data, table_type):
    # 获取验证结果
    validation_result = format_manager.format_renderer.validate_data_types(
        data, table_type
    )
    
    # 处理验证结果
    if validation_result['errors']:
        print("数据验证失败:")
        for error in validation_result['errors']:
            print(f"  - {error}")
    
    if validation_result['warnings']:
        print("数据验证警告:")
        for warning in validation_result['warnings']:
            print(f"  - {warning}")
    
    return len(validation_result['errors']) == 0

# 使用示例
is_valid = validate_salary_data(employee_data, 'active_employees')
```

## 📊 性能优化

### 1. 缓存使用

```python
# 系统会自动缓存格式化结果
# 相同数据的重复格式化会使用缓存

# 清除缓存（如果需要）
format_manager.clear_cache()
```

### 2. 批量处理

```python
# 对于大量数据，使用批量处理
def format_large_dataset(data, table_type, batch_size=1000):
    results = []
    
    for i in range(0, len(data), batch_size):
        batch = data[i:i+batch_size]
        headers, formatted_batch = format_manager.format_table_complete(
            batch, table_type
        )
        results.append(formatted_batch)
    
    # 合并结果
    final_result = pd.concat(results, ignore_index=True)
    return headers, final_result
```

### 3. 异步处理

```python
import asyncio

async def format_data_async(data, table_type):
    # 异步格式化（对于大数据集）
    loop = asyncio.get_event_loop()
    
    # 在线程池中执行格式化
    result = await loop.run_in_executor(
        None, 
        format_manager.format_table_complete,
        data, table_type
    )
    
    return result

# 使用示例
headers, formatted_data = await format_data_async(large_data, 'active_employees')
```

## 🚨 错误处理

### 1. 格式化失败处理

```python
try:
    headers, formatted_data = format_manager.format_table_complete(
        data, 'active_employees'
    )
except Exception as e:
    print(f"格式化失败: {e}")
    # 使用原始数据作为后备
    headers = list(data.columns)
    formatted_data = data
```

### 2. 配置文件错误处理

```python
# 检查配置文件状态
config_status = format_manager.format_config.is_loaded()
if not config_status:
    print("配置文件加载失败，使用默认配置")
    format_manager.format_config.reset_to_default()
```

### 3. 字段映射缺失处理

```python
# 检查字段映射是否存在
if not format_manager.field_registry.has_table_mapping('new_table_type'):
    print("字段映射不存在，创建默认映射")
    default_mapping = {field: field for field in data.columns}
    format_manager.update_field_mapping('new_table_type', default_mapping)
```

## 🔍 调试和监控

### 1. 获取系统状态

```python
# 获取系统状态
status = format_manager.get_system_status()
print(f"系统状态: {status}")

# 获取渲染统计
stats = format_manager.format_renderer.get_rendering_statistics()
print(f"渲染统计: {stats}")
```

### 2. 日志监控

```python
import logging

# 设置日志级别
logging.getLogger('format_management').setLevel(logging.DEBUG)

# 格式化时会输出详细日志
headers, data = format_manager.format_table_complete(
    employee_data, 'active_employees'
)
```

### 3. 性能监控

```python
import time

# 监控格式化性能
start_time = time.time()
headers, formatted_data = format_manager.format_table_complete(
    large_data, 'active_employees'
)
end_time = time.time()

print(f"格式化耗时: {(end_time - start_time) * 1000:.2f}ms")
print(f"处理记录数: {len(formatted_data)}")
print(f"平均每条记录耗时: {(end_time - start_time) / len(formatted_data) * 1000:.2f}ms")
```

## 📚 常见问题解答

### Q1: 如何添加新的格式类型？

**A**: 需要在 `FormatRenderer` 中添加新的渲染方法，并在配置文件中定义格式规则。

```python
# 1. 在 FormatRenderer 中添加方法
def _render_phone_value(self, value, format_config):
    # 电话号码格式化逻辑
    return formatted_phone

# 2. 在配置文件中添加格式定义
{
  "default_formats": {
    "phone": {
      "country_code": "+86",
      "separator": "-",
      "format": "{country_code} {area}-{number}"
    }
  }
}
```

### Q2: 如何修改现有格式的显示效果？

**A**: 直接修改配置文件中的相应设置。

```python
# 修改货币格式为美元
format_manager.update_format_config(
    'default_formats.currency.symbol', '$'
)
format_manager.format_config.save_config()
```

### Q3: 如何处理格式化失败的情况？

**A**: 系统会自动降级到原始数据，并记录错误日志。

```python
# 系统会自动处理格式化失败
# 如果需要自定义处理，可以捕获异常
try:
    result = format_manager.format_table_complete(data, table_type)
except Exception as e:
    logger.error(f"格式化失败: {e}")
    # 自定义处理逻辑
    result = handle_format_failure(data, table_type)
```

### Q4: 如何提高大数据量的格式化性能？

**A**: 使用批量处理、缓存和异步处理。

```python
# 批量处理
headers, data = format_large_dataset(big_data, 'active_employees', batch_size=1000)

# 异步处理
headers, data = await format_data_async(big_data, 'active_employees')
```

### Q5: 如何确保格式化结果的一致性？

**A**: 使用统一的配置文件和格式管理器实例。

```python
# 全局使用同一个格式管理器实例
global_format_manager = UnifiedFormatManager()

# 所有格式化操作都使用这个实例
def format_data_everywhere(data, table_type):
    return global_format_manager.format_table_complete(data, table_type)
```

---

通过这个使用指南，您可以充分利用统一格式管理系统的强大功能，实现**统一、高效、灵活**的数据格式化需求。