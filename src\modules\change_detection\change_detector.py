"""
月度工资异动处理系统 - 异动识别器

本模块负责：
- 异动类型识别算法实现
- 多种异动模式的检测
- 异动置信度评估
- 复杂异动场景处理

支持的异动类型：
1. 新进人员
2. 离职人员  
3. 工资调整
4. 津贴变动
5. 扣款异动
6. 补发工资
7. 职务变动
8. 部门调动
9. 其他异动

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, date
from enum import Enum
import numpy as np

from src.utils.log_config import get_module_logger
from src.core.config_manager import ConfigManager
from .baseline_manager import BaselineManager

logger = get_module_logger(__name__)


class ChangeType(Enum):
    """异动类型枚举"""
    NEW_EMPLOYEE = "新进人员"
    RESIGNATION = "离职人员"
    SALARY_ADJUSTMENT = "工资调整"
    ALLOWANCE_CHANGE = "津贴变动"
    DEDUCTION = "扣款异动"
    SUPPLEMENTARY_PAY = "补发工资"
    POSITION_CHANGE = "职务变动"
    DEPARTMENT_TRANSFER = "部门调动"
    OTHER_CHANGE = "其他异动"


class ChangeDetector:
    """异动识别器"""
    
    def __init__(self, config_manager: ConfigManager, baseline_manager: BaselineManager):
        """
        初始化异动识别器
        
        Args:
            config_manager: 配置管理器实例
            baseline_manager: 基准数据管理器实例
        """
        self.config_manager = config_manager
        self.baseline_manager = baseline_manager
        self.detected_changes: List[Dict[str, Any]] = []
        
        # 异动检测配置
        self.salary_change_threshold = 50.0  # 工资变动阈值
        self.allowance_change_threshold = 20.0  # 津贴变动阈值
        self.confidence_threshold = 0.7  # 置信度阈值
        
        logger.info("异动识别器初始化完成")
    
    def detect_changes(
        self,
        current_data: pd.DataFrame,
        detection_period: str
    ) -> List[Dict[str, Any]]:
        """
        检测工资异动
        
        Args:
            current_data: 当前期间工资数据
            detection_period: 检测周期 (如 "2024-02")
            
        Returns:
            List[Dict[str, Any]]: 检测到的异动列表
        """
        try:
            logger.info(f"开始检测异动，检测周期: {detection_period}")
            
            # 清空之前的检测结果
            self.detected_changes = []
            
            # 验证输入数据
            if not self._validate_current_data(current_data):
                logger.error("当前数据验证失败")
                return []
            
            # 检查基准数据是否可用
            if self.baseline_manager.baseline_data is None:
                logger.error("基准数据未加载，无法进行异动检测")
                return []
            
            # 执行各种类型的异动检测
            self._detect_new_employees(current_data, detection_period)
            self._detect_resignations(current_data, detection_period)
            self._detect_salary_adjustments(current_data, detection_period)
            self._detect_allowance_changes(current_data, detection_period)
            self._detect_deductions(current_data, detection_period)
            self._detect_supplementary_pay(current_data, detection_period)
            self._detect_position_changes(current_data, detection_period)
            self._detect_department_transfers(current_data, detection_period)
            
            # 排序和过滤结果
            self._filter_and_rank_changes()
            
            logger.info(f"异动检测完成，发现 {len(self.detected_changes)} 项异动")
            return self.detected_changes
            
        except Exception as e:
            logger.error(f"异动检测失败: {str(e)}")
            return []
    
    def get_change_summary(self) -> Dict[str, Any]:
        """
        获取异动检测摘要
        
        Returns:
            Dict[str, Any]: 异动摘要信息
        """
        try:
            if not self.detected_changes:
                return {"message": "未检测到异动"}
            
            # 按异动类型统计
            type_counts = {}
            for change in self.detected_changes:
                change_type = change.get('change_type', '未知')
                type_counts[change_type] = type_counts.get(change_type, 0) + 1
            
            # 按置信度统计
            confidence_stats = {
                "高置信度(≥0.9)": len([c for c in self.detected_changes if c.get('confidence', 0) >= 0.9]),
                "中等置信度(0.7-0.9)": len([c for c in self.detected_changes if 0.7 <= c.get('confidence', 0) < 0.9]),
                "低置信度(<0.7)": len([c for c in self.detected_changes if c.get('confidence', 0) < 0.7])
            }
            
            summary = {
                "总异动数": len(self.detected_changes),
                "异动类型分布": type_counts,
                "置信度分布": confidence_stats,
                "检测时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            logger.info("异动摘要生成完成")
            return summary
            
        except Exception as e:
            logger.error(f"生成异动摘要失败: {str(e)}")
            return {"error": str(e)}
    
    def _validate_current_data(self, data: pd.DataFrame) -> bool:
        """
        验证当前数据格式
        
        Args:
            data: 当前期间数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if data.empty:
                logger.error("当前数据为空")
                return False
            
            # 检查必需字段
            required_fields = ["工号", "姓名"]
            missing_fields = [field for field in required_fields if field not in data.columns]
            if missing_fields:
                logger.error(f"当前数据缺少必需字段: {missing_fields}")
                return False
            
            logger.debug("当前数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def _detect_new_employees(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测新进人员
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            
            # 获取基准数据中的所有工号
            baseline_ids = set(baseline_data['工号'].astype(str))
            current_ids = set(current_data['工号'].astype(str))
            
            # 找出新增的工号
            new_ids = current_ids - baseline_ids
            
            for employee_id in new_ids:
                # 获取新员工信息
                employee_data = current_data[current_data['工号'].astype(str) == employee_id].iloc[0]
                
                change_record = {
                    'employee_id': employee_id,
                    'name': employee_data.get('姓名', ''),
                    'change_type': ChangeType.NEW_EMPLOYEE.value,
                    'period': period,
                    'confidence': 0.95,  # 新进人员置信度很高
                    'details': {
                        'description': f"新进人员: {employee_data.get('姓名', '')}",
                        'employee_data': employee_data.to_dict()
                    },
                    'detected_time': datetime.now()
                }
                
                self.detected_changes.append(change_record)
                logger.debug(f"检测到新进人员: {employee_id} - {employee_data.get('姓名', '')}")
            
            logger.info(f"新进人员检测完成，发现 {len(new_ids)} 人")
            
        except Exception as e:
            logger.error(f"新进人员检测失败: {str(e)}")
    
    def _detect_resignations(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测离职人员
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            
            # 获取基准数据和当前数据中的所有工号
            baseline_ids = set(baseline_data['工号'].astype(str))
            current_ids = set(current_data['工号'].astype(str))
            
            # 找出消失的工号（可能是离职）
            missing_ids = baseline_ids - current_ids
            
            for employee_id in missing_ids:
                # 获取基准数据中的员工信息
                employee_data = baseline_data[baseline_data['工号'].astype(str) == employee_id].iloc[0]
                
                change_record = {
                    'employee_id': employee_id,
                    'name': employee_data.get('姓名', ''),
                    'change_type': ChangeType.RESIGNATION.value,
                    'period': period,
                    'confidence': 0.85,  # 离职检测置信度较高
                    'details': {
                        'description': f"疑似离职人员: {employee_data.get('姓名', '')}",
                        'baseline_data': employee_data.to_dict()
                    },
                    'detected_time': datetime.now()
                }
                
                self.detected_changes.append(change_record)
                logger.debug(f"检测到疑似离职人员: {employee_id} - {employee_data.get('姓名', '')}")
            
            logger.info(f"离职人员检测完成，发现 {len(missing_ids)} 人")
            
        except Exception as e:
            logger.error(f"离职人员检测失败: {str(e)}")
    
    def _detect_salary_adjustments(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测工资调整
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            salary_columns = self._find_salary_columns(current_data, baseline_data)
            
            if not salary_columns:
                logger.warning("未找到工资相关字段，跳过工资调整检测")
                return
            
            # 遍历每个员工检测工资调整
            for _, current_row in current_data.iterrows():
                self._process_employee_salary_adjustments(
                    current_row, baseline_data, salary_columns, period
                )
            
            logger.info("工资调整检测完成")
            
        except Exception as e:
            logger.error(f"工资调整检测失败: {str(e)}")
    
    def _find_salary_columns(self, current_data: pd.DataFrame, baseline_data: pd.DataFrame) -> List[str]:
        """
        查找工资相关的列
        
        Args:
            current_data: 当前数据
            baseline_data: 基准数据
            
        Returns:
            List[str]: 工资列名列表
        """
        return [col for col in current_data.columns if '工资' in col and col in baseline_data.columns]
    
    def _process_employee_salary_adjustments(self, current_row: pd.Series, baseline_data: pd.DataFrame, 
                                           salary_columns: List[str], period: str) -> None:
        """
        处理单个员工的工资调整检测
        
        Args:
            current_row: 当前员工数据行
            baseline_data: 基准数据
            salary_columns: 工资列列表
            period: 检测周期
        """
        employee_id = str(current_row['工号'])
        baseline_row = self._get_employee_baseline(baseline_data, employee_id)
        
        if baseline_row is None:
            return  # 新员工，已在新进检测中处理
        
        # 检查每个工资字段
        for col in salary_columns:
            self._check_salary_field_change(current_row, baseline_row, col, period)
    
    def _check_salary_field_change(self, current_row: pd.Series, baseline_row: pd.Series, 
                                  field: str, period: str) -> None:
        """
        检查工资字段的变动
        
        Args:
            current_row: 当前数据行
            baseline_row: 基准数据行
            field: 工资字段名
            period: 检测周期
        """
        current_value = pd.to_numeric(current_row.get(field, 0), errors='coerce') or 0
        baseline_value = pd.to_numeric(baseline_row.get(field, 0), errors='coerce') or 0
        change_amount = current_value - baseline_value
        
        # 判断是否为显著变动
        if abs(change_amount) >= self.salary_change_threshold:
            change_record = self._create_salary_change_record(
                current_row, baseline_value, current_value, change_amount, field, period
            )
            self.detected_changes.append(change_record)
            logger.debug(f"检测到工资调整: {current_row['工号']} - {field}: {change_amount:+.2f}")
    
    def _create_salary_change_record(self, current_row: pd.Series, baseline_value: float, 
                                    current_value: float, change_amount: float, 
                                    field: str, period: str) -> Dict[str, Any]:
        """
        创建工资调整记录
        
        Args:
            current_row: 当前数据行
            baseline_value: 基准值
            current_value: 当前值
            change_amount: 变动金额
            field: 字段名
            period: 检测周期
            
        Returns:
            Dict[str, Any]: 变动记录
        """
        change_rate = self._calculate_change_rate(baseline_value, current_value)
        confidence = min(0.95, 0.5 + change_rate * 0.5)
        
        return {
            'employee_id': str(current_row['工号']),
            'name': current_row.get('姓名', ''),
            'change_type': ChangeType.SALARY_ADJUSTMENT.value,
            'period': period,
            'confidence': confidence,
            'details': {
                'field': field,
                'description': f"{field}变动: {baseline_value:.2f} → {current_value:.2f}",
                'change_amount': change_amount,
                'change_rate': change_rate,
                'baseline_value': baseline_value,
                'current_value': current_value
            },
            'detected_time': datetime.now()
        }
    
    def _calculate_change_rate(self, baseline_value: float, current_value: float) -> float:
        """
        计算变动率
        
        Args:
            baseline_value: 基准值
            current_value: 当前值
            
        Returns:
            float: 变动率
        """
        if baseline_value != 0:
            return abs(current_value - baseline_value) / baseline_value
        else:
            return 1.0 if current_value != 0 else 0.0
    
    def _detect_allowance_changes(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测津贴变动
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            allowance_columns = self._find_allowance_columns(current_data, baseline_data)
            
            if not allowance_columns:
                logger.warning("未找到津贴相关字段，跳过津贴变动检测")
                return
            
            # 遍历每个员工检测津贴变动
            for _, current_row in current_data.iterrows():
                self._process_employee_allowance_changes(
                    current_row, baseline_data, allowance_columns, period
                )
            
            logger.info("津贴变动检测完成")
            
        except Exception as e:
            logger.error(f"津贴变动检测失败: {str(e)}")
    
    def _find_allowance_columns(self, current_data: pd.DataFrame, baseline_data: pd.DataFrame) -> List[str]:
        """
        查找津贴相关的列
        
        Args:
            current_data: 当前数据
            baseline_data: 基准数据
            
        Returns:
            List[str]: 津贴列名列表
        """
        allowance_keywords = ['津贴', '补贴']
        return [col for col in current_data.columns 
                if any(keyword in col for keyword in allowance_keywords) and col in baseline_data.columns]
    
    def _process_employee_allowance_changes(self, current_row: pd.Series, baseline_data: pd.DataFrame, 
                                          allowance_columns: List[str], period: str) -> None:
        """
        处理单个员工的津贴变动检测
        
        Args:
            current_row: 当前员工数据行
            baseline_data: 基准数据
            allowance_columns: 津贴列列表
            period: 检测周期
        """
        employee_id = str(current_row['工号'])
        baseline_row = self._get_employee_baseline(baseline_data, employee_id)
        
        if baseline_row is None:
            return
        
        # 检查每个津贴字段
        for col in allowance_columns:
            self._check_allowance_field_change(current_row, baseline_row, col, period)
    
    def _get_employee_baseline(self, baseline_data: pd.DataFrame, employee_id: str) -> Optional[pd.Series]:
        """
        获取员工的基准数据
        
        Args:
            baseline_data: 基准数据
            employee_id: 员工工号
            
        Returns:
            Optional[pd.Series]: 员工基准数据行，如果不存在返回None
        """
        baseline_rows = baseline_data[baseline_data['工号'].astype(str) == employee_id]
        return baseline_rows.iloc[0] if not baseline_rows.empty else None
    
    def _check_allowance_field_change(self, current_row: pd.Series, baseline_row: pd.Series, 
                                     field: str, period: str) -> None:
        """
        检查津贴字段的变动
        
        Args:
            current_row: 当前数据行
            baseline_row: 基准数据行
            field: 津贴字段名
            period: 检测周期
        """
        current_value = pd.to_numeric(current_row.get(field, 0), errors='coerce') or 0
        baseline_value = pd.to_numeric(baseline_row.get(field, 0), errors='coerce') or 0
        change_amount = current_value - baseline_value
        
        # 判断是否为显著变动
        if abs(change_amount) >= self.allowance_change_threshold:
            change_record = self._create_allowance_change_record(
                current_row, baseline_value, current_value, change_amount, field, period
            )
            self.detected_changes.append(change_record)
            logger.debug(f"检测到津贴变动: {current_row['工号']} - {field}: {change_amount:+.2f}")
    
    def _create_allowance_change_record(self, current_row: pd.Series, baseline_value: float, 
                                       current_value: float, change_amount: float, 
                                       field: str, period: str) -> Dict[str, Any]:
        """
        创建津贴变动记录
        
        Args:
            current_row: 当前数据行
            baseline_value: 基准值
            current_value: 当前值
            change_amount: 变动金额
            field: 字段名
            period: 检测周期
            
        Returns:
            Dict[str, Any]: 变动记录
        """
        change_rate = self._calculate_change_rate(baseline_value, current_value)
        confidence = min(0.90, 0.6 + change_rate * 0.3)
        
        return {
            'employee_id': str(current_row['工号']),
            'name': current_row.get('姓名', ''),
            'change_type': ChangeType.ALLOWANCE_CHANGE.value,
            'period': period,
            'confidence': confidence,
            'details': {
                'field': field,
                'description': f"{field}变动: {baseline_value:.2f} → {current_value:.2f}",
                'change_amount': change_amount,
                'change_rate': change_rate,
                'baseline_value': baseline_value,
                'current_value': current_value
            },
            'detected_time': datetime.now()
        }
    
    def _detect_deductions(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测扣款异动
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            # 查找包含"扣"的列
            deduction_columns = [col for col in current_data.columns if '扣' in col]
            
            # 遍历每个员工
            for _, current_row in current_data.iterrows():
                employee_id = str(current_row['工号'])
                
                # 检查扣款字段
                for col in deduction_columns:
                    deduction_value = pd.to_numeric(current_row.get(col, 0), errors='coerce') or 0
                    
                    # 如果扣款金额大于0，记录为扣款异动
                    if deduction_value > 0:
                        change_record = {
                            'employee_id': employee_id,
                            'name': current_row.get('姓名', ''),
                            'change_type': ChangeType.DEDUCTION.value,
                            'period': period,
                            'confidence': 0.85,
                            'details': {
                                'field': col,
                                'description': f"{col}: {deduction_value:.2f}",
                                'deduction_amount': deduction_value
                            },
                            'detected_time': datetime.now()
                        }
                        
                        self.detected_changes.append(change_record)
                        logger.debug(f"检测到扣款异动: {employee_id} - {col}: {deduction_value:.2f}")
            
            logger.info("扣款异动检测完成")
            
        except Exception as e:
            logger.error(f"扣款异动检测失败: {str(e)}")
    
    def _detect_supplementary_pay(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测补发工资
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            # 查找包含"补发"或"补"的列
            supplement_columns = [col for col in current_data.columns if '补发' in col or '补' in col]
            
            # 遍历每个员工
            for _, current_row in current_data.iterrows():
                employee_id = str(current_row['工号'])
                
                # 检查补发字段
                for col in supplement_columns:
                    supplement_value = pd.to_numeric(current_row.get(col, 0), errors='coerce') or 0
                    
                    # 如果补发金额大于0，记录为补发异动
                    if supplement_value > 0:
                        change_record = {
                            'employee_id': employee_id,
                            'name': current_row.get('姓名', ''),
                            'change_type': ChangeType.SUPPLEMENTARY_PAY.value,
                            'period': period,
                            'confidence': 0.90,
                            'details': {
                                'field': col,
                                'description': f"{col}: {supplement_value:.2f}",
                                'supplement_amount': supplement_value
                            },
                            'detected_time': datetime.now()
                        }
                        
                        self.detected_changes.append(change_record)
                        logger.debug(f"检测到补发工资: {employee_id} - {col}: {supplement_value:.2f}")
            
            logger.info("补发工资检测完成")
            
        except Exception as e:
            logger.error(f"补发工资检测失败: {str(e)}")
    
    def _detect_position_changes(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测职务变动
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            
            # 查找职务相关字段
            position_fields = [col for col in current_data.columns 
                             if any(keyword in col for keyword in ['职务', '职位', '岗位', '职级'])]
            position_fields = [col for col in position_fields if col in baseline_data.columns]
            
            if not position_fields:
                logger.warning("未找到职务相关字段，跳过职务变动检测")
                return
            
            # 遍历每个员工
            for _, current_row in current_data.iterrows():
                employee_id = str(current_row['工号'])
                
                # 获取基准数据
                baseline_row = baseline_data[baseline_data['工号'].astype(str) == employee_id]
                if baseline_row.empty:
                    continue
                
                baseline_row = baseline_row.iloc[0]
                
                # 检查职务字段变化
                for col in position_fields:
                    current_value = str(current_row.get(col, '')).strip()
                    baseline_value = str(baseline_row.get(col, '')).strip()
                    
                    # 如果职务发生变化
                    if current_value != baseline_value and current_value and baseline_value:
                        change_record = {
                            'employee_id': employee_id,
                            'name': current_row.get('姓名', ''),
                            'change_type': ChangeType.POSITION_CHANGE.value,
                            'period': period,
                            'confidence': 0.80,
                            'details': {
                                'field': col,
                                'description': f"{col}变动: {baseline_value} → {current_value}",
                                'old_position': baseline_value,
                                'new_position': current_value
                            },
                            'detected_time': datetime.now()
                        }
                        
                        self.detected_changes.append(change_record)
                        logger.debug(f"检测到职务变动: {employee_id} - {col}: {baseline_value} → {current_value}")
            
            logger.info("职务变动检测完成")
            
        except Exception as e:
            logger.error(f"职务变动检测失败: {str(e)}")
    
    def _detect_department_transfers(self, current_data: pd.DataFrame, period: str) -> None:
        """
        检测部门调动
        
        Args:
            current_data: 当前数据
            period: 检测周期
        """
        try:
            baseline_data = self.baseline_manager.baseline_data
            
            # 查找部门相关字段
            department_fields = [col for col in current_data.columns 
                               if any(keyword in col for keyword in ['部门', '科室', '单位', '机构'])]
            department_fields = [col for col in department_fields if col in baseline_data.columns]
            
            if not department_fields:
                logger.warning("未找到部门相关字段，跳过部门调动检测")
                return
            
            # 遍历每个员工
            for _, current_row in current_data.iterrows():
                employee_id = str(current_row['工号'])
                
                # 获取基准数据
                baseline_row = baseline_data[baseline_data['工号'].astype(str) == employee_id]
                if baseline_row.empty:
                    continue
                
                baseline_row = baseline_row.iloc[0]
                
                # 检查部门字段变化
                for col in department_fields:
                    current_value = str(current_row.get(col, '')).strip()
                    baseline_value = str(baseline_row.get(col, '')).strip()
                    
                    # 如果部门发生变化
                    if current_value != baseline_value and current_value and baseline_value:
                        change_record = {
                            'employee_id': employee_id,
                            'name': current_row.get('姓名', ''),
                            'change_type': ChangeType.DEPARTMENT_TRANSFER.value,
                            'period': period,
                            'confidence': 0.85,
                            'details': {
                                'field': col,
                                'description': f"{col}调动: {baseline_value} → {current_value}",
                                'old_department': baseline_value,
                                'new_department': current_value
                            },
                            'detected_time': datetime.now()
                        }
                        
                        self.detected_changes.append(change_record)
                        logger.debug(f"检测到部门调动: {employee_id} - {col}: {baseline_value} → {current_value}")
            
            logger.info("部门调动检测完成")
            
        except Exception as e:
            logger.error(f"部门调动检测失败: {str(e)}")
    
    def _filter_and_rank_changes(self) -> None:
        """
        过滤和排序异动结果
        """
        try:
            # 按置信度过滤
            filtered_changes = [
                change for change in self.detected_changes 
                if change.get('confidence', 0) >= self.confidence_threshold
            ]
            
            # 按置信度降序排序
            filtered_changes.sort(key=lambda x: x.get('confidence', 0), reverse=True)
            
            self.detected_changes = filtered_changes
            
            logger.info(f"异动结果过滤完成，保留 {len(self.detected_changes)} 项高置信度异动")
            
        except Exception as e:
            logger.error(f"异动结果过滤失败: {str(e)}") 