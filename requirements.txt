# 月度工资异动处理系统 - 依赖包清单
# 项目名称: 月度工资异动处理系统
# 技术栈: Python 3.12 + PyQt5 + SQLite + pandas
# 创建时间: 2025-06-13 (VAN阶段)
# 最后更新: 2025-01-22 (版本升级: Python 3.9 → 3.12)

# ============================================
# 版本升级说明 (2025-01-22)
# ============================================
# 升级原因: 解决Python 3.9.8 + PyInstaller 5.13.0在Windows平台的兼容性问题
# 主要问题: Windows清单文件处理导致的打包和执行异常
# 解决方案: 升级到Python 3.12 + PyInstaller 5.13.0组合
# 测试状态: 已验证打包和执行正常

# ============================================
# GUI框架 - 用户界面
# ============================================
PyQt5==5.15.10
# 注释：5.15.10版本对Python 3.12兼容性更好，解决了5.15.9的依赖问题
# 升级原因：5.15.9在Python 3.12环境下可能遇到PyQt5-Qt5依赖包版本冲突

# ============================================
# 数据处理库 - Excel和数据分析
# ============================================
pandas==2.0.3
# 注释：Excel数据读取、数据清洗、数据分析
openpyxl==3.1.2
# 注释：.xlsx文件读写、格式保持
xlrd==2.0.1
# 注释：.xls文件读取（老格式Excel）
xlsxwriter==3.1.2
# 注释：高性能Excel文件写入

# ============================================
# 数据库相关 (SQLite内置，无需额外安装)
# ============================================
# SQLite - Python内置支持，无需额外依赖

# ============================================
# 文档处理库 - Word文档和模板
# ============================================
python-docx==0.8.11
# 注释：Word文档模板填充，格式保持
Jinja2==3.1.2
# 注释：模板引擎，文档内容生成

# ============================================
# 日志和工具库
# ============================================
loguru==0.7.0
# 注释：结构化日志系统，替代标准logging
click==8.1.7
# 注释：命令行接口支持
tqdm==4.65.0
# 注释：进度条显示，用户体验改善

# ============================================
# 打包和部署工具
# ============================================
PyInstaller==5.13.0
# 注释：生成Windows exe文件，单文件部署

# ============================================
# 开发和测试工具
# ============================================
pytest==7.4.0
# 注释：单元测试框架
pytest-cov==4.1.0
# 注释：代码覆盖率测试插件
pytest-qt==4.2.0
# 注释：PyQt GUI测试支持
black==23.7.0
# 注释：Python代码自动格式化
flake8==6.0.0
# 注释：PEP 8规范检查和静态分析

# ============================================
# 性能监控工具 (REFLECT Day 4 新增)
# ============================================
# memory-profiler==0.61.0
# 注释：内存使用分析工具，性能调优时使用
psutil==5.9.5
# 注释：系统资源监控，性能基准测试必需 (REFLECT Day 4启用)

# ============================================
# 安装说明
# ============================================
# 1. 创建虚拟环境:
#    python -m venv salary_system_env
#    salary_system_env\Scripts\activate
#
# 2. 升级pip:
#    python -m pip install --upgrade pip
#
# 3. 安装依赖:
#    pip install -r requirements.txt
#
# 4. 验证安装:
#    python -c "import PyQt5; import pandas; import openpyxl; print('All dependencies installed successfully!')"

# ============================================
# 版本兼容性说明
# ============================================
# - Python 3.12.x (推荐) 或 3.11.x
# - Windows 10/11 (64位)
# - 内存要求: 开发环境≥8GB, 运行环境≥4GB
# - 磁盘空间: 开发环境≥2GB, 部署包≥500MB

# ============================================
# 升级注意事项
# ============================================
# 1. 从Python 3.9升级到3.12的兼容性变化:
#    - 移除了一些已弃用的模块和函数
#    - 某些语法可能需要调整
#    - 第三方库版本可能需要更新
#
# 2. 升级步骤:
#    - 卸载Python 3.9环境
#    - 安装Python 3.12
#    - 重新创建虚拟环境
#    - 重新安装所有依赖
#    - 运行完整测试

# ============================================
# 依赖包总大小估算
# ============================================
# PyQt5: ~50MB
# pandas + numpy: ~40MB
# openpyxl + xlrd + xlsxwriter: ~5MB
# python-docx + Jinja2: ~3MB
# psutil: ~2MB (新增)
# 其他工具库: ~10MB
# 总计约: ~110MB (开发环境)
# PyInstaller打包后: ~150-200MB (exe文件) 