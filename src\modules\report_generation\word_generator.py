"""
Word文档生成器 (Word Generator)

专门处理Word模板的数据填充、格式保持和文档生成。
支持复杂的Word文档结构，包括表格、段落、页眉页脚等。

主要功能：
- Word模板解析和数据绑定
- 保持原始格式和样式
- 支持表格数据批量填充
- 段落和文本的动态替换
- 生成新的Word文档

依赖库：
- python-docx: Word文档处理
- docxtpl: Word模板引擎

Author: Development Team
Date: 2025-06-13
"""

import os
import shutil
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging

try:
    from docx import Document  # type: ignore
    from docxtpl import DocxTemplate  # type: ignore
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    
from src.utils.log_config import get_module_logger
from .template_engine import TemplateEngine


class WordGenerator:
    """Word文档生成器"""
    
    def __init__(self, template_engine: Optional[TemplateEngine] = None):
        """
        初始化Word生成器
        
        Args:
            template_engine: 模板引擎实例，如果为None则创建新实例
        """
        self.logger = get_module_logger(__name__)
        
        # 检查依赖库
        if not DOCX_AVAILABLE:
            raise ImportError("缺少Word处理依赖库，请安装: pip install python-docx docxtpl")
            
        # 初始化模板引擎
        if template_engine is None:
            self.template_engine = TemplateEngine()
        else:
            self.template_engine = template_engine
            
        self.logger.info("Word生成器初始化完成")
        
    def generate_document(self, 
                         template_key: str, 
                         data: Dict[str, Any], 
                         output_path: str, 
                         save_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        生成Word文档
        
        Args:
            template_key: 模板键名
            data: 填充数据字典
            output_path: 输出文件路径
            save_options: 保存选项
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 验证模板类型
            template_info = self.template_engine.get_template_info(template_key)
            if template_info['type'] != 'word':
                raise ValueError(f"模板 {template_key} 不是Word类型")
                
            # 获取模板路径
            template_path = self.template_engine.get_template_path(template_key)
            
            # 验证数据完整性
            validation_result = self.template_engine.validate_template_data(template_key, data)
            if not validation_result['valid']:
                self.logger.warning(f"数据验证未通过，缺失字段: {validation_result['missing_fields']}")
                
            self.logger.info(f"开始生成Word文档: {template_key} -> {output_path}")
            
            # 预处理数据
            processed_data = self._preprocess_data(data, template_key)
            
            # 使用docxtpl处理模板
            success = self._generate_with_docxtpl(template_path, processed_data, output_path)
            
            if success:
                self.logger.info(f"Word文档生成成功: {output_path}")
                # 验证生成的文件
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    return True
                else:
                    self.logger.error("生成的文件不存在或为空")
                    return False
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成Word文档失败: {str(e)}", exc_info=True)
            return False
            
    def _generate_with_docxtpl(self, 
                              template_path: Path, 
                              data: Dict[str, Any], 
                              output_path: str) -> bool:
        """
        使用docxtpl生成文档
        
        Args:
            template_path: 模板文件路径
            data: 数据字典
            output_path: 输出路径
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 创建DocxTemplate实例
            template = DocxTemplate(str(template_path))
            
            # 渲染模板
            template.render(data)
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                
            # 保存文档
            template.save(output_path)
            
            self.logger.debug(f"使用docxtpl生成文档成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"docxtpl生成失败: {str(e)}")
            # 尝试备用方法
            return self._generate_with_simple_replace(template_path, data, output_path)
            
    def _generate_with_simple_replace(self, 
                                     template_path: Path, 
                                     data: Dict[str, Any], 
                                     output_path: str) -> bool:
        """
        使用简单替换方法生成文档（备用方法）
        
        Args:
            template_path: 模板文件路径
            data: 数据字典
            output_path: 输出路径
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 复制模板文件
            shutil.copy2(template_path, output_path)
            
            # 打开文档进行文本替换
            document = Document(output_path)
            
            # 替换段落中的文本
            for paragraph in document.paragraphs:
                if paragraph.text:
                    new_text = self.template_engine.replace_placeholders(paragraph.text, data)
                    if new_text != paragraph.text:
                        # 清除段落内容并重新设置
                        paragraph.clear()
                        paragraph.add_run(new_text)
                        
            # 替换表格中的文本
            for table in document.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text:
                                new_text = self.template_engine.replace_placeholders(paragraph.text, data)
                                if new_text != paragraph.text:
                                    paragraph.clear()
                                    paragraph.add_run(new_text)
                                    
            # 保存文档
            document.save(output_path)
            
            self.logger.debug(f"使用简单替换生成文档成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"简单替换生成失败: {str(e)}")
            return False
            
    def _preprocess_data(self, data: Dict[str, Any], template_key: str) -> Dict[str, Any]:
        """
        预处理数据，进行格式化和转换
        
        Args:
            data: 原始数据
            template_key: 模板键名
            
        Returns:
            Dict: 处理后的数据
        """
        processed_data = data.copy()
        
        # 格式化金额
        if 'total_amount' in processed_data:
            processed_data['total_amount_formatted'] = self.template_engine.format_currency(
                processed_data['total_amount']
            )
            
        # 格式化日期
        if 'year' in processed_data and 'month' in processed_data:
            processed_data['year_month'] = f"{processed_data['year']}年{processed_data['month']}月"
            
        # 根据模板类型进行特殊处理
        if template_key == 'department_meeting':
            processed_data = self._preprocess_department_meeting_data(processed_data)
        elif template_key == 'oa_process':
            processed_data = self._preprocess_oa_process_data(processed_data)
            
        self.logger.debug(f"数据预处理完成，字段数: {len(processed_data)}")
        return processed_data
        
    def _preprocess_department_meeting_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理部务会请示数据
        
        Args:
            data: 原始数据
            
        Returns:
            Dict: 处理后的数据
        """
        processed = data.copy()
        
        # 添加部务会特有字段
        if 'meeting_date' not in processed:
            processed['meeting_date'] = f"{processed.get('year', '2025')}年{processed.get('month', '5')}月"
            
        # 添加会议主题
        if 'meeting_subject' not in processed:
            processed['meeting_subject'] = f"审议{processed['year_month']}教职工薪酬发放事宜"
            
        return processed
        
    def _preprocess_oa_process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理OA流程请示数据
        
        Args:
            data: 原始数据
            
        Returns:
            Dict: 处理后的数据
        """
        processed = data.copy()
        
        # 添加OA流程特有字段
        if 'process_title' not in processed:
            processed['process_title'] = f"关于发放{processed['year_month']}教职工薪酬的请示"
            
        # 添加流程编号
        if 'process_id' not in processed:
            processed['process_id'] = f"PAY-{processed.get('year', '2025')}{processed.get('month', '05').zfill(2)}-001"
            
        return processed
        
    def get_template_placeholders(self, template_key: str) -> List[str]:
        """
        获取模板中的占位符列表
        
        Args:
            template_key: 模板键名
            
        Returns:
            List[str]: 占位符列表
        """
        try:
            template_path = self.template_engine.get_template_path(template_key)
            template_info = self.template_engine.get_template_info(template_key)
            
            if template_info['type'] != 'word':
                raise ValueError(f"模板 {template_key} 不是Word类型")
                
            placeholders = []
            
            # 打开文档提取占位符
            document = Document(template_path)
            
            # 从段落中提取
            for paragraph in document.paragraphs:
                if paragraph.text:
                    placeholders.extend(self.template_engine.extract_placeholders(paragraph.text))
                    
            # 从表格中提取
            for table in document.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text:
                                placeholders.extend(self.template_engine.extract_placeholders(paragraph.text))
                                
            # 去重并返回
            unique_placeholders = list(set(placeholders))
            self.logger.debug(f"模板 {template_key} 包含占位符: {unique_placeholders}")
            return unique_placeholders
            
        except Exception as e:
            self.logger.error(f"提取模板占位符失败: {str(e)}")
            return []
            
    def preview_document(self, template_key: str, data: Dict[str, Any]) -> Optional[str]:
        """
        预览文档内容（生成临时文件）
        
        Args:
            template_key: 模板键名
            data: 数据字典
            
        Returns:
            Optional[str]: 临时文件路径，如果失败返回None
        """
        try:
            # 创建临时文件路径
            import tempfile
            import uuid
            
            temp_dir = tempfile.gettempdir()
            temp_filename = f"preview_{template_key}_{uuid.uuid4().hex[:8]}.docx"
            temp_path = os.path.join(temp_dir, temp_filename)
            
            # 生成预览文档
            if self.generate_document(template_key, data, temp_path):
                self.logger.info(f"预览文档生成成功: {temp_path}")
                return temp_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"生成预览文档失败: {str(e)}")
            return None 