#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能工作表匹配器

提供基于人员类别的智能工作表匹配功能，包括：
- 精确匹配
- 模糊匹配
- 语义匹配
- 学习用户选择偏好

创建时间: 2025-01-20
"""

import re
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict
from functools import lru_cache

from src.utils.log_config import setup_logger
from .import_defaults_manager import ImportDefaultsManager


@dataclass
class MatchResult:
    """匹配结果"""
    sheet_name: str
    score: float
    match_type: str  # 'exact', 'fuzzy', 'semantic'
    matched_keyword: str


class SmartSheetMatcher:
    """智能工作表匹配器"""
    
    def __init__(self, defaults_manager: Optional[ImportDefaultsManager] = None):
        """
        初始化智能匹配器

        Args:
            defaults_manager: 默认设置管理器实例
        """
        self.logger = setup_logger(__name__)
        self.defaults_manager = defaults_manager or ImportDefaultsManager()

        # 用户选择历史记录（用于学习用户偏好）
        self.user_preferences = defaultdict(dict)

        # 缓存机制
        self._match_cache = {}
        self._keyword_cache = {}
        self._similarity_cache = {}
        self._cache_max_size = 1000
        self._cache_ttl = 3600  # 缓存1小时

        # 性能统计
        self._stats = {
            'total_matches': 0,
            'cache_hits': 0,
            'avg_match_time': 0.0
        }

        # 语义相似词典
        self.semantic_dict = {
            '在职': ['现职', '正式', '编制', '员工'],
            '离休': ['离岗', '离职'],
            '退休': ['退岗', '退职'],
            '临时': ['临聘', '合同', '外聘'],
            '实习': ['见习', '培训', '学员'],
            '管理': ['领导', '干部', '主管'],
            '技术': ['工程', '专业', '技能'],
            '全部': ['所有', '全员', '整体', '总计']
        }
    
    def find_best_match(self, category: str, sheet_names: List[str]) -> Optional[MatchResult]:
        """
        查找最佳匹配的工作表

        Args:
            category: 人员类别名称
            sheet_names: 可用的工作表名称列表

        Returns:
            最佳匹配结果，如果没有找到则返回None
        """
        if not category or not sheet_names:
            return None

        start_time = time.time()

        # 生成缓存键
        cache_key = f"{category}:{':'.join(sorted(sheet_names))}"

        # 检查缓存
        if cache_key in self._match_cache:
            cache_entry = self._match_cache[cache_key]
            if time.time() - cache_entry['timestamp'] < self._cache_ttl:
                self._stats['cache_hits'] += 1
                self._stats['total_matches'] += 1
                return cache_entry['result']
            else:
                # 缓存过期，删除
                del self._match_cache[cache_key]

        # 执行匹配
        result = self._perform_match(category, sheet_names)

        # 更新缓存
        self._update_cache(cache_key, result)

        # 更新性能统计
        match_time = time.time() - start_time
        self._update_stats(match_time)

        return result

    def _perform_match(self, category: str, sheet_names: List[str]) -> Optional[MatchResult]:
        """执行实际的匹配逻辑"""
        # 1. 检查用户历史偏好
        preference_match = self._check_user_preference(category, sheet_names)
        if preference_match:
            return preference_match

        # 2. 精确匹配
        exact_match = self._exact_match(category, sheet_names)
        if exact_match:
            return exact_match

        # 3. 模糊匹配
        fuzzy_matches = self._fuzzy_match(category, sheet_names)

        # 4. 语义匹配
        semantic_matches = self._semantic_match(category, sheet_names)

        # 合并所有匹配结果并选择最佳
        all_matches = fuzzy_matches + semantic_matches
        if all_matches:
            # 按得分排序，选择最高分
            best_match = max(all_matches, key=lambda x: x.score)
            if best_match.score > 0.3:  # 最低阈值
                return best_match

        return None

    def _update_cache(self, cache_key: str, result: Optional[MatchResult]):
        """更新缓存"""
        # 如果缓存太大，清理最旧的条目
        if len(self._match_cache) >= self._cache_max_size:
            oldest_key = min(self._match_cache.keys(),
                           key=lambda k: self._match_cache[k]['timestamp'])
            del self._match_cache[oldest_key]

        self._match_cache[cache_key] = {
            'result': result,
            'timestamp': time.time()
        }

    def _update_stats(self, match_time: float):
        """更新性能统计"""
        self._stats['total_matches'] += 1

        # 计算平均匹配时间
        total_time = self._stats['avg_match_time'] * (self._stats['total_matches'] - 1) + match_time
        self._stats['avg_match_time'] = total_time / self._stats['total_matches']
    
    def _check_user_preference(self, category: str, sheet_names: List[str]) -> Optional[MatchResult]:
        """
        检查用户历史偏好
        
        Args:
            category: 人员类别
            sheet_names: 工作表名称列表
            
        Returns:
            偏好匹配结果
        """
        if category in self.user_preferences:
            for sheet_name in sheet_names:
                if sheet_name in self.user_preferences[category]:
                    count = self.user_preferences[category][sheet_name]
                    score = min(1.0, count * 0.1)  # 使用次数越多得分越高
                    return MatchResult(
                        sheet_name=sheet_name,
                        score=score,
                        match_type='preference',
                        matched_keyword='用户偏好'
                    )
        return None
    
    def _exact_match(self, category: str, sheet_names: List[str]) -> Optional[MatchResult]:
        """
        精确匹配
        
        Args:
            category: 人员类别
            sheet_names: 工作表名称列表
            
        Returns:
            精确匹配结果
        """
        for sheet_name in sheet_names:
            if category == sheet_name:
                return MatchResult(
                    sheet_name=sheet_name,
                    score=1.0,
                    match_type='exact',
                    matched_keyword=category
                )
        return None
    
    def _fuzzy_match(self, category: str, sheet_names: List[str]) -> List[MatchResult]:
        """
        模糊匹配
        
        Args:
            category: 人员类别
            sheet_names: 工作表名称列表
            
        Returns:
            模糊匹配结果列表
        """
        results = []
        mapping = self.defaults_manager.get_category_sheet_mapping()
        keywords = mapping.get(category, [category])
        
        for sheet_name in sheet_names:
            for keyword in keywords:
                # 包含匹配
                if keyword in sheet_name:
                    score = len(keyword) / len(sheet_name)
                    results.append(MatchResult(
                        sheet_name=sheet_name,
                        score=score * 0.9,  # 稍微降低模糊匹配的得分
                        match_type='fuzzy',
                        matched_keyword=keyword
                    ))
                
                # 反向包含匹配
                elif sheet_name in keyword:
                    score = len(sheet_name) / len(keyword)
                    results.append(MatchResult(
                        sheet_name=sheet_name,
                        score=score * 0.8,
                        match_type='fuzzy',
                        matched_keyword=keyword
                    ))
        
        return results
    
    def _semantic_match(self, category: str, sheet_names: List[str]) -> List[MatchResult]:
        """
        语义匹配
        
        Args:
            category: 人员类别
            sheet_names: 工作表名称列表
            
        Returns:
            语义匹配结果列表
        """
        results = []
        
        # 提取类别中的关键词
        category_keywords = self._extract_keywords(category)
        
        for sheet_name in sheet_names:
            sheet_keywords = self._extract_keywords(sheet_name)
            
            # 计算语义相似度
            similarity = self._calculate_semantic_similarity(category_keywords, sheet_keywords)
            
            if similarity > 0.3:
                results.append(MatchResult(
                    sheet_name=sheet_name,
                    score=similarity * 0.7,  # 语义匹配得分相对较低
                    match_type='semantic',
                    matched_keyword='语义相似'
                ))
        
        return results
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            关键词列表
        """
        # 移除常见的无意义词汇
        stop_words = {'人员', '职工', '员工', '表', '数据', '信息'}
        
        # 使用正则表达式分割文本
        words = re.findall(r'[\u4e00-\u9fff]+', text)
        
        keywords = []
        for word in words:
            if word not in stop_words and len(word) >= 2:
                keywords.append(word)
        
        return keywords
    
    def _calculate_semantic_similarity(self, keywords1: List[str], keywords2: List[str]) -> float:
        """
        计算语义相似度
        
        Args:
            keywords1: 第一组关键词
            keywords2: 第二组关键词
            
        Returns:
            相似度得分 (0-1)
        """
        if not keywords1 or not keywords2:
            return 0.0
        
        similarity_score = 0.0
        total_comparisons = 0
        
        for word1 in keywords1:
            for word2 in keywords2:
                total_comparisons += 1
                
                # 直接匹配
                if word1 == word2:
                    similarity_score += 1.0
                    continue
                
                # 语义词典匹配
                semantic_score = self._check_semantic_dict(word1, word2)
                similarity_score += semantic_score
        
        return similarity_score / total_comparisons if total_comparisons > 0 else 0.0
    
    def _check_semantic_dict(self, word1: str, word2: str) -> float:
        """
        检查语义词典中的相似度
        
        Args:
            word1: 第一个词
            word2: 第二个词
            
        Returns:
            语义相似度得分
        """
        for key, synonyms in self.semantic_dict.items():
            if word1 == key and word2 in synonyms:
                return 0.8
            if word2 == key and word1 in synonyms:
                return 0.8
            if word1 in synonyms and word2 in synonyms:
                return 0.6
        
        return 0.0
    
    def record_user_choice(self, category: str, chosen_sheet: str):
        """
        记录用户选择，用于学习偏好
        
        Args:
            category: 人员类别
            chosen_sheet: 用户选择的工作表
        """
        if category not in self.user_preferences:
            self.user_preferences[category] = defaultdict(int)
        
        self.user_preferences[category][chosen_sheet] += 1
        self.logger.info(f"记录用户选择: {category} -> {chosen_sheet}")
    
    def get_match_suggestions(self, category: str, sheet_names: List[str], top_n: int = 3) -> List[MatchResult]:
        """
        获取匹配建议列表
        
        Args:
            category: 人员类别
            sheet_names: 工作表名称列表
            top_n: 返回前N个建议
            
        Returns:
            匹配建议列表
        """
        if not category or not sheet_names:
            return []
        
        all_matches = []
        
        # 收集所有类型的匹配结果
        exact_match = self._exact_match(category, sheet_names)
        if exact_match:
            all_matches.append(exact_match)
        
        fuzzy_matches = self._fuzzy_match(category, sheet_names)
        all_matches.extend(fuzzy_matches)
        
        semantic_matches = self._semantic_match(category, sheet_names)
        all_matches.extend(semantic_matches)
        
        # 去重并排序
        unique_matches = {}
        for match in all_matches:
            if match.sheet_name not in unique_matches or match.score > unique_matches[match.sheet_name].score:
                unique_matches[match.sheet_name] = match
        
        # 按得分排序并返回前N个
        sorted_matches = sorted(unique_matches.values(), key=lambda x: x.score, reverse=True)
        return sorted_matches[:top_n]

    def clear_cache(self):
        """清空所有缓存"""
        self._match_cache.clear()
        self._keyword_cache.clear()
        self._similarity_cache.clear()
        self.logger.info("缓存已清空")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        cache_hit_rate = 0.0
        if self._stats['total_matches'] > 0:
            cache_hit_rate = self._stats['cache_hits'] / self._stats['total_matches']

        return {
            'total_matches': self._stats['total_matches'],
            'cache_hits': self._stats['cache_hits'],
            'cache_hit_rate': cache_hit_rate,
            'avg_match_time_ms': self._stats['avg_match_time'] * 1000,
            'cache_size': len(self._match_cache),
            'cache_max_size': self._cache_max_size
        }

    def optimize_performance(self):
        """性能优化"""
        # 清理过期缓存
        current_time = time.time()
        expired_keys = [
            key for key, value in self._match_cache.items()
            if current_time - value['timestamp'] > self._cache_ttl
        ]

        for key in expired_keys:
            del self._match_cache[key]

        if expired_keys:
            self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")

    @lru_cache(maxsize=256)
    def _cached_extract_keywords(self, text: str) -> Tuple[str, ...]:
        """缓存版本的关键词提取"""
        keywords = self._extract_keywords(text)
        return tuple(keywords)  # 返回元组以支持缓存
