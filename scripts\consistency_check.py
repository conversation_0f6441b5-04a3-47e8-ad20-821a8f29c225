#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目一致性检查工具

用于快速诊断文档与代码实现不一致的问题，
检查主程序入口、导入语句、配置文件等关键要素。

使用方法:
    python scripts/consistency_check.py
    python scripts/consistency_check.py --detailed
"""

import os
import sys
import ast
import json
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ConsistencyChecker:
    """项目一致性检查器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.issues = []
        self.warnings = []
        self.successes = []
    
    def check_all(self, detailed: bool = False) -> Dict[str, bool]:
        """执行所有检查"""
        print("🔍 开始项目一致性检查...")
        print("=" * 50)
        
        results = {}
        
        # 1. 检查主程序入口
        results['main_entry'] = self.check_main_entry()
        
        # 2. 检查导入语句有效性
        results['imports'] = self.check_imports()
        
        # 3. 检查配置文件
        results['configs'] = self.check_config_files()
        
        # 4. 检查关键组件存在性
        results['components'] = self.check_key_components()
        
        if detailed:
            # 5. 检查文档一致性 (详细模式)
            results['documentation'] = self.check_documentation_consistency()
            
            # 6. 检查测试覆盖 (详细模式)
            results['tests'] = self.check_test_coverage()
        
        self.print_summary(results)
        return results
    
    def check_main_entry(self) -> bool:
        """检查主程序入口一致性"""
        print("\n📋 检查主程序入口...")
        
        main_py = self.project_root / "main.py"
        if not main_py.exists():
            self.issues.append("❌ main.py 文件不存在")
            return False
        
        try:
            with open(main_py, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查导入的主窗口类
            main_window_imports = []
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'MainWindow' in line and ('import' in line or 'from' in line):
                    main_window_imports.append((i, line.strip()))
            
            if not main_window_imports:
                self.issues.append("❌ 未找到主窗口类导入")
                return False
            
            # 检查是否导入了正确的原型主窗口
            correct_import = False
            for line_num, line in main_window_imports:
                if 'PrototypeMainWindow' in line and 'prototype' in line:
                    correct_import = True
                    self.successes.append(f"✅ 正确导入原型主窗口 (行{line_num})")
                    break
                elif 'ModernMainWindow' in line:
                    self.issues.append(f"❌ 使用了旧版主窗口 (行{line_num}): {line}")
                elif 'MainWindow' in line:
                    self.warnings.append(f"⚠️  发现其他主窗口导入 (行{line_num}): {line}")
            
            if correct_import:
                # 验证导入的类是否可用
                try:
                    from src.gui.prototype.prototype_main_window import PrototypeMainWindow
                    self.successes.append("✅ 原型主窗口类可正常导入")
                    return True
                except ImportError as e:
                    self.issues.append(f"❌ 原型主窗口类导入失败: {e}")
                    return False
            else:
                self.issues.append("❌ 未使用正确的原型主窗口")
                return False
                
        except Exception as e:
            self.issues.append(f"❌ 检查主程序入口失败: {e}")
            return False
    
    def check_imports(self) -> bool:
        """检查关键导入语句有效性"""
        print("\n📦 检查导入语句有效性...")
        
        key_imports = [
            'src.gui.prototype.prototype_main_window',
            'src.gui.prototype.managers.responsive_layout_manager',
            'src.gui.prototype.managers.communication_manager',
            'src.gui.prototype.widgets.enhanced_navigation_panel',
            'src.gui.prototype.widgets.virtualized_expandable_table',
            'src.gui.prototype.widgets.material_tab_widget'
        ]
        
        failed_imports = []
        success_count = 0
        
        for module_path in key_imports:
            try:
                __import__(module_path)
                success_count += 1
                self.successes.append(f"✅ {module_path}")
            except ImportError as e:
                failed_imports.append((module_path, str(e)))
                self.issues.append(f"❌ {module_path}: {e}")
        
        if failed_imports:
            print(f"   导入失败: {len(failed_imports)}/{len(key_imports)}")
            return False
        else:
            print(f"   所有关键模块导入成功: {success_count}/{len(key_imports)}")
            return True
    
    def check_config_files(self) -> bool:
        """检查配置文件"""
        print("\n⚙️ 检查配置文件...")
        
        config_files = [
            'config.json',
            'test_config.json',
            'requirements.txt'
        ]
        
        all_valid = True
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            
            if not config_path.exists():
                self.warnings.append(f"⚠️  配置文件不存在: {config_file}")
                continue
            
            try:
                if config_file.endswith('.json'):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        json.load(f)
                    self.successes.append(f"✅ {config_file} 格式正确")
                else:
                    # 简单检查文件是否可读
                    with open(config_path, 'r', encoding='utf-8') as f:
                        f.read()
                    self.successes.append(f"✅ {config_file} 可正常读取")
                    
            except Exception as e:
                self.issues.append(f"❌ {config_file} 格式错误: {e}")
                all_valid = False
        
        return all_valid
    
    def check_key_components(self) -> bool:
        """检查关键组件文件存在性"""
        print("\n🧩 检查关键组件...")
        
        key_files = [
            'src/gui/prototype/prototype_main_window.py',
            'src/gui/prototype/managers/responsive_layout_manager.py',
            'src/gui/prototype/managers/communication_manager.py',
            'src/gui/prototype/widgets/enhanced_navigation_panel.py',
            'src/gui/prototype/widgets/virtualized_expandable_table.py',
            'src/gui/prototype/widgets/material_tab_widget.py',
            'src/utils/log_config.py'
        ]
        
        missing_files = []
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.successes.append(f"✅ {file_path}")
            else:
                missing_files.append(file_path)
                self.issues.append(f"❌ 文件缺失: {file_path}")
        
        if missing_files:
            print(f"   缺失文件: {len(missing_files)}/{len(key_files)}")
            return False
        else:
            print(f"   所有关键文件存在: {len(key_files)}/{len(key_files)}")
            return True
    
    def check_documentation_consistency(self) -> bool:
        """检查文档一致性 (详细模式)"""
        print("\n📚 检查文档一致性...")
        
        doc_files = [
            'memory_bank/tasks.md',
            'memory_bank/activeContext.md',
            'memory_bank/progress.md'
        ]
        
        consistency_issues = []
        
        for doc_file in doc_files:
            doc_path = self.project_root / doc_file
            if not doc_path.exists():
                self.warnings.append(f"⚠️  文档文件不存在: {doc_file}")
                continue
            
            try:
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否声明使用 PrototypeMainWindow
                if 'PrototypeMainWindow' in content:
                    self.successes.append(f"✅ {doc_file} 提到原型主窗口")
                elif 'ModernMainWindow' in content:
                    consistency_issues.append(f"❌ {doc_file} 仍提到旧版主窗口")
                
                # 检查完成状态声明
                if '100%完成' in content or '✅' in content:
                    if doc_file == 'memory_bank/tasks.md':
                        self.successes.append(f"✅ {doc_file} 标记为完成状态")
                
            except Exception as e:
                self.warnings.append(f"⚠️  读取文档失败 {doc_file}: {e}")
        
        if consistency_issues:
            self.issues.extend(consistency_issues)
            return False
        return True
    
    def check_test_coverage(self) -> bool:
        """检查测试覆盖 (详细模式)"""
        print("\n🧪 检查测试覆盖...")
        
        test_files = list((self.project_root / "test").glob("*.py")) if (self.project_root / "test").exists() else []
        
        if not test_files:
            self.warnings.append("⚠️  未找到测试文件")
            return False
        
        main_entry_test = False
        prototype_test = False
        
        for test_file in test_files:
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'PrototypeMainWindow' in content:
                    prototype_test = True
                    self.successes.append(f"✅ {test_file.name} 包含原型测试")
                
                if 'main.py' in content or '__main__' in content:
                    main_entry_test = True
                    self.successes.append(f"✅ {test_file.name} 包含主程序测试")
                    
            except Exception as e:
                self.warnings.append(f"⚠️  读取测试文件失败 {test_file}: {e}")
        
        if not main_entry_test:
            self.warnings.append("⚠️  缺少主程序入口测试")
        
        if not prototype_test:
            self.warnings.append("⚠️  缺少原型主窗口测试")
        
        return main_entry_test and prototype_test
    
    def print_summary(self, results: Dict[str, bool]):
        """打印检查摘要"""
        print("\n" + "=" * 50)
        print("📊 检查结果摘要")
        print("=" * 50)
        
        # 统计结果
        total_checks = len(results)
        passed_checks = sum(results.values())
        
        print(f"总检查项: {total_checks}")
        print(f"通过检查: {passed_checks}")
        print(f"失败检查: {total_checks - passed_checks}")
        print(f"通过率: {passed_checks/total_checks*100:.1f}%")
        
        # 详细结果
        print(f"\n✅ 成功 ({len(self.successes)} 项):")
        for success in self.successes:
            print(f"   {success}")
        
        if self.warnings:
            print(f"\n⚠️  警告 ({len(self.warnings)} 项):")
            for warning in self.warnings:
                print(f"   {warning}")
        
        if self.issues:
            print(f"\n❌ 问题 ({len(self.issues)} 项):")
            for issue in self.issues:
                print(f"   {issue}")
        
        print("\n" + "=" * 50)
        
        if self.issues:
            print("🚨 发现问题，建议查看解决方案:")
            print("   docs/problems/文档与代码实现不一致问题分析.md")
        else:
            print("🎉 所有检查通过！项目状态良好。")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="项目一致性检查工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python scripts/consistency_check.py          # 基础检查
    python scripts/consistency_check.py --detailed  # 详细检查
        """
    )
    
    parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='执行详细检查，包括文档一致性和测试覆盖'
    )
    
    args = parser.parse_args()
    
    # 执行检查
    checker = ConsistencyChecker(project_root)
    results = checker.check_all(detailed=args.detailed)
    
    # 返回适当的退出码
    if any(not result for result in results.values()) or checker.issues:
        sys.exit(1)  # 有问题
    else:
        sys.exit(0)  # 无问题


if __name__ == "__main__":
    main() 