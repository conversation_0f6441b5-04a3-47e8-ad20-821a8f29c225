#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志分析工具

功能：
- 分析应用程序日志文件
- 识别错误模式和异常
- 生成使用统计报告
- 性能趋势分析
"""

import re
import json
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
import argparse


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir: str = "logs"):
        """初始化日志分析器
        
        Args:
            log_dir: 日志文件目录
        """
        self.log_dir = Path(log_dir)
        self.log_patterns = {
            'error': re.compile(r'\[ERROR\].*'),
            'warning': re.compile(r'\[WARNING\].*'),
            'info': re.compile(r'\[INFO\].*'),
            'debug': re.compile(r'\[DEBUG\].*'),
            'timestamp': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'),
            'performance': re.compile(r'处理时间: ([\d.]+).*'),
            'memory': re.compile(r'内存使用: ([\d.]+)MB'),
            'user_action': re.compile(r'用户操作: (.+)'),
            'data_processing': re.compile(r'处理数据: (\d+)行')
        }
    
    def analyze_logs(self, hours: int = 24) -> Dict[str, Any]:
        """分析日志文件
        
        Args:
            hours: 分析最近几小时的日志
            
        Returns:
            分析结果
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        analysis_result = {
            'time_range': f"最近{hours}小时",
            'log_files': [],
            'error_analysis': {},
            'performance_analysis': {},
            'usage_statistics': {},
            'recommendations': []
        }
        
        # 分析所有日志文件
        for log_file in self.log_dir.glob("*.log"):
            if log_file.stat().st_mtime > cutoff_time.timestamp():
                analysis_result['log_files'].append(str(log_file))
                
                # 分析单个日志文件
                file_analysis = self._analyze_single_file(log_file, cutoff_time)
                
                # 合并分析结果
                self._merge_analysis_results(analysis_result, file_analysis)
        
        # 生成建议
        analysis_result['recommendations'] = self._generate_recommendations(analysis_result)
        
        return analysis_result
    
    def _analyze_single_file(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """分析单个日志文件"""
        result = {
            'errors': [],
            'warnings': [],
            'performance_data': [],
            'user_actions': [],
            'data_processing': []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 检查时间戳，只分析指定时间范围内的日志
                    timestamp_match = self.log_patterns['timestamp'].search(line)
                    if timestamp_match:
                        log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if log_time < cutoff_time:
                            continue
                    
                    # 分析错误
                    if self.log_patterns['error'].match(line):
                        result['errors'].append(line)
                    
                    # 分析警告
                    elif self.log_patterns['warning'].match(line):
                        result['warnings'].append(line)
                    
                    # 分析性能数据
                    perf_match = self.log_patterns['performance'].search(line)
                    if perf_match:
                        result['performance_data'].append(float(perf_match.group(1)))
                    
                    # 分析用户操作
                    action_match = self.log_patterns['user_action'].search(line)
                    if action_match:
                        result['user_actions'].append(action_match.group(1))
                    
                    # 分析数据处理
                    data_match = self.log_patterns['data_processing'].search(line)
                    if data_match:
                        result['data_processing'].append(int(data_match.group(1)))
        
        except Exception as e:
            print(f"分析文件 {log_file} 时出错: {e}")
        
        return result
    
    def _merge_analysis_results(self, main_result: Dict[str, Any], file_result: Dict[str, Any]) -> None:
        """合并分析结果"""
        # 错误分析
        if 'error_analysis' not in main_result:
            main_result['error_analysis'] = {
                'total_errors': 0,
                'error_types': Counter(),
                'error_frequency': defaultdict(int)
            }
        
        main_result['error_analysis']['total_errors'] += len(file_result['errors'])
        
        for error in file_result['errors']:
            # 提取错误类型
            error_type = self._extract_error_type(error)
            main_result['error_analysis']['error_types'][error_type] += 1
            
            # 提取时间并统计频率（按小时）
            timestamp_match = self.log_patterns['timestamp'].search(error)
            if timestamp_match:
                hour = timestamp_match.group(1)[:13]  # YYYY-MM-DD HH
                main_result['error_analysis']['error_frequency'][hour] += 1
        
        # 性能分析
        if 'performance_analysis' not in main_result:
            main_result['performance_analysis'] = {
                'processing_times': [],
                'avg_processing_time': 0,
                'max_processing_time': 0,
                'min_processing_time': float('inf')
            }
        
        if file_result['performance_data']:
            main_result['performance_analysis']['processing_times'].extend(file_result['performance_data'])
            main_result['performance_analysis']['max_processing_time'] = max(
                main_result['performance_analysis']['max_processing_time'],
                max(file_result['performance_data'])
            )
            main_result['performance_analysis']['min_processing_time'] = min(
                main_result['performance_analysis']['min_processing_time'],
                min(file_result['performance_data'])
            )
        
        # 使用统计
        if 'usage_statistics' not in main_result:
            main_result['usage_statistics'] = {
                'user_actions': Counter(),
                'total_data_processed': 0,
                'processing_sessions': 0
            }
        
        for action in file_result['user_actions']:
            main_result['usage_statistics']['user_actions'][action] += 1
        
        main_result['usage_statistics']['total_data_processed'] += sum(file_result['data_processing'])
        main_result['usage_statistics']['processing_sessions'] += len(file_result['data_processing'])
        
        # 计算平均处理时间
        if main_result['performance_analysis']['processing_times']:
            main_result['performance_analysis']['avg_processing_time'] = sum(
                main_result['performance_analysis']['processing_times']
            ) / len(main_result['performance_analysis']['processing_times'])
    
    def _extract_error_type(self, error_line: str) -> str:
        """提取错误类型"""
        # 简单的错误类型提取逻辑
        if "文件" in error_line and "不存在" in error_line:
            return "文件不存在错误"
        elif "权限" in error_line or "拒绝" in error_line:
            return "权限错误"
        elif "内存" in error_line:
            return "内存错误"
        elif "数据库" in error_line:
            return "数据库错误"
        elif "网络" in error_line:
            return "网络错误"
        elif "格式" in error_line or "解析" in error_line:
            return "数据格式错误"
        else:
            return "其他错误"
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 错误分析建议
        if analysis['error_analysis']['total_errors'] > 10:
            recommendations.append("⚠️ 错误数量较多，建议检查日志并修复主要问题")
        
        # 性能分析建议
        if analysis['performance_analysis']['avg_processing_time'] > 10:
            recommendations.append("⚠️ 平均处理时间较长，建议优化性能")
        
        if analysis['performance_analysis']['max_processing_time'] > 30:
            recommendations.append("⚠️ 发现处理时间过长的操作，建议检查性能瓶颈")
        
        # 使用统计建议
        top_actions = analysis['usage_statistics']['user_actions'].most_common(3)
        if top_actions:
            most_used = top_actions[0][0]
            recommendations.append(f"💡 最常用功能是'{most_used}'，建议优化该功能的用户体验")
        
        if analysis['usage_statistics']['total_data_processed'] > 100000:
            recommendations.append("📊 处理大量数据，建议考虑性能优化和数据分批处理")
        
        return recommendations
    
    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """生成分析报告"""
        report = f"""
# 日志分析报告

## 分析概况
- 分析时间范围: {analysis['time_range']}
- 分析的日志文件: {len(analysis['log_files'])} 个
- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 错误分析
- 总错误数: {analysis['error_analysis']['total_errors']}

### 错误类型分布
"""
        
        for error_type, count in analysis['error_analysis']['error_types'].most_common():
            report += f"- {error_type}: {count} 次\n"
        
        report += f"""
### 错误时间分布
"""
        for hour, count in sorted(analysis['error_analysis']['error_frequency'].items()):
            report += f"- {hour}:00: {count} 次错误\n"
        
        report += f"""
## 性能分析
- 平均处理时间: {analysis['performance_analysis']['avg_processing_time']:.2f} 秒
- 最长处理时间: {analysis['performance_analysis']['max_processing_time']:.2f} 秒
- 最短处理时间: {analysis['performance_analysis']['min_processing_time']:.2f} 秒
- 处理操作次数: {len(analysis['performance_analysis']['processing_times'])} 次

## 使用统计
- 总处理数据量: {analysis['usage_statistics']['total_data_processed']} 行
- 处理会话数: {analysis['usage_statistics']['processing_sessions']} 次

### 用户操作统计
"""
        
        for action, count in analysis['usage_statistics']['user_actions'].most_common():
            report += f"- {action}: {count} 次\n"
        
        report += f"""
## 改进建议
"""
        
        for recommendation in analysis['recommendations']:
            report += f"{recommendation}\n"
        
        return report
    
    def export_analysis(self, analysis: Dict[str, Any], output_file: str) -> None:
        """导出分析结果"""
        output_path = Path(output_file)
        
        if output_path.suffix.lower() == '.json':
            # 导出为JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
        else:
            # 导出为文本报告
            report = self.generate_report(analysis)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
        
        print(f"分析结果已导出到: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志分析工具')
    parser.add_argument('--log-dir', default='logs', help='日志文件目录')
    parser.add_argument('--hours', type=int, default=24, help='分析最近几小时的日志')
    parser.add_argument('--output', default='log_analysis_report.txt', help='输出文件')
    parser.add_argument('--format', choices=['txt', 'json'], default='txt', help='输出格式')
    
    args = parser.parse_args()
    
    # 创建日志分析器
    analyzer = LogAnalyzer(args.log_dir)
    
    print(f"开始分析日志...")
    print(f"日志目录: {args.log_dir}")
    print(f"分析时间范围: 最近{args.hours}小时")
    
    # 执行分析
    analysis = analyzer.analyze_logs(args.hours)
    
    # 输出分析结果
    if args.format == 'json':
        output_file = args.output if args.output.endswith('.json') else f"{args.output}.json"
    else:
        output_file = args.output if args.output.endswith('.txt') else f"{args.output}.txt"
    
    analyzer.export_analysis(analysis, output_file)
    
    # 控制台输出摘要
    print("\n=== 分析摘要 ===")
    print(f"错误总数: {analysis['error_analysis']['total_errors']}")
    print(f"处理操作: {len(analysis['performance_analysis']['processing_times'])} 次")
    print(f"数据处理: {analysis['usage_statistics']['total_data_processed']} 行")
    
    if analysis['recommendations']:
        print("\n主要建议:")
        for rec in analysis['recommendations'][:3]:
            print(f"  {rec}")


if __name__ == "__main__":
    main() 