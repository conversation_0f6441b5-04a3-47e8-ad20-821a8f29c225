#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的分页修复验证脚本
验证分页按钮变灰问题的正确修复
"""

import sys
import os
import re
from pathlib import Path

def main():
    """主验证函数"""
    print("真正的分页修复验证测试")
    print("=" * 60)
    print("问题分析: 分页时错误重置total_records导致按钮变灰")
    print("修复方案: 检测分页上下文，避免错误重置")
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    try:
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("错误: prototype_main_window.py文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n>>> 验证根本原因修复")
        
        fixes_found = 0
        
        # 1. 检查分页上下文检测
        if "current_context.get('operation_type') == 'page_change'" in content:
            print("通过: 找到分页上下文检测逻辑")
            fixes_found += 1
        
        # 2. 检查分页调用时跳过total_records重置
        if "分页调用时不重置total_records，避免按钮变灰问题" in content:
            print("通过: 找到分页调用时跳过重置的逻辑")
            fixes_found += 1
        
        # 3. 检查条件判断逻辑
        if "if is_pagination_call:" in content:
            print("通过: 找到分页调用条件判断")
            fixes_found += 1
        
        # 4. 检查非分页时的正常设置
        if "只在非分页调用时设置总记录数" in content:
            print("通过: 找到非分页时的正常设置逻辑")
            fixes_found += 1
        
        # 5. 检查P0-真正修复标识
        if "[P0-真正修复]" in content:
            print("通过: 找到P0-真正修复标识")
            fixes_found += 1
        
        # 6. 检查调试日志
        context_log_patterns = [
            "分页上下文中，跳过total_records重置",
            "非分页上下文，设置total_records"
        ]
        
        log_patterns_found = sum(1 for pattern in context_log_patterns if pattern in content)
        if log_patterns_found == 2:
            print("通过: 找到完整的调试日志")
            fixes_found += 1
        
        # 7. 验证逻辑完整性
        if "is_pagination_call = current_context.get('operation_type') == 'page_change'" in content:
            print("通过: 找到分页调用检测变量定义")
            fixes_found += 1
        
        print(f"\n验证结果: {fixes_found}/7 项通过")
        
        if fixes_found >= 6:
            print("\n真正的分页修复验证通过!")
            print("\n修复原理:")
            print("1. 问题根源: 分页数据加载后调用set_data()时")
            print("   len(df_converted)只有50条(当前页)，不是1396条(总数)")
            print("2. 错误行为: set_total_records(50)导致total_pages=1")
            print("3. 按钮状态: current_page=2, total_pages=1 -> 2<1=false -> 按钮变灰")
            print("4. 修复方案: 检测分页上下文，跳过错误的total_records重置")
            
            print("\n预期效果:")
            print("- 第1页加载: total_records=1396, total_pages=28")
            print("- 点击下一页: 跳转到第2页，保持total_records=1396")
            print("- 第2页状态: current_page=2, total_pages=28 -> 2<28=true -> 下一页可用")
            print("- 后续分页: 所有28页都能正常翻页")
            
            print("\n建议重新启动系统测试分页功能!")
            return True
        else:
            print(f"\n警告: {7 - fixes_found} 项验证失败，需要进一步检查")
            return False
            
    except Exception as e:
        print(f"验证异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)