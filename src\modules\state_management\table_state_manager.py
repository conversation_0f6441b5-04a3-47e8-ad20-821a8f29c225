"""
表格状态管理器 - 薪资管理系统状态持久化模块

该模块负责管理表格的状态信息，确保在表格切换时能够正确恢复状态。

主要功能:
- 表格状态保存和恢复
- 格式配置持久化
- 排序状态管理
- 列宽状态保存
- 状态过期管理

技术特性:
- 内存缓存 + 可选磁盘持久化
- 状态版本控制
- 自动过期清理
- 状态完整性验证

作者: 薪资管理系统修复团队
创建时间: 2025-08-01
版本: 1.0.0
"""

import time
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from PyQt5.QtCore import Qt

from src.utils.log_config import setup_logger


class StateType(Enum):
    """状态类型"""
    TABLE_HEADERS = "table_headers"
    SORT_STATE = "sort_state"
    COLUMN_WIDTHS = "column_widths"
    FORMAT_CONFIG = "format_config"
    DISPLAY_ORDER = "display_order"
    FILTER_STATE = "filter_state"


@dataclass
class TableState:
    """表格状态数据结构"""
    table_name: str
    headers: List[str]
    sort_column: int = -1
    sort_order: int = Qt.AscendingOrder
    column_widths: Dict[str, int] = None
    format_config: Dict[str, Any] = None
    display_order: List[str] = None
    filter_state: Dict[str, Any] = None
    timestamp: float = 0.0
    version: str = "1.0"
    
    def __post_init__(self):
        if self.column_widths is None:
            self.column_widths = {}
        if self.format_config is None:
            self.format_config = {}
        if self.display_order is None:
            self.display_order = []
        if self.filter_state is None:
            self.filter_state = {}
        if self.timestamp == 0.0:
            self.timestamp = time.time()


class TableStateManager:
    """
    表格状态管理器
    
    负责管理表格的各种状态信息，确保状态的持久化和恢复
    """
    
    def __init__(self, cache_dir: Optional[Path] = None, max_cache_size: int = 100):
        """
        初始化状态管理器
        
        Args:
            cache_dir: 缓存目录，None表示仅使用内存缓存
            max_cache_size: 最大缓存条目数
        """
        self.logger = setup_logger("TableStateManager")
        self.cache_dir = cache_dir
        self.max_cache_size = max_cache_size
        
        # 内存缓存
        self.state_cache: Dict[str, TableState] = {}
        
        # 状态过期时间（秒）
        self.state_expiry_time = 1800  # 30分钟
        
        # 初始化缓存目录
        if self.cache_dir:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"🔧 [状态管理] 启用磁盘缓存: {self.cache_dir}")
        else:
            self.logger.info("🔧 [状态管理] 仅使用内存缓存")
        
        self.logger.info(f"🔧 [状态管理] 初始化完成，最大缓存: {max_cache_size}")
    
    def save_table_state(
        self, 
        table_name: str, 
        state_data: Dict[str, Any],
        force_persist: bool = False
    ) -> bool:
        """
        保存表格状态
        
        Args:
            table_name: 表名
            state_data: 状态数据
            force_persist: 是否强制持久化到磁盘
            
        Returns:
            是否保存成功
        """
        try:
            # 创建状态对象
            table_state = TableState(
                table_name=table_name,
                headers=state_data.get('headers', []),
                sort_column=state_data.get('sort_column', -1),
                sort_order=state_data.get('sort_order', Qt.AscendingOrder),
                column_widths=state_data.get('column_widths', {}),
                format_config=state_data.get('format_config', {}),
                display_order=state_data.get('display_order', []),
                filter_state=state_data.get('filter_state', {}),
                timestamp=time.time()
            )
            
            # 保存到内存缓存
            self.state_cache[table_name] = table_state
            
            # 清理过期状态
            self._cleanup_expired_states()
            
            # 限制缓存大小
            self._limit_cache_size()
            
            # 可选的磁盘持久化
            if (force_persist or self.cache_dir) and self.cache_dir:
                self._persist_state_to_disk(table_name, table_state)
            
            self.logger.debug(f"🔧 [状态管理] 保存状态成功: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 保存状态失败 {table_name}: {e}")
            return False
    
    def restore_table_state(self, table_name: str) -> Optional[TableState]:
        """
        恢复表格状态
        
        Args:
            table_name: 表名
            
        Returns:
            表格状态对象，如果不存在或过期则返回None
        """
        try:
            # 首先检查内存缓存
            if table_name in self.state_cache:
                state = self.state_cache[table_name]
                
                # 检查是否过期
                if time.time() - state.timestamp < self.state_expiry_time:
                    self.logger.debug(f"🔧 [状态管理] 从内存恢复状态: {table_name}")
                    return state
                else:
                    # 过期，从缓存中移除
                    del self.state_cache[table_name]
                    self.logger.debug(f"🔧 [状态管理] 状态已过期: {table_name}")
            
            # 尝试从磁盘加载
            if self.cache_dir:
                disk_state = self._load_state_from_disk(table_name)
                if disk_state:
                    # 检查磁盘状态是否过期
                    if time.time() - disk_state.timestamp < self.state_expiry_time:
                        # 加载到内存缓存
                        self.state_cache[table_name] = disk_state
                        self.logger.debug(f"🔧 [状态管理] 从磁盘恢复状态: {table_name}")
                        return disk_state
                    else:
                        # 磁盘状态也过期，删除文件
                        self._delete_disk_state(table_name)
                        self.logger.debug(f"🔧 [状态管理] 磁盘状态已过期: {table_name}")
            
            self.logger.debug(f"🔧 [状态管理] 未找到有效状态: {table_name}")
            return None
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 恢复状态失败 {table_name}: {e}")
            return None
    
    def update_state_field(
        self, 
        table_name: str, 
        field_name: str, 
        field_value: Any
    ) -> bool:
        """
        更新状态的特定字段
        
        Args:
            table_name: 表名
            field_name: 字段名
            field_value: 字段值
            
        Returns:
            是否更新成功
        """
        try:
            # 获取现有状态或创建新状态
            state = self.restore_table_state(table_name)
            if not state:
                state = TableState(table_name=table_name, headers=[])
            
            # 更新字段
            if hasattr(state, field_name):
                setattr(state, field_name, field_value)
                state.timestamp = time.time()  # 更新时间戳
                
                # 保存更新后的状态
                state_data = asdict(state)
                return self.save_table_state(table_name, state_data)
            else:
                self.logger.warning(f"🔧 [状态管理] 未知字段: {field_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 更新状态字段失败 {table_name}.{field_name}: {e}")
            return False
    
    def clear_table_state(self, table_name: str) -> bool:
        """
        清除表格状态
        
        Args:
            table_name: 表名
            
        Returns:
            是否清除成功
        """
        try:
            # 从内存缓存中移除
            if table_name in self.state_cache:
                del self.state_cache[table_name]
            
            # 从磁盘中删除
            if self.cache_dir:
                self._delete_disk_state(table_name)
            
            self.logger.debug(f"🔧 [状态管理] 清除状态成功: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 清除状态失败 {table_name}: {e}")
            return False
    
    def clear_all_states(self) -> bool:
        """清除所有状态"""
        try:
            # 清除内存缓存
            self.state_cache.clear()
            
            # 清除磁盘缓存
            if self.cache_dir and self.cache_dir.exists():
                for file_path in self.cache_dir.glob("*.json"):
                    file_path.unlink()
            
            self.logger.info("🔧 [状态管理] 清除所有状态成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 清除所有状态失败: {e}")
            return False
    
    def get_state_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        try:
            active_states = len(self.state_cache)
            
            # 统计磁盘状态
            disk_states = 0
            if self.cache_dir and self.cache_dir.exists():
                disk_states = len(list(self.cache_dir.glob("*.json")))
            
            return {
                "active_memory_states": active_states,
                "disk_states": disk_states,
                "max_cache_size": self.max_cache_size,
                "expiry_time_minutes": self.state_expiry_time / 60,
                "cache_dir": str(self.cache_dir) if self.cache_dir else None
            }
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 获取状态摘要失败: {e}")
            return {"error": str(e)}
    
    def _cleanup_expired_states(self):
        """清理过期状态"""
        try:
            current_time = time.time()
            expired_keys = [
                key for key, state in self.state_cache.items()
                if current_time - state.timestamp > self.state_expiry_time
            ]
            
            for key in expired_keys:
                del self.state_cache[key]
                self.logger.debug(f"🔧 [状态管理] 清理过期状态: {key}")
                
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 清理过期状态失败: {e}")
    
    def _limit_cache_size(self):
        """限制缓存大小"""
        try:
            if len(self.state_cache) > self.max_cache_size:
                # 按时间戳排序，移除最旧的状态
                sorted_items = sorted(
                    self.state_cache.items(),
                    key=lambda x: x[1].timestamp
                )
                
                # 移除最旧的状态
                remove_count = len(self.state_cache) - self.max_cache_size
                for i in range(remove_count):
                    key = sorted_items[i][0]
                    del self.state_cache[key]
                    self.logger.debug(f"🔧 [状态管理] 移除旧状态: {key}")
                    
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 限制缓存大小失败: {e}")
    
    def _persist_state_to_disk(self, table_name: str, state: TableState):
        """持久化状态到磁盘"""
        try:
            if not self.cache_dir:
                return
            
            file_path = self.cache_dir / f"{table_name}.json"
            state_dict = asdict(state)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(state_dict, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 持久化状态失败 {table_name}: {e}")
    
    def _load_state_from_disk(self, table_name: str) -> Optional[TableState]:
        """从磁盘加载状态"""
        try:
            if not self.cache_dir:
                return None
            
            file_path = self.cache_dir / f"{table_name}.json"
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                state_dict = json.load(f)
            
            return TableState(**state_dict)
            
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 从磁盘加载状态失败 {table_name}: {e}")
            return None
    
    def _delete_disk_state(self, table_name: str):
        """删除磁盘状态文件"""
        try:
            if not self.cache_dir:
                return
            
            file_path = self.cache_dir / f"{table_name}.json"
            if file_path.exists():
                file_path.unlink()
                
        except Exception as e:
            self.logger.error(f"🔧 [状态管理] 删除磁盘状态失败 {table_name}: {e}")
