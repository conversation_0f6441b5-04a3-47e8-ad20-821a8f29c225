# Qt开发安全规范

## 📋 强制性安全规范

### 1. 绘制操作安全规范

**🚫 绝对禁止**：
```python
# 危险：直接调用repaint()
widget.repaint()
header.repaint()
viewport.repaint()
```

**✅ 必须使用**：
```python
# 安全：使用update()
widget.update()
header.update()
viewport.update()

# 或者使用安全包装
def safe_update_display(widget):
    if not getattr(widget, '_is_painting', False):
        widget.update()
```

### 2. Timer使用安全规范

**🚫 绝对禁止**：
```python
# 危险：在任意位置创建QTimer
def some_function():
    timer = QTimer()  # 可能不在主线程
    timer.start(100)
```

**✅ 必须使用**：
```python
# 安全：检查线程后使用
def safe_timer_usage():
    if QThread.currentThread() == QApplication.instance().thread():
        timer = QTimer()
        timer.start(100)
    else:
        # 使用信号槽机制
        self.timer_needed.emit(100)
```

### 3. QPainter使用安全规范

**🚫 绝对禁止**：
```python
# 危险：可能忘记end()
painter = QPainter(widget)
painter.drawText(...)
# 如果异常发生，end()不会被调用
```

**✅ 必须使用**：
```python
# 安全：使用上下文管理
def safe_painting(widget):
    try:
        painter = QPainter(widget)
        painter.drawText(...)
    finally:
        if painter.isActive():
            painter.end()

# 更好：使用with语句（如果可能）
```

### 4. 事件处理安全规范

**🚫 绝对禁止**：
```python
# 危险：在事件处理中阻塞
def paintEvent(self, event):
    time.sleep(0.1)  # 阻塞UI线程
    # 或任何耗时操作

def mousePressEvent(self, event):
    while condition:  # 长时间循环
        process_something()
```

**✅ 必须使用**：
```python
# 安全：异步处理
def mousePressEvent(self, event):
    # 立即发射信号，在其他地方处理
    self.mouse_pressed.emit(event.pos())

def paintEvent(self, event):
    # 只进行必要的绘制，不做耗时操作
    super().paintEvent(event)
```

## 🛡️ 防护机制模板

### 1. 绘制状态防护

```python
class SafeWidget(QWidget):
    def __init__(self):
        super().__init__()
        self._is_painting = False
        self._is_repainting = False
        self._repaint_depth = 0
        self._max_repaint_depth = 3
    
    def safe_update(self):
        """安全的更新方法"""
        if self._is_painting or self._is_repainting:
            return
        
        if self._repaint_depth >= self._max_repaint_depth:
            return
            
        self._repaint_depth += 1
        try:
            self.update()
        finally:
            self._repaint_depth -= 1
```

### 2. Timer安全包装

```python
class SafeTimer:
    @staticmethod
    def safe_single_shot(interval, callback):
        """线程安全的单次定时器"""
        if QThread.currentThread() == QApplication.instance().thread():
            QTimer.singleShot(interval, callback)
        else:
            # 使用QMetaObject进行线程安全调用
            QMetaObject.invokeMethod(
                QApplication.instance(),
                "processEvents",
                Qt.QueuedConnection
            )
```

### 3. 架构组件初始化检查

```python
def ensure_architecture_factory(self):
    """确保架构工厂可用"""
    if not hasattr(self, 'architecture_factory') or not self.architecture_factory:
        try:
            self._emergency_init_architecture_factory()
        except Exception as e:
            self.logger.error(f"架构工厂初始化失败: {e}")
            return False
    return True
```

## 📊 代码审查检查清单

### 提交前必检项目

**高优先级检查**：
- [ ] 无直接repaint()调用
- [ ] 所有QTimer有线程检查
- [ ] QPainter有异常处理
- [ ] 事件处理无阻塞操作

**中优先级检查**：
- [ ] 绘制状态有防护标志
- [ ] 架构组件有容错处理
- [ ] 信号槽连接正确
- [ ] 内存管理合理

**低优先级检查**：
- [ ] 日志记录完善
- [ ] 注释说明清晰
- [ ] 性能考虑合理
- [ ] 测试覆盖充分

### 自动化检查工具

```bash
# 运行Qt安全检查
python scripts/qt_safety_checker.py

# 检查结果
if [ $? -eq 0 ]; then
    echo "✅ Qt安全检查通过"
else 
    echo "❌ 发现Qt安全问题，禁止提交"
    exit 1
fi
```

## 🎯 团队培训要点

### 必须掌握的概念

1. **Qt线程模型**：理解主线程与工作线程的区别
2. **事件循环机制**：了解Qt事件驱动架构
3. **绘制生命周期**：掌握Qt绘制的时机和顺序
4. **信号槽机制**：正确使用跨线程通信

### 常见错误模式

1. **在工作线程操作Qt组件**
2. **在绘制过程中触发重绘**
3. **长时间阻塞UI线程**
4. **不正确的Qt对象生命周期管理**

### 调试技巧

1. **使用Qt Creator的线程检查**
2. **启用Qt Debug模式**
3. **监控事件队列状态**
4. **记录关键操作时序**

---

**制定时间**: 2025-07-17  
**适用项目**: 所有PyQt5/PyQt6项目  
**执行级别**: 强制性  
**违规后果**: 代码审查不通过  