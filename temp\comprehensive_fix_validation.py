"""
🎯 综合修复验证测试
验证P0-P3级所有修复点的有效性
"""

import sys
import os
import time
import traceback
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_timer_thread_safety():
    """测试P0: Timer线程安全修复"""
    print("[P0测试] 验证Timer线程安全修复...")
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QThread
        
        # 初始化Qt应用（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试导航面板Timer
        from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.core.component_communication_manager import ComponentCommunicationManager
        
        # 创建必要的依赖
        table_manager = DynamicTableManager()
        comm_manager = ComponentCommunicationManager()
        
        # 创建导航面板（应该安全创建Timer）
        panel = EnhancedNavigationPanel(table_manager, comm_manager)
        
        # 验证Timer是否在主线程中创建
        if hasattr(panel, 'stats_timer') and panel.stats_timer:
            is_main_thread = QThread.currentThread() == QApplication.instance().thread()
            if is_main_thread:
                print("   [PASS] NavigationPanel Timer已在主线程中安全创建")
                return True
            else:
                print("   [FAIL] NavigationPanel Timer不在主线程")
                return False
        else:
            print("   [WARN] NavigationPanel Timer未创建")
            return True  # 可能是条件性创建，不算错误
            
    except Exception as e:
        print(f"   [FAIL] Timer线程安全测试失败: {e}")
        return False

def test_table_virtualization_fix():
    """测试P1: 表格虚拟化参数修复"""
    print("[P1测试] 验证表格虚拟化参数修复...")
    try:
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        
        # 创建表格实例
        table = VirtualizedExpandableTable(max_visible_rows=5)  # 故意设置小值
        
        # 验证自动调整功能
        success = table.set_max_visible_rows(2)  # 应该自动调整为10
        actual_value = table.get_max_visible_rows()
        
        if actual_value >= 10:
            print(f"   [PASS] 小值自动调整成功: 2 -> {actual_value}")
        else:
            print(f"   [FAIL] 小值未调整: 实际值 {actual_value}")
            return False
            
        # 测试自动调整算法
        table.auto_adjust_max_visible_rows(50)  # 小数据集
        adjusted_value = table.get_max_visible_rows()
        
        if adjusted_value >= 10:
            print(f"   [PASS] 自动调整算法工作正常: 50行数据 -> {adjusted_value}行显示")
            return True
        else:
            print(f"   [FAIL] 自动调整算法失败: {adjusted_value}")
            return False
            
    except Exception as e:
        print(f"   [FAIL] 表格虚拟化测试失败: {e}")
        return False

def test_recursion_guard_optimization():
    """测试P2: 递归防护机制优化"""
    print("[P2测试] 验证递归防护机制优化...")
    try:
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        # 这里只能测试方法存在性，不能完整测试
        if hasattr(PrototypeMainWindow, '_is_legitimate_data_update'):
            print("   [PASS] 智能递归检测方法已添加")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(PrototypeMainWindow._is_legitimate_data_update)
            if len(sig.parameters) == 1:  # 只有self参数
                print("   [PASS] 递归检测方法签名正确")
                return True
            else:
                print("   [FAIL] 递归检测方法签名错误")
                return False
        else:
            print("   [FAIL] 智能递归检测方法未找到")
            return False
            
    except Exception as e:
        print(f"   [FAIL] 递归防护测试失败: {e}")
        return False

def test_format_config_validation():
    """测试P3: 格式配置完整性验证"""
    print("[P3测试] 验证格式配置完整性验证...")
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 创建字段注册表实例
        registry = FieldRegistry("state/data/field_mappings.json")
        
        # 验证新的验证方法是否存在
        validation_methods = [
            '_validate_critical_fields_coverage',
            '_validate_field_type_consistency', 
            '_validate_existing_display_fields'
        ]
        
        missing_methods = []
        for method in validation_methods:
            if not hasattr(registry, method):
                missing_methods.append(method)
        
        if not missing_methods:
            print("   [PASS] 所有P3增强验证方法已添加")
            
            # 尝试运行配置验证
            result = registry._validate_configuration_consistency()
            if isinstance(result, dict) and 'summary' in result:
                print(f"   [PASS] 配置验证运行成功: {result['summary']}")
                return True
            else:
                print("   [FAIL] 配置验证结果格式错误")
                return False
        else:
            print(f"   [FAIL] 缺少验证方法: {missing_methods}")
            return False
            
    except Exception as e:
        print(f"   [FAIL] 格式配置验证测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("[综合修复验证] 开始验证所有修复点")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 执行所有测试
        test_results.append(("P0-Timer线程安全", test_timer_thread_safety()))
        test_results.append(("P1-表格虚拟化", test_table_virtualization_fix()))
        test_results.append(("P2-递归防护优化", test_recursion_guard_optimization()))
        test_results.append(("P3-格式配置验证", test_format_config_validation()))
        
    except Exception as e:
        print(f"测试执行异常: {e}")
        traceback.print_exc()
        return False
    
    # 汇总测试结果
    print("\n" + "=" * 80)
    print("[修复验证结果汇总]")
    
    passed_count = 0
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    total_tests = len(test_results)
    success_rate = (passed_count / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n[总体结果] {passed_count}/{total_tests} 修复验证通过 ({success_rate:.1f}%)")
    
    if passed_count == total_tests:
        print("\n[修复成功] 所有问题修复验证通过！")
        print("P0-Critical: Timer线程安全问题已修复")
        print("P1-Performance: 表格虚拟化参数异常已修复")
        print("P2-Optimization: 递归防护机制已优化")
        print("P3-Enhancement: 格式配置验证已增强")
        print("\n建议：现在可以重启系统测试UI样式和QBasicTimer错误是否消失")
        return True
    else:
        print(f"\n[部分成功] {total_tests - passed_count}个修复点需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)