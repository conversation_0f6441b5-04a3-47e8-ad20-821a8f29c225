"""
快捷键系统模块

提供全局快捷键管理、上下文快捷键、快捷键提示和自定义设置功能。
支持现代化的快捷键体验和用户自定义配置。

主要组件:
- KeyboardShortcutManager: 快捷键管理器
- ShortcutHelpDialog: 快捷键帮助对话框
- ShortcutSettingsDialog: 快捷键设置对话框
- ContextualShortcutHandler: 上下文快捷键处理器
"""

import json
import os
from typing import Dict, List, Optional, Callable, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QLineEdit, QComboBox, QCheckBox, QGroupBox, QScrollArea,
    QFrame, QSplitter, QTextEdit, QTabWidget, QMessageBox,
    QApplication, QShortcut, QKeySequenceEdit, QTreeWidget,
    QTreeWidgetItem, QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QObject, QEvent,
    QSettings, QSize, QRect, QPoint
)
from PyQt5.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap, QPainter,
    QKeySequence, QFontMetrics, QCursor
)

from src.utils.log_config import get_module_logger
from src.gui.toast_system import ToastManager

logger = get_module_logger(__name__)

class KeyboardShortcutManager(QObject):
    """快捷键管理器"""
    
    # 信号定义
    shortcut_triggered = pyqtSignal(str, dict)  # 快捷键触发信号
    shortcuts_changed = pyqtSignal()  # 快捷键配置改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.shortcuts = {}  # 快捷键字典
        self.contexts = {}  # 上下文字典
        self.settings = QSettings('SalaryChanges', 'Shortcuts')
        self.toast_manager = ToastManager()
        
        # 默认快捷键配置
        self.default_shortcuts = {
            # 文件操作
            'file_new': {'key': 'Ctrl+N', 'description': '新建文件', 'context': 'global'},
            'file_open': {'key': 'Ctrl+O', 'description': '打开文件', 'context': 'global'},
            'file_save': {'key': 'Ctrl+S', 'description': '保存文件', 'context': 'global'},
            'file_save_as': {'key': 'Ctrl+Shift+S', 'description': '另存为', 'context': 'global'},
            'file_export': {'key': 'Ctrl+E', 'description': '导出数据', 'context': 'global'},
            'file_import': {'key': 'Ctrl+I', 'description': '导入数据', 'context': 'global'},
            'file_close': {'key': 'Ctrl+W', 'description': '关闭文件', 'context': 'global'},
            'file_quit': {'key': 'Ctrl+Q', 'description': '退出程序', 'context': 'global'},
            
            # 编辑操作
            'edit_undo': {'key': 'Ctrl+Z', 'description': '撤销', 'context': 'global'},
            'edit_redo': {'key': 'Ctrl+Y', 'description': '重做', 'context': 'global'},
            'edit_cut': {'key': 'Ctrl+X', 'description': '剪切', 'context': 'global'},
            'edit_copy': {'key': 'Ctrl+C', 'description': '复制', 'context': 'global'},
            'edit_paste': {'key': 'Ctrl+V', 'description': '粘贴', 'context': 'global'},
            'edit_select_all': {'key': 'Ctrl+A', 'description': '全选', 'context': 'global'},
            'edit_find': {'key': 'Ctrl+F', 'description': '查找', 'context': 'global'},
            'edit_replace': {'key': 'Ctrl+H', 'description': '替换', 'context': 'global'},
            
            # 表格操作
            'table_add_row': {'key': 'Ctrl+Shift+N', 'description': '添加行', 'context': 'table'},
            'table_delete_row': {'key': 'Delete', 'description': '删除行', 'context': 'table'},
            'table_edit_cell': {'key': 'F2', 'description': '编辑单元格', 'context': 'table'},
            'table_next_cell': {'key': 'Tab', 'description': '下一个单元格', 'context': 'table'},
            'table_prev_cell': {'key': 'Shift+Tab', 'description': '上一个单元格', 'context': 'table'},
            'table_first_row': {'key': 'Ctrl+Home', 'description': '第一行', 'context': 'table'},
            'table_last_row': {'key': 'Ctrl+End', 'description': '最后一行', 'context': 'table'},
            'table_page_up': {'key': 'Page Up', 'description': '向上翻页', 'context': 'table'},
            'table_page_down': {'key': 'Page Down', 'description': '向下翻页', 'context': 'table'},
            
            # 批量操作
            'batch_select_all': {'key': 'Ctrl+Shift+A', 'description': '批量全选', 'context': 'table'},
            'batch_select_none': {'key': 'Ctrl+Shift+D', 'description': '取消选择', 'context': 'table'},
            'batch_delete': {'key': 'Ctrl+Shift+Delete', 'description': '批量删除', 'context': 'table'},
            'batch_edit': {'key': 'Ctrl+Shift+E', 'description': '批量编辑', 'context': 'table'},
            
            # 视图操作
            'view_refresh': {'key': 'F5', 'description': '刷新', 'context': 'global'},
            'view_zoom_in': {'key': 'Ctrl++', 'description': '放大', 'context': 'global'},
            'view_zoom_out': {'key': 'Ctrl+-', 'description': '缩小', 'context': 'global'},
            'view_zoom_reset': {'key': 'Ctrl+0', 'description': '重置缩放', 'context': 'global'},
            'view_fullscreen': {'key': 'F11', 'description': '全屏', 'context': 'global'},
            
            # 工具操作
            'tools_detect_changes': {'key': 'Ctrl+D', 'description': '检测异动', 'context': 'global'},
            'tools_generate_report': {'key': 'Ctrl+R', 'description': '生成报告', 'context': 'global'},
            'tools_settings': {'key': 'Ctrl+,', 'description': '设置', 'context': 'global'},
            'tools_help': {'key': 'F1', 'description': '帮助', 'context': 'global'},
            
            # 导航操作
            'nav_next_tab': {'key': 'Ctrl+Tab', 'description': '下一个标签', 'context': 'global'},
            'nav_prev_tab': {'key': 'Ctrl+Shift+Tab', 'description': '上一个标签', 'context': 'global'},
            'nav_close_tab': {'key': 'Ctrl+F4', 'description': '关闭标签', 'context': 'global'},
        }
        
        self.load_shortcuts()
        
    def load_shortcuts(self):
        """加载快捷键配置"""
        try:
            # 从设置中加载自定义快捷键
            custom_shortcuts = {}
            self.settings.beginGroup('shortcuts')
            for key in self.settings.allKeys():
                custom_shortcuts[key] = self.settings.value(key)
            self.settings.endGroup()
            
            # 合并默认和自定义快捷键
            self.shortcuts = self.default_shortcuts.copy()
            for action, shortcut_data in custom_shortcuts.items():
                if action in self.shortcuts:
                    if isinstance(shortcut_data, dict):
                        self.shortcuts[action].update(shortcut_data)
                    else:
                        self.shortcuts[action]['key'] = shortcut_data
                        
            logger.info(f"加载了 {len(self.shortcuts)} 个快捷键配置")
            
        except Exception as e:
            logger.error(f"加载快捷键配置失败: {e}")
            self.shortcuts = self.default_shortcuts.copy()
    
    def save_shortcuts(self):
        """保存快捷键配置"""
        try:
            self.settings.beginGroup('shortcuts')
            self.settings.clear()
            
            # 只保存与默认配置不同的快捷键
            for action, shortcut_data in self.shortcuts.items():
                if action in self.default_shortcuts:
                    default_data = self.default_shortcuts[action]
                    if shortcut_data != default_data:
                        self.settings.setValue(action, shortcut_data)
                else:
                    self.settings.setValue(action, shortcut_data)
                    
            self.settings.endGroup()
            self.settings.sync()
            
            logger.info("快捷键配置保存成功")
            self.shortcuts_changed.emit()
            
        except Exception as e:
            logger.error(f"保存快捷键配置失败: {e}")
    
    def register_shortcut(self, action: str, callback: Callable, context: str = 'global'):
        """注册快捷键"""
        try:
            if action not in self.shortcuts:
                logger.warning(f"未知的快捷键动作: {action}")
                return False
                
            shortcut_data = self.shortcuts[action]
            key_sequence = QKeySequence(shortcut_data['key'])
            
            if not key_sequence.isEmpty():
                shortcut = QShortcut(key_sequence, self.parent_widget)
                shortcut.activated.connect(lambda: self._handle_shortcut(action, callback))
                shortcut.setContext(Qt.ApplicationShortcut if context == 'global' else Qt.WidgetShortcut)
                
                logger.debug(f"注册快捷键: {action} -> {shortcut_data['key']}")
                return True
                
        except Exception as e:
            logger.error(f"注册快捷键失败 {action}: {e}")
            
        return False
    
    def _handle_shortcut(self, action: str, callback: Callable):
        """处理快捷键触发"""
        try:
            shortcut_data = self.shortcuts.get(action, {})
            
            # 触发回调
            if callback:
                callback()
                
            # 发送信号
            self.shortcut_triggered.emit(action, shortcut_data)
            
            # 显示快捷键提示
            if self.toast_manager:
                description = shortcut_data.get('description', action)
                self.toast_manager.show_info(f"执行: {description}")
                
        except Exception as e:
            logger.error(f"处理快捷键触发失败 {action}: {e}")
    
    def get_shortcut(self, action: str) -> Optional[Dict]:
        """获取快捷键信息"""
        return self.shortcuts.get(action)
    
    def set_shortcut(self, action: str, key: str, description: str = None, context: str = 'global'):
        """设置快捷键"""
        try:
            if action not in self.shortcuts:
                self.shortcuts[action] = {}
                
            self.shortcuts[action]['key'] = key
            if description:
                self.shortcuts[action]['description'] = description
            if context:
                self.shortcuts[action]['context'] = context
                
            self.save_shortcuts()
            return True
            
        except Exception as e:
            logger.error(f"设置快捷键失败 {action}: {e}")
            return False
    
    def remove_shortcut(self, action: str):
        """移除快捷键"""
        try:
            if action in self.shortcuts:
                del self.shortcuts[action]
                self.save_shortcuts()
                return True
        except Exception as e:
            logger.error(f"移除快捷键失败 {action}: {e}")
            
        return False
    
    def get_shortcuts_by_context(self, context: str) -> Dict:
        """按上下文获取快捷键"""
        return {
            action: data for action, data in self.shortcuts.items()
            if data.get('context') == context
        }
    
    def get_all_shortcuts(self) -> Dict:
        """获取所有快捷键"""
        return self.shortcuts.copy()
    
    def reset_to_defaults(self):
        """重置为默认快捷键"""
        try:
            self.shortcuts = self.default_shortcuts.copy()
            self.settings.beginGroup('shortcuts')
            self.settings.clear()
            self.settings.endGroup()
            self.settings.sync()
            
            logger.info("快捷键已重置为默认配置")
            self.shortcuts_changed.emit()
            
        except Exception as e:
            logger.error(f"重置快捷键失败: {e}")


class ShortcutHelpDialog(QDialog):
    """快捷键帮助对话框"""
    
    def __init__(self, shortcut_manager: KeyboardShortcutManager, parent=None):
        super().__init__(parent)
        self.shortcut_manager = shortcut_manager
        self.setWindowTitle("快捷键帮助")
        self.setModal(True)
        self.resize(800, 600)
        
        self.init_ui()
        self.load_shortcuts()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("快捷键帮助")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入快捷键或描述进行搜索...")
        self.search_edit.textChanged.connect(self.filter_shortcuts)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        # 上下文标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.print_btn = QPushButton("打印")
        self.export_btn = QPushButton("导出")
        self.close_btn = QPushButton("关闭")
        
        self.print_btn.clicked.connect(self.print_shortcuts)
        self.export_btn.clicked.connect(self.export_shortcuts)
        self.close_btn.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(self.print_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)
        
    def load_shortcuts(self):
        """加载快捷键到表格"""
        try:
            shortcuts = self.shortcut_manager.get_all_shortcuts()
            
            # 按上下文分组
            contexts = {}
            for action, data in shortcuts.items():
                context = data.get('context', 'global')
                if context not in contexts:
                    contexts[context] = []
                contexts[context].append((action, data))
            
            # 为每个上下文创建标签页
            for context, shortcuts_list in contexts.items():
                table = self.create_shortcuts_table(shortcuts_list)
                self.tab_widget.addTab(table, context.title())
                
        except Exception as e:
            logger.error(f"加载快捷键帮助失败: {e}")
    
    def create_shortcuts_table(self, shortcuts_list: List) -> QTableWidget:
        """创建快捷键表格"""
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["快捷键", "描述", "动作"])
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSortingEnabled(True)
        
        # 调整列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # 填充数据
        table.setRowCount(len(shortcuts_list))
        for row, (action, data) in enumerate(shortcuts_list):
            key_item = QTableWidgetItem(data.get('key', ''))
            desc_item = QTableWidgetItem(data.get('description', action))
            action_item = QTableWidgetItem(action)
            
            # 设置字体
            key_item.setFont(QFont("Consolas", 10))
            
            table.setItem(row, 0, key_item)
            table.setItem(row, 1, desc_item)
            table.setItem(row, 2, action_item)
        
        return table
    
    def filter_shortcuts(self):
        """过滤快捷键"""
        search_text = self.search_edit.text().lower()
        
        for i in range(self.tab_widget.count()):
            table = self.tab_widget.widget(i)
            if isinstance(table, QTableWidget):
                for row in range(table.rowCount()):
                    show_row = False
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        if item and search_text in item.text().lower():
                            show_row = True
                            break
                    table.setRowHidden(row, not show_row)
    
    def print_shortcuts(self):
        """打印快捷键"""
        try:
            # 这里可以实现打印功能
            QMessageBox.information(self, "提示", "打印功能待实现")
        except Exception as e:
            logger.error(f"打印快捷键失败: {e}")
    
    def export_shortcuts(self):
        """导出快捷键"""
        try:
            # 这里可以实现导出功能
            QMessageBox.information(self, "提示", "导出功能待实现")
        except Exception as e:
            logger.error(f"导出快捷键失败: {e}")


class ShortcutSettingsDialog(QDialog):
    """快捷键设置对话框"""
    
    def __init__(self, shortcut_manager: KeyboardShortcutManager, parent=None):
        super().__init__(parent)
        self.shortcut_manager = shortcut_manager
        self.setWindowTitle("快捷键设置")
        self.setModal(True)
        self.resize(900, 700)
        
        self.modified = False
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("快捷键设置")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 搜索和过滤
        filter_layout = QHBoxLayout()
        
        search_label = QLabel("搜索:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入动作名称或描述...")
        self.search_edit.textChanged.connect(self.filter_shortcuts)
        
        context_label = QLabel("上下文:")
        self.context_combo = QComboBox()
        self.context_combo.addItems(["全部", "global", "table"])
        self.context_combo.currentTextChanged.connect(self.filter_shortcuts)
        
        filter_layout.addWidget(search_label)
        filter_layout.addWidget(self.search_edit)
        filter_layout.addWidget(context_label)
        filter_layout.addWidget(self.context_combo)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # 快捷键设置表格
        self.shortcuts_table = QTableWidget()
        self.shortcuts_table.setColumnCount(4)
        self.shortcuts_table.setHorizontalHeaderLabels(["动作", "描述", "快捷键", "上下文"])
        
        # 设置表格属性
        self.shortcuts_table.setAlternatingRowColors(True)
        self.shortcuts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.shortcuts_table.setSortingEnabled(True)
        
        # 调整列宽
        header = self.shortcuts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.shortcuts_table)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("添加")
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.reset_btn = QPushButton("重置")
        
        self.add_btn.clicked.connect(self.add_shortcut)
        self.edit_btn.clicked.connect(self.edit_shortcut)
        self.delete_btn.clicked.connect(self.delete_shortcut)
        self.reset_btn.clicked.connect(self.reset_shortcuts)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()
        
        # 对话框按钮
        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        self.apply_btn = QPushButton("应用")
        
        self.ok_btn.clicked.connect(self.accept_changes)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.clicked.connect(self.apply_changes)
        
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.apply_btn)
        
        layout.addLayout(button_layout)
        
    def load_settings(self):
        """加载设置"""
        try:
            shortcuts = self.shortcut_manager.get_all_shortcuts()
            
            self.shortcuts_table.setRowCount(len(shortcuts))
            
            for row, (action, data) in enumerate(shortcuts.items()):
                action_item = QTableWidgetItem(action)
                desc_item = QTableWidgetItem(data.get('description', ''))
                key_item = QTableWidgetItem(data.get('key', ''))
                context_item = QTableWidgetItem(data.get('context', 'global'))
                
                # 设置字体
                key_item.setFont(QFont("Consolas", 10))
                
                self.shortcuts_table.setItem(row, 0, action_item)
                self.shortcuts_table.setItem(row, 1, desc_item)
                self.shortcuts_table.setItem(row, 2, key_item)
                self.shortcuts_table.setItem(row, 3, context_item)
                
        except Exception as e:
            logger.error(f"加载快捷键设置失败: {e}")
    
    def filter_shortcuts(self):
        """过滤快捷键"""
        search_text = self.search_edit.text().lower()
        context_filter = self.context_combo.currentText()
        
        for row in range(self.shortcuts_table.rowCount()):
            show_row = True
            
            # 搜索过滤
            if search_text:
                found = False
                for col in range(self.shortcuts_table.columnCount()):
                    item = self.shortcuts_table.item(row, col)
                    if item and search_text in item.text().lower():
                        found = True
                        break
                if not found:
                    show_row = False
            
            # 上下文过滤
            if context_filter != "全部":
                context_item = self.shortcuts_table.item(row, 3)
                if context_item and context_item.text() != context_filter:
                    show_row = False
            
            self.shortcuts_table.setRowHidden(row, not show_row)
    
    def add_shortcut(self):
        """添加快捷键"""
        try:
            dialog = ShortcutEditDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                action, key, description, context = dialog.get_shortcut_data()
                
                # 检查动作是否已存在
                for row in range(self.shortcuts_table.rowCount()):
                    item = self.shortcuts_table.item(row, 0)
                    if item and item.text() == action:
                        QMessageBox.warning(self, "警告", f"动作 '{action}' 已存在")
                        return
                
                # 添加到表格
                row = self.shortcuts_table.rowCount()
                self.shortcuts_table.insertRow(row)
                
                self.shortcuts_table.setItem(row, 0, QTableWidgetItem(action))
                self.shortcuts_table.setItem(row, 1, QTableWidgetItem(description))
                self.shortcuts_table.setItem(row, 2, QTableWidgetItem(key))
                self.shortcuts_table.setItem(row, 3, QTableWidgetItem(context))
                
                self.modified = True
                
        except Exception as e:
            logger.error(f"添加快捷键失败: {e}")
            QMessageBox.critical(self, "错误", f"添加快捷键失败: {e}")
    
    def edit_shortcut(self):
        """编辑快捷键"""
        try:
            current_row = self.shortcuts_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "提示", "请选择要编辑的快捷键")
                return
            
            # 获取当前数据
            action = self.shortcuts_table.item(current_row, 0).text()
            description = self.shortcuts_table.item(current_row, 1).text()
            key = self.shortcuts_table.item(current_row, 2).text()
            context = self.shortcuts_table.item(current_row, 3).text()
            
            dialog = ShortcutEditDialog(self, action, key, description, context)
            if dialog.exec_() == QDialog.Accepted:
                new_action, new_key, new_description, new_context = dialog.get_shortcut_data()
                
                # 更新表格
                self.shortcuts_table.setItem(current_row, 0, QTableWidgetItem(new_action))
                self.shortcuts_table.setItem(current_row, 1, QTableWidgetItem(new_description))
                self.shortcuts_table.setItem(current_row, 2, QTableWidgetItem(new_key))
                self.shortcuts_table.setItem(current_row, 3, QTableWidgetItem(new_context))
                
                self.modified = True
                
        except Exception as e:
            logger.error(f"编辑快捷键失败: {e}")
            QMessageBox.critical(self, "错误", f"编辑快捷键失败: {e}")
    
    def delete_shortcut(self):
        """删除快捷键"""
        try:
            current_row = self.shortcuts_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "提示", "请选择要删除的快捷键")
                return
            
            action = self.shortcuts_table.item(current_row, 0).text()
            
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除快捷键 '{action}' 吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.shortcuts_table.removeRow(current_row)
                self.modified = True
                
        except Exception as e:
            logger.error(f"删除快捷键失败: {e}")
            QMessageBox.critical(self, "错误", f"删除快捷键失败: {e}")
    
    def reset_shortcuts(self):
        """重置快捷键"""
        try:
            reply = QMessageBox.question(
                self, "确认重置", 
                "确定要重置所有快捷键为默认设置吗？\n这将丢失所有自定义设置。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.shortcut_manager.reset_to_defaults()
                self.load_settings()
                self.modified = False
                
        except Exception as e:
            logger.error(f"重置快捷键失败: {e}")
            QMessageBox.critical(self, "错误", f"重置快捷键失败: {e}")
    
    def apply_changes(self):
        """应用更改"""
        try:
            if not self.modified:
                return
            
            # 收集表格中的数据
            shortcuts = {}
            for row in range(self.shortcuts_table.rowCount()):
                if self.shortcuts_table.isRowHidden(row):
                    continue
                    
                action = self.shortcuts_table.item(row, 0).text()
                description = self.shortcuts_table.item(row, 1).text()
                key = self.shortcuts_table.item(row, 2).text()
                context = self.shortcuts_table.item(row, 3).text()
                
                shortcuts[action] = {
                    'key': key,
                    'description': description,
                    'context': context
                }
            
            # 更新快捷键管理器
            self.shortcut_manager.shortcuts = shortcuts
            self.shortcut_manager.save_shortcuts()
            
            self.modified = False
            QMessageBox.information(self, "成功", "快捷键设置已保存")
            
        except Exception as e:
            logger.error(f"应用快捷键更改失败: {e}")
            QMessageBox.critical(self, "错误", f"保存快捷键设置失败: {e}")
    
    def accept_changes(self):
        """接受更改并关闭"""
        if self.modified:
            self.apply_changes()
        self.accept()
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.modified:
            reply = QMessageBox.question(
                self, "未保存的更改", 
                "有未保存的更改，是否保存？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel
            )
            
            if reply == QMessageBox.Save:
                self.apply_changes()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


class ShortcutEditDialog(QDialog):
    """快捷键编辑对话框"""
    
    def __init__(self, parent=None, action="", key="", description="", context="global"):
        super().__init__(parent)
        self.setWindowTitle("编辑快捷键")
        self.setModal(True)
        self.resize(400, 300)
        
        self.init_ui()
        
        # 设置初始值
        if action:
            self.action_edit.setText(action)
        if key:
            self.key_edit.setKeySequence(QKeySequence(key))
        if description:
            self.description_edit.setText(description)
        if context:
            index = self.context_combo.findText(context)
            if index >= 0:
                self.context_combo.setCurrentIndex(index)
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 动作名称
        action_layout = QHBoxLayout()
        action_layout.addWidget(QLabel("动作名称:"))
        self.action_edit = QLineEdit()
        self.action_edit.setPlaceholderText("例如: file_new")
        action_layout.addWidget(self.action_edit)
        layout.addLayout(action_layout)
        
        # 快捷键
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("快捷键:"))
        self.key_edit = QKeySequenceEdit()
        key_layout.addWidget(self.key_edit)
        layout.addLayout(key_layout)
        
        # 描述
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("例如: 新建文件")
        desc_layout.addWidget(self.description_edit)
        layout.addLayout(desc_layout)
        
        # 上下文
        context_layout = QHBoxLayout()
        context_layout.addWidget(QLabel("上下文:"))
        self.context_combo = QComboBox()
        self.context_combo.addItems(["global", "table"])
        context_layout.addWidget(self.context_combo)
        layout.addLayout(context_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
    
    def get_shortcut_data(self):
        """获取快捷键数据"""
        return (
            self.action_edit.text().strip(),
            self.key_edit.keySequence().toString(),
            self.description_edit.text().strip(),
            self.context_combo.currentText()
        )
    
    def accept(self):
        """验证并接受"""
        action = self.action_edit.text().strip()
        key = self.key_edit.keySequence().toString()
        description = self.description_edit.text().strip()
        
        if not action:
            QMessageBox.warning(self, "警告", "请输入动作名称")
            return
        
        if not key:
            QMessageBox.warning(self, "警告", "请设置快捷键")
            return
        
        if not description:
            QMessageBox.warning(self, "警告", "请输入描述")
            return
        
        super().accept()


class ContextualShortcutHandler(QObject):
    """上下文快捷键处理器"""
    
    def __init__(self, shortcut_manager: KeyboardShortcutManager, parent=None):
        super().__init__(parent)
        self.shortcut_manager = shortcut_manager
        self.current_context = 'global'
        self.context_widgets = {}
        
    def set_context(self, context: str, widget: QWidget = None):
        """设置当前上下文"""
        try:
            self.current_context = context
            if widget:
                self.context_widgets[context] = widget
                
            logger.debug(f"切换到上下文: {context}")
            
        except Exception as e:
            logger.error(f"设置上下文失败: {e}")
    
    def get_current_context(self) -> str:
        """获取当前上下文"""
        return self.current_context
    
    def get_context_shortcuts(self) -> Dict:
        """获取当前上下文的快捷键"""
        return self.shortcut_manager.get_shortcuts_by_context(self.current_context)
    
    def handle_key_event(self, event: QEvent) -> bool:
        """处理按键事件"""
        try:
            if event.type() == QEvent.KeyPress:
                key_sequence = QKeySequence(event.key() | int(event.modifiers()))
                
                # 查找匹配的快捷键
                context_shortcuts = self.get_context_shortcuts()
                for action, data in context_shortcuts.items():
                    if QKeySequence(data['key']) == key_sequence:
                        logger.debug(f"触发上下文快捷键: {action}")
                        self.shortcut_manager.shortcut_triggered.emit(action, data)
                        return True
                        
        except Exception as e:
            logger.error(f"处理按键事件失败: {e}")
            
        return False


class ShortcutTooltip(QWidget):
    """快捷键提示工具"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.ToolTip)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 200);
                border: 1px solid #555;
                border-radius: 5px;
                color: white;
                font-size: 12px;
                padding: 5px;
            }
        """)
        
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(8, 8, 8, 8)
        
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.hide)
        
    def show_shortcuts(self, shortcuts: Dict, position: QPoint = None):
        """显示快捷键提示"""
        try:
            # 清除之前的内容
            for i in reversed(range(self.layout.count())):
                self.layout.itemAt(i).widget().setParent(None)
            
            # 添加快捷键信息
            for action, data in shortcuts.items():
                key = data.get('key', '')
                description = data.get('description', action)
                
                shortcut_label = QLabel(f"{key}: {description}")
                shortcut_label.setFont(QFont("Consolas", 10))
                self.layout.addWidget(shortcut_label)
            
            # 调整大小和位置
            self.adjustSize()
            
            if position:
                self.move(position)
            else:
                # 默认位置在鼠标附近
                cursor_pos = QCursor.pos()
                self.move(cursor_pos.x() + 10, cursor_pos.y() + 10)
            
            self.show()
            
            # 3秒后自动隐藏
            self.timer.start(3000)
            
        except Exception as e:
            logger.error(f"显示快捷键提示失败: {e}")
    
    def hide_tooltip(self):
        """隐藏提示"""
        self.hide()
        self.timer.stop() 