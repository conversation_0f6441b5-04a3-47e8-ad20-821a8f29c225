#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页事件流优化器
🔧 [立即修复] 减少重复的分页事件发布，合并UI更新操作
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger


class EventType(Enum):
    """事件类型枚举"""
    PAGE_CHANGE = "page_change"
    UI_UPDATE = "ui_update"
    DATA_LOAD = "data_load"
    STATE_SYNC = "state_sync"


@dataclass
class PaginationEvent:
    """分页事件记录"""
    event_type: EventType
    table_name: str
    page: int
    timestamp: float
    data: Optional[Dict[str, Any]] = None
    callback: Optional[Callable] = None
    priority: int = 0  # 优先级，数字越大优先级越高


class PaginationEventOptimizer:
    """
    🔧 [立即修复] 分页事件流优化器
    
    功能：
    1. 合并重复的分页事件
    2. 批量处理UI更新
    3. 智能延迟执行
    4. 防止事件风暴
    """
    
    def __init__(self):
        """初始化分页事件优化器"""
        self.logger = setup_logger(self.__class__.__name__)
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 事件队列
        self._pending_events: Dict[str, PaginationEvent] = {}
        
        # 批处理配置
        self._batch_delay = 0.1  # 批处理延迟（秒）
        self._max_batch_size = 10  # 最大批处理大小
        
        # 去重配置
        self._dedup_window = 0.5  # 去重时间窗口（秒）
        
        # 执行状态
        self._is_processing = False
        self._last_process_time = 0.0
        
        self.logger.info("🔧 [立即修复] 分页事件优化器初始化完成")
    
    def add_event(self, event_type: EventType, table_name: str, page: int, 
                  data: Optional[Dict[str, Any]] = None, 
                  callback: Optional[Callable] = None,
                  priority: int = 0) -> bool:
        """
        🔧 [立即修复] 添加分页事件到优化队列
        
        Args:
            event_type: 事件类型
            table_name: 表名
            page: 页码
            data: 事件数据
            callback: 回调函数
            priority: 优先级
            
        Returns:
            True: 事件已添加, False: 事件被去重
        """
        current_time = time.time()
        event_key = self._generate_event_key(event_type, table_name, page)
        
        with self._lock:
            # 检查是否有重复事件
            if event_key in self._pending_events:
                existing_event = self._pending_events[event_key]
                time_diff = current_time - existing_event.timestamp
                
                if time_diff < self._dedup_window:
                    # 在去重窗口内，更新现有事件
                    if priority >= existing_event.priority:
                        existing_event.timestamp = current_time
                        existing_event.data = data
                        existing_event.callback = callback
                        existing_event.priority = priority
                        self.logger.debug(f"🔧 [事件去重] 更新事件: {event_key}")
                    else:
                        self.logger.debug(f"🔧 [事件去重] 忽略低优先级事件: {event_key}")
                    return False
            
            # 创建新事件
            event = PaginationEvent(
                event_type=event_type,
                table_name=table_name,
                page=page,
                timestamp=current_time,
                data=data,
                callback=callback,
                priority=priority
            )
            
            self._pending_events[event_key] = event
            self.logger.debug(f"🔧 [事件添加] 新事件: {event_key}, 队列大小: {len(self._pending_events)}")
            
            # 检查是否需要立即处理
            if self._should_process_immediately():
                self._schedule_processing()
            
            return True
    
    def process_events(self) -> int:
        """
        🔧 [立即修复] 处理待处理的事件
        
        Returns:
            处理的事件数量
        """
        with self._lock:
            if self._is_processing:
                self.logger.debug("🔧 [事件处理] 正在处理中，跳过")
                return 0
            
            if not self._pending_events:
                return 0
            
            self._is_processing = True
            
        try:
            # 获取待处理事件
            events_to_process = self._get_events_to_process()
            
            if not events_to_process:
                return 0
            
            # 按优先级和时间排序
            events_to_process.sort(key=lambda e: (-e.priority, e.timestamp))
            
            processed_count = 0
            
            # 批量处理事件
            for event in events_to_process:
                try:
                    self._process_single_event(event)
                    processed_count += 1
                except Exception as e:
                    self.logger.error(f"🔧 [事件处理] 处理事件失败: {e}", exc_info=True)
            
            self.logger.info(f"🔧 [事件处理] 批量处理完成: {processed_count}个事件")
            return processed_count
            
        finally:
            with self._lock:
                self._is_processing = False
                self._last_process_time = time.time()
    
    def _generate_event_key(self, event_type: EventType, table_name: str, page: int) -> str:
        """生成事件键"""
        return f"{event_type.value}:{table_name}:p{page}"
    
    def _should_process_immediately(self) -> bool:
        """检查是否应该立即处理事件"""
        current_time = time.time()
        
        # 检查队列大小
        if len(self._pending_events) >= self._max_batch_size:
            return True
        
        # 检查时间间隔
        if current_time - self._last_process_time >= self._batch_delay:
            return True
        
        # 检查高优先级事件
        for event in self._pending_events.values():
            if event.priority >= 10:  # 高优先级阈值
                return True
        
        return False
    
    def _get_events_to_process(self) -> List[PaginationEvent]:
        """获取需要处理的事件"""
        with self._lock:
            events = list(self._pending_events.values())
            self._pending_events.clear()
            return events
    
    def _process_single_event(self, event: PaginationEvent):
        """处理单个事件"""
        try:
            if event.callback:
                # 🔧 [P1-CRITICAL修复] 智能回调参数处理，解决参数不匹配问题
                import inspect
                try:
                    # 检查回调函数的参数签名
                    sig = inspect.signature(event.callback)
                    param_count = len(sig.parameters)

                    if param_count == 0:
                        # 无参数函数，直接调用
                        event.callback()
                        self.logger.debug(f"🔧 [P1-修复] 执行无参数回调: {event.event_type.value}")
                    elif param_count == 1 and event.data:
                        # 单参数函数且有数据，传递数据
                        event.callback(event.data)
                        self.logger.debug(f"🔧 [P1-修复] 执行单参数回调: {event.event_type.value}")
                    else:
                        # 其他情况，尝试无参数调用
                        event.callback()
                        self.logger.debug(f"🔧 [P1-修复] 执行回调(默认无参数): {event.event_type.value}")

                except TypeError as te:
                    # 🔧 [P1-CRITICAL修复] 参数不匹配时的降级处理
                    try:
                        event.callback()
                        self.logger.warning(f"🔧 [P1-修复] 回调参数不匹配，降级为无参数调用: {te}")
                    except Exception as fallback_e:
                        self.logger.error(f"🔧 [P1-修复] 回调降级调用也失败: {fallback_e}")
                        raise

            else:
                self.logger.debug(f"🔧 [事件执行] 无回调函数: {event.event_type.value}")

        except Exception as e:
            self.logger.error(f"🔧 [事件执行] 回调执行失败: {e}", exc_info=True)
    
    def _schedule_processing(self):
        """调度事件处理"""
        from src.utils.thread_safe_timer import safe_single_shot
        
        # 使用线程安全的延迟处理
        safe_single_shot(int(self._batch_delay * 1000), self.process_events)
        self.logger.debug(f"🔧 [事件调度] 已调度延迟处理: {self._batch_delay}s")
    
    def force_process_all(self) -> int:
        """🔧 [立即修复] 强制处理所有待处理事件"""
        return self.process_events()
    
    def clear_events(self, table_name: Optional[str] = None):
        """🔧 [立即修复] 清理事件队列"""
        with self._lock:
            if table_name:
                # 清理特定表的事件
                keys_to_remove = [
                    key for key in self._pending_events.keys()
                    if table_name in key
                ]
                for key in keys_to_remove:
                    del self._pending_events[key]
                self.logger.info(f"🔧 [事件清理] 清理表事件: {table_name}, 清理数量: {len(keys_to_remove)}")
            else:
                # 清理所有事件
                count = len(self._pending_events)
                self._pending_events.clear()
                self.logger.info(f"🔧 [事件清理] 清理所有事件: {count}个")
    
    def get_status(self) -> Dict[str, Any]:
        """获取优化器状态"""
        with self._lock:
            return {
                'pending_events_count': len(self._pending_events),
                'is_processing': self._is_processing,
                'last_process_time': self._last_process_time,
                'batch_delay': self._batch_delay,
                'max_batch_size': self._max_batch_size,
                'dedup_window': self._dedup_window
            }


# 全局单例实例
_pagination_event_optimizer = None


def get_pagination_event_optimizer() -> PaginationEventOptimizer:
    """获取分页事件优化器单例"""
    global _pagination_event_optimizer
    if _pagination_event_optimizer is None:
        _pagination_event_optimizer = PaginationEventOptimizer()
    return _pagination_event_optimizer
