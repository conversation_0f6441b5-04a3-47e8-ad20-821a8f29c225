#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存优化修复
解决缓存被禁用导致的重复数据库查询问题

问题：性能缓存被暂时禁用，导致每次分页都要重新查询数据库
解决：修复缓存键冲突问题，重新启用智能缓存机制
"""

import time
import hashlib
import json
import sys
import os
import threading
from typing import Dict, Any, Optional, List, Tuple
from collections import OrderedDict
import pandas as pd

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.log_config import setup_logger


class OptimizedDataCache:
    """🚀 [缓存优化] 优化的数据缓存系统"""
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        self.logger = setup_logger(__name__ + ".OptimizedDataCache")
        
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
        # 分层缓存设计
        self._page_cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()  # 分页数据缓存
        self._sort_cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()  # 排序结果缓存
        self._metadata_cache: Dict[str, Dict[str, Any]] = {}  # 元数据缓存
        
        # 缓存统计
        self.stats = {
            'page_hits': 0,
            'page_misses': 0,
            'sort_hits': 0,
            'sort_misses': 0,
            'evictions': 0,
            'expired': 0
        }
        
        self.logger.info(f"🚀 [缓存优化] 优化数据缓存初始化 - 最大: {max_size}, TTL: {ttl_seconds}s")

    def _generate_stable_cache_key(self, table_name: str, page: int, page_size: int,
                                 sort_columns: Optional[List[Dict]] = None,
                                 filters: Optional[Dict] = None) -> str:
        """🔧 [关键修复] 生成稳定的缓存键，解决键冲突问题"""
        
        # 标准化排序参数
        sort_key = ""
        if sort_columns:
            # 排序列标准化：按列名排序，确保键的一致性
            normalized_sorts = []
            for col in sort_columns:
                col_name = str(col.get('column_name', '')).strip()
                order = str(col.get('order', 'asc')).lower().strip()
                if col_name:  # 只包含有效的列名
                    normalized_sorts.append(f"{col_name}:{order}")
            
            # 按列名排序，确保相同排序的不同顺序产生相同的键
            normalized_sorts.sort()
            sort_key = "|".join(normalized_sorts)
        
        # 标准化过滤器参数
        filter_key = ""
        if filters:
            # 将过滤器转为JSON字符串，确保一致性
            try:
                filter_key = json.dumps(filters, sort_keys=True, separators=(',', ':'))
            except Exception as e:
                self.logger.warning(f"过滤器序列化失败: {e}")
                filter_key = str(filters)
        
        # 构建缓存键
        key_parts = [
            str(table_name).strip(),
            str(page),
            str(page_size),
            sort_key,
            filter_key
        ]
        
        # 使用MD5确保键的长度稳定
        key_content = "_".join(key_parts)
        cache_key = hashlib.md5(key_content.encode('utf-8')).hexdigest()
        
        # 添加可读前缀
        readable_prefix = f"{table_name}_p{page}_s{page_size}"
        if sort_key:
            readable_prefix += f"_sort"
        
        final_key = f"{readable_prefix}_{cache_key[:8]}"
        
        self.logger.debug(f"🔧 [缓存键] 生成: {final_key} <- {key_content}")
        return final_key

    def get_page_data(self, table_name: str, page: int, page_size: int,
                     sort_columns: Optional[List[Dict]] = None,
                     filters: Optional[Dict] = None) -> Optional[Tuple[Any, Dict]]:
        """🚀 [缓存优化] 获取分页数据"""
        
        cache_key = self._generate_stable_cache_key(table_name, page, page_size, sort_columns, filters)
        
        # 检查分页缓存
        if cache_key in self._page_cache:
            cache_entry = self._page_cache[cache_key]
            
            # 检查TTL
            if (time.time() - cache_entry['timestamp']) < self.ttl_seconds:
                # 缓存命中，移到末尾（LRU）
                self._page_cache.move_to_end(cache_key)
                self.stats['page_hits'] += 1
                
                data = cache_entry['data']
                metadata = cache_entry.get('metadata', {})
                
                self.logger.info(f"🚀 [分页缓存命中] {table_name} 第{page}页")
                return data, metadata
            else:
                # 缓存过期
                del self._page_cache[cache_key]
                self.stats['expired'] += 1
                self.logger.debug(f"🚀 [缓存过期] {cache_key}")
        
        # 缓存未命中
        self.stats['page_misses'] += 1
        self.logger.debug(f"🚀 [分页缓存未命中] {table_name} 第{page}页")
        return None

    def cache_page_data(self, table_name: str, page: int, page_size: int, data: Any,
                       metadata: Optional[Dict] = None,
                       sort_columns: Optional[List[Dict]] = None,
                       filters: Optional[Dict] = None):
        """🚀 [缓存优化] 缓存分页数据"""
        
        cache_key = self._generate_stable_cache_key(table_name, page, page_size, sort_columns, filters)
        
        cache_entry = {
            'data': data,
            'metadata': metadata or {},
            'timestamp': time.time(),
            'table_name': table_name,
            'page': page,
            'page_size': page_size,
            'sort_columns': sort_columns,
            'filters': filters
        }
        
        self._page_cache[cache_key] = cache_entry
        
        # LRU清理
        if len(self._page_cache) > self.max_size:
            # 移除最老的条目
            oldest_key = next(iter(self._page_cache))
            del self._page_cache[oldest_key]
            self.stats['evictions'] += 1
            self.logger.debug(f"🚀 [LRU清理] 移除: {oldest_key}")
        
        self.logger.info(f"🚀 [分页缓存存储] {table_name} 第{page}页 -> {cache_key}")

    def get_sort_result(self, table_name: str, sort_columns: List[Dict]) -> Optional[Any]:
        """🚀 [缓存优化] 获取排序结果"""
        
        if not sort_columns:
            return None
        
        sort_key = self._generate_sort_cache_key(table_name, sort_columns)
        
        if sort_key in self._sort_cache:
            cache_entry = self._sort_cache[sort_key]
            
            # 检查TTL（排序结果TTL更长）
            sort_ttl = self.ttl_seconds * 2  # 排序结果缓存时间更长
            if (time.time() - cache_entry['timestamp']) < sort_ttl:
                self._sort_cache.move_to_end(sort_key)
                self.stats['sort_hits'] += 1
                
                self.logger.info(f"🚀 [排序缓存命中] {table_name}")
                return cache_entry['data']
            else:
                del self._sort_cache[sort_key]
                self.stats['expired'] += 1
        
        self.stats['sort_misses'] += 1
        return None

    def cache_sort_result(self, table_name: str, sort_columns: List[Dict], data: Any):
        """🚀 [缓存优化] 缓存排序结果"""
        
        if not sort_columns:
            return
        
        sort_key = self._generate_sort_cache_key(table_name, sort_columns)
        
        cache_entry = {
            'data': data,
            'timestamp': time.time(),
            'table_name': table_name,
            'sort_columns': sort_columns
        }
        
        self._sort_cache[sort_key] = cache_entry
        
        # 排序缓存LRU清理
        max_sort_cache = self.max_size // 2  # 排序缓存占一半
        if len(self._sort_cache) > max_sort_cache:
            oldest_key = next(iter(self._sort_cache))
            del self._sort_cache[oldest_key]
            self.stats['evictions'] += 1
        
        self.logger.info(f"🚀 [排序缓存存储] {table_name} -> {sort_key}")

    def _generate_sort_cache_key(self, table_name: str, sort_columns: List[Dict]) -> str:
        """生成排序缓存键"""
        sort_parts = []
        for col in sort_columns:
            col_name = str(col.get('column_name', '')).strip()
            order = str(col.get('order', 'asc')).lower().strip()
            if col_name:
                sort_parts.append(f"{col_name}:{order}")
        
        sort_parts.sort()  # 确保顺序一致性
        sort_key = "|".join(sort_parts)
        
        key_content = f"sort_{table_name}_{sort_key}"
        return hashlib.md5(key_content.encode('utf-8')).hexdigest()[:16]

    def invalidate_table_cache(self, table_name: str):
        """🧹 [缓存管理] 清理指定表的缓存"""
        
        # 清理分页缓存
        page_keys_to_remove = [
            key for key, entry in self._page_cache.items()
            if entry.get('table_name') == table_name
        ]
        
        for key in page_keys_to_remove:
            del self._page_cache[key]
        
        # 清理排序缓存
        sort_keys_to_remove = [
            key for key, entry in self._sort_cache.items()
            if entry.get('table_name') == table_name
        ]
        
        for key in sort_keys_to_remove:
            del self._sort_cache[key]
        
        # 清理元数据缓存
        self._metadata_cache.pop(table_name, None)
        
        self.logger.info(f"🧹 [缓存清理] 已清理表 {table_name} 的缓存: "
                        f"{len(page_keys_to_remove)}页 + {len(sort_keys_to_remove)}排序")

    def get_cache_stats(self) -> Dict[str, Any]:
        """📊 [缓存统计] 获取缓存统计信息"""
        
        total_requests = self.stats['page_hits'] + self.stats['page_misses']
        page_hit_rate = (self.stats['page_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        total_sort_requests = self.stats['sort_hits'] + self.stats['sort_misses']
        sort_hit_rate = (self.stats['sort_hits'] / total_sort_requests * 100) if total_sort_requests > 0 else 0
        
        return {
            'page_cache_size': len(self._page_cache),
            'sort_cache_size': len(self._sort_cache),
            'page_hit_rate': f"{page_hit_rate:.1f}%",
            'sort_hit_rate': f"{sort_hit_rate:.1f}%",
            'total_evictions': self.stats['evictions'],
            'total_expired': self.stats['expired'],
            **self.stats
        }

    def cleanup_expired_cache(self):
        """🧹 [定期清理] 清理过期缓存"""
        
        current_time = time.time()
        
        # 清理过期的分页缓存
        expired_page_keys = [
            key for key, entry in self._page_cache.items()
            if (current_time - entry['timestamp']) > self.ttl_seconds
        ]
        
        for key in expired_page_keys:
            del self._page_cache[key]
            self.stats['expired'] += 1
        
        # 清理过期的排序缓存
        sort_ttl = self.ttl_seconds * 2
        expired_sort_keys = [
            key for key, entry in self._sort_cache.items()
            if (current_time - entry['timestamp']) > sort_ttl
        ]
        
        for key in expired_sort_keys:
            del self._sort_cache[key]
            self.stats['expired'] += 1
        
        if expired_page_keys or expired_sort_keys:
            self.logger.info(f"🧹 [定期清理] 清理过期缓存: "
                           f"{len(expired_page_keys)}页 + {len(expired_sort_keys)}排序")


class ThreadSafeCleanupManager:
    """🧹 [线程安全] 线程安全的缓存清理管理器"""
    
    def __init__(self, cache_instance: OptimizedDataCache, cleanup_interval: int = 60):
        self.cache = cache_instance
        self.cleanup_interval = cleanup_interval
        self.logger = setup_logger(__name__ + ".ThreadSafeCleanupManager")
        
        # 使用线程而不是QTimer
        self._cleanup_thread = None
        self._stop_event = threading.Event()
        self._start_cleanup_thread()
        
        self.logger.info(f"🧹 [线程安全清理] 清理管理器初始化完成 - 间隔: {cleanup_interval}秒")

    def _start_cleanup_thread(self):
        """启动清理线程"""
        self._cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        self.logger.info("🧹 [清理线程] 后台清理线程已启动")

    def _cleanup_worker(self):
        """清理工作线程"""
        while not self._stop_event.is_set():
            try:
                # 等待指定时间或停止信号
                if self._stop_event.wait(self.cleanup_interval):
                    break  # 收到停止信号
                
                # 执行清理
                self.cache.cleanup_expired_cache()
                
            except Exception as e:
                self.logger.error(f"🧹 [清理错误] 定期清理失败: {e}")

    def stop_cleanup(self):
        """停止清理线程"""
        if self._stop_event:
            self._stop_event.set()
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
        self.logger.info("🧹 [清理停止] 后台清理线程已停止")


class CacheOptimizedDataLoader:
    """🚀 [缓存集成] 集成优化缓存的数据加载器"""
    
    def __init__(self, db_manager=None):
        self.logger = setup_logger(__name__ + ".CacheOptimizedDataLoader")
        self.cache = OptimizedDataCache()
        
        # 数据库管理器（延迟获取）
        self.db_manager = db_manager
        
        # 使用线程安全的清理管理器
        self.cleanup_manager = ThreadSafeCleanupManager(self.cache, cleanup_interval=60)
        
        self.logger.info("🚀 [缓存集成] 缓存优化数据加载器初始化完成")
    
    def _get_db_manager(self):
        """🔧 [延迟获取] 获取数据库管理器"""
        if self.db_manager is None:
            try:
                # 尝试从主应用获取数据库管理器
                from src.core.architecture_factory import ArchitectureFactory
                factory = ArchitectureFactory.get_instance()
                if hasattr(factory, 'db_manager'):
                    self.db_manager = factory.db_manager
                    self.logger.info("🔧 [延迟获取] 成功获取数据库管理器")
                else:
                    self.logger.warning("🔧 [延迟获取] 架构工厂中未找到数据库管理器")
            except Exception as e:
                self.logger.error(f"🔧 [延迟获取] 获取数据库管理器失败: {e}")
        
        return self.db_manager

    def load_table_data_with_cache(self, table_name: str, page: int, page_size: int = 50,
                                  sort_columns: Optional[List[Dict]] = None,
                                  force_reload: bool = False) -> Tuple[Any, Dict, bool]:
        """🚀 [缓存集成] 带缓存的数据加载"""
        
        # 强制重载时跳过缓存
        if force_reload:
            self.logger.info(f"🚀 [强制重载] 跳过缓存: {table_name} 第{page}页")
            return self._load_from_database(table_name, page, page_size, sort_columns)
        
        # 先检查缓存
        cached_result = self.cache.get_page_data(table_name, page, page_size, sort_columns)
        if cached_result is not None:
            data, metadata = cached_result
            metadata['from_cache'] = True
            return data, metadata, True
        
        # 缓存未命中，从数据库加载
        return self._load_from_database(table_name, page, page_size, sort_columns)

    def _load_from_database(self, table_name: str, page: int, page_size: int,
                           sort_columns: Optional[List[Dict]] = None) -> Tuple[Any, Dict, bool]:
        """🔧 [数据库加载] 从数据库加载数据"""
        
        start_time = time.time()
        
        try:
            # 获取数据库管理器
            db_manager = self._get_db_manager()
            if db_manager is None:
                self.logger.error("🚀 [数据库连接] 无法获取数据库管理器")
                return None, {'error': '无法获取数据库管理器', 'from_cache': False}, False
            
            self.logger.info(f"🚀 [直接数据库] 开始加载: {table_name} 第{page}页")
            
            # 直接调用数据库管理器的分页方法
            df = db_manager.get_dataframe_paginated_with_sort(
                table_name=table_name,
                page=page,
                page_size=page_size,
                sort_columns=sort_columns or []
            )
            
            # 获取总记录数
            total_count = db_manager.get_total_count(table_name)
            
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            if df is not None and not df.empty:
                metadata = {
                    'total_records': total_count,
                    'current_page': page,
                    'page_size': page_size,
                    'sort_columns': sort_columns,
                    'from_cache': False,
                    'processing_time_ms': processing_time,
                    'data_source': 'database_direct'
                }
                
                # 缓存结果
                self.cache.cache_page_data(
                    table_name, page, page_size, df, metadata, sort_columns
                )
                
                self.logger.info(f"🚀 [数据库成功] {table_name} 第{page}页 - {len(df)}行，耗时{processing_time:.1f}ms")
                return df, metadata, False
            else:
                self.logger.warning(f"🚀 [数据库空结果] {table_name} 第{page}页返回空数据")
                return None, {'error': '数据库返回空结果', 'from_cache': False}, False
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            error_msg = f"数据库加载失败: {str(e)}"
            self.logger.error(f"🚀 [数据库加载失败] {error_msg}，耗时{processing_time:.1f}ms")
            return None, {'error': error_msg, 'from_cache': False, 'processing_time_ms': processing_time}, False

    def get_cache_performance_report(self) -> str:
        """📊 [性能报告] 获取缓存性能报告"""
        
        stats = self.cache.get_cache_stats()
        
        report = f"""
🚀 缓存性能报告
================
分页缓存: {stats['page_cache_size']} 条目
排序缓存: {stats['sort_cache_size']} 条目
分页命中率: {stats['page_hit_rate']}
排序命中率: {stats['sort_hit_rate']}
清理次数: {stats['total_evictions']}
过期次数: {stats['total_expired']}

详细统计:
- 分页命中: {stats['page_hits']}
- 分页未命中: {stats['page_misses']}
- 排序命中: {stats['sort_hits']}
- 排序未命中: {stats['sort_misses']}
"""
        return report

    def shutdown(self):
        """关闭缓存加载器"""
        if hasattr(self, 'cleanup_manager'):
            self.cleanup_manager.stop_cleanup()
        self.logger.info("🚀 [缓存关闭] 缓存优化数据加载器已关闭")


# 🔧 [修复应用] 应用缓存优化到现有代码
def apply_cache_optimization_fix():
    """应用缓存优化修复"""
    
    print("🚀 [缓存修复] 应用缓存优化修复...")
    
    # 创建全局缓存实例
    global optimized_cache_loader
    optimized_cache_loader = CacheOptimizedDataLoader()
    
    print("🚀 [缓存修复] 缓存优化已应用")
    
    return optimized_cache_loader


if __name__ == "__main__":
    """🧪 [测试] 缓存优化测试"""
    
    print("🧪 [测试] 开始缓存优化测试...")
    
    # 创建缓存优化加载器
    loader = CacheOptimizedDataLoader()
    
    # 模拟数据加载
    table_name = "test_table"
    
    # 第一次加载（应该从数据库）
    print("第一次加载...")
    data1, meta1, from_cache1 = loader.load_table_data_with_cache(table_name, 1, 50)
    print(f"从缓存: {from_cache1}")
    
    # 第二次加载（应该从缓存）
    print("第二次加载...")
    data2, meta2, from_cache2 = loader.load_table_data_with_cache(table_name, 1, 50)
    print(f"从缓存: {from_cache2}")
    
    # 带排序的加载
    sort_columns = [{'column_name': 'name', 'order': 'asc'}]
    print("带排序加载...")
    data3, meta3, from_cache3 = loader.load_table_data_with_cache(table_name, 1, 50, sort_columns)
    print(f"从缓存: {from_cache3}")
    
    # 性能报告
    print(loader.get_cache_performance_report())
    
    # 清理
    loader.shutdown()
    
    print("🧪 [测试] 缓存优化测试完成") 