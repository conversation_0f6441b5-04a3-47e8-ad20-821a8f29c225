#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试当前系统问题的测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_header_order():
    """测试表头顺序问题"""
    print("=" * 60)
    print("测试1: 表头顺序问题")
    print("=" * 60)
    
    try:
        # 导入主窗口类
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        # 创建临时实例（不显示UI）
        import sys
        from PyQt5.QtWidgets import QApplication
        
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建主窗口实例
        main_window = PrototypeMainWindow()
        
        # 测试全部在职人员表头
        active_headers = main_window._get_standard_active_employee_headers()
        print(f"全部在职人员表头前5个: {active_headers[:5]}")
        
        # 测试A岗职工表头
        a_grade_headers = main_window._get_table_headers_by_type("salary_data_2025_08_a_grade_employees")
        print(f"A岗职工表头前5个: {a_grade_headers[:5]}")
        
        # 检查人员类别代码位置
        if "人员类别代码" in active_headers:
            active_pos = active_headers.index("人员类别代码")
            print(f"全部在职人员 - 人员类别代码位置: {active_pos + 1}")
        
        if "人员类别代码" in a_grade_headers:
            a_grade_pos = a_grade_headers.index("人员类别代码")
            print(f"A岗职工 - 人员类别代码位置: {a_grade_pos + 1}")
        
        # 检查是否一致
        if active_headers[:5] == a_grade_headers[:5]:
            print("✅ 表头顺序一致")
        else:
            print("❌ 表头顺序不一致")
            print(f"差异: 全部在职人员={active_headers[:5]}")
            print(f"     A岗职工={a_grade_headers[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_insurance_deduction_formatting():
    """测试保险扣款字段格式化"""
    print("\n" + "=" * 60)
    print("测试2: 保险扣款字段格式化")
    print("=" * 60)
    
    try:
        # 测试格式化渲染器
        from src.modules.format_management.format_renderer import FormatRenderer
        
        renderer = FormatRenderer()
        
        # 测试不同的空值情况
        test_values = [None, "", "0", 0, 0.0, "nan", "null", "-"]
        
        print("保险扣款字段空值格式化测试:")
        for value in test_values:
            # 模拟浮点数格式化配置
            format_config = {
                'decimal_places': 2,
                'zero_display': '0.00'
            }
            
            result = renderer._render_float_value(value, format_config, "保险扣款")
            print(f"  输入: {repr(value)} -> 输出: '{result}'")
        
        # 检查是否都显示为"0.00"
        expected = "0.00"
        all_correct = True
        for value in test_values:
            result = renderer._render_float_value(value, format_config, "保险扣款")
            if result != expected:
                all_correct = False
                print(f"❌ 值 {repr(value)} 格式化结果 '{result}' 不等于期望的 '{expected}'")
        
        if all_correct:
            print("✅ 保险扣款字段格式化正确")
        else:
            print("❌ 保险扣款字段格式化有问题")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_column_width_management():
    """测试列宽管理问题"""
    print("\n" + "=" * 60)
    print("测试3: 列宽管理问题")
    print("=" * 60)
    
    try:
        # 模拟表格组件
        from PyQt5.QtWidgets import QTableWidget, QApplication
        import sys
        
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建测试表格
        table = QTableWidget()
        
        # 设置初始列数
        initial_columns = 20
        table.setColumnCount(initial_columns)
        
        print(f"初始列数: {table.columnCount()}")
        
        # 模拟排序后数据列数变化
        new_data_columns = 22  # 假设排序后数据有22列
        
        # 检查列数不匹配的情况
        if table.columnCount() != new_data_columns:
            print(f"❌ 列数不匹配: 表头{table.columnCount()}列, 数据{new_data_columns}列")
            print("这可能导致空白列出现")
            
            # 修复列数
            table.setColumnCount(new_data_columns)
            print(f"✅ 已修复列数为: {table.columnCount()}")
        else:
            print("✅ 列数匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始调试当前系统问题...")
    
    results = []
    
    # 测试1: 表头顺序
    results.append(test_header_order())
    
    # 测试2: 保险扣款格式化
    results.append(test_insurance_deduction_formatting())
    
    # 测试3: 列宽管理
    results.append(test_column_width_management())
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    main()
