#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离休人员工资表格式处理测试脚本

验证用户需求的完整实现：
1. 浮点型字段转换为小数点后保留两位
2. 空值统一显示为"0.00"
3. 月份字段提取后两位转换为字符串
4. 年份字段转换为字符串
5. 隐藏系统字段
6. 样式统一性

创建时间: 2025-08-01
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    # 导入格式管理系统
    from src.modules.format_management.unified_format_manager import get_unified_format_manager
    from src.modules.format_management.format_config import FormatConfig
    from src.modules.format_management.field_registry import FieldRegistry
    
    print("[SUCCESS] 成功导入格式管理系统")
except ImportError as e:
    print(f"[ERROR] 导入格式管理系统失败: {e}")
    sys.exit(1)

def create_test_data():
    """创建测试数据"""
    test_data = {
        'sequence_number': ['1', '2', '3'],
        'employee_id': ['R001', 'R002', 'R003'],
        'employee_name': ['张三', '李四', '王五'],
        'department': ['财务部', '人事部', '技术部'],
        'basic_retirement_salary': [3500.0, None, 4200.5],
        'balance_allowance': ['3200', 'nan', '0'],
        'living_allowance': [800.0, 0.0, ''],
        'housing_allowance': [1200.0, '-', 1300.0],
        'property_allowance': [200.0, ' ', 250.0],
        'retirement_allowance': [500.0, '0.0', 600.0],
        'nursing_fee': [300.0, None, '-'],
        'one_time_living_allowance': [1000.0, 'None', 1200.0],
        'supplement': [100.0, 0, '200'],
        'total': [7700.0, 'nan', 8250.5],
        'advance': [0.0, '-', 100.0],
        'remarks': ['正常', '', '调整'],
        'month': ['2025年5月', '202505', '5'],
        'year': ['2025', '2025年', '2025'],
        'id': [1, 2, 3],
        'created_at': ['2025-08-01 10:00:00', '2025-08-01 11:00:00', '2025-08-01 12:00:00'],
        'updated_at': ['2025-08-01 10:30:00', '2025-08-01 11:30:00', '2025-08-01 12:30:00']
    }
    
    return pd.DataFrame(test_data)

def test_float_formatting():
    """测试浮点型字段格式化"""
    print("\n=== 测试浮点型字段格式化 ===")
    
    # 创建格式管理器
    format_manager = get_unified_format_manager()
    
    # 创建测试数据
    df = create_test_data()
    table_type = 'retired_employees'
    
    print(f"原始数据形状: {df.shape}")
    print(f"原始列名: {list(df.columns)}")
    
    # 格式化数据
    try:
        formatted_headers, formatted_df = format_manager.format_table_complete(
            data=df,
            table_type=table_type,
            data_source='test'
        )
        
        print(f"格式化后数据形状: {formatted_df.shape}")
        print(f"格式化后列名: {formatted_headers}")
        
        # 验证浮点型字段格式化
        float_fields = [
            '基本离休费', '结余津贴', '生活补贴', '住房补贴', 
            '物业补贴', '离休补贴', '护理费', '增发一次性生活补贴', 
            '补发', '合计', '借支'
        ]
        
        print("\n--- 浮点型字段格式化结果 ---")
        for field in float_fields:
            if field in formatted_df.columns:
                values = formatted_df[field].tolist()
                print(f"{field}: {values}")
                
                # 验证格式
                for i, value in enumerate(values):
                    if isinstance(value, str):
                        # 检查是否为两位小数格式
                        if value != "0.00" and not (value.replace('.', '').replace('-', '').isdigit() and value.count('.') == 1):
                            print(f"  ⚠️  行{i+1}格式可能不正确: {value}")
                        else:
                            print(f"  ✓ 行{i+1}格式正确: {value}")
            else:
                print(f"  ✗ 字段 {field} 未找到")
        
        # 验证月份和年份字段
        print("\n--- 月份和年份字段格式化结果 ---")
        if '月份' in formatted_df.columns:
            month_values = formatted_df['月份'].tolist()
            print(f"月份: {month_values}")
            for i, value in enumerate(month_values):
                if len(str(value)) <= 2 and str(value).isdigit():
                    print(f"  ✓ 行{i+1}月份格式正确: {value}")
                else:
                    print(f"  ⚠️  行{i+1}月份格式可能不正确: {value}")
        
        if '年份' in formatted_df.columns:
            year_values = formatted_df['年份'].tolist()
            print(f"年份: {year_values}")
            for i, value in enumerate(year_values):
                if str(value).isdigit() and len(str(value)) == 4:
                    print(f"  ✓ 行{i+1}年份格式正确: {value}")
                else:
                    print(f"  ⚠️  行{i+1}年份格式可能不正确: {value}")
        
        # 验证隐藏字段
        print("\n--- 隐藏字段验证 ---")
        hidden_fields = ['id', 'created_at', 'updated_at', 'sequence_number', '自增主键', '创建时间', '更新时间', '序号']
        found_hidden = [field for field in hidden_fields if field in formatted_headers]
        if found_hidden:
            print(f"  ⚠️  发现未隐藏的字段: {found_hidden}")
        else:
            print("  ✓ 所有系统字段已正确隐藏")
        
        print("\n--- 完整格式化结果 ---")
        print(formatted_df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"✗ 格式化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 创建包含各种边界情况的测试数据
    edge_case_data = {
        'employee_name': ['测试用户'],
        'basic_retirement_salary': [None],
        'balance_allowance': ['nan'],
        'living_allowance': [''],
        'housing_allowance': [' '],
        'property_allowance': ['-'],
        'retirement_allowance': ['0'],
        'nursing_fee': ['0.0'],
        'one_time_living_allowance': ['None'],
        'supplement': [np.nan],
        'total': [0.0],
        'advance': ['  '],
        'month': [''],
        'year': [None]
    }
    
    df = pd.DataFrame(edge_case_data)
    format_manager = get_unified_format_manager()
    
    try:
        # 只测试格式化数据部分
        formatted_df = format_manager.format_data(
            data=df,
            table_type='retired_employees',
            data_source='edge_case_test'
        )
        
        print("边界情况格式化成功:")
        print(formatted_df.to_string(index=False))
        return True
        
    except Exception as e:
        print(f"✗ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("离休人员工资表格式处理测试")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 执行测试
    print("开始测试...")
    
    try:
        # 测试基本格式化功能
        result1 = test_float_formatting()
        results.append(("浮点型字段格式化", result1))
        
        # 测试边界情况
        result2 = test_edge_cases()
        results.append(("边界情况处理", result2))
        
    except Exception as e:
        print(f"测试执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    # 总体结果
    passed_tests = sum(1 for _, result in results if result)
    total_tests = len(results)
    
    print(f"\n总计: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！离休人员工资表格式处理功能已正确实现。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)