#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt安全检查工具

自动扫描代码中可能导致Qt底层问题的危险模式
预防QBasicTimer、QPaintDevice、recursive repaint等问题

使用方法:
python scripts/qt_safety_checker.py
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple

class QtSafetyChecker:
    """Qt安全检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        
        # 危险模式定义
        self.dangerous_patterns = {
            "repaint_calls": {
                "pattern": r"\.repaint\(\)",
                "severity": "HIGH",
                "description": "直接调用repaint()可能导致递归重绘",
                "suggestion": "使用update()替代repaint()"
            },
            "timer_in_thread": {
                "pattern": r"QTimer\(\)|QTimer\.singleShot\(",
                "severity": "MEDIUM", 
                "description": "QTimer使用可能存在线程安全问题",
                "suggestion": "确保在主线程中使用QTimer"
            },
            "painter_without_context": {
                "pattern": r"QPainter\(\s*[^)]*\s*\)",
                "severity": "HIGH",
                "description": "QPainter创建可能缺少上下文管理",
                "suggestion": "使用with语句或确保begin()/end()配对"
            },
            "processEvents_in_loop": {
                "pattern": r"processEvents\(\).*\n.*while|while.*\n.*processEvents\(\)",
                "severity": "HIGH", 
                "description": "在循环中调用processEvents可能导致事件循环嵌套",
                "suggestion": "避免在循环中直接调用processEvents"
            },
            "blocking_in_event": {
                "pattern": r"def.*Event\(.*\):.*\n(.*\n)*.*time\.sleep\(|def.*Event\(.*\):.*\n(.*\n)*.*input\(",
                "severity": "HIGH",
                "description": "在事件处理中进行阻塞操作",
                "suggestion": "使用异步处理或信号槽机制"
            }
        }
    
    def scan_file(self, file_path: Path) -> List[Dict]:
        """扫描单个文件"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for pattern_name, pattern_info in self.dangerous_patterns.items():
                matches = re.finditer(pattern_info["pattern"], content, re.MULTILINE)
                
                for match in matches:
                    # 计算行号
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                    
                    issues.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "line": line_num,
                        "pattern": pattern_name,
                        "severity": pattern_info["severity"],
                        "description": pattern_info["description"],
                        "suggestion": pattern_info["suggestion"],
                        "code": line_content,
                        "match": match.group()
                    })
                    
        except Exception as e:
            print(f"扫描文件失败 {file_path}: {e}")
            
        return issues
    
    def scan_project(self) -> List[Dict]:
        """扫描整个项目"""
        all_issues = []
        
        # 扫描src目录下的所有Python文件
        src_dir = self.project_root / "src"
        if src_dir.exists():
            for py_file in src_dir.rglob("*.py"):
                issues = self.scan_file(py_file)
                all_issues.extend(issues)
        
        # 扫描主要的Python文件
        main_files = ["main.py", "app.py", "run.py"]
        for main_file in main_files:
            main_path = self.project_root / main_file
            if main_path.exists():
                issues = self.scan_file(main_path)
                all_issues.extend(issues)
        
        return all_issues
    
    def generate_report(self, issues: List[Dict]) -> str:
        """生成检查报告"""
        if not issues:
            return "✅ 未发现Qt安全问题！"
        
        # 按严重程度分组
        severity_groups = {"HIGH": [], "MEDIUM": [], "LOW": []}
        for issue in issues:
            severity_groups[issue["severity"]].append(issue)
        
        report = []
        report.append("🚨 Qt安全检查报告")
        report.append("=" * 50)
        report.append(f"总计发现 {len(issues)} 个潜在问题")
        report.append("")
        
        for severity in ["HIGH", "MEDIUM", "LOW"]:
            if severity_groups[severity]:
                emoji = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}[severity]
                report.append(f"{emoji} {severity}级问题 ({len(severity_groups[severity])}个)")
                report.append("-" * 30)
                
                for issue in severity_groups[severity]:
                    report.append(f"📁 文件: {issue['file']}")
                    report.append(f"📍 行号: {issue['line']}")
                    report.append(f"🔍 模式: {issue['pattern']}")
                    report.append(f"📝 描述: {issue['description']}")
                    report.append(f"💡 建议: {issue['suggestion']}")
                    report.append(f"💻 代码: {issue['code']}")
                    report.append("")
        
        # 添加修复建议
        report.append("🛠️ 通用修复建议")
        report.append("-" * 30)
        report.append("1. 使用update()替代repaint()")
        report.append("2. 确保QTimer在主线程中使用")
        report.append("3. QPainter使用with语句管理")
        report.append("4. 避免在事件处理中阻塞")
        report.append("5. 实施绘制状态防护机制")
        
        return "\n".join(report)
    
    def run_check(self) -> None:
        """运行完整检查"""
        print("🔍 开始Qt安全检查...")
        issues = self.scan_project()
        
        report = self.generate_report(issues)
        print(report)
        
        # 保存报告到文件
        report_file = self.project_root / "qt_safety_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 返回退出码
        high_issues = sum(1 for issue in issues if issue["severity"] == "HIGH")
        if high_issues > 0:
            print(f"\n⚠️ 发现 {high_issues} 个高风险问题，建议立即修复！")
            return 1
        else:
            print("\n✅ 未发现高风险Qt安全问题")
            return 0

def main():
    """主函数"""
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    checker = QtSafetyChecker(project_root)
    exit_code = checker.run_check()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()