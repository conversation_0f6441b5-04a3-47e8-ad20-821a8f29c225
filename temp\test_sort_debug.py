#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序功能深度调试脚本

目标：直接验证排序功能链路的每个环节
1. 表头点击事件是否触发
2. 排序请求事件是否发布
3. 排序数据是否正确查询
4. UI是否正确更新
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import pandas as pd
from pathlib import Path
from src.utils.log_config import setup_logger

def test_database_sort_query():
    """测试数据库层面的排序查询"""
    print("\n🔧 [测试1] 数据库排序查询测试")
    print("="*50)
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 创建数据库管理器
        db_path = "data/db/salary_system.db"
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
            
        table_manager = DynamicTableManager(db_path)
        
        # 获取表列表 - 修复方法名
        if hasattr(table_manager, 'get_table_list'):
            table_info_list = table_manager.get_table_list()
            tables = [info['table_name'] for info in table_info_list]
        elif hasattr(table_manager, 'list_tables'):
            tables = table_manager.list_tables()
        else:
            print("❌ 无法获取表列表方法")
            return False
            
        print(f"📋 数据库中的表: {tables[:3]}...")  # 只显示前3个
        
        if not tables:
            print("❌ 数据库中没有表")
            return False
            
        # 选择第一个表进行测试
        test_table = tables[0]
        print(f"🎯 测试表: {test_table}")
        
        # 获取表结构
        columns = table_manager.get_table_columns(test_table)
        print(f"📝 表字段: {columns[:5]}...")  # 只显示前5个字段
        
        if not columns:
            print("❌ 无法获取表字段")
            return False
            
        # 测试普通查询
        print("\n🔍 测试普通查询（前3条）:")
        df_normal, total = table_manager.get_dataframe_paginated(test_table, 1, 3)
        if df_normal is not None and not df_normal.empty:
            print("✅ 普通查询成功")
            print(f"   总记录数: {total}")
            print(f"   返回数据: {len(df_normal)}行")
            if 'employee_id' in df_normal.columns:
                print(f"   工号示例: {df_normal['employee_id'].tolist()}")
        else:
            print("❌ 普通查询失败")
            return False
            
        # 测试排序查询
        print("\n🔄 测试排序查询（按第一个字段排序）:")
        sort_field = columns[0]  # 使用第一个字段排序
        sort_columns = [{
            'column_name': sort_field,
            'order': 'ascending'
        }]
        
        if hasattr(table_manager, 'get_dataframe_paginated_with_sort'):
            df_sorted, total_sorted = table_manager.get_dataframe_paginated_with_sort(
                test_table, 1, 3, sort_columns
            )
            
            if df_sorted is not None and not df_sorted.empty:
                print("✅ 排序查询成功")
                print(f"   总记录数: {total_sorted}")
                print(f"   返回数据: {len(df_sorted)}行")
                if sort_field in df_sorted.columns:
                    sorted_values = df_sorted[sort_field].tolist()
                    print(f"   排序字段值: {sorted_values}")
                    
                    # 验证是否真的排序了
                    if len(sorted_values) > 1:
                        is_sorted = all(sorted_values[i] <= sorted_values[i+1] for i in range(len(sorted_values)-1))
                        print(f"   排序验证: {'✅ 正确排序' if is_sorted else '❌ 排序错误'}")
                        return is_sorted
                    else:
                        print("   ⚠️ 数据量太少，无法验证排序")
                        return True
            else:
                print("❌ 排序查询返回空数据")
                return False
        else:
            print("❌ 不支持带排序的分页查询方法")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试异常: {e}")
        return False

def test_event_system():
    """测试事件系统"""
    print("\n🔧 [测试2] 事件系统测试")
    print("="*50)
    
    try:
        from src.core.event_bus import EventBus, SortRequestEvent
        
        # 创建事件总线
        event_bus = EventBus()
        
        # 记录接收到的事件
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
            print(f"📨 接收到事件: {type(event).__name__}")
            print(f"   表名: {getattr(event, 'table_name', 'N/A')}")
            print(f"   排序列: {getattr(event, 'sort_columns', 'N/A')}")
        
        # 订阅排序请求事件
        event_bus.subscribe("sort_request", event_handler)
        
        # 发布测试排序事件
        test_event = SortRequestEvent(
            event_type="sort_request",
            table_name="test_table",
            sort_columns=[{
                'column_name': 'test_column',
                'order': 'ascending'
            }],
            current_page=1
        )
        
        print("📤 发布测试排序事件...")
        event_bus.publish(test_event)
        
        # 短暂等待事件处理
        time.sleep(0.1)
        
        if received_events:
            print("✅ 事件系统正常工作")
            print(f"   接收到 {len(received_events)} 个事件")
            return True
        else:
            print("❌ 事件系统失效：未接收到任何事件")
            return False
            
    except Exception as e:
        print(f"❌ 事件系统测试异常: {e}")
        return False

def test_table_data_service():
    """测试TableDataService"""
    print("\n🔧 [测试3] TableDataService排序功能测试")
    print("="*50)
    
    try:
        from src.services.table_data_service import TableDataService
        from src.core.event_bus import EventBus
        from src.core.unified_state_manager import UnifiedStateManager
        from src.core.unified_data_request_manager import UnifiedDataRequestManager
        
        # 创建服务组件
        event_bus = EventBus()
        state_manager = UnifiedStateManager()
        
        # 创建必要的依赖
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        
        db_path = "data/db/salary_system.db"
        db_manager = DynamicTableManager(db_path)
        config_manager = ConfigManager("config.json")
        
        request_manager = UnifiedDataRequestManager(db_manager, config_manager)
        
        # 创建TableDataService
        table_service = TableDataService(
            event_bus=event_bus,
            state_manager=state_manager,
            data_request_manager=request_manager
        )
        
        print("✅ TableDataService创建成功")
        
        # 测试数据加载
        test_table = "salary_data_2025_07_active_employees"  # 常见的表名
        
        print(f"🔍 测试加载表: {test_table}")
        
        # 测试不带排序的加载
        response = table_service.load_table_data(test_table, page=1, page_size=5)
        
        if response and response.success:
            print("✅ 普通数据加载成功")
            print(f"   数据量: {len(response.data)}行")
            print(f"   总记录数: {response.total_records}")
        else:
            print(f"❌ 普通数据加载失败: {getattr(response, 'error', 'Unknown error')}")
            return False
        
        # 测试带排序的加载
        sort_columns = [{
            'column_name': 'employee_id',
            'order': 'descending'
        }]
        
        print(f"🔄 测试排序加载: {sort_columns}")
        response_sorted = table_service.load_table_data(
            test_table, 
            page=1, 
            page_size=5,
            sort_columns=sort_columns
        )
        
        if response_sorted and response_sorted.success:
            print("✅ 排序数据加载成功")
            print(f"   数据量: {len(response_sorted.data)}行")
            
            # 比较排序前后的数据
            if hasattr(response.data, 'iloc') and hasattr(response_sorted.data, 'iloc'):
                normal_first = response.data.iloc[0] if len(response.data) > 0 else None
                sorted_first = response_sorted.data.iloc[0] if len(response_sorted.data) > 0 else None
                
                if normal_first is not None and sorted_first is not None:
                    normal_id = normal_first.get('employee_id', 'N/A')
                    sorted_id = sorted_first.get('employee_id', 'N/A')
                    print(f"   普通查询第一个工号: {normal_id}")
                    print(f"   排序查询第一个工号: {sorted_id}")
                    
                    if normal_id != sorted_id:
                        print("✅ 排序确实产生了不同的结果")
                        return True
                    else:
                        print("⚠️ 排序前后结果相同（可能数据量小或已经有序）")
                        return True
            else:
                print("⚠️ 无法比较排序结果（数据格式问题）")
                return True
        else:
            print(f"❌ 排序数据加载失败: {getattr(response_sorted, 'error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ TableDataService测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 排序功能深度调试测试")
    print("="*60)
    
    tests = [
        ("数据库排序查询", test_database_sort_query),
        ("事件系统", test_event_system),
        ("TableDataService", test_table_data_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n💥 {test_name} 测试异常: {e}")
    
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有底层组件都正常工作！")
        print("💡 如果UI没有排序效果，问题可能在UI层面的事件连接或数据更新逻辑")
    else:
        print("⚠️ 存在底层问题，需要先修复这些组件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 