"""
统一数据管理器 - 根本解决分页排序问题
"""
from typing import Dict, List, Any, Tuple, Optional
import pandas as pd
from dataclasses import dataclass
from loguru import logger

@dataclass
class DataQuery:
    """统一的数据查询参数"""
    table_name: str
    page: int = 1
    page_size: int = 50
    sort_columns: List[Dict[str, Any]] = None
    filters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.sort_columns is None:
            self.sort_columns = []
        if self.filters is None:
            self.filters = {}

@dataclass
class DataResult:
    """统一的数据查询结果"""
    data: pd.DataFrame
    total_count: int
    page: int
    page_size: int
    sort_columns: List[Dict[str, Any]]
    field_mappings: Dict[str, str]  # 英文->中文映射
    
    @property
    def total_pages(self) -> int:
        return (self.total_count + self.page_size - 1) // self.page_size

class UnifiedDataManager:
    """
    统一数据管理器 - 根本解决分页排序问题
    
    核心原则：
    1. 分页和排序必须在数据库层统一处理
    2. 字段映射在数据传输层统一处理
    3. 状态管理简化到最小
    """
    
    def __init__(self, db_manager, config_manager):
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.logger = logger
        
    def query_data(self, query: DataQuery) -> DataResult:
        """
        统一的数据查询接口
        
        Args:
            query: 查询参数
            
        Returns:
            DataResult: 统一的查询结果
        """
        try:
            # 1. 构建SQL查询（统一处理分页+排序）
            sql_query = self._build_unified_query(query)
            
            # 2. 执行查询
            data = self.db_manager.execute_query(sql_query)
            total_count = self._get_total_count(query.table_name, query.filters)
            
            # 3. 转换为DataFrame（需要包含列名）
            if data:
                # 获取表的列名
                column_names = self._get_table_column_names(query.table_name)
                df = pd.DataFrame(data, columns=column_names)
            else:
                df = pd.DataFrame()
            
            # 4. 获取字段映射
            field_mappings = self._get_field_mappings(query.table_name)
            
            # 5. 应用字段映射（统一处理）
            df_mapped = self._apply_field_mapping(df, field_mappings)
            
            # 6. 返回统一结果
            return DataResult(
                data=df_mapped,
                total_count=total_count,
                page=query.page,
                page_size=query.page_size,
                sort_columns=query.sort_columns,
                field_mappings=field_mappings
            )
            
        except Exception as e:
            self.logger.error(f"统一数据查询失败: {e}")
            return DataResult(
                data=pd.DataFrame(),
                total_count=0,
                page=query.page,
                page_size=query.page_size,
                sort_columns=query.sort_columns,
                field_mappings={}
            )
    
    def _build_unified_query(self, query: DataQuery) -> str:
        """构建统一的SQL查询（分页+排序）"""
        # 基础查询
        sql = f'SELECT * FROM "{query.table_name}"'
        
        # 添加过滤条件
        if query.filters:
            where_clauses = []
            for column, value in query.filters.items():
                where_clauses.append(f'"{column}" = ?')
            sql += f' WHERE {" AND ".join(where_clauses)}'
        
        # 添加排序（关键：统一处理）
        if query.sort_columns:
            order_clauses = []
            for sort_col in query.sort_columns:
                column_name = sort_col.get('column_name', '')
                order = sort_col.get('order', 'ascending')
                
                if column_name:
                    # 确保列名安全
                    safe_column = column_name.replace('"', '""')
                    direction = 'ASC' if order == 'ascending' else 'DESC'
                    
                    # 数值列特殊处理
                    if self._is_numeric_column(query.table_name, column_name):
                        order_clauses.append(f'CAST("{safe_column}" AS REAL) {direction}')
                    else:
                        order_clauses.append(f'"{safe_column}" {direction}')
            
            if order_clauses:
                sql += f' ORDER BY {", ".join(order_clauses)}'
        
        # 添加分页
        offset = (query.page - 1) * query.page_size
        sql += f' LIMIT {query.page_size} OFFSET {offset}'
        
        self.logger.info(f"统一查询SQL: {sql}")
        return sql
    
    def _get_total_count(self, table_name: str, filters: Dict[str, Any]) -> int:
        """获取总记录数"""
        sql = f'SELECT COUNT(*) FROM "{table_name}"'
        
        if filters:
            where_clauses = []
            for column, value in filters.items():
                where_clauses.append(f'"{column}" = ?')
            sql += f' WHERE {" AND ".join(where_clauses)}'
        
        result = self.db_manager.execute_query(sql)
        return result[0][0] if result else 0
    
    def _get_field_mappings(self, table_name: str) -> Dict[str, str]:
        """获取字段映射（英文->中文）"""
        try:
            field_mappings = self.config_manager.get_field_mappings()
            return field_mappings.get(table_name, {})
        except Exception as e:
            self.logger.error(f"获取字段映射失败: {e}")
            return {}
    
    def _apply_field_mapping(self, df: pd.DataFrame, mappings: Dict[str, str]) -> pd.DataFrame:
        """应用字段映射（统一处理中英文转换）"""
        if df.empty or not mappings:
            return df
        
        # 只映射存在的列
        rename_dict = {eng: chn for eng, chn in mappings.items() if eng in df.columns}
        
        if rename_dict:
            df = df.rename(columns=rename_dict)
            self.logger.info(f"已应用字段映射: {len(rename_dict)} 个字段")
        
        return df
    
    def _get_table_column_names(self, table_name: str) -> List[str]:
        """获取表的列名"""
        try:
            query = f'PRAGMA table_info("{table_name}")'
            schema_info = self.db_manager.execute_query(query)
            
            # schema_info格式：(cid, name, type, notnull, dflt_value, pk)
            column_names = [col[1] for col in schema_info]
            return column_names
        except Exception as e:
            self.logger.error(f"获取表列名失败: {e}")
            return []
    
    def _is_numeric_column(self, table_name: str, column_name: str) -> bool:
        """判断是否为数值列"""
        try:
            # 简化的数值类型判断
            query = f'PRAGMA table_info("{table_name}")'
            schema_info = self.db_manager.execute_query(query)
            
            for column_info in schema_info:
                if column_info[1] == column_name:
                    column_type = column_info[2].upper()
                    return column_type in ['INTEGER', 'REAL', 'NUMERIC', 'DECIMAL']
            
            return False
        except Exception:
            return False