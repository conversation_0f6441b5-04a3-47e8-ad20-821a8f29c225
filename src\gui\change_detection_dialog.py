"""
异动检测对话框

提供独立的异动检测界面，包括：
- 基准期间选择
- 异动检测配置
- 检测结果显示
- 异动详情分析
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QPushButton, QLabel, QComboBox, QCheckBox, QSpinBox, QDateEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QTextEdit,
    QTabWidget, QWidget, QProgressBar, QMessageBox, QSplitter,
    QTreeWidget, QTreeWidgetItem, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QThread, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime, date
from typing import Dict, List, Any, Optional
import logging

from src.utils.log_config import setup_logger


class ChangeDetectionWorker(QThread):
    """异动检测工作线程"""
    
    progress_updated = pyqtSignal(int, str)
    detection_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, current_period: str, baseline_period: str, detection_config: Dict[str, Any]):
        super().__init__()
        self.current_period = current_period
        self.baseline_period = baseline_period
        self.detection_config = detection_config
        self.logger = setup_logger(__name__)
    
    def run(self):
        """执行异动检测"""
        try:
            self.progress_updated.emit(10, "正在加载当前数据...")
            
            # 模拟异动检测过程
            import time
            time.sleep(0.5)
            
            self.progress_updated.emit(30, "正在加载基准数据...")
            time.sleep(0.5)
            
            self.progress_updated.emit(50, "正在执行异动检测...")
            time.sleep(1.0)
            
            self.progress_updated.emit(80, "正在分析检测结果...")
            time.sleep(0.5)
            
            # 模拟检测结果
            detection_result = {
                'detection_summary': {
                    'total_changes': 15,
                    'new_employees': 3,
                    'resignations': 2,
                    'salary_adjustments': 8,
                    'allowance_changes': 2,
                    'total_increase': 12500.00,
                    'total_decrease': -3200.00,
                    'net_change': 9300.00
                },
                'change_details': [
                    {
                        'employee_id': '001',
                        'name': '张三',
                        'department': '技术部',
                        'change_type': '工资调整',
                        'description': '基本工资调整: 5000.00 → 5500.00',
                        'change_amount': 500.00,
                        'confidence': 0.95,
                        'effective_date': self.current_period
                    },
                    {
                        'employee_id': '002',
                        'name': '李四',
                        'department': '财务部',
                        'change_type': '津贴变动',
                        'description': '交通津贴调整: 300.00 → 400.00',
                        'change_amount': 100.00,
                        'confidence': 0.88,
                        'effective_date': self.current_period
                    },
                    {
                        'employee_id': '999',
                        'name': '新员工',
                        'department': '市场部',
                        'change_type': '新进人员',
                        'description': '新进人员入职',
                        'change_amount': 4500.00,
                        'confidence': 1.00,
                        'effective_date': self.current_period
                    }
                ],
                'detection_config': self.detection_config,
                'current_period': self.current_period,
                'baseline_period': self.baseline_period,
                'detection_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.progress_updated.emit(100, "检测完成")
            self.detection_completed.emit(detection_result)
            
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            self.error_occurred.emit(f"异动检测失败: {e}")


class ChangeDetectionDialog(QDialog):
    """异动检测对话框"""
    
    detection_completed = pyqtSignal(dict)  # 检测完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.detection_result = None
        self.worker_thread = None
        self._init_ui()
        self._load_available_periods()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("工资异动检测")
        self.setMinimumSize(900, 700)
        self.resize(1200, 800)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：检测配置面板
        self._create_config_panel(splitter)
        
        # 右侧：结果显示面板
        self._create_result_panel(splitter)
        
        # 设置分割比例
        splitter.setSizes([350, 850])
        main_layout.addWidget(splitter)
        
        # 底部按钮区域
        self._create_button_area(main_layout)
        
        # 设置样式
        self._set_dialog_style()
        
    def _create_config_panel(self, parent):
        """创建检测配置面板"""
        config_widget = QWidget()
        config_widget.setMaximumWidth(400)
        config_layout = QVBoxLayout(config_widget)
        
        # 面板标题
        title_label = QLabel("异动检测配置")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        config_layout.addWidget(title_label)
        
        # 期间选择组
        self._create_period_selection_group(config_layout)
        
        # 检测选项组
        self._create_detection_options_group(config_layout)
        
        # 阈值设置组
        self._create_threshold_settings_group(config_layout)
        
        # 进度显示
        self._create_progress_display(config_layout)
        
        config_layout.addStretch()
        parent.addWidget(config_widget)
        
    def _create_period_selection_group(self, parent_layout):
        """创建期间选择组"""
        period_group = QGroupBox("检测期间")
        period_layout = QFormLayout(period_group)
        
        # 当前数据期间
        self.current_period_combo = QComboBox()
        self.current_period_combo.setToolTip("选择要检测的当前数据期间")
        period_layout.addRow("当前数据:", self.current_period_combo)
        
        # 基准数据期间
        self.baseline_period_combo = QComboBox()
        self.baseline_period_combo.setToolTip("选择作为对比基准的历史数据期间")
        period_layout.addRow("基准数据:", self.baseline_period_combo)
        
        # 期间说明
        period_info = QLabel("基准数据通常选择上一个月或上一期间的数据")
        period_info.setStyleSheet("color: #666; font-size: 11px;")
        period_info.setWordWrap(True)
        period_layout.addRow("", period_info)
        
        parent_layout.addWidget(period_group)
        
    def _create_detection_options_group(self, parent_layout):
        """创建检测选项组"""
        options_group = QGroupBox("检测选项")
        options_layout = QVBoxLayout(options_group)
        
        # 异动类型选择
        type_label = QLabel("检测异动类型:")
        type_label.setFont(QFont("", 9, QFont.Bold))
        options_layout.addWidget(type_label)
        
        self.detect_new_employees = QCheckBox("新进人员")
        self.detect_new_employees.setChecked(True)
        options_layout.addWidget(self.detect_new_employees)
        
        self.detect_resignations = QCheckBox("离职人员")
        self.detect_resignations.setChecked(True)
        options_layout.addWidget(self.detect_resignations)
        
        self.detect_salary_changes = QCheckBox("工资调整")
        self.detect_salary_changes.setChecked(True)
        options_layout.addWidget(self.detect_salary_changes)
        
        self.detect_allowance_changes = QCheckBox("津贴变动")
        self.detect_allowance_changes.setChecked(True)
        options_layout.addWidget(self.detect_allowance_changes)
        
        self.detect_deductions = QCheckBox("扣款异动")
        self.detect_deductions.setChecked(False)
        options_layout.addWidget(self.detect_deductions)
        
        self.detect_position_changes = QCheckBox("职务变动")
        self.detect_position_changes.setChecked(False)
        options_layout.addWidget(self.detect_position_changes)
        
        parent_layout.addWidget(options_group)
        
    def _create_threshold_settings_group(self, parent_layout):
        """创建阈值设置组"""
        threshold_group = QGroupBox("检测阈值")
        threshold_layout = QFormLayout(threshold_group)
        
        # 工资变动阈值
        self.salary_threshold_spin = QSpinBox()
        self.salary_threshold_spin.setRange(1, 10000)
        self.salary_threshold_spin.setValue(50)
        self.salary_threshold_spin.setSuffix(" 元")
        threshold_layout.addRow("工资变动阈值:", self.salary_threshold_spin)
        
        # 津贴变动阈值
        self.allowance_threshold_spin = QSpinBox()
        self.allowance_threshold_spin.setRange(1, 5000)
        self.allowance_threshold_spin.setValue(20)
        self.allowance_threshold_spin.setSuffix(" 元")
        threshold_layout.addRow("津贴变动阈值:", self.allowance_threshold_spin)
        
        # 置信度阈值
        self.confidence_threshold_spin = QSpinBox()
        self.confidence_threshold_spin.setRange(50, 100)
        self.confidence_threshold_spin.setValue(70)
        self.confidence_threshold_spin.setSuffix(" %")
        threshold_layout.addRow("置信度阈值:", self.confidence_threshold_spin)
        
        parent_layout.addWidget(threshold_group)
        
    def _create_progress_display(self, parent_layout):
        """创建进度显示"""
        progress_group = QGroupBox("检测进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("就绪")
        self.progress_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.progress_label)
        
        parent_layout.addWidget(progress_group)
        
    def _create_result_panel(self, parent):
        """创建结果显示面板"""
        self.result_tabs = QTabWidget()
        
        # 检测摘要选项卡
        self._create_summary_tab()
        
        # 异动详情选项卡
        self._create_details_tab()
        
        # 统计分析选项卡
        self._create_analysis_tab()
        
        parent.addWidget(self.result_tabs)
        
    def _create_summary_tab(self):
        """创建检测摘要选项卡"""
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        
        # 摘要信息显示
        self.summary_display = QTextEdit()
        self.summary_display.setReadOnly(True)
        self.summary_display.setMaximumHeight(200)
        summary_layout.addWidget(self.summary_display)
        
        # 异动概览表格
        self.summary_table = QTableWidget()
        self.summary_table.setColumnCount(4)
        self.summary_table.setHorizontalHeaderLabels(["异动类型", "人数", "变动金额", "占比"])
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        summary_layout.addWidget(self.summary_table)
        
        self.result_tabs.addTab(summary_widget, "检测摘要")
        
    def _create_details_tab(self):
        """创建异动详情选项卡"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        # 详情表格
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(7)
        self.details_table.setHorizontalHeaderLabels([
            "工号", "姓名", "部门", "异动类型", "变动描述", "变动金额", "置信度"
        ])
        self.details_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.details_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        
        details_layout.addWidget(self.details_table)
        
        self.result_tabs.addTab(details_widget, "异动详情")
        
    def _create_analysis_tab(self):
        """创建统计分析选项卡"""
        analysis_widget = QWidget()
        analysis_layout = QVBoxLayout(analysis_widget)
        
        # 分析结果显示
        self.analysis_display = QTextEdit()
        self.analysis_display.setReadOnly(True)
        analysis_layout.addWidget(self.analysis_display)
        
        self.result_tabs.addTab(analysis_widget, "统计分析")
        
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 开始检测按钮
        self.start_detection_btn = QPushButton("开始检测")
        self.start_detection_btn.setDefault(True)
        self.start_detection_btn.clicked.connect(self._start_detection)
        
        # 停止检测按钮
        self.stop_detection_btn = QPushButton("停止检测")
        self.stop_detection_btn.setEnabled(False)
        self.stop_detection_btn.clicked.connect(self._stop_detection)
        
        # 导出结果按钮
        self.export_result_btn = QPushButton("导出结果")
        self.export_result_btn.setEnabled(False)
        self.export_result_btn.clicked.connect(self._export_result)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.start_detection_btn)
        button_layout.addWidget(self.stop_detection_btn)
        button_layout.addWidget(self.export_result_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        parent_layout.addLayout(button_layout)
        
    def _set_dialog_style(self):
        """设置对话框样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QTableWidget {
                gridline-color: #e0e0e0;
                selection-background-color: #e3f2fd;
            }
        """)
        
    def _load_available_periods(self):
        """加载可用的数据期间"""
        try:
            # 模拟从数据库加载可用期间
            available_periods = [
                "2024-06", "2024-05", "2024-04", "2024-03", "2024-02", "2024-01"
            ]
            
            # 填充下拉框
            self.current_period_combo.clear()
            self.baseline_period_combo.clear()
            
            self.current_period_combo.addItems(available_periods)
            self.baseline_period_combo.addItems(available_periods)
            
            # 设置默认选择
            if len(available_periods) >= 2:
                self.current_period_combo.setCurrentIndex(0)  # 最新期间
                self.baseline_period_combo.setCurrentIndex(1)  # 上一期间
                
            self.logger.info(f"已加载 {len(available_periods)} 个可用期间")
            
        except Exception as e:
            self.logger.error(f"加载可用期间失败: {e}")
            QMessageBox.warning(self, "警告", f"加载可用期间失败: {e}")
            
    def _start_detection(self):
        """开始异动检测"""
        try:
            # 验证选择
            current_period = self.current_period_combo.currentText()
            baseline_period = self.baseline_period_combo.currentText()
            
            if not current_period or not baseline_period:
                QMessageBox.warning(self, "警告", "请选择当前数据和基准数据期间")
                return
                
            if current_period == baseline_period:
                QMessageBox.warning(self, "警告", "当前数据和基准数据不能是同一期间")
                return
            
            # 构建检测配置
            detection_config = {
                'detect_new_employees': self.detect_new_employees.isChecked(),
                'detect_resignations': self.detect_resignations.isChecked(),
                'detect_salary_changes': self.detect_salary_changes.isChecked(),
                'detect_allowance_changes': self.detect_allowance_changes.isChecked(),
                'detect_deductions': self.detect_deductions.isChecked(),
                'detect_position_changes': self.detect_position_changes.isChecked(),
                'salary_threshold': self.salary_threshold_spin.value(),
                'allowance_threshold': self.allowance_threshold_spin.value(),
                'confidence_threshold': self.confidence_threshold_spin.value() / 100.0
            }
            
            # 更新UI状态
            self.start_detection_btn.setEnabled(False)
            self.stop_detection_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText("正在初始化...")
            
            # 启动检测线程
            self.worker_thread = ChangeDetectionWorker(
                current_period, baseline_period, detection_config
            )
            self.worker_thread.progress_updated.connect(self._on_progress_updated)
            self.worker_thread.detection_completed.connect(self._on_detection_completed)
            self.worker_thread.error_occurred.connect(self._on_detection_error)
            self.worker_thread.start()
            
            self.logger.info(f"开始异动检测: {baseline_period} -> {current_period}")
            
        except Exception as e:
            self.logger.error(f"启动异动检测失败: {e}")
            QMessageBox.critical(self, "错误", f"启动异动检测失败: {e}")
            self._reset_ui_state()
            
    def _stop_detection(self):
        """停止异动检测"""
        try:
            if self.worker_thread and self.worker_thread.isRunning():
                self.worker_thread.terminate()
                self.worker_thread.wait(3000)  # 等待3秒
                
            self._reset_ui_state()
            self.progress_label.setText("检测已停止")
            
        except Exception as e:
            self.logger.error(f"停止检测失败: {e}")
            
    def _on_progress_updated(self, value: int, message: str):
        """处理进度更新"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        
    def _on_detection_completed(self, result: Dict[str, Any]):
        """处理检测完成"""
        try:
            self.detection_result = result
            
            # 更新结果显示
            self._update_summary_display(result)
            self._update_details_display(result)
            self._update_analysis_display(result)
            
            # 切换到结果显示
            self.result_tabs.setCurrentIndex(0)
            
            # 重置UI状态
            self._reset_ui_state()
            self.export_result_btn.setEnabled(True)
            self.progress_label.setText("检测完成")
            
            # 发射完成信号
            self.detection_completed.emit(result)
            
            # 显示完成消息
            total_changes = result['detection_summary']['total_changes']
            QMessageBox.information(
                self, "检测完成", 
                f"异动检测完成！\n共发现 {total_changes} 项异动\n"
                f"检测期间: {result['baseline_period']} → {result['current_period']}"
            )
            
        except Exception as e:
            self.logger.error(f"处理检测结果失败: {e}")
            QMessageBox.critical(self, "错误", f"处理检测结果失败: {e}")
            
    def _on_detection_error(self, error_message: str):
        """处理检测错误"""
        QMessageBox.critical(self, "检测失败", error_message)
        self._reset_ui_state()
        self.progress_label.setText("检测失败")
        
    def _reset_ui_state(self):
        """重置UI状态"""
        self.start_detection_btn.setEnabled(True)
        self.stop_detection_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
    def _update_summary_display(self, result: Dict[str, Any]):
        """更新摘要显示"""
        try:
            summary = result['detection_summary']
            
            # 更新摘要文本
            summary_text = f"""
检测摘要报告

检测期间: {result['baseline_period']} → {result['current_period']}
检测时间: {result['detection_time']}

总体统计:
• 检测到异动总数: {summary['total_changes']} 项
• 工资总增长: {summary['total_increase']:,.2f} 元
• 工资总减少: {summary['total_decrease']:,.2f} 元
• 净变动金额: {summary['net_change']:,.2f} 元

分类统计:
• 新进人员: {summary['new_employees']} 人
• 离职人员: {summary['resignations']} 人  
• 工资调整: {summary['salary_adjustments']} 人
• 津贴变动: {summary['allowance_changes']} 人
            """.strip()
            
            self.summary_display.setPlainText(summary_text)
            
            # 更新摘要表格
            summary_data = [
                ("新进人员", summary['new_employees'], 0, ""),
                ("离职人员", summary['resignations'], 0, ""),
                ("工资调整", summary['salary_adjustments'], 0, ""),
                ("津贴变动", summary['allowance_changes'], 0, "")
            ]
            
            self.summary_table.setRowCount(len(summary_data))
            for row, (type_name, count, amount, percentage) in enumerate(summary_data):
                self.summary_table.setItem(row, 0, QTableWidgetItem(type_name))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(count)))
                self.summary_table.setItem(row, 2, QTableWidgetItem(f"{amount:,.2f}"))
                self.summary_table.setItem(row, 3, QTableWidgetItem(percentage))
                
        except Exception as e:
            self.logger.error(f"更新摘要显示失败: {e}")
            
    def _update_details_display(self, result: Dict[str, Any]):
        """更新详情显示"""
        try:
            details = result['change_details']
            
            self.details_table.setRowCount(len(details))
            for row, detail in enumerate(details):
                self.details_table.setItem(row, 0, QTableWidgetItem(detail['employee_id']))
                self.details_table.setItem(row, 1, QTableWidgetItem(detail['name']))
                self.details_table.setItem(row, 2, QTableWidgetItem(detail['department']))
                self.details_table.setItem(row, 3, QTableWidgetItem(detail['change_type']))
                self.details_table.setItem(row, 4, QTableWidgetItem(detail['description']))
                self.details_table.setItem(row, 5, QTableWidgetItem(f"{detail['change_amount']:,.2f}"))
                self.details_table.setItem(row, 6, QTableWidgetItem(f"{detail['confidence']:.1%}"))
                
                # 根据变动金额设置颜色
                amount_item = self.details_table.item(row, 5)
                if detail['change_amount'] > 0:
                    amount_item.setForeground(QColor("#4CAF50"))  # 绿色表示增加
                elif detail['change_amount'] < 0:
                    amount_item.setForeground(QColor("#F44336"))  # 红色表示减少
                    
        except Exception as e:
            self.logger.error(f"更新详情显示失败: {e}")
            
    def _update_analysis_display(self, result: Dict[str, Any]):
        """更新分析显示"""
        try:
            analysis_text = f"""
深度分析报告

一、检测配置分析
检测期间: {result['baseline_period']} → {result['current_period']}
工资变动阈值: {result['detection_config']['salary_threshold']} 元
津贴变动阈值: {result['detection_config']['allowance_threshold']} 元
置信度阈值: {result['detection_config']['confidence_threshold']:.1%}

二、异动趋势分析
本次检测共发现 {result['detection_summary']['total_changes']} 项异动
相比上期，工资总体呈现{'上升' if result['detection_summary']['net_change'] > 0 else '下降'}趋势

三、风险提示
• 请重点关注置信度较低的异动项
• 建议对大额变动进行人工复核
• 新进和离职人员需确认人事档案

四、建议措施
• 及时更新员工档案信息
• 建立异动变化追踪机制
• 定期进行数据质量检查
            """.strip()
            
            self.analysis_display.setPlainText(analysis_text)
            
        except Exception as e:
            self.logger.error(f"更新分析显示失败: {e}")
            
    def _export_result(self):
        """导出检测结果"""
        try:
            if not self.detection_result:
                QMessageBox.warning(self, "警告", "没有可导出的检测结果")
                return
                
            from PyQt5.QtWidgets import QFileDialog
            
            # 生成默认文件名
            current_period = self.detection_result['current_period']
            baseline_period = self.detection_result['baseline_period']
            default_filename = f"异动检测结果_{baseline_period}_{current_period}.xlsx"
            
            # 选择保存位置
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出检测结果", default_filename,
                "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
            )
            
            if filename:
                # 这里实现具体的导出逻辑
                QMessageBox.information(self, "导出完成", f"检测结果已导出到:\n{filename}")
                
        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")
            QMessageBox.critical(self, "错误", f"导出结果失败: {e}")
            
    def get_detection_result(self) -> Optional[Dict[str, Any]]:
        """获取检测结果"""
        return self.detection_result 