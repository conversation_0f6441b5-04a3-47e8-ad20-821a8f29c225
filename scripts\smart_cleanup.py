#!/usr/bin/env python3
"""
智能配置清理脚本
保留核心数据，重建可能污染的配置
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

def backup_current_state():
    """备份当前状态"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup/cleanup_backup_{timestamp}"
    
    print(f"📦 创建备份: {backup_dir}")
    Path(backup_dir).mkdir(parents=True, exist_ok=True)
    
    # 备份关键文件
    files_to_backup = [
        "state/data/field_mappings.json",
        "state/unified_state.json", 
        "state/user/navigation_state.json",
        "data/db/salary_system.db"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, file_path)
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            shutil.copy2(file_path, backup_path)
            print(f"  ✅ 已备份: {file_path}")
    
    return backup_dir

def clean_state_configs():
    """清理状态配置（保留字段映射）"""
    print("🧹 清理状态配置...")
    
    # 只删除可能污染的状态文件
    files_to_clean = [
        "state/unified_state.json",
        "state/user/navigation_state.json",
        "state/data/navigation_config.json"
    ]
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"  🗑️  已删除: {file_path}")

def reset_field_mappings():
    """重置字段映射为干净状态"""
    print("🔄 重置字段映射...")
    
    clean_mapping = {
        "version": "2.0",
        "last_updated": datetime.now().isoformat(),
        "global_settings": {
            "auto_generate_mappings": True,
            "enable_smart_suggestions": True,
            "save_edit_history": True,
            "preserve_chinese_headers": True
        },
        "table_mappings": {},  # 清空，让系统重新生成
        "field_templates": {},
        "user_preferences": {
            "default_field_patterns": {},
            "recent_edits": [],
            "favorite_mappings": []
        }
    }
    
    os.makedirs("state/data", exist_ok=True)
    with open("state/data/field_mappings.json", "w", encoding="utf-8") as f:
        json.dump(clean_mapping, f, ensure_ascii=False, indent=2)
    
    print("  ✅ 字段映射已重置为干净状态")

def main():
    """主清理流程"""
    print("🚀 开始智能配置清理...")
    
    # 1. 备份
    backup_dir = backup_current_state()
    
    # 2. 清理状态配置
    clean_state_configs()
    
    # 3. 重置字段映射
    reset_field_mappings()
    
    print(f"""
✅ 清理完成！

📋 清理总结:
  - 数据库文件: 保留 (1473条工资记录)
  - 字段映射: 重置为干净状态
  - 系统状态: 清空重建
  - 导航配置: 清空重建
  - 备份位置: {backup_dir}

🔄 下次启动时:
  - 系统将使用干净的配置
  - 字段映射需要重新生成
  - 导航树将重新构建

⚠️  如果出现问题，可以从备份恢复:
  cp -r {backup_dir}/* ./
""")

if __name__ == "__main__":
    main()