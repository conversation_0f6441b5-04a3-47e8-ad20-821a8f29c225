#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分页策略测试
验证智能分页决策逻辑是否按预期工作
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.smart_pagination_strategy import SmartPaginationStrategy, get_smart_pagination_strategy


class TestSmartPaginationStrategy(unittest.TestCase):
    """智能分页策略测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.strategy = SmartPaginationStrategy()
    
    def test_empty_dataset(self):
        """测试空数据集"""
        decision = self.strategy.should_use_pagination(0)
        
        self.assertFalse(decision.use_pagination)
        self.assertEqual(decision.strategy, 'full_display')
        self.assertIn('空数据集', decision.reason)
        self.assertLessEqual(decision.estimated_performance_ms, 100)
    
    def test_very_small_dataset(self):
        """测试极小数据集 (≤50条)"""
        decision = self.strategy.should_use_pagination(30)
        
        self.assertFalse(decision.use_pagination)
        self.assertEqual(decision.strategy, 'full_display')
        self.assertIn('页面大小', decision.reason)
        self.assertLessEqual(decision.estimated_performance_ms, 300)
    
    def test_small_dataset_within_threshold(self):
        """测试小数据集 (≤75条)"""
        decision = self.strategy.should_use_pagination(65)
        
        self.assertFalse(decision.use_pagination)
        self.assertEqual(decision.strategy, 'full_display')
        self.assertIn('阈值', decision.reason)
        self.assertLessEqual(decision.estimated_performance_ms, 400)
    
    def test_medium_dataset_good_performance(self):
        """测试中等数据集，性能可接受"""
        decision = self.strategy.should_use_pagination(120)
        
        # 120条记录预估渲染时间应该在阈值内，所以应该选择全量显示
        if decision.estimated_performance_ms <= 500:
            self.assertFalse(decision.use_pagination)
            self.assertEqual(decision.strategy, 'full_display')
        else:
            self.assertTrue(decision.use_pagination)
            self.assertEqual(decision.strategy, 'pagination')
    
    def test_large_dataset(self):
        """测试大数据集 (>200条)"""
        decision = self.strategy.should_use_pagination(500)
        
        self.assertTrue(decision.use_pagination)
        self.assertEqual(decision.strategy, 'pagination')
        self.assertIn('分页显示', decision.reason)
        self.assertEqual(decision.recommended_page_size, 50)
    
    def test_force_pagination_preference(self):
        """测试强制分页偏好"""
        decision = self.strategy.should_use_pagination(30, user_preference='force_pagination')
        
        self.assertTrue(decision.use_pagination)
        self.assertEqual(decision.strategy, 'pagination')
        self.assertIn('强制', decision.reason)
    
    def test_custom_page_size(self):
        """测试自定义页面大小"""
        decision = self.strategy.should_use_pagination(80, page_size=100)
        
        # 80条记录，页面大小100，应该选择全量显示
        self.assertFalse(decision.use_pagination)
        self.assertEqual(decision.strategy, 'full_display')
    
    def test_performance_estimation(self):
        """测试性能估算逻辑"""
        # 小数据集性能估算
        small_decision = self.strategy.should_use_pagination(20)
        self.assertLessEqual(small_decision.estimated_performance_ms, 300)
        
        # 中等数据集性能估算
        medium_decision = self.strategy.should_use_pagination(100)
        # 不管是否分页，性能估算都应该合理
        self.assertLessEqual(medium_decision.estimated_performance_ms, 1000)
        
        # 大数据集分页性能估算
        large_decision = self.strategy.should_use_pagination(1000)
        self.assertTrue(large_decision.use_pagination)
        # 分页模式下应该是基于页面大小的估算
        self.assertLessEqual(large_decision.estimated_performance_ms, 1000)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        initial_stats = self.strategy.get_strategy_statistics()
        self.assertEqual(initial_stats['total_decisions'], 0)
        
        # 执行几次决策
        self.strategy.should_use_pagination(20)  # full_display
        self.strategy.should_use_pagination(30)  # full_display
        self.strategy.should_use_pagination(500) # pagination
        
        stats = self.strategy.get_strategy_statistics()
        self.assertEqual(stats['total_decisions'], 3)
        self.assertEqual(stats['full_display_count'], 2)
        self.assertEqual(stats['pagination_count'], 1)
        self.assertAlmostEqual(stats['full_display_percentage'], 66.67, places=1)
        self.assertAlmostEqual(stats['pagination_percentage'], 33.33, places=1)
    
    def test_threshold_configuration(self):
        """测试阈值配置"""
        # 配置新的阈值
        self.strategy.configure_thresholds(
            small_dataset_threshold=100,
            performance_threshold_ms=800
        )
        
        # 验证新阈值生效
        decision = self.strategy.should_use_pagination(90)
        self.assertFalse(decision.use_pagination)  # 90 < 100，应该全量显示
        
        decision = self.strategy.should_use_pagination(120)
        # 根据新的性能阈值判断
        if decision.estimated_performance_ms <= 800:
            self.assertFalse(decision.use_pagination)
        else:
            self.assertTrue(decision.use_pagination)
    
    def test_singleton_instance(self):
        """测试单例模式"""
        instance1 = get_smart_pagination_strategy()
        instance2 = get_smart_pagination_strategy()
        
        self.assertIs(instance1, instance2)
        self.assertIsInstance(instance1, SmartPaginationStrategy)


class TestSmartPaginationIntegration(unittest.TestCase):
    """智能分页策略集成测试"""
    
    @patch('src.core.smart_pagination_strategy.setup_logger')
    def test_logger_integration(self, mock_logger):
        """测试日志集成"""
        mock_logger.return_value = MagicMock()
        strategy = SmartPaginationStrategy()
        
        # 执行一些操作
        decision = strategy.should_use_pagination(50)
        
        # 验证日志调用
        self.assertTrue(mock_logger.called)
    
    def test_edge_cases(self):
        """测试边界情况"""
        strategy = SmartPaginationStrategy()
        
        # 负数记录数
        decision = strategy.should_use_pagination(-1)
        self.assertFalse(decision.use_pagination)
        
        # 极大记录数
        decision = strategy.should_use_pagination(1000000)
        self.assertTrue(decision.use_pagination)
        
        # 非常小的页面大小
        decision = strategy.should_use_pagination(10, page_size=5)
        self.assertTrue(decision.use_pagination)
        
        # 页面大小为0
        decision = strategy.should_use_pagination(10, page_size=0)
        # 应该使用默认页面大小
        self.assertIsNotNone(decision)


if __name__ == '__main__':
    # 配置测试日志
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    unittest.main(verbosity=2)