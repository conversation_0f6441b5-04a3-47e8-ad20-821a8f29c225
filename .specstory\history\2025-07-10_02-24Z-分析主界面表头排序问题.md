<!-- Generated by SpecStory -->

# 分析主界面表头排序问题 (2025-07-10 02:24Z)

_**User**_

@/src  @main.py 
现在，主界面列表展示区域表头排序功能存在不少问题，这段时间不断修改也没有解决。
请你仔细阅读项目代码，梳理出项目当前情况，一步一步思考，要有系统思维，全局的视角，综合分析，不能头痛医头，脚痛医脚。
找出项目当前存在的问题，尤其是表头排序相关的问题，给出详细解决方案。暂不需要具体实现，待用户确认后，再开始具体实现。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我仔细阅读项目代码，梳理出项目当前情况，找出表头排序相关的问题，给出详细解决方案。

首先我需要：
1. 探索项目结构，了解项目整体架构
2. 找到表头排序相关的代码
3. 分析问题的根本原因
4. 提供系统性的解决方案

让我先从项目结构开始分析，然后使用代码搜索工具找到相关的代码。

从项目结构可以看到：
- 主应用入口是 main.py
- 主要代码在 src/ 目录下
- GUI 相关代码在 src/gui/ 目录
- 有一个 prototype/ 目录，表明这是一个原型版本
- 有大量的文档在 docs/ 目录中，包括很多问题记录和解决方案

我应该先探索核心的 GUI 代码和表头排序相关的实现。</details></think>

---

我来仔细分析项目的当前情况，特别是表头排序相关的问题。让我先探索项目结构，了解整体架构。

---

<details>
            <summary>Searched codebase "表头排序功能实现" • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>搜索结果是空的，让我换个方式搜索，或者直接搜索关键词。</details></think>

---

<details>
            <summary>Grep search for "排序" • **7** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L2 | `验证新的分页排序实现是否解决根本问题` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L88 | `self.setWindowTitle("统一分页排序解决方案测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L114 | `<h3>统一分页排序解决方案测试</h3>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L117 | `<li>1. 点击表头进行排序，观察表头是否保持中文显示</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L118 | `<li>2. 点击下一页，观察排序是否保持，页码是否正确</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L119 | `<li>3. 在第2页点击表头排序，观察是否还在第2页</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L120 | `<li>4. 重复操作，验证排序和分页状态的稳定性</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L139 | `self.test_button2 = QPushButton("测试2：按薪级工资排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L141 | `self.test_button4 = QPushButton("测试4：在第3页按津贴排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L166 | `"""测试按薪级工资排序"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L173 | `self.status_label.setText("测试：按薪级工资升序排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L183 | `self.status_label.setText("测试：跳转到第3页（保持排序）")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L186 | `"""测试在第3页按津贴排序"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L193 | `self.status_label.setText("测试：在第3页按津贴降序排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L200 | `sort_info = f"，排序：{sort_col.get('column_name', '')} {sort_col.get('order', '')}"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L225 | `logger.info("🚀 统一分页排序解决方案测试启动")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L228 | `logger.info("2. 手动点击表头进行排序测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L230 | `logger.info("4. 观察排序状态和页码是否正确保持")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L2 | `验证新的分页排序实现是否解决根本问题` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L89 | `print("🚀 开始测试统一分页排序解决方案")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L111 | `# 测试2：排序功能` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L112 | `print("\n📋 测试2：排序功能")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L121 | `print(f"✅ 排序加载成功：{len(result2.data)} 条数据")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L123 | `# 验证排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L127 | `print(f"✅ 排序验证：{'成功' if is_sorted else '失败'}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L130 | `# 测试3：分页+排序组合` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L131 | `print("\n📋 测试3：分页+排序组合")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L140 | `print(f"✅ 第3页降序排序成功：{len(result3.data)} 条数据")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L151 | `print("   模拟：点击表头排序 → 翻页 → 再次排序 → 验证状态")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L153 | `# 步骤1：点击表头排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L161 | `print(f"   步骤1：表头排序 - 页码：{step1_result.page}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L163 | `# 步骤2：翻页（保持排序）` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L171 | `print(f"   步骤2：翻页保持排序 - 页码：{step2_result.page}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L173 | `# 步骤3：在第2页再次排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L181 | `print(f"   步骤3：第2页重新排序 - 页码：{step3_result.page}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L185 | `print("✅ 用户操作序列测试成功：排序不重置页码")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L208 | `print("✅ 分页和排序统一处理正确")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L214 | `print("1. ✅ 排序不再重置页码")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L1 | `P4-005 拖拽排序功能测试程序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L3 | `测试虚拟化可展开表格的拖拽排序功能，包括：` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L6 | `- 行数据重排序算法` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L42 | `"""拖拽排序测试控制面板"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L54 | `logger.info("拖拽排序测试控制面板初始化完成")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L61 | `title_label = QLabel("🚀 P4-005 拖拽排序功能测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L80 | `self.drag_enabled_cb = QCheckBox("启用拖拽排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L115 | `self.test_row_reorder_btn = QPushButton("3. 测试行重排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L232 | `raise Exception("拖拽排序管理器未初始化")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L305 | `"""测试行重排序功能"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L307 | `self.update_status("测试行重排序功能...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L310 | `raise Exception("表格行数不足，无法测试重排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L338 | `"test_name": "行重排序",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L351 | `self.update_status("行重排序测试通过")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L352 | `self.test_completed.emit("row_reorder", True, "行重排序功能正常")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L357 | `error_msg = f"行重排序测试失败: {e}"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L440 | `self.update_status("开始运行全部拖拽排序测试...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L472 | `"拖拽排序功能测试报告",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L512 | `msg_box.setWindowTitle("P4-005 拖拽排序测试报告")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L521 | `logger.info(f"拖拽排序测试完成: {passed_tests}/{total_tests} 通过")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L540 | `"📊 拖拽排序统计信息",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L569 | `"""行重排序回调"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L570 | `self.update_status(f"行重排序完成: {from_row} -> {to_row}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L581 | `"""拖拽排序测试主窗口"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L588 | `logger.info("拖拽排序测试窗口初始化完成")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L592 | `self.setWindowTitle("P4-005 拖拽排序功能测试 - 虚拟化可展开表格")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L611 | `table_title = QLabel("🗂️ 虚拟化可展开表格 (支持拖拽排序)")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L694 | `app.setApplicationName("P4-005 拖拽排序功能测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L702 | `logger.info("P4-005 拖拽排序功能测试程序启动成功")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_import_defaults.py` | L198 | `# 验证建议按得分排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L108 | `"""测试数据表格排序功能"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L113 | `# 测试排序功能` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L121 | `# 启用排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L124 | `# 验证排序功能可用` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L91 | `# 3. 检查排序页码保持修复` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L95 | `if '🔧 排序时保持页码:' in content:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L96 | `fixes_verified.append("✅ 排序时页码保持修复")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L98 | `fixes_verified.append("❌ 排序时页码保持修复未找到")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L100 | `fixes_verified.append(f"❌ 检查排序页码修复失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L116 | `print("2. ✅ 排序时保持当前页码不重置")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L128 | `print("3. 点击'2025年薪级工资'进行排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L130 | `print("5. 再次点击'2025年薪级工资'排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L131 | `print("6. 验证：表头保持中文，页码不重置，排序正常")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L2 | `测试分页排序修复效果` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L10 | `print("🚀 开始测试分页排序修复效果")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L48 | `# 3. 检查表格组件的排序逻辑` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L53 | `fixes_checked.append("✅ 排序时保持页码逻辑")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L55 | `fixes_checked.append("❌ 排序时保持页码逻辑未找到")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L72 | `print("1. ✅ 修复排序时页码重置问题 - 排序操作保持当前页码")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L81 | `print("• 确保排序触发时保持当前页码不变")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L84 | `print("• 用户点击表头排序时，不再跳回第1页")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L86 | `print("• 分页切换时排序状态正确保持")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L87 | `print("• 在任意页面点击排序都保持在当前页")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L99 | `print("2. 在主界面列表展示区域点击表头'2025年薪级工资'进行排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L101 | `print("4. 再次点击表头'2025年薪级工资'进行排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L104 | `print("   • 排序后应该仍在当前页，不跳回第1页")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L105 | `print("   • 分页和排序状态应该正确保持")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L109 | `print("• 在第2页点击排序后，仍然在第2页显示排序结果")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L110 | `print("• 多次操作分页和排序，状态保持稳定")` |

</details>

---

<details>
            <summary>Grep search for "sort" • **26** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L156 | `self.test_button2.clicked.connect(self.test_sort_by_grade_salary)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L158 | `self.test_button4.clicked.connect(self.test_sort_on_page_3)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L165 | `def test_sort_by_grade_salary(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L167 | `sort_columns = [{'column_name': 'grade_salary_2025', 'order': 'ascending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L171 | `sort_columns=sort_columns` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L177 | `current_sort = self.table_widget.current_query.sort_columns` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L181 | `sort_columns=current_sort` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L185 | `def test_sort_on_page_3(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L187 | `sort_columns = [{'column_name': 'allowance', 'order': 'descending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L191 | `sort_columns=sort_columns` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L197 | `sort_info = ""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L198 | `if result.sort_columns:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L199 | `sort_col = result.sort_columns[0]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L200 | `sort_info = f"，排序：{sort_col.get('column_name', '')} {sort_col.get('order', '')}"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L202 | `self.status_label.setText(f"数据加载完成：第{result.page}页，共{result.total_pages}页{sort_info}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L41 | `class DragSortTestWidget(QWidget):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L207 | `self.table.set_drag_sort_enabled(self.drag_enabled_cb.isChecked())` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L216 | `if hasattr(self.table.drag_sort_manager, 'drag_threshold'):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L217 | `self.table.drag_sort_manager.drag_threshold = self.threshold_spin.value()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L231 | `if not hasattr(self.table, 'drag_sort_manager'):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L234 | `manager = self.table.drag_sort_manager` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L268 | `manager = self.table.drag_sort_manager` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L367 | `manager = self.table.drag_sort_manager` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L406 | `stats = self.table.get_drag_sort_statistics()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L495 | `stats = self.table.get_drag_sort_statistics()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L537 | `stats = self.table.get_drag_sort_statistics()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L580 | `class DragSortTestMainWindow(QMainWindow):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L639 | `self.test_widget = DragSortTestWidget(self.table)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L699 | `window = DragSortTestMainWindow()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L640 | `for action, count in sorted(action_counts.items(), key=lambda x: x[1], reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_navigation_display_fix.py` | L147 | `for year in sorted(tree_data.keys(), reverse=True)[:2]:  # 显示最新2年` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_navigation_display_fix.py` | L151 | `for month in sorted(months.keys(), reverse=True)[:2]:  # 显示最新2个月` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L117 | `sort_columns=[{'column_name': 'grade_salary_2025', 'order': 'ascending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L126 | `is_sorted = salary_values == sorted(salary_values)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L127 | `print(f"✅ 排序验证：{'成功' if is_sorted else '失败'}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L136 | `sort_columns=[{'column_name': 'grade_salary_2025', 'order': 'descending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L158 | `sort_columns=[{'column_name': 'grade_salary_2025', 'order': 'ascending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L168 | `sort_columns=[{'column_name': 'grade_salary_2025', 'order': 'ascending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L178 | `sort_columns=[{'column_name': 'allowance', 'order': 'descending'}]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L107 | `def test_data_table_widget_sorting(self, qapp):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L122 | `widget.setSortingEnabled(True)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L125 | `assert widget.isSortingEnabled() == True` |
| `file:///c%3A/test/salary_changes/salary_changes/test/quick_fix_pagination_sort.py` | L52 | `current_sort = self.get_current_sort_state()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/quick_fix_pagination_sort.py` | L60 | `self.restore_sort_state(current_sort)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/demo_default_config.py` | L63 | `for year in sorted(tree_data.keys(), reverse=True)[:3]:  # 显示最新3年` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/check_salary_table_records.py` | L66 | `for period in sorted(table_data.keys(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/check_salary_table_records.py` | L92 | `for period in sorted(table_data.keys(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/check_table_data.py` | L50 | `for table, count in sorted(tables_with_data, key=lambda x: x[1], reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/dependency_analyzer.py` | L285 | `top_coupled = sorted(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/dependency_analyzer.py` | L323 | `for dep in sorted(deps):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L339 | `selected_fields.sort(key=lambda x: self.all_fields.index(x))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L375 | `selected_fields.sort(key=lambda x: self.all_fields.index(x))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_hierarchy_fix.py` | L69 | `for year, months_data in sorted(navigation_data.items(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_hierarchy_fix.py` | L88 | `for month, items in sorted(months_data.items()):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh.py` | L171 | `for month in sorted(months_data.keys(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_startup_fix.py` | L52 | `for year in sorted(year_month_stats.keys(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_startup_fix.py` | L53 | `months = sorted(year_month_stats[year])` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_startup_fix.py` | L80 | `for year, months_data in sorted(navigation_data.items(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_startup_fix.py` | L82 | `for month, items in sorted(months_data.items()):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_new_pagination_threshold.py` | L68 | `recent_periods = sorted(table_data.keys(), reverse=True)[:3]` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh_fix.py` | L38 | `for year, months_data in sorted(navigation_data.items(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh_fix.py` | L49 | `months = sorted(months_data.keys())` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh_fix.py` | L80 | `for year, months_data in sorted(navigation_data.items(), reverse=True):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh_fix.py` | L84 | `for month, items in sorted(months_data.items()):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/analyze_table_structures.py` | L82 | `for field in sorted(all_fields):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/analyze_table_structures.py` | L93 | `print(f"  独有字段: {sorted(unique_fields)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/analyze_table_structures.py` | L98 | `print(f"  缺少字段: {sorted(missing_fields)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/analyze_table_structures.py` | L108 | `print(f"公共字段 ({len(common_fields)} 个): {sorted(common_fields)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/report_generation/report_manager.py` | L383 | `completed_tasks.sort(key=lambda t: t.end_time or t.created_time, reverse=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/system_config/config_manager.py` | L504 | `backup_files.sort(key=lambda x: x.stat().st_mtime)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L554 | `unified_df = unified_df[sorted(all_columns)]` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L567 | `merged_df = merged_df.sort_values('employee_id').reset_index(drop=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/smart_sheet_matcher.py` | L93 | `cache_key = f"{category}:{':'.join(sorted(sheet_names))}"` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/smart_sheet_matcher.py` | L408 | `sorted_matches = sorted(unique_matches.values(), key=lambda x: x.score, reverse=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/smart_sheet_matcher.py` | L409 | `return sorted_matches[:top_n]` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/data_validator.py` | L596 | `'most_problematic_fields': sorted(field_errors.items(),` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/data_validator.py` | L598 | `'most_common_errors': sorted(error_type_counts.items(),` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/config_sync_manager.py` | L804 | `backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/auto_field_mapping_generator.py` | L323 | `sorted_patterns = sorted(field_mapping.items(), key=lambda x: len(x[0]), reverse=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/auto_field_mapping_generator.py` | L324 | `for excel_pattern, db_field in sorted_patterns:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L553 | `return json.dumps(schema_dict, ensure_ascii=False, sort_keys=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1442 | `def get_dataframe_paginated_with_sort(self, table_name: str, page: int = 1, page_size: int = 100,` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1443 | `sort_columns: Optional[List[Dict[str, Any]]] = None) -> Tuple[Optional[pd.DataFrame], int]:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1451 | `sort_columns (List[Dict], optional): 排序列信息` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1457 | `self.logger.info(f"正在从表 {table_name} 分页获取数据（支持排序）: 第{page}页, 每页{page_size}条, 排序={len(sort_columns) if sort_columns else 0}列")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1478 | `if sort_columns:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1480 | `for sort_col in sort_columns:` |

</details>

---

<details>
            <summary>Grep search for "表头" • **18** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/verify_field_mapping_fix.py` | L243 | `print("用户现在可以享受一致、可靠的中文表头显示体验。")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L117 | `<li>1. 点击表头进行排序，观察表头是否保持中文显示</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L119 | `<li>3. 在第2页点击表头排序，观察是否还在第2页</li>` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L204 | `# 验证表头映射` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L209 | `logger.info(f"✅ 表头映射成功：{chinese_columns}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L211 | `logger.warning("❌ 表头映射失败：全部为英文")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_solution.py` | L228 | `logger.info("2. 手动点击表头进行排序测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L102 | `print(f"   表头映射：{list(result1.data.columns)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L151 | `print("   模拟：点击表头排序 → 翻页 → 再次排序 → 验证状态")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L153 | `# 步骤1：点击表头排序` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L161 | `print(f"   步骤1：表头排序 - 页码：{step1_result.page}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_unified_core.py` | L215 | `print("2. ✅ 表头映射保持一致")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L152 | `• 观察空白区域和表头的菜单` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L185 | `simulate_header_btn = QPushButton("模拟表头右键")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L278 | `"HEADER - 表头菜单",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L290 | `elif "表头" in menu_type or "空白" in menu_type:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L387 | `(QPoint(50, 20), "表头区域"),` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L665 | `# 模拟表头右键` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L5 | `1. 中文表头和英文表头的分页一致性` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L43 | `"salary_data_2026_01_a_grade_employees": "英文表头表（无映射）",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L44 | `"salary_data_2026_04_a_grade_employees": "中文表头表（有映射）"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L4 | `工资表表头映射与自定义显示功能完整测试脚本` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L10 | `4. 用户表头偏好管理` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L12 | `6. 表头自定义对话框` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L41 | `"""表头映射功能测试器"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L57 | `self.logger.info("表头映射功能测试器初始化完成")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L61 | `self.logger.info("开始运行表头映射功能完整测试")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L70 | `# 3. 测试用户表头偏好管理` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L168 | `"""测试用户表头偏好管理"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L169 | `self.logger.info("测试用户表头偏好管理...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L177 | `assert success, "保存用户表头偏好失败"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L189 | `assert success, "删除用户表头偏好失败"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L203 | `self.logger.info("✅ 用户表头偏好管理测试通过")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L206 | `self.logger.error(f"❌ 用户表头偏好管理测试失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L347 | `self.logger.info("表头映射功能测试报告")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L383 | `print("开始运行工资表表头映射与自定义显示功能完整测试...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L3 | `表头重影修复效果测试脚本` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L5 | `测试三层防护机制是否有效防止表头重影问题：` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L8 | `3. 第三层防护：表头管理器的智能检测` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L10 | `作者: 表头重影修复项目组` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L40 | `"""表头重影修复测试器"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L73 | `"""测试导航切换时的表头重影修复"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L74 | `logger.info("开始测试导航切换时的表头重影修复...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L100 | `# 检查表头状态` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L103 | `logger.info(f"测试 {test_count}: 成功 - 无表头重影")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L105 | `logger.warning(f"测试 {test_count}: 失败 - 检测到表头重影")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L127 | `"""检查表头状态是否正常（无重影）"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L138 | `# 获取表头标签` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L145 | `# 检查是否有重复的表头` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L151 | `logger.warning(f"检测到重复表头: {set(duplicates)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L157 | `logger.error(f"检查表头状态失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L161 | `"""测试表头管理器的检测功能"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L162 | `logger.info("开始测试表头管理器的检测功能...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L166 | `logger.warning("表头管理器不存在")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L178 | `logger.info(f"表头管理器检测报告:")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L185 | `'test_name': '表头管理器检测',` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L198 | `logger.error(f"表头管理器检测测试失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L203 | `logger.info("开始运行表头重影修复效果测试...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L221 | `# 测试2: 表头管理器检测` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L223 | `test_results.append(('表头管理器检测', result2))` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L236 | `logger.info("表头重影修复效果测试总结")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L261 | `logger.info("表头重影修复方案验证成功！")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L264 | `logger.error("表头重影修复方案验证失败！")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L53 | `"""测试带表头的数据表格组件"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_widgets_comprehensive.py` | L59 | `# 验证表头设置` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_dialogs_comprehensive.py` | L113 | `# 验证表头` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L73 | `print("2. ✅ 修复字段映射不一致问题 - 确保表头始终显示中文")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L84 | `print("• 用户点击表头排序时，不再跳回第1页")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L85 | `print("• 表头始终显示中文，不会出现英文")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L99 | `print("2. 在主界面列表展示区域点击表头'2025年薪级工资'进行排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L101 | `print("4. 再次点击表头'2025年薪级工资'进行排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L103 | `print("   • 表头应该保持中文显示")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_fixes.py` | L108 | `print("• 表头始终显示'2025年薪级工资'，不变成'grade_salary_2025'")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L123 | `print("• 最后设置到表格显示（中文表头）")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L127 | `print("2. 查看表头应显示中文字段名")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L131 | `print("6. 验证：表头保持中文，页码不重置，排序正常")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_data_import_dialog_updated.py` | L53 | `# 写入表头` |
| `file:///c%3A/test/salary_changes/salary_changes/test/quick_fix_pagination_sort.py` | L15 | `1. 在表头点击排序时阻止页码重置` |
| `file:///c%3A/test/salary_changes/salary_changes/test/quick_fix_pagination_sort.py` | L82 | `print("1. 在表头点击排序时保持当前页码")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/final_test.py` | L72 | `print("• 点击表头排序时，获取并保持当前页码")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/final_test.py` | L74 | `print("• 确保表头始终显示中文，不会变成英文")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/final_test.py` | L80 | `print("3. 点击'2025年薪级工资'表头排序")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/final_test.py` | L82 | `print("   • 表头显示'2025年薪级工资'（不是grade_salary_2025）")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/final_test.py` | L99 | `print("   • 表头不再变成英文")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/system_config/specialized_table_templates.py` | L196 | `"""根据Excel表头检测应使用的模板"""` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/system_config/specialized_table_templates.py` | L197 | `# 清理表头：去除换行符、空格等特殊字符` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/report_generation/excel_generator.py` | L228 | `检查单元格是否为表头单元格` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/report_generation/excel_generator.py` | L235 | `bool: 是否为表头单元格` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/report_generation/excel_generator.py` | L492 | `# 设置表头` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/specialized_field_mapping_generator.py` | L5 | `为4类工资表生成专用的字段映射配置，确保Excel表头能正确映射到专用数据库字段。` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/specialized_field_mapping_generator.py` | L25 | `# Excel表头到数据库字段的映射规则` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/specialized_field_mapping_generator.py` | L94 | `excel_headers: Excel表头列表` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/specialized_field_mapping_generator.py` | L110 | `# 清理Excel表头` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/specialized_field_mapping_generator.py` | L146 | `"""清理Excel表头"""` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L739 | `"""检测是否包含中文表头"""` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L744 | `return chinese_count > len(columns) * 0.3  # 超过30%为中文认为是中文表头` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L1008 | `# 注意：这里需要先读取Sheet数据来获取表头，但为了保持方法简洁，` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/multi_sheet_importer.py` | L1096 | `excel_headers: Excel原始表头` |

</details>

---

<think><details><summary>Thought Process</summary>从搜索结果可以看到，项目中存在大量关于表头排序的测试文件和相关代码。让我继续搜索更多相关信息，特别是主要的GUI组件和表格组件。</details></think>

---

Read file: src/gui/prototype/prototype_main_window.py

---

<details>
            <summary>Listed directory src/gui/widgets • **7** results</summary>
        
| Name |
|-------|
| 📁 `__pycache__` |
| 📄 `pagination_widget.py` |
| 📄 `unified_table_widget.py` |
| 📄 `pagination_widget.py.backup` |
| 📄 `pagination_cache_manager.py` |
| 📄 `__init__.py` |
| 📄 `target_selection_widget.py` |

</details>

---

<details>
            <summary>Searched codebase "主界面表格组件排序实现" • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>从读取的代码中，我看到有一个 `SortedPaginationWorker` 类，这可能是处理排序的关键组件。让我进一步查看这个类的实现，以及其他相关的表格组件。</details></think>

---

Read file: src/gui/prototype/prototype_main_window.py

---

Read file: src/gui/widgets/pagination_widget.py

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---

<think><details><summary>Thought Process</summary>基于我对项目代码的分析，我已经获得了足够的信息来做出全面的诊断。让我继续查看更多关键的组件，特别是排序相关的状态管理和数据库管理。</details></think>

---

<details>
            <summary>Searched codebase "排序状态管理器实现" • **0** results</summary>
        
No results found
</details>

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---

<details>
            <summary>Listed directory docs/problems • **8** results</summary>
        
| Name |
|-------|
| 📁 `字段映射` |
| 📁 `文档与代码实现不一致问题分析` |
| 📄 `离休人员表命名问题修复.md` |
| 📄 `导入数据导航显示问题分析与解决方案.md` |
| 📄 `分页字段映射问题的真实解决方案.md` |
| 📄 `分页字段映射问题真正原因与解决方案.md` |
| 📄 `导航刷新问题修复报告.md` |
| 📄 `系统启动问题修复总结.md` |

</details>

---

<think><details><summary>Thought Process</summary>我现在有了足够的信息来进行全面的诊断。让我再查看一些关键的问题文档，特别是字段映射相关的问题。</details></think>

---

Read file: docs/problems/分页字段映射问题的真实解决方案.md

---

Read file: docs/problems/字段映射/多次修改依然无法根本解决的问题-字段映射-总结.md

---

<details>
            <summary>Searched codebase "sort_state_manager" • **2** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经收集到了足够的信息来进行全面的分析。从代码和文档的分析中，我发现了一些关键问题。让我先查看一下排序状态管理器的实现，然后给出完整的分析报告。</details></think>

---

Read file: src/core/table_sort_state_manager.py

---

<think><details><summary>Thought Process</summary>基于对项目代码的全面分析，现在我有了足够的信息来进行详细的诊断和分析。让我整理一下发现的问题，并提供完整的解决方案。</details></think>

---

## 项目现状分析及表头排序问题诊断报告

基于对项目代码的深入分析，我发现了多个系统性问题。以下是我的分析和建议：

### 🔍 **项目现状梳理**

#### **1. 架构现状**
- 项目采用 **PyQt5** 重构设计，实现了三段式布局（Header + Workspace + Footer）
- 使用 **原型主窗口** (`PrototypeMainWindow`) 作为主界面
- 数据处理通过 **虚拟化表格** (`VirtualizedExpandableTable`) 实现
- 包含 **分页组件** (`PaginationWidget`) 和 **排序状态管理器** (`TableSortStateManager`)

#### **2. 数据流架构**
- **数据库** → **DynamicTableManager** → **PaginationWorker** → **字段映射** → **表格显示**
- 支持多种 Worker 线程：`PaginationWorker`, `SortedPaginationWorker`
- 字段映射通过 `ConfigSyncManager` 管理

### 🚨 **核心问题分析**

#### **问题1：排序与分页状态管理混乱**

---

```mermaid
graph TD
    A[用户点击表头排序] --> B[_on_header_clicked]
    B --> C[_save_current_table_sort_state]
    C --> D[TableSortStateManager]
    D --> E[SortedPaginationWorker]
    E --> F[数据库查询]
    F --> G[_apply_field_mapping_to_dataframe]
    G --> H[表格显示]
    
    I[用户切换分页] --> J[_on_page_changed]
    J --> K[PaginationWorker]
    K --> L[数据库查询]
    L --> M[_apply_field_mapping_to_dataframe]
    M --> N[表格显示]
    
    O[问题: 排序状态丢失] --> P[原因1: 两个Worker使用不同处理逻辑]
    O --> Q[原因2: 字段映射不一致]
    O --> R[原因3: 状态管理器未正确同步]
    
    style O fill:#ff9999
    style P fill:#ffcc99
    style Q fill:#ffcc99
    style R fill:#ffcc99
```

---

<think><details><summary>Thought Process</summary>我需要开始基于这个诊断编写一个完整的解决方案。让我先为用户提供一个完整的问题分析和解决方案。</details></think>

---

**主要问题：**
1. **排序和分页使用不同的Worker线程**，导致状态不一致
2. **字段映射在不同环节不同步**，表头显示混乱
3. **排序状态管理器**与实际排序操作脱节

#### **问题2：字段映射系统复杂且不一致**

从文档分析可见，项目经历了多次字段映射问题的修复，但仍然存在：
- **112个工资表**使用不同的字段映射配置
- **中英文字段名混合**存储
- **分页时字段映射丢失**问题反复出现

#### **问题3：组件间耦合度过高**

---

```mermaid
graph TD
    A[PrototypeMainWindow] --> B[MainWorkspaceArea]
    A --> C[PaginationWidget]
    A --> D[VirtualizedExpandableTable]
    A --> E[TableSortStateManager]
    A --> F[ConfigSyncManager]
    
    B --> G[PaginationWorker]
    B --> H[SortedPaginationWorker]
    
    D --> I[TableContextMenu]
    D --> J[TableCellEditor]
    D --> K[DragSortManager]
    
    L[问题: 组件间循环依赖] --> M[MainWorkspaceArea调用主窗口方法]
    L --> N[分页组件直接访问主窗口]
    L --> O[表格组件与多个管理器耦合]
    
    style L fill:#ff9999
    style M fill:#ffcc99
    style N fill:#ffcc99
    style O fill:#ffcc99
```

---

<think><details><summary>Thought Process</summary>现在我需要为用户提供一个完整的解决方案架构图，然后给出具体的实施步骤。</details></think>

---

### 💡 **系统性解决方案**

#### **方案1：统一数据处理管道**

---

```mermaid
graph TD
    A[用户操作] --> B[统一数据请求管理器]
    B --> C[数据查询参数标准化]
    C --> D[DynamicTableManager]
    
    D --> E[get_dataframe_paginated_with_sort统一接口]
    E --> F[SQL查询构建]
    F --> G[排序子句处理]
    G --> H[分页子句处理]
    H --> I[数据库执行]
    
    I --> J[字段映射统一处理器]
    J --> K[字段映射配置获取]
    J --> L[DataFrame列重命名]
    J --> M[数据格式化]
    
    M --> N[表格显示]
    
    O[排序状态管理器] --> P[保存排序状态]
    P --> Q[字段映射适配]
    Q --> R[状态持久化]
    
    B --> O
    N --> S[用户看到正确排序的中文表头]
    
    style S fill:#99ff99
    style J fill:#99ccff
    style E fill:#99ccff
```

---

<think><details><summary>Thought Process</summary>我需要为用户提供一个完整的解决方案，包括具体的实施步骤。</details></think>

---

#### **方案2：解耦组件架构**

---

```mermaid
graph TD
    A[UI层] --> B[事件总线]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    
    E[表格组件] --> F[排序事件]
    G[分页组件] --> H[分页事件]
    I[导航组件] --> J[导航事件]
    
    F --> B
    H --> B
    J --> B
    
    B --> K[数据服务管理器]
    K --> L[排序状态服务]
    K --> M[分页状态服务]
    K --> N[字段映射服务]
    
    L --> O[TableSortStateManager]
    M --> P[PaginationCacheManager]
    N --> Q[ConfigSyncManager]
    
    K --> R[统一数据请求]
    R --> S[DynamicTableManager]
    S --> T[数据库]
    
    style K fill:#99ccff
    style B fill:#ffcc99
    style R fill:#99ff99
```

---

<think><details><summary>Thought Process</summary>现在我需要为用户提供一个完整的解决方案，包括具体的实施步骤。</details></think>

---

### 🔧 **详细解决方案**

#### **第一阶段：统一数据处理管道**

**1. 创建统一数据请求管理器**
```python
# src/core/unified_data_request_manager.py
class UnifiedDataRequestManager:
    """统一数据请求管理器"""
    
    def request_table_data(self, 
                          table_name: str, 
                          page: int = 1, 
                          page_size: int = 50,
                          sort_columns: List[Dict] = None,
                          preserve_sort_state: bool = True) -> DataResponse:
        """
        统一的数据请求接口，整合排序、分页、字段映射
        """
        # 1. 获取或恢复排序状态
        if preserve_sort_state:
            sort_state = self.sort_state_manager.restore_sort_state(table_name)
            if sort_state and sort_state.sort_columns:
                sort_columns = sort_state.sort_columns
        
        # 2. 数据库查询
        df, total_records = self.db_manager.get_dataframe_paginated_with_sort(
            table_name, page, page_size, sort_columns
        )
        
        # 3. 统一字段映射处理
        mapped_df = self.field_mapping_service.apply_mapping(df, table_name)
        
        # 4. 保存状态
        if sort_columns:
            self.sort_state_manager.save_sort_state(
                table_name, 
                self._extract_table_type(table_name),
                sort_columns, 
                self.field_mapping_service.get_mapping(table_name),
                {'page': page, 'page_size': page_size}
            )
        
        return DataResponse(
            data=mapped_df,
            page=page,
            total_records=total_records,
            sort_columns=sort_columns,
            success=True
        )
```

**2. 重构表格组件排序处理**
```python
# 修改 VirtualizedExpandableTable._on_header_clicked
def _on_header_clicked(self, logical_index: int):
    """表头点击处理 - 重构版本"""
    if not self.current_table_name:
        return
        
    # 获取数据库字段名
    db_field_name = self._get_db_field_name_by_column_index(logical_index)
    if not db_field_name:
        return
    
    # 构建排序参数
    sort_columns = self._build_sort_columns(db_field_name)
    
    # 🔧 关键修复：使用统一数据请求管理器
    response = self.unified_data_manager.request_table_data(
        table_name=self.current_table_name,
        page=self.current_page,  # 保持当前页码
        page_size=self.current_page_size,
        sort_columns=sort_columns,
        preserve_sort_state=False  # 新排序，不保留旧状态
    )
    
    if response.success:
        # 更新表格显示
        self._update_table_display(response.data)
        # 发送排序变更信号
        self.sort_state_changed.emit(sort_columns)
```

**3. 修复分页组件**
```python
# 修改 PaginationWidget._on_page_changed
def _on_page_changed(self, page: int):
    """分页变化处理 - 重构版本"""
    if not self.current_table_name:
        return
    
    # 🔧 关键修复：使用统一数据请求管理器，保持排序状态
    response = self.unified_data_manager.request_table_data(
        table_name=self.current_table_name,
        page=page,
        page_size=self.current_page_size,
        preserve_sort_state=True  # 保持排序状态
    )
    
    if response.success:
        # 更新分页状态
        self.state.current_page = page
        self.state.sort_columns = response.sort_columns
        # 发送数据变更信号
        self.page_changed.emit(page)
```

#### **第二阶段：字段映射统一化**

**1. 创建字段映射服务**
```python
# src/core/field_mapping_service.py
class FieldMappingService:
    """字段映射统一服务"""
    
    def apply_mapping(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """统一的字段映射应用"""
        # 获取字段映射配置
        field_mapping = self.config_sync_manager.load_mapping(table_name)
        if not field_mapping:
            return df
        
        # 创建重命名映射
        column_rename_map = {}
        for db_field, display_name in field_mapping.items():
            if db_field in df.columns and display_name:
                column_rename_map[db_field] = display_name
        
        # 应用重命名
        if column_rename_map:
            self.logger.info(f"应用字段映射 {table_name}: {len(column_rename_map)} 个字段")
            return df.rename(columns=column_rename_map)
        
        return df
```

**2. 排序状态与字段映射同步**
```python
# 修改 TableSortStateManager.save_sort_state
def save_sort_state(self, table_name: str, table_type: str, 
                   sort_columns: List[Dict], field_mapping: Dict[str, str],
                   pagination_context: Optional[Dict] = None) -> bool:
    """保存排序状态时同步字段映射"""
    
    # 检测字段映射变更
    if self._detect_field_mapping_change(table_name, field_mapping):
        self.logger.info(f"检测到字段映射变更: {table_name}")
        # 适配排序列到新的字段映射
        sort_columns = self._adapt_sort_columns_to_mapping(sort_columns, field_mapping)
    
    # 保存状态
    state = TableSortState(
        table_name=table_name,
        table_type=table_type,
        sort_columns=sort_columns,
        field_mapping=field_mapping.copy(),
        # 保存分页上下文
        current_page=pagination_context.get('page', 1) if pagination_context else 1,
        page_size=pagination_context.get('page_size', 50) if pagination_context else 50
    )
    
    self.table_sort_states[table_name] = state
    self._save_states_to_file()
    return True
```

#### **第三阶段：组件解耦**

**1. 创建事件总线**
```python
# src/core/event_bus.py
class EventBus:
    """事件总线，解耦组件间通信"""
    
    def emit_sort_changed(self, table_name: str, sort_columns: List[Dict]):
        """发送排序变更事件"""
        event = SortChangedEvent(table_name, sort_columns)
        self._distribute_event(event)
    
    def emit_page_changed(self, table_name: str, page: int, page_size: int):
        """发送分页变更事件"""
        event = PageChangedEvent(table_name, page, page_size)
        self._distribute_event(event)
```

**2. 服务层重构**
```python
# src/services/table_data_service.py
class TableDataService:
    """表格数据服务层"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.unified_data_manager = UnifiedDataRequestManager()
        
        # 订阅事件
        self.event_bus.subscribe(SortChangedEvent, self._handle_sort_changed)
        self.event_bus.subscribe(PageChangedEvent, self._handle_page_changed)
    
    def _handle_sort_changed(self, event: SortChangedEvent):
        """处理排序变更事件"""
        response = self.unified_data_manager.request_table_data(
            table_name=event.table_name,
            sort_columns=event.sort_columns,
            preserve_sort_state=False
        )
        
        # 通知UI更新
        self.event_bus.emit_data_updated(event.table_name, response.data)
```

### 📋 **实施计划**

#### **Phase 1: 修复紧急问题 (1-2天)**
1. 修复 `_on_header_clicked` 方法，确保排序时保持当前页码
2. 统一 `PaginationWorker` 和 `SortedPaginationWorker` 的字段映射逻辑
3. 修复字段映射在分页时丢失的问题

#### **Phase 2: 重构数据处理管道 (3-5天)**
1. 创建 `UnifiedDataRequestManager`
2. 重构表格组件的排序处理逻辑
3. 重构分页组件的数据请求逻辑
4. 完善 `FieldMappingService` 统一字段映射

#### **Phase 3: 架构解耦 (5-7天)**
1. 创建事件总线系统
2. 重构服务层架构
3. 解耦组件间直接依赖
4. 完善错误处理和日志记录

#### **Phase 4: 测试和优化 (2-3天)**
1. 编写单元测试
2. 集成测试
3. 性能优化
4. 文档更新

### 🎯 **预期效果**

**用户体验改善：**
- ✅ 表头排序时保持当前页码
- ✅ 分页时排序状态不丢失
- ✅ 中文表头显示一致
- ✅ 响应速度提升

**代码质量提升：**
- ✅ 组件解耦，降低维护成本
- ✅ 统一数据处理管道，减少重复代码
- ✅ 完善错误处理，提高稳定性
- ✅ 清晰的架构层次，便于扩展

**技术债务清理：**
- ✅ 消除循环依赖
- ✅ 统一字段映射处理
- ✅ 简化状态管理逻辑
- ✅ 提高代码可测试性

这是一个系统性的解决方案，需要分阶段实施。请确认这个方案是否符合您的期望，我可以开始具体的代码实现。

---

_**User**_

请将本次对话内容梳理一下，以 markdown 格式独立保存到目录 docs/todo 下，以中文命名文件名。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我将本次对话内容梳理一下，以 markdown 格式独立保存到目录 docs/todo 下，以中文命名文件名。

我需要：
1. 梳理整个对话的内容
2. 整理成完整的 markdown 文档
3. 保存到 docs/todo 目录
4. 使用中文文件名

让我回顾一下对话的主要内容：
1. 用户问题：主界面列表展示区域表头排序功能存在问题
2. 我的分析：项目现状梳理、问题分析、解决方案
3. 主要发现：
   - 排序与分页状态管理混乱
   - 字段映射系统复杂且不一致
   - 组件间耦合度过高
4. 解决方案：
   - 统一数据处理管道
   - 解耦组件架构
   - 分阶段实施计划

我需要将这些内容整理成一个完整的技术文档，保存到指定目录。</details></think>

---

我来将本次对话内容整理成完整的技术文档，保存到指定目录。

---

```diff
- 
+ # 表头排序功能问题分析与系统性解决方案
+ 
+ ## 📋 问题背景
+ 
+ **问题描述：** 主界面列表展示区域表头排序功能存在不少问题，这段时间不断修改也没有解决。
+ 
+ **分析目标：** 
+ - 系统思维，全局视角，综合分析
+ - 找出项目当前存在的问题，尤其是表头排序相关的问题
+ - 给出详细解决方案
+ 
+ ## 🔍 项目现状梳理
+ 
+ ### 1. 架构现状
+ - **前端框架：** PyQt5 重构设计，三段式布局（Header + Workspace + Footer）
+ - **主界面：** `PrototypeMainWindow` 作为主窗口
+ - **数据展示：** `VirtualizedExpandableTable` 虚拟化表格组件
+ - **分页系统：** `PaginationWidget` 分页组件
+ - **状态管理：** `TableSortStateManager` 排序状态管理器
+ 
+ ### 2. 数据流架构
+ ```
+ 数据库 → DynamicTableManager → PaginationWorker → 字段映射 → 表格显示
+ ```
+ 
+ **核心组件：**
+ - `PaginationWorker` - 普通分页数据加载
+ - `SortedPaginationWorker` - 支持排序的分页数据加载
+ - `ConfigSyncManager` - 字段映射配置管理
+ - `TableSortStateManager` - 排序状态管理
+ 
+ ### 3. 现有问题记录
+ 根据项目文档分析，已知存在以下问题：
+ - **字段映射问题：** 经历过多次修复，112个工资表使用不同的字段映射配置
+ - **分页字段映射丢失：** 分页时字段映射不一致的问题反复出现
+ - **中英文字段名混合：** 数据库存储格式不统一
+ 
+ ## 🚨 核心问题分析
+ 
+ ### 问题1：排序与分页状态管理混乱
+ 
+ **问题表现：**
+ - 用户点击表头排序后，页码会重置到第1页
+ - 分页切换后，排序状态丢失
+ - 不同页面的表头显示不一致
+ 
+ **根本原因：**
+ ```mermaid
+ graph TD
+     A[用户点击表头排序] --> B[_on_header_clicked]
+     B --> C[_save_current_table_sort_state]
+     C --> D[TableSortStateManager]
+     D --> E[SortedPaginationWorker]
+     E --> F[数据库查询]
+     F --> G[_apply_field_mapping_to_dataframe]
+     G --> H[表格显示]
+     
+     I[用户切换分页] --> J[_on_page_changed]
+     J --> K[PaginationWorker]
+     K --> L[数据库查询]
+     L --> M[_apply_field_mapping_to_dataframe]
+     M --> N[表格显示]
+     
+     O[问题: 排序状态丢失] --> P[原因1: 两个Worker使用不同处理逻辑]
+     O --> Q[原因2: 字段映射不一致]
+     O --> R[原因3: 状态管理器未正确同步]
+     
+     style O fill:#ff9999
+     style P fill:#ffcc99
+     style Q fill:#ffcc99
+     style R fill:#ffcc99
+ ```
+ 
+ **具体原因：**
+ 1. **排序和分页使用不同的Worker线程**，导致状态不一致
+ 2. **字段映射在不同环节不同步**，表头显示混乱
+ 3. **排序状态管理器**与实际排序操作脱节
+ 
+ ### 问题2：字段映射系统复杂且不一致
+ 
+ **问题表现：**
+ - 同一个表在不同页面显示不同的表头
+ - 部分表头显示英文字段名
+ - 用户编辑表头后，分页时丢失修改
+ 
+ **根本原因：**
+ - **112个工资表**使用不同的字段映射配置
+ - **中英文字段名混合**存储在数据库中
+ - **分页组件的字段映射逻辑**与主窗口不一致
+ 
+ ### 问题3：组件间耦合度过高
+ 
+ **问题表现：**
+ ```mermaid
+ graph TD
+     A[PrototypeMainWindow] --> B[MainWorkspaceArea]
+     A --> C[PaginationWidget]
+     A --> D[VirtualizedExpandableTable]
+     A --> E[TableSortStateManager]
+     A --> F[ConfigSyncManager]
+     
+     B --> G[PaginationWorker]
+     B --> H[SortedPaginationWorker]
+     
+     D --> I[TableContextMenu]
+     D --> J[TableCellEditor]
+     D --> K[DragSortManager]
+     
+     L[问题: 组件间循环依赖] --> M[MainWorkspaceArea调用主窗口方法]
+     L --> N[分页组件直接访问主窗口]
+     L --> O[表格组件与多个管理器耦合]
+     
+     style L fill:#ff9999
+     style M fill:#ffcc99
+     style N fill:#ffcc99
+     style O fill:#ffcc99
+ ```
+ 
+ **具体问题：**
+ - `MainWorkspaceArea` 调用主窗口的私有方法
+ - `PaginationWidget` 直接访问主窗口实例
+ - 表格组件与多个管理器紧密耦合
+ 
+ ## 💡 系统性解决方案
+ 
+ ### 方案1：统一数据处理管道
+ 
+ **核心思路：** 创建统一的数据请求管理器，整合排序、分页、字段映射处理逻辑。
+ 
+ ```mermaid
+ graph TD
+     A[用户操作] --> B[统一数据请求管理器]
+     B --> C[数据查询参数标准化]
+     C --> D[DynamicTableManager]
+     
+     D --> E[get_dataframe_paginated_with_sort统一接口]
+     E --> F[SQL查询构建]
+     F --> G[排序子句处理]
+     G --> H[分页子句处理]
+     H --> I[数据库执行]
+     
+     I --> J[字段映射统一处理器]
+     J --> K[字段映射配置获取]
+     J --> L[DataFrame列重命名]
+     J --> M[数据格式化]
+     
+     M --> N[表格显示]
+     
+     O[排序状态管理器] --> P[保存排序状态]
+     P --> Q[字段映射适配]
+     Q --> R[状态持久化]
+     
+     B --> O
+     N --> S[用户看到正确排序的中文表头]
+     
+     style S fill:#99ff99
+     style J fill:#99ccff
+     style E fill:#99ccff
+ ```
+ 
+ **实现要点：**
+ 1. **统一数据请求接口**
+ 2. **一致的字段映射处理**
+ 3. **排序状态与分页状态同步**
+ 4. **数据库查询优化**
+ 
+ ### 方案2：解耦组件架构
+ 
+ **核心思路：** 使用事件总线模式，解耦组件间的直接依赖。
+ 
+ ```mermaid
+ graph TD
+     A[UI层] --> B[事件总线]
+     B --> C[业务逻辑层]
+     C --> D[数据访问层]
+     
+     E[表格组件] --> F[排序事件]
+     G[分页组件] --> H[分页事件]
+     I[导航组件] --> J[导航事件]
+     
+     F --> B
+     H --> B
+     J --> B
+     
+     B --> K[数据服务管理器]
+     K --> L[排序状态服务]
+     K --> M[分页状态服务]
+     K --> N[字段映射服务]
+     
+     L --> O[TableSortStateManager]
+     M --> P[PaginationCacheManager]
+     N --> Q[ConfigSyncManager]
+     
+     K --> R[统一数据请求]
+     R --> S[DynamicTableManager]
+     S --> T[数据库]
+     
+     style K fill:#99ccff
+     style B fill:#ffcc99
+     style R fill:#99ff99
+ ```
+ 
+ **实现要点：**
+ 1. **事件总线系统**
+ 2. **服务层架构**
+ 3. **组件解耦**
+ 4. **统一错误处理**
+ 
+ ## 🔧 详细技术方案
+ 
+ ### 第一阶段：统一数据处理管道
+ 
+ #### 1. 创建统一数据请求管理器
+ 
+ ```python
+ # src/core/unified_data_request_manager.py
+ class UnifiedDataRequestManager:
+     """统一数据请求管理器"""
+     
+     def request_table_data(self, 
+                           table_name: str, 
+                           page: int = 1, 
+                           page_size: int = 50,
+                           sort_columns: List[Dict] = None,
+                           preserve_sort_state: bool = True) -> DataResponse:
+         """
+         统一的数据请求接口，整合排序、分页、字段映射
+         """
+         # 1. 获取或恢复排序状态
+         if preserve_sort_state:
+             sort_state = self.sort_state_manager.restore_sort_state(table_name)
+             if sort_state and sort_state.sort_columns:
+                 sort_columns = sort_state.sort_columns
+         
+         # 2. 数据库查询
+         df, total_records = self.db_manager.get_dataframe_paginated_with_sort(
+             table_name, page, page_size, sort_columns
+         )
+         
+         # 3. 统一字段映射处理
+         mapped_df = self.field_mapping_service.apply_mapping(df, table_name)
+         
+         # 4. 保存状态
+         if sort_columns:
+             self.sort_state_manager.save_sort_state(
+                 table_name, 
+                 self._extract_table_type(table_name),
+                 sort_columns, 
+                 self.field_mapping_service.get_mapping(table_name),
+                 {'page': page, 'page_size': page_size}
+             )
+         
+         return DataResponse(
+             data=mapped_df,
+             page=page,
+             total_records=total_records,
+             sort_columns=sort_columns,
+             success=True
+         )
+ ```
+ 
+ #### 2. 重构表格组件排序处理
+ 
+ ```python
+ # 修改 VirtualizedExpandableTable._on_header_clicked
+ def _on_header_clicked(self, logical_index: int):
+     """表头点击处理 - 重构版本"""
+     if not self.current_table_name:
+         return
+         
+     # 获取数据库字段名
+     db_field_name = self._get_db_field_name_by_column_index(logical_index)
+     if not db_field_name:
+         return
+     
+     # 构建排序参数
+     sort_columns = self._build_sort_columns(db_field_name)
+     
+     # 🔧 关键修复：使用统一数据请求管理器
+     response = self.unified_data_manager.request_table_data(
+         table_name=self.current_table_name,
+         page=self.current_page,  # 保持当前页码
+         page_size=self.current_page_size,
+         sort_columns=sort_columns,
+         preserve_sort_state=False  # 新排序，不保留旧状态
+     )
+     
+     if response.success:
+         # 更新表格显示
+         self._update_table_display(response.data)
+         # 发送排序变更信号
+         self.sort_state_changed.emit(sort_columns)
+ ```
+ 
+ #### 3. 修复分页组件
+ 
+ ```python
+ # 修改 PaginationWidget._on_page_changed
+ def _on_page_changed(self, page: int):
+     """分页变化处理 - 重构版本"""
+     if not self.current_table_name:
+         return
+     
+     # 🔧 关键修复：使用统一数据请求管理器，保持排序状态
+     response = self.unified_data_manager.request_table_data(
+         table_name=self.current_table_name,
+         page=page,
+         page_size=self.current_page_size,
+         preserve_sort_state=True  # 保持排序状态
+     )
+     
+     if response.success:
+         # 更新分页状态
+         self.state.current_page = page
+         self.state.sort_columns = response.sort_columns
+         # 发送数据变更信号
+         self.page_changed.emit(page)
+ ```
+ 
+ ### 第二阶段：字段映射统一化
+ 
+ #### 1. 创建字段映射服务
+ 
+ ```python
+ # src/core/field_mapping_service.py
+ class FieldMappingService:
+     """字段映射统一服务"""
+     
+     def apply_mapping(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
+         """统一的字段映射应用"""
+         # 获取字段映射配置
+         field_mapping = self.config_sync_manager.load_mapping(table_name)
+         if not field_mapping:
+             return df
+         
+         # 创建重命名映射
+         column_rename_map = {}
+         for db_field, display_name in field_mapping.items():
+             if db_field in df.columns and display_name:
+                 column_rename_map[db_field] = display_name
+         
+         # 应用重命名
+         if column_rename_map:
+             self.logger.info(f"应用字段映射 {table_name}: {len(column_rename_map)} 个字段")
+             return df.rename(columns=column_rename_map)
+         
+         return df
+ ```
+ 
+ #### 2. 排序状态与字段映射同步
+ 
+ ```python
+ # 修改 TableSortStateManager.save_sort_state
+ def save_sort_state(self, table_name: str, table_type: str, 
+                    sort_columns: List[Dict], field_mapping: Dict[str, str],
+                    pagination_context: Optional[Dict] = None) -> bool:
+     """保存排序状态时同步字段映射"""
+     
+     # 检测字段映射变更
+     if self._detect_field_mapping_change(table_name, field_mapping):
+         self.logger.info(f"检测到字段映射变更: {table_name}")
+         # 适配排序列到新的字段映射
+         sort_columns = self._adapt_sort_columns_to_mapping(sort_columns, field_mapping)
+     
+     # 保存状态
+     state = TableSortState(
+         table_name=table_name,
+         table_type=table_type,
+         sort_columns=sort_columns,
+         field_mapping=field_mapping.copy(),
+         # 保存分页上下文
+         current_page=pagination_context.get('page', 1) if pagination_context else 1,
+         page_size=pagination_context.get('page_size', 50) if pagination_context else 50
+     )
+     
+     self.table_sort_states[table_name] = state
+     self._save_states_to_file()
+     return True
+ ```
+ 
+ ### 第三阶段：组件解耦
+ 
+ #### 1. 创建事件总线
+ 
+ ```python
+ # src/core/event_bus.py
+ class EventBus:
+     """事件总线，解耦组件间通信"""
+     
+     def emit_sort_changed(self, table_name: str, sort_columns: List[Dict]):
+         """发送排序变更事件"""
+         event = SortChangedEvent(table_name, sort_columns)
+         self._distribute_event(event)
+     
+     def emit_page_changed(self, table_name: str, page: int, page_size: int):
+         """发送分页变更事件"""
+         event = PageChangedEvent(table_name, page, page_size)
+         self._distribute_event(event)
+ ```
+ 
+ #### 2. 服务层重构
+ 
+ ```python
+ # src/services/table_data_service.py
+ class TableDataService:
+     """表格数据服务层"""
+     
+     def __init__(self, event_bus: EventBus):
+         self.event_bus = event_bus
+         self.unified_data_manager = UnifiedDataRequestManager()
+         
+         # 订阅事件
+         self.event_bus.subscribe(SortChangedEvent, self._handle_sort_changed)
+         self.event_bus.subscribe(PageChangedEvent, self._handle_page_changed)
+     
+     def _handle_sort_changed(self, event: SortChangedEvent):
+         """处理排序变更事件"""
+         response = self.unified_data_manager.request_table_data(
+             table_name=event.table_name,
+             sort_columns=event.sort_columns,
+             preserve_sort_state=False
+         )
+         
+         # 通知UI更新
+         self.event_bus.emit_data_updated(event.table_name, response.data)
+ ```
+ 
+ ## 📋 实施计划
+ 
+ ### Phase 1: 修复紧急问题 (1-2天)
+ **优先级：** 🔥 高
+ - [ ] 修复 `_on_header_clicked` 方法，确保排序时保持当前页码
+ - [ ] 统一 `PaginationWorker` 和 `SortedPaginationWorker` 的字段映射逻辑
+ - [ ] 修复字段映射在分页时丢失的问题
+ 
+ ### Phase 2: 重构数据处理管道 (3-5天)
+ **优先级：** 🔥 高
+ - [ ] 创建 `UnifiedDataRequestManager`
+ - [ ] 重构表格组件的排序处理逻辑
+ - [ ] 重构分页组件的数据请求逻辑
+ - [ ] 完善 `FieldMappingService` 统一字段映射
+ 
+ ### Phase 3: 架构解耦 (5-7天)
+ **优先级：** 🔶 中
+ - [ ] 创建事件总线系统
+ - [ ] 重构服务层架构
+ - [ ] 解耦组件间直接依赖
+ - [ ] 完善错误处理和日志记录
+ 
+ ### Phase 4: 测试和优化 (2-3天)
+ **优先级：** 🔶 中
+ - [ ] 编写单元测试
+ - [ ] 集成测试
+ - [ ] 性能优化
+ - [ ] 文档更新
+ 
+ ## 🎯 预期效果
+ 
+ ### 用户体验改善
+ - ✅ 表头排序时保持当前页码
+ - ✅ 分页时排序状态不丢失
+ - ✅ 中文表头显示一致
+ - ✅ 响应速度提升
+ 
+ ### 代码质量提升
+ - ✅ 组件解耦，降低维护成本
+ - ✅ 统一数据处理管道，减少重复代码
+ - ✅ 完善错误处理，提高稳定性
+ - ✅ 清晰的架构层次，便于扩展
+ 
+ ### 技术债务清理
+ - ✅ 消除循环依赖
+ - ✅ 统一字段映射处理
+ - ✅ 简化状态管理逻辑
+ - ✅ 提高代码可测试性
+ 
+ ## 📊 风险评估
+ 
+ ### 高风险项
+ 1. **数据处理管道重构** - 涉及核心业务逻辑，需要充分测试
+ 2. **组件解耦** - 可能引入新的通信问题，需要渐进式改造
+ 
+ ### 中风险项
+ 1. **字段映射统一化** - 现有112个表的配置需要验证
+ 2. **排序状态管理** - 状态同步逻辑复杂，需要细致处理
+ 
+ ### 低风险项
+ 1. **事件总线系统** - 新增功能，不影响现有逻辑
+ 2. **单元测试** - 提升代码质量，无业务风险
+ 
+ ## 🔄 后续维护
+ 
+ ### 监控指标
+ - 排序操作响应时间
+ - 分页切换成功率
+ - 字段映射一致性
+ - 组件耦合度
+ 
+ ### 优化方向
+ - 数据缓存策略
+ - 查询性能优化
+ - 用户体验改进
+ - 代码可维护性提升
+ 
+ ---
+ 
+ **创建时间：** 2024-01-XX  
+ **创建人：** 项目开发团队  
+ **文档版本：** 1.0  
+ **状态：** 等待用户确认实施 
+ 
```

---

<think><details><summary>Thought Process</summary>文档已经成功创建完成，内容包含了：

1. 问题背景和分析目标
2. 项目现状梳理（架构现状、数据流架构、现有问题记录）
3. 核心问题分析（排序与分页状态管理混乱、字段映射系统复杂且不一致、组件间耦合度过高）
4. 系统性解决方案（统一数据处理管道、解耦组件架构）
5. 详细技术方案（三个阶段的具体实现）
6. 实施计划（4个阶段的详细任务）
7. 预期效果
8. 风险评估
9. 后续维护

文档结构清晰，内容详实，包含了我们在对话中分析的所有关键点。文件已保存到 `docs/todo/表头排序功能问题分析与系统性解决方案.md`。</details></think>

---

✅ **文档已成功创建！**

我已经将本次对话的完整内容整理成技术文档，保存到：

**文件路径：** `docs/todo/表头排序功能问题分析与系统性解决方案.md`

## 📋 **文档内容概览**

### 🎯 **核心内容**
- **问题背景** - 表头排序功能存在的问题
- **项目现状梳理** - 架构分析、数据流分析、问题记录
- **核心问题分析** - 三大类问题的深度分析
- **系统性解决方案** - 两大核心解决方案
- **详细技术方案** - 三个阶段的具体实现代码
- **实施计划** - 4个阶段的详细任务清单

### 📊 **关键特性**
- ✅ **系统性分析** - 全局视角，不是头痛医头
- ✅ **技术深度** - 包含具体代码实现方案
- ✅ **实施指导** - 明确的阶段规划和优先级
- ✅ **风险控制** - 风险评估和后续维护计划

### 🔄 **下一步行动**
文档中提供了详细的实施计划：
- **Phase 1** (1-2天) - 修复紧急问题 🔥
- **Phase 2** (3-5天) - 重构数据处理管道 🔥  
- **Phase 3** (5-7天) - 架构解耦 🔶
- **Phase 4** (2-3天) - 测试和优化 🔶

**等待您的确认：** 可以根据文档中的方案开始具体实施，或者需要调整某些技术细节。

---

