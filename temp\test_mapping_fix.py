#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的表类型映射和配置生成
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_table_type_mapping():
    """测试表类型映射修复"""
    print("测试表类型映射修复")
    
    try:
        from src.services.table_data_service import TableDataService
        from src.core.unified_state_manager import get_unified_state_manager
        
        # 初始化服务
        state_manager = get_unified_state_manager()
        table_service = TableDataService(state_manager)
        
        # 测试A岗职工表类型映射
        test_table_name = "salary_data_2025_08_a_grade_employees"
        table_type = table_service._extract_table_type_from_name(test_table_name)
        
        print(f"表名: {test_table_name}")
        print(f"映射结果: {table_type}")
        
        if table_type == 'a_grade_employees':
            print("SUCCESS: A岗职工表类型映射修复成功!")
            return True
        else:
            print(f"FAILED: A岗职工表类型映射仍然错误: {table_type}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_registry_matching():
    """测试字段注册系统匹配逻辑"""
    print("\n🧪 测试字段注册系统匹配逻辑")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化字段注册系统
        registry = FieldRegistry()
        registry.load_mappings()
        
        # 测试A岗职工的匹配
        test_table_name = "salary_data_2025_08_a_grade_employees"
        default_mappings = registry._default_mappings.get("table_mappings", {})
        
        matched_config = registry._find_matching_default_config(test_table_name, default_mappings)
        
        if matched_config:
            field_types = matched_config.get('field_types', {})
            print(f"📊 匹配成功，找到配置")
            print(f"📊 字段类型数量: {len(field_types)}")
            
            # 检查是否包含错误的临时工字段
            invalid_fields = {'hourly_rate', 'hours_worked'}
            found_invalid = invalid_fields & set(field_types.keys())
            
            if found_invalid:
                print(f"❌ 仍然包含错误字段: {found_invalid}")
                return False
            else:
                print("✅ 不包含错误的临时工字段")
                
            # 检查是否包含A岗职工应有的字段
            expected_fields = {'position_salary_2025', 'seniority_salary_2025', 'insurance_deduction'}
            found_expected = expected_fields & set(field_types.keys())
            
            if len(found_expected) > 0:
                print(f"✅ 包含A岗职工应有字段: {found_expected}")
                return True
            else:
                print(f"❌ 缺少A岗职工应有字段")
                return False
        else:
            print("❌ 未找到匹配配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证机制"""
    print("\n🧪 测试配置验证机制")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        registry = FieldRegistry()
        
        # 测试验证A岗职工表的错误配置
        test_table = "salary_data_2025_08_a_grade_employees"
        bad_config = {
            'field_types': {
                'employee_id': 'string',
                'hourly_rate': 'currency',  # 这是错误的
                'hours_worked': 'float'     # 这也是错误的
            }
        }
        
        is_valid = registry._validate_match_result(test_table, bad_config)
        
        if not is_valid:
            print("✅ 配置验证机制正确识别了错误配置")
            return True
        else:
            print("❌ 配置验证机制未能识别错误配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始测试修复效果")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_table_type_mapping()
    test2_passed = test_field_registry_matching()
    test3_passed = test_config_validation()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"  表类型映射测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  字段注册匹配测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"  配置验证测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有修复测试通过！系统已经正确修复")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")