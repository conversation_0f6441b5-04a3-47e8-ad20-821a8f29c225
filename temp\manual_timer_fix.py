#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动修复剩余的QTimer问题

简化的修复脚本，逐个处理剩余的QTimer导入和调用。

创建时间: 2025-07-25
"""

import sys
import os
import re
from pathlib import Path

def fix_remaining_qtimer_issues():
    """修复剩余的QTimer问题"""
    
    print("🔧 [手动修复] 开始修复剩余的QTimer问题...")
    
    target_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        # 读取文件内容
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 逐个处理剩余的QTimer.singleShot调用
        print("🔧 [步骤1] 替换剩余的QTimer.singleShot调用...")
        
        qtimer_singleshot_patterns = [
            (r'from PyQt5\.QtCore import QTimer, QThread, QTimer\s*\n\s*QTimer\.singleShot\((\d+),\s*([^)]+)\)', 
             r'safe_single_shot(\1, \2)'),
            (r'from PyQt5\.QtCore import Qt, QThread, QTimer\s*\n\s*QTimer\.singleShot\((\d+),\s*([^)]+)\)', 
             r'safe_single_shot(\1, \2)'),
            (r'QTimer\.singleShot\((\d+),\s*([^)]+)\)', 
             r'safe_single_shot(\1, \2)')
        ]
        
        # 应用替换
        for pattern, replacement in qtimer_singleshot_patterns:
            old_content = content
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            if content != old_content:
                matches = len(re.findall(pattern, old_content, flags=re.MULTILINE))
                print(f"  ✅ 替换了 {matches} 个匹配项: {pattern[:50]}...")
        
        # 清除剩余的独立QTimer导入
        print("🔧 [步骤2] 清除剩余的独立QTimer导入...")
        
        import_patterns = [
            r'\s*from PyQt5\.QtCore import QTimer, QThread, QTimer\s*\n',
            r'\s*from PyQt5\.QtCore import Qt, QThread, QTimer\s*\n',
            r'\s*from PyQt5\.QtCore import QTimer\s*\n'
        ]
        
        for pattern in import_patterns:
            old_content = content
            content = re.sub(pattern, '\n', content, flags=re.MULTILINE)
            if content != old_content:
                matches = len(re.findall(pattern, old_content, flags=re.MULTILINE))
                if matches > 0:
                    print(f"  ✅ 移除了 {matches} 个导入: {pattern[:30]}...")
        
        # 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 写入修改后的文件
        if content != original_content:
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复已写入文件: {target_file}")
            
            # 统计修复结果
            remaining_qtimer_imports = len(re.findall(r'from PyQt5\.QtCore import.*QTimer', content))
            remaining_qtimer_singleshot = len(re.findall(r'QTimer\.singleShot', content))
            safe_single_shot_calls = len(re.findall(r'safe_single_shot', content))
            
            print(f"📊 修复结果:")
            print(f"  - 剩余QTimer导入: {remaining_qtimer_imports}")
            print(f"  - 剩余QTimer.singleShot调用: {remaining_qtimer_singleshot}")
            print(f"  - safe_single_shot调用: {safe_single_shot_calls}")
            
            return True
        else:
            print("⚠️ 没有发现需要修复的内容")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

def add_cleanup_method():
    """添加定时器清理方法"""
    
    print("🔧 [步骤3] 添加定时器清理方法...")
    
    target_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有清理方法
        if '_cleanup_timers' in content:
            print("  ✅ 定时器清理方法已存在")
            return True
        
        # 在文件末尾类定义内添加清理方法
        cleanup_method = '''
    def _cleanup_timers(self):
        """🆕 [Qt定时器修复] 清理所有定时器"""
        try:
            if hasattr(self, '_timer_manager') and hasattr(self, '_timer_ids'):
                for timer_id in self._timer_ids.copy():
                    self._timer_manager.stop_timer(timer_id)
                    self._timer_ids.discard(timer_id)
                self.logger.debug("🆕 [定时器清理] 所有定时器已清理")
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"🆕 [定时器清理] 清理定时器时出错: {e}")

    def closeEvent(self, event):
        """🆕 [Qt定时器修复] 重写closeEvent确保定时器清理"""
        try:
            self._cleanup_timers()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"🆕 [定时器清理] closeEvent清理定时器失败: {e}")
        super().closeEvent(event)
'''
        
        # 在最后一个方法后面添加
        # 查找类的结尾位置
        class_end_pattern = r'(\n)(# [\-=]+.*\n.*class|\Z)'
        if re.search(class_end_pattern, content, re.MULTILINE):
            content = re.sub(class_end_pattern, cleanup_method + r'\1\2', content, count=1)
            
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ 已添加定时器清理方法")
            return True
        else:
            print("  ⚠️ 无法找到合适位置添加清理方法")
            return False
            
    except Exception as e:
        print(f"❌ 添加清理方法时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 [手动修复] 开始修复剩余的Qt定时器问题...")
    
    # 修复剩余的QTimer问题
    if fix_remaining_qtimer_issues():
        print("✅ QTimer问题修复成功")
    else:
        print("❌ QTimer问题修复失败")
        return
    
    # 添加清理方法
    if add_cleanup_method():
        print("✅ 定时器清理方法添加成功")
    else:
        print("⚠️ 定时器清理方法添加失败")
    
    print("\n📋 [修复完成] 建议:")
    print("1. 检查语法错误: python -m py_compile src/gui/prototype/widgets/virtualized_expandable_table.py")
    print("2. 测试应用程序运行")
    print("3. 观察是否还有QBasicTimer警告")
    
    print("\n✅ 手动Qt定时器修复完成")

if __name__ == "__main__":
    main() 