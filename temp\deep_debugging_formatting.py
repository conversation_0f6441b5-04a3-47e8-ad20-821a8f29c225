#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试格式化问题
分析实际运行时的格式化状态和数据流
"""

import sys
import os

# 添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def deep_debug_formatting():
    """深度调试格式化问题"""
    print("[DEEP DEBUG] 开始深度调试格式化问题...")
    
    try:
        # 1. 检查UnifiedFormatManager的实际工作状态
        print("\n[DEBUG 1] 检查UnifiedFormatManager状态:")
        
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        unified_formatter = get_unified_format_manager()
        
        # 测试关键字段的格式化
        test_data = [
            (1234, "2025年基础性绩效", "active_employees"),
            (5678, "2025年奖励性绩效预发", "active_employees"),
            ("-", "车补", "active_employees"),
            (999, "代扣代存养老保险", "active_employees"),
            ("01", "人员类别代码", "active_employees")
        ]
        
        print("UnifiedFormatManager的格式化结果:")
        for value, column_name, table_type in test_data:
            try:
                result = unified_formatter.format_display_value(value, column_name, table_type)
                print(f"  {column_name}: {value} -> '{result}'")
            except Exception as e:
                print(f"  {column_name}: 格式化失败 - {e}")
        
        # 2. 检查field_types配置
        print("\n[DEBUG 2] 检查field_types配置:")
        
        field_registry = unified_formatter.field_registry
        active_employee_types = field_registry.get_table_field_types("active_employees")
        salary_data_types = field_registry.get_table_field_types("salary_data_2025_07_active_employees")
        
        print("active_employees 表的字段类型:")
        for field, field_type in active_employee_types.items():
            if field in ["basic_performance_2025", "performance_bonus_2025", "car_allowance", "pension_insurance"]:
                print(f"  {field}: {field_type}")
        
        print("\nsalary_data_2025_07_active_employees 表的字段类型:")
        for field, field_type in salary_data_types.items():
            if field in ["basic_performance_2025", "performance_bonus_2025", "car_allowance", "pension_insurance"]:
                print(f"  {field}: {field_type}")
        
        # 3. 直接测试format_renderer
        print("\n[DEBUG 3] 直接测试FormatRenderer:")
        
        format_renderer = unified_formatter.format_renderer
        
        # 测试float类型的渲染
        print("Float类型渲染测试:")
        for value in [1234, 5678, "-", 999]:
            try:
                result = format_renderer._render_float_value(value, {'decimal_places': 2}, "车补")
                print(f"  _render_float_value({value}, 车补) -> '{result}'")
            except Exception as e:
                print(f"  _render_float_value({value}, 车补) -> 失败: {e}")
        
        # 4. 模拟virtualized_expandable_table的行为
        print("\n[DEBUG 4] 模拟virtualized_expandable_table行为:")
        
        # 模拟表格数据
        mock_table_data = [
            {
                "工号": "20061463",
                "姓名": "张三",
                "2025年基础性绩效": 1234,
                "2025年奖励性绩效预发": 5678,
                "车补": "-",
                "代扣代存养老保险": 999,
                "人员类别代码": "01"
            }
        ]
        
        print("原始数据:")
        for key, value in mock_table_data[0].items():
            print(f"  {key}: {value} (类型: {type(value).__name__})")
        
        # 模拟预格式化检测
        print("\n预格式化检测模拟:")
        
        def simulate_pre_formatted_detection(data):
            """模拟预格式化检测逻辑"""
            if not data:
                return False
            
            first_row = data[0]
            headers = list(first_row.keys())
            
            # 检查货币字段
            for header in headers:
                if '工资' in header or '津贴' in header or '补贴' in header:
                    sample_value = first_row.get(header)
                    if isinstance(sample_value, str) and ('¥' in str(sample_value) or '￥' in str(sample_value)):
                        print(f"    检测到格式化标记: {header} = '{sample_value}'")
                        return True
            
            print("    未检测到预格式化标记")
            return False
        
        is_pre_formatted = simulate_pre_formatted_detection(mock_table_data)
        print(f"_data_pre_formatted 应该设置为: {is_pre_formatted}")
        
        # 5. 模拟_format_cell_value的完整逻辑
        print("\n[DEBUG 5] 模拟_format_cell_value完整逻辑:")
        
        def simulate_format_cell_value(value, column_name, data_pre_formatted):
            """模拟_format_cell_value方法"""
            print(f"\n  模拟格式化: {column_name} = {value}")
            print(f"    _data_pre_formatted: {data_pre_formatted}")
            
            # 检查预格式化状态
            if data_pre_formatted:
                # 验证格式化完整性
                print("    数据已标记为预格式化，执行完整性验证...")
                
                problem_float_fields = [
                    '2025年基础性绩效', '卫生费', '车补', '2025年奖励性绩效预发',
                    '补发', '借支', '2025公积金', '代扣代存养老保险'
                ]
                
                if column_name in problem_float_fields:
                    if value is None or value == '':
                        print("    空值，通过验证")
                        return str(value) if value is not None else ""
                    
                    value_str = str(value)
                    # 检查是否为两位小数格式
                    is_two_decimal = False
                    try:
                        if '.' in value_str:
                            decimal_part = value_str.split('.')[-1]
                            is_two_decimal = len(decimal_part) == 2
                        else:
                            is_two_decimal = False
                    except:
                        is_two_decimal = False
                    
                    print(f"    两位小数格式检查: {is_two_decimal}")
                    
                    if not is_two_decimal:
                        print("    验证失败，应该重新格式化")
                        data_pre_formatted = False  # 重置标记
                    else:
                        print("    验证通过，使用原值")
                        return str(value)
            
            if not data_pre_formatted:
                print("    执行统一格式化...")
                try:
                    result = unified_formatter.format_display_value(value, column_name, "active_employees")
                    print(f"    格式化结果: '{result}'")
                    return result
                except Exception as e:
                    print(f"    格式化失败: {e}")
                    return str(value) if value is not None else ""
            
            return str(value) if value is not None else ""
        
        # 测试关键字段
        for key, value in mock_table_data[0].items():
            if key in ["2025年基础性绩效", "2025年奖励性绩效预发", "车补", "代扣代存养老保险"]:
                result = simulate_format_cell_value(value, key, is_pre_formatted)
                print(f"  最终结果: {key} -> '{result}'")
        
        print("\n[DEEP DEBUG] 深度调试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 深度调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    deep_debug_formatting()