#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理A岗职工缓存并验证配置的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def clear_a_grade_cache():
    """清理A岗职工相关缓存"""
    print("=== 清理A岗职工缓存 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化field_registry
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 强制清理A岗缓存
        field_registry.force_clear_a_grade_cache()
        print("A岗职工缓存清理完成")
        
        return field_registry
        
    except Exception as e:
        print(f"缓存清理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_a_grade_config(field_registry):
    """验证A岗职工配置"""
    print("\n=== 验证A岗职工配置 ===")
    
    try:
        # 测试直接表名
        table_name = 'salary_data_2025_08_a_grade_employees'
        print(f"测试表名: {table_name}")
        
        # 测试get_display_fields
        display_fields = field_registry.get_display_fields(table_name)
        print(f"Display fields数量: {len(display_fields)}")
        
        if display_fields:
            print("前10个字段:")
            for i, field in enumerate(display_fields[:10]):
                print(f"  {i+1}. {field}")
            
            # 检查"人员类别代码"位置
            if 'employee_type_code' in display_fields:
                pos = display_fields.index('employee_type_code')
                print(f"\n人员类别代码位置: 第{pos+1}个")
                
                # 检查是否在"部门名称"之后
                if 'department' in display_fields:
                    dept_pos = display_fields.index('department')
                    print(f"部门名称位置: 第{dept_pos+1}个")
                    
                    if pos > dept_pos:
                        print("人员类别代码在部门名称之后 - 正确")
                    else:
                        print("人员类别代码在部门名称之前 - 错误")
                else:
                    print("未找到部门名称字段")
            else:
                print("未找到人员类别代码字段")
        else:
            print("Display fields为空")
        
        # 测试通用类型
        generic_type = 'a_grade_employees'
        print(f"\n测试通用类型: {generic_type}")
        display_fields_generic = field_registry.get_display_fields(generic_type)
        print(f"Generic display fields数量: {len(display_fields_generic)}")
        
        return len(display_fields) > 0
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_type_identification():
    """测试表类型识别"""
    print("\n=== 测试表类型识别 ===")
    
    try:
        from src.services.table_data_service import TableDataService
        
        # 创建服务实例
        service = TableDataService()
        
        # 测试不同的表名
        test_names = [
            'salary_data_2025_08_a_grade_employees',
            'a_grade_employees',
            'A岗职工',
            'a岗'
        ]
        
        for name in test_names:
            table_type = service._extract_table_type_from_name(name)
            print(f"  {name} -> {table_type}")
            
            if table_type == 'a_grade_employees':
                print("    正确识别")
            else:
                print(f"    错误识别，应该是 a_grade_employees")
        
        return True
        
    except Exception as e:
        print(f"表类型识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始清理A岗职工缓存并验证配置")
    print("=" * 60)
    
    # 步骤1: 清理缓存
    field_registry = clear_a_grade_cache()
    
    # 步骤2: 验证配置
    config_ok = False
    if field_registry:
        config_ok = verify_a_grade_config(field_registry)
    
    # 步骤3: 测试表类型识别
    type_id_ok = test_table_type_identification()
    
    print("\n" + "=" * 60)
    print("修复验证结果汇总:")
    print(f"  缓存清理: {'成功' if field_registry else '失败'}")
    print(f"  配置验证: {'通过' if config_ok else '失败'}")
    print(f"  类型识别: {'通过' if type_id_ok else '失败'}")
    
    if field_registry and config_ok and type_id_ok:
        print("\n所有修复验证通过！")
        print("现在可以启动系统测试A岗职工功能")
    else:
        print("\n部分修复验证失败，需要进一步检查")