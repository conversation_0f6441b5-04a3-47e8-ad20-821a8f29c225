"""
月度工资异动处理系统 - 异动原因分析器

本模块负责：
- 异动原因的智能分析
- 异动原因的分类和标记
- 异动合理性评估
- 异动处理建议生成

分析维度：
1. 时间维度分析 - 异动发生的时间模式
2. 人员维度分析 - 个人历史异动模式
3. 部门维度分析 - 部门异动模式
4. 金额维度分析 - 异动金额合理性
5. 政策维度分析 - 是否符合政策规定

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, date, timedelta
from enum import Enum
import re

from src.utils.log_config import get_module_logger
from src.core.config_manager import ConfigManager

logger = get_module_logger(__name__)


class ReasonCategory(Enum):
    """异动原因分类"""
    POLICY_ADJUSTMENT = "政策调整"
    POSITION_PROMOTION = "职务晋升"
    SALARY_REVIEW = "薪资调整"
    NEW_RECRUITMENT = "新员工入职"
    RESIGNATION = "员工离职"
    OVERTIME_COMPENSATION = "加班补偿"
    PERFORMANCE_BONUS = "绩效奖金"
    ALLOWANCE_ADJUSTMENT = "津贴调整"
    DEDUCTION_PENALTY = "扣款处罚"
    RETROACTIVE_PAY = "补发工资"
    SYSTEM_ERROR = "系统错误"
    DATA_CORRECTION = "数据更正"
    UNKNOWN = "原因不明"


class ReasonAnalyzer:
    """异动原因分析器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化异动原因分析器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.analysis_history: List[Dict[str, Any]] = []
        
        # 分析规则配置
        self.reason_patterns = self._init_reason_patterns()
        self.policy_keywords = self._init_policy_keywords()
        self.suspicious_thresholds = {
            "large_amount": 50000,  # 大额异动阈值
            "frequent_changes": 3,   # 频繁异动阈值（月内次数）
            "unusual_timing": 25     # 异常时间阈值（月末几天）
        }
        
        logger.info("异动原因分析器初始化完成")
    
    def analyze_change_reason(
        self,
        change_record: Dict[str, Any],
        calculation_result: Optional[Dict[str, Any]] = None,
        historical_data: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        分析异动原因
        
        Args:
            change_record: 异动记录
            calculation_result: 计算结果
            historical_data: 历史异动数据
            
        Returns:
            Dict[str, Any]: 原因分析结果
        """
        try:
            employee_id = change_record.get('employee_id', '')
            change_type = change_record.get('change_type', '')
            
            logger.info(f"开始分析异动原因，员工: {employee_id}, 异动类型: {change_type}")
            
            # 执行多维度分析
            time_analysis = self._analyze_timing_patterns(change_record)
            amount_analysis = self._analyze_amount_patterns(change_record, calculation_result)
            frequency_analysis = self._analyze_frequency_patterns(change_record, historical_data)
            policy_analysis = self._analyze_policy_compliance(change_record)
            pattern_analysis = self._analyze_change_patterns(change_record, historical_data)
            
            # 综合分析结果
            comprehensive_analysis = self._synthesize_analysis_results(
                change_record,
                time_analysis,
                amount_analysis,
                frequency_analysis,
                policy_analysis,
                pattern_analysis
            )
            
            # 生成原因推断和建议
            reason_inference = self._infer_reason(comprehensive_analysis)
            recommendations = self._generate_recommendations(comprehensive_analysis)
            
            # 构建最终分析结果
            analysis_result = {
                "employee_id": employee_id,
                "change_type": change_type,
                "analysis_dimensions": {
                    "time_analysis": time_analysis,
                    "amount_analysis": amount_analysis,
                    "frequency_analysis": frequency_analysis,
                    "policy_analysis": policy_analysis,
                    "pattern_analysis": pattern_analysis
                },
                "reason_inference": reason_inference,
                "recommendations": recommendations,
                "analysis_time": datetime.now(),
                "confidence_score": self._calculate_confidence_score(comprehensive_analysis)
            }
            
            # 记录分析历史
            self._record_analysis(analysis_result)
            
            logger.info(f"异动原因分析完成，推断原因: {reason_inference.get('primary_reason', '未知')}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"异动原因分析失败: {str(e)}")
            return self._create_error_analysis(str(e))
    
    def batch_analyze_reasons(
        self,
        change_records: List[Dict[str, Any]],
        calculation_results: Optional[List[Dict[str, Any]]] = None,
        historical_data_dict: Optional[Dict[str, List[Dict[str, Any]]]] = None
    ) -> List[Dict[str, Any]]:
        """
        批量分析异动原因
        
        Args:
            change_records: 异动记录列表
            calculation_results: 计算结果列表
            historical_data_dict: 历史数据字典 {employee_id: [historical_records]}
            
        Returns:
            List[Dict[str, Any]]: 分析结果列表
        """
        try:
            logger.info(f"开始批量分析异动原因，共 {len(change_records)} 条记录")
            
            results = []
            for i, change_record in enumerate(change_records):
                employee_id = change_record.get('employee_id', '')
                
                # 获取对应的计算结果和历史数据
                calculation_result = calculation_results[i] if calculation_results and i < len(calculation_results) else None
                historical_data = historical_data_dict.get(employee_id) if historical_data_dict else None
                
                # 分析单个异动原因
                analysis_result = self.analyze_change_reason(change_record, calculation_result, historical_data)
                results.append(analysis_result)
                
                # 记录进度
                if (i + 1) % 50 == 0:
                    logger.info(f"批量分析进度: {i + 1}/{len(change_records)}")
            
            logger.info(f"批量分析完成，成功分析 {len(results)} 条记录")
            return results
            
        except Exception as e:
            logger.error(f"批量分析异动原因失败: {str(e)}")
            return []
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """
        获取分析统计摘要
        
        Returns:
            Dict[str, Any]: 分析统计信息
        """
        try:
            if not self.analysis_history:
                return {"message": "无分析历史记录"}
            
            # 统计原因分类
            reason_counts = {}
            confidence_scores = []
            
            for record in self.analysis_history:
                reason_inference = record.get('reason_inference', {})
                primary_reason = reason_inference.get('primary_reason', '未知')
                confidence = record.get('confidence_score', 0)
                
                reason_counts[primary_reason] = reason_counts.get(primary_reason, 0) + 1
                confidence_scores.append(confidence)
            
            # 计算置信度统计
            if confidence_scores:
                avg_confidence = sum(confidence_scores) / len(confidence_scores)
                high_confidence_count = len([c for c in confidence_scores if c >= 0.8])
                medium_confidence_count = len([c for c in confidence_scores if 0.6 <= c < 0.8])
                low_confidence_count = len([c for c in confidence_scores if c < 0.6])
            else:
                avg_confidence = 0
                high_confidence_count = medium_confidence_count = low_confidence_count = 0
            
            summary = {
                "总分析次数": len(self.analysis_history),
                "原因分布": reason_counts,
                "置信度统计": {
                    "平均置信度": round(avg_confidence, 3),
                    "高置信度(≥0.8)": high_confidence_count,
                    "中等置信度(0.6-0.8)": medium_confidence_count,
                    "低置信度(<0.6)": low_confidence_count
                },
                "统计时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            logger.info("分析统计摘要生成完成")
            return summary
            
        except Exception as e:
            logger.error(f"生成分析统计摘要失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_timing_patterns(self, change_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析时间模式
        
        Args:
            change_record: 异动记录
            
        Returns:
            Dict[str, Any]: 时间分析结果
        """
        try:
            period = change_record.get('period', '')
            detected_time = change_record.get('detected_time', datetime.now())
            
            # 解析期间信息
            if period:
                try:
                    year, month = period.split('-')
                    period_date = datetime(int(year), int(month), 1)
                    days_in_month = (datetime(int(year), int(month) + 1, 1) - timedelta(days=1)).day
                    current_day = detected_time.day if detected_time.month == int(month) else days_in_month
                except:
                    period_date = detected_time
                    current_day = detected_time.day
                    days_in_month = 30
            else:
                period_date = detected_time
                current_day = detected_time.day
                days_in_month = 30
            
            # 时间模式分析
            is_month_end = current_day >= days_in_month - 5  # 月末5天内
            is_month_start = current_day <= 5  # 月初5天内
            is_mid_month = not is_month_end and not is_month_start
            
            timing_analysis = {
                "period": period,
                "analysis_date": detected_time,
                "day_of_month": current_day,
                "is_month_end": is_month_end,
                "is_month_start": is_month_start,
                "is_mid_month": is_mid_month,
                "timing_pattern": "月末" if is_month_end else "月初" if is_month_start else "月中",
                "timing_normal": not (is_month_end and current_day > self.suspicious_thresholds["unusual_timing"])
            }
            
            logger.debug(f"时间分析: {timing_analysis['timing_pattern']}")
            return timing_analysis
            
        except Exception as e:
            logger.error(f"时间模式分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_amount_patterns(
        self,
        change_record: Dict[str, Any],
        calculation_result: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        分析金额模式
        
        Args:
            change_record: 异动记录
            calculation_result: 计算结果
            
        Returns:
            Dict[str, Any]: 金额分析结果
        """
        try:
            if not calculation_result:
                return {"message": "无计算结果，跳过金额分析"}
            
            total_amount = calculation_result.get('total_amount', 0)
            abs_amount = abs(total_amount)
            
            # 金额级别分析
            if abs_amount >= 50000:
                amount_level = "巨额"
            elif abs_amount >= 10000:
                amount_level = "大额"
            elif abs_amount >= 1000:
                amount_level = "中等"
            elif abs_amount >= 100:
                amount_level = "小额"
            else:
                amount_level = "微额"
            
            # 金额合理性分析
            is_large_amount = abs_amount >= self.suspicious_thresholds["large_amount"]
            is_round_number = abs_amount % 100 == 0  # 是否为整百数
            is_suspicious_amount = is_large_amount or (is_round_number and abs_amount >= 1000)
            
            amount_analysis = {
                "total_amount": total_amount,
                "absolute_amount": abs_amount,
                "amount_level": amount_level,
                "is_increase": total_amount > 0,
                "is_decrease": total_amount < 0,
                "is_large_amount": is_large_amount,
                "is_round_number": is_round_number,
                "is_suspicious": is_suspicious_amount,
                "amount_pattern": "正常" if not is_suspicious_amount else "需关注"
            }
            
            logger.debug(f"金额分析: {amount_level}异动，金额: {total_amount:.2f}")
            return amount_analysis
            
        except Exception as e:
            logger.error(f"金额模式分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_frequency_patterns(
        self,
        change_record: Dict[str, Any],
        historical_data: Optional[List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """
        分析频率模式
        
        Args:
            change_record: 当前异动记录
            historical_data: 历史异动数据
            
        Returns:
            Dict[str, Any]: 频率分析结果
        """
        try:
            if not historical_data:
                return {"message": "无历史数据，跳过频率分析"}
            
            current_period = change_record.get('period', '')
            employee_id = change_record.get('employee_id', '')
            
            # 统计当月异动次数
            current_month_changes = 0
            recent_changes = 0
            similar_changes = 0
            
            for record in historical_data:
                if record.get('period') == current_period:
                    current_month_changes += 1
                
                # 最近3个月的异动
                if self._is_recent_period(record.get('period', ''), current_period, months=3):
                    recent_changes += 1
                
                # 相似类型异动
                if record.get('change_type') == change_record.get('change_type'):
                    similar_changes += 1
            
            # 频率模式评估
            is_frequent = current_month_changes >= self.suspicious_thresholds["frequent_changes"]
            is_recurring = similar_changes >= 3
            
            frequency_analysis = {
                "current_month_changes": current_month_changes,
                "recent_3months_changes": recent_changes,
                "similar_type_changes": similar_changes,
                "is_frequent": is_frequent,
                "is_recurring": is_recurring,
                "frequency_pattern": "频繁" if is_frequent else "正常",
                "historical_trend": "递增" if recent_changes > similar_changes else "稳定"
            }
            
            logger.debug(f"频率分析: 当月{current_month_changes}次，历史{similar_changes}次")
            return frequency_analysis
            
        except Exception as e:
            logger.error(f"频率模式分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_policy_compliance(self, change_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析政策合规性
        
        Args:
            change_record: 异动记录
            
        Returns:
            Dict[str, Any]: 政策分析结果
        """
        try:
            change_type = change_record.get('change_type', '')
            details = change_record.get('details', {})
            
            # 检查是否包含政策关键词
            policy_keywords_found = []
            description = details.get('description', '')
            
            for category, keywords in self.policy_keywords.items():
                for keyword in keywords:
                    if keyword in description:
                        policy_keywords_found.append((category, keyword))
            
            # 政策合规性评估
            has_policy_indication = len(policy_keywords_found) > 0
            compliance_level = "高" if has_policy_indication else "需确认"
            
            policy_analysis = {
                "change_type": change_type,
                "policy_keywords_found": policy_keywords_found,
                "has_policy_indication": has_policy_indication,
                "compliance_level": compliance_level,
                "policy_categories": list(set([item[0] for item in policy_keywords_found])),
                "compliance_note": "发现政策相关关键词" if has_policy_indication else "未发现明确政策依据"
            }
            
            logger.debug(f"政策分析: 合规级别{compliance_level}，关键词{len(policy_keywords_found)}个")
            return policy_analysis
            
        except Exception as e:
            logger.error(f"政策合规性分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_change_patterns(
        self,
        change_record: Dict[str, Any],
        historical_data: Optional[List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """
        分析异动模式
        
        Args:
            change_record: 当前异动记录
            historical_data: 历史异动数据
            
        Returns:
            Dict[str, Any]: 模式分析结果
        """
        try:
            change_type = change_record.get('change_type', '')
            
            if not historical_data:
                return {"message": "无历史数据，跳过模式分析"}
            
            # 分析历史模式
            type_history = [r for r in historical_data if r.get('change_type') == change_type]
            
            pattern_analysis = {
                "change_type": change_type,
                "historical_occurrences": len(type_history),
                "is_first_time": len(type_history) == 0,
                "is_common_pattern": len(type_history) >= 2,
                "pattern_consistency": self._calculate_pattern_consistency(type_history),
                "pattern_description": self._describe_pattern(change_type, type_history)
            }
            
            logger.debug(f"模式分析: {change_type}，历史{len(type_history)}次")
            return pattern_analysis
            
        except Exception as e:
            logger.error(f"异动模式分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _synthesize_analysis_results(self, change_record: Dict[str, Any], *analyses) -> Dict[str, Any]:
        """
        综合分析结果
        
        Args:
            change_record: 异动记录
            *analyses: 各维度分析结果
            
        Returns:
            Dict[str, Any]: 综合分析结果
        """
        try:
            time_analysis, amount_analysis, frequency_analysis, policy_analysis, pattern_analysis = analyses
            
            # 计算综合风险评分
            risk_factors = []
            
            # 时间风险
            if not time_analysis.get('timing_normal', True):
                risk_factors.append("异常时间")
            
            # 金额风险
            if amount_analysis.get('is_suspicious', False):
                risk_factors.append("异常金额")
            
            # 频率风险
            if frequency_analysis.get('is_frequent', False):
                risk_factors.append("频繁异动")
            
            # 政策风险
            if not policy_analysis.get('has_policy_indication', False):
                risk_factors.append("政策依据不明")
            
            # 计算综合评分
            risk_score = len(risk_factors) / 4.0  # 标准化到0-1
            risk_level = "高" if risk_score >= 0.75 else "中" if risk_score >= 0.5 else "中等" if risk_score >= 0.25 else "低"
            
            synthesis = {
                "risk_factors": risk_factors,
                "risk_score": risk_score,
                "risk_level": risk_level,
                "overall_assessment": "需要重点关注" if risk_score >= 0.5 else "正常范围内",
                "key_indicators": self._extract_key_indicators(analyses)
            }
            
            return synthesis
            
        except Exception as e:
            logger.error(f"综合分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _infer_reason(self, comprehensive_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        推断异动原因
        
        Args:
            comprehensive_analysis: 综合分析结果
            
        Returns:
            Dict[str, Any]: 原因推断结果
        """
        try:
            key_indicators = comprehensive_analysis.get('key_indicators', {})
            risk_factors = comprehensive_analysis.get('risk_factors', [])
            
            # 基于关键指标推断原因
            possible_reasons = []
            confidence_factors = []
            
            # 推断逻辑
            if "政策相关" in key_indicators:
                possible_reasons.append(ReasonCategory.POLICY_ADJUSTMENT.value)
                confidence_factors.append(0.8)
            
            if "职务变动" in key_indicators:
                possible_reasons.append(ReasonCategory.POSITION_PROMOTION.value)
                confidence_factors.append(0.7)
            
            if "新员工" in key_indicators:
                possible_reasons.append(ReasonCategory.NEW_RECRUITMENT.value)
                confidence_factors.append(0.9)
            
            if "补发相关" in key_indicators:
                possible_reasons.append(ReasonCategory.RETROACTIVE_PAY.value)
                confidence_factors.append(0.8)
            
            if "扣款相关" in key_indicators:
                possible_reasons.append(ReasonCategory.DEDUCTION_PENALTY.value)
                confidence_factors.append(0.8)
            
            # 如果没有明确指标，使用默认推断
            if not possible_reasons:
                possible_reasons.append(ReasonCategory.SALARY_REVIEW.value)
                confidence_factors.append(0.5)
            
            # 选择最可能的原因
            primary_reason = possible_reasons[0] if possible_reasons else ReasonCategory.UNKNOWN.value
            primary_confidence = max(confidence_factors) if confidence_factors else 0.3
            
            reason_inference = {
                "primary_reason": primary_reason,
                "primary_confidence": primary_confidence,
                "possible_reasons": possible_reasons,
                "inference_method": "基于关键指标的规则推断",
                "inference_time": datetime.now()
            }
            
            logger.debug(f"原因推断: {primary_reason}，置信度: {primary_confidence:.2f}")
            return reason_inference
            
        except Exception as e:
            logger.error(f"原因推断失败: {str(e)}")
            return {"primary_reason": ReasonCategory.UNKNOWN.value, "error": str(e)}
    
    def _generate_recommendations(self, comprehensive_analysis: Dict[str, Any]) -> List[str]:
        """
        生成处理建议
        
        Args:
            comprehensive_analysis: 综合分析结果
            
        Returns:
            List[str]: 建议列表
        """
        try:
            recommendations = []
            risk_level = comprehensive_analysis.get('risk_level', '低')
            risk_factors = comprehensive_analysis.get('risk_factors', [])
            
            # 基于风险等级的建议
            if risk_level == "高":
                recommendations.append("建议人工核查此异动，确认其合规性和准确性")
                recommendations.append("建议联系相关部门确认异动依据")
            elif risk_level == "中":
                recommendations.append("建议关注此异动，可进行抽查验证")
            
            # 基于具体风险因素的建议
            if "异常时间" in risk_factors:
                recommendations.append("异动发生在月末，建议确认是否为正常的月末结算")
            
            if "异常金额" in risk_factors:
                recommendations.append("涉及大额异动，建议核查相关审批文件")
            
            if "频繁异动" in risk_factors:
                recommendations.append("该员工本月异动频繁，建议了解具体情况")
            
            if "政策依据不明" in risk_factors:
                recommendations.append("未找到明确政策依据，建议补充相关说明文档")
            
            # 默认建议
            if not recommendations:
                recommendations.append("异动情况正常，建议按常规流程处理")
            
            logger.debug(f"生成建议: {len(recommendations)}条")
            return recommendations
            
        except Exception as e:
            logger.error(f"生成建议失败: {str(e)}")
            return ["生成建议时出现错误，建议人工处理"]
    
    def _calculate_confidence_score(self, comprehensive_analysis: Dict[str, Any]) -> float:
        """
        计算分析置信度
        
        Args:
            comprehensive_analysis: 综合分析结果
            
        Returns:
            float: 置信度分数 (0-1)
        """
        try:
            base_confidence = 0.7  # 基础置信度
            
            # 根据可用信息调整置信度
            key_indicators = comprehensive_analysis.get('key_indicators', {})
            data_completeness = len(key_indicators) / 5.0  # 假设5个关键指标为完整
            
            # 根据风险评估调整
            risk_score = comprehensive_analysis.get('risk_score', 0)
            risk_adjustment = 0.2 if risk_score < 0.3 else -0.1 if risk_score > 0.7 else 0
            
            final_confidence = min(1.0, max(0.1, base_confidence * data_completeness + risk_adjustment))
            
            return round(final_confidence, 3)
            
        except Exception as e:
            logger.error(f"计算置信度失败: {str(e)}")
            return 0.5
    
    def _extract_key_indicators(self, analyses: Tuple) -> Dict[str, Any]:
        """
        提取关键指标
        
        Args:
            analyses: 分析结果元组
            
        Returns:
            Dict[str, Any]: 关键指标字典
        """
        try:
            time_analysis, amount_analysis, frequency_analysis, policy_analysis, pattern_analysis = analyses
            
            indicators = {}
            
            # 提取关键指标
            if policy_analysis.get('has_policy_indication'):
                indicators["政策相关"] = True
            
            if amount_analysis.get('amount_level') in ['大额', '巨额']:
                indicators["大额异动"] = True
            
            if frequency_analysis.get('is_frequent'):
                indicators["频繁异动"] = True
            
            if time_analysis.get('timing_pattern') == '月末':
                indicators["月末异动"] = True
            
            if pattern_analysis.get('is_first_time'):
                indicators["首次异动"] = True
            
            return indicators
            
        except Exception as e:
            logger.error(f"提取关键指标失败: {str(e)}")
            return {}
    
    def _create_error_analysis(self, error_message: str) -> Dict[str, Any]:
        """
        创建错误分析结果
        
        Args:
            error_message: 错误信息
            
        Returns:
            Dict[str, Any]: 错误分析结果
        """
        return {
            "error": error_message,
            "analysis_time": datetime.now(),
            "reason_inference": {
                "primary_reason": ReasonCategory.UNKNOWN.value,
                "primary_confidence": 0.0
            },
            "recommendations": ["分析过程出现错误，建议人工处理"],
            "confidence_score": 0.0
        }
    
    def _record_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """
        记录分析历史
        
        Args:
            analysis_result: 分析结果
        """
        try:
            history_record = {
                "employee_id": analysis_result.get('employee_id', ''),
                "change_type": analysis_result.get('change_type', ''),
                "reason_inference": analysis_result.get('reason_inference', {}),
                "confidence_score": analysis_result.get('confidence_score', 0),
                "analysis_time": datetime.now()
            }
            
            self.analysis_history.append(history_record)
            
            # 限制历史记录数量
            if len(self.analysis_history) > 5000:
                self.analysis_history = self.analysis_history[-2500:]
                logger.info("清理分析历史记录，保留最近2500条")
            
        except Exception as e:
            logger.error(f"记录分析历史失败: {str(e)}")
    
    def _init_reason_patterns(self) -> Dict[str, List[str]]:
        """
        初始化原因模式
        
        Returns:
            Dict[str, List[str]]: 原因模式字典
        """
        return {
            "政策调整": ["政策", "调整", "改革", "文件", "通知"],
            "职务变动": ["晋升", "提拔", "职务", "岗位", "任命"],
            "绩效相关": ["绩效", "考核", "奖金", "表现", "业绩"],
            "补发工资": ["补发", "补", "追发", "补偿", "调整"],
            "扣款处罚": ["扣", "罚", "处罚", "违规", "纪律"]
        }
    
    def _init_policy_keywords(self) -> Dict[str, List[str]]:
        """
        初始化政策关键词
        
        Returns:
            Dict[str, List[str]]: 政策关键词字典
        """
        return {
            "工资政策": ["工资标准", "薪酬体系", "工资制度"],
            "津贴政策": ["津贴标准", "补贴政策", "福利制度"],
            "人事政策": ["人事制度", "职务管理", "岗位设置"],
            "财务政策": ["财务制度", "支付标准", "预算管理"]
        }
    
    def _is_recent_period(self, period1: str, period2: str, months: int = 3) -> bool:
        """
        判断是否为最近几个月
        
        Args:
            period1: 期间1 (格式: YYYY-MM)
            period2: 期间2 (格式: YYYY-MM)
            months: 月数
            
        Returns:
            bool: 是否在最近几个月内
        """
        try:
            if not period1 or not period2:
                return False
            
            year1, month1 = map(int, period1.split('-'))
            year2, month2 = map(int, period2.split('-'))
            
            date1 = datetime(year1, month1, 1)
            date2 = datetime(year2, month2, 1)
            
            diff_months = (date2.year - date1.year) * 12 + (date2.month - date1.month)
            
            return 0 <= diff_months <= months
            
        except Exception:
            return False
    
    def _calculate_pattern_consistency(self, type_history: List[Dict[str, Any]]) -> float:
        """
        计算模式一致性
        
        Args:
            type_history: 同类型历史记录
            
        Returns:
            float: 一致性分数 (0-1)
        """
        try:
            if len(type_history) <= 1:
                return 0.5
            
            # 简单的一致性计算：基于发生频率
            consistency = min(1.0, len(type_history) / 12.0)  # 假设一年12次为高一致性
            
            return round(consistency, 3)
            
        except Exception:
            return 0.5
    
    def _describe_pattern(self, change_type: str, type_history: List[Dict[str, Any]]) -> str:
        """
        描述异动模式
        
        Args:
            change_type: 异动类型
            type_history: 同类型历史记录
            
        Returns:
            str: 模式描述
        """
        try:
            count = len(type_history)
            
            if count == 0:
                return f"首次发生{change_type}"
            elif count == 1:
                return f"{change_type}偶尔发生"
            elif count <= 3:
                return f"{change_type}偶有发生"
            elif count <= 6:
                return f"{change_type}定期发生"
            else:
                return f"{change_type}频繁发生"
                
        except Exception:
            return "模式不明" 