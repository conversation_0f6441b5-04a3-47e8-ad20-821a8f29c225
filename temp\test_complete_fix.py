# -*- coding: utf-8 -*-
"""
测试完整修复效果
验证所有阶段的修复是否正常工作，特别是用户需求的三个问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_complete_system():
    """测试完整系统修复效果"""
    print("=" * 60)
    print("[COMPLETE FIX TEST] 测试完整系统修复效果")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    try:
        # 1. 测试字段注册系统
        print("\n1. 测试字段注册系统修复...")
        total_tests += 1
        
        from src.modules.format_management.field_registry import FieldRegistry
        field_registry = FieldRegistry("state/data/field_mappings.json")
        
        # 测试智能匹配功能
        default_mappings = field_registry._get_default_mappings()
        retired_config = default_mappings.get("table_mappings", {}).get("retired_employees", {})
        
        if retired_config:
            field_types = retired_config.get("field_types", {})
            
            # 检查用户需求的字段类型
            required_types = {
                "one_time_living_allowance": "float",
                "supplement": "float", 
                "advance": "float",
                "remarks": "string",
                "month": "month_string_extract_last_two"
            }
            
            all_correct = True
            for field, expected_type in required_types.items():
                actual_type = field_types.get(field)
                if actual_type != expected_type:
                    print(f"   [FAIL] {field}: {actual_type} != {expected_type}")
                    all_correct = False
                else:
                    print(f"   [OK] {field}: {actual_type}")
            
            if all_correct:
                print("   [SUCCESS] 字段注册系统修复成功")
                success_count += 1
            else:
                print("   [ERROR] 字段注册系统修复失败")
        else:
            print("   [ERROR] 未找到retired_employees配置")
    
    except Exception as e:
        print(f"   [ERROR] 字段注册系统测试失败: {e}")
    
    # 2. 测试格式渲染系统
    print("\n2. 测试格式渲染系统修复...")
    total_tests += 1
    
    try:
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        format_config = FormatConfig("src/modules/format_management/format_config.json")
        format_config.load_config()
        
        format_renderer = FormatRenderer(format_config, field_registry)
        
        # 测试用户需求的格式化
        test_cases = [
            # 浮点型字段测试
            (None, "float", "0.00"),
            ("nan", "float", "0.00"),
            ("", "float", "0.00"),
            ("-", "float", "0.00"),
            ("0", "float", "0.00"),
            (123.456, "float", "123.46"),
            
            # 字符串字段测试
            (None, "string", ""),
            ("nan", "string", ""),
            ("-", "string", ""),
            ("正常备注", "string", "正常备注"),
            
            # 月份字段测试
            ("202505", "month_string_extract_last_two", "05"),
            ("2025年5月", "month_string_extract_last_two", "05"),
        ]
        
        all_format_correct = True
        for value, field_type, expected in test_cases:
            actual = format_renderer.render_value(value, field_type)
            if actual != expected:
                print(f"   [FAIL] render_value({repr(value)}, {field_type}) = {repr(actual)}, 期望: {repr(expected)}")
                all_format_correct = False
            else:
                print(f"   [OK] render_value({repr(value)}, {field_type}) = {repr(actual)}")
        
        if all_format_correct:
            print("   [SUCCESS] 格式渲染系统修复成功")
            success_count += 1
        else:
            print("   [ERROR] 格式渲染系统修复失败")
    
    except Exception as e:
        print(f"   [ERROR] 格式渲染系统测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试配置同步机制
    print("\n3. 测试配置同步机制修复...")
    total_tests += 1
    
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        from src.core.event_bus import EventBus
        
        event_bus = EventBus()
        config_sync = ConfigSyncManager(
            config_path="state/data/field_mappings.json",
            event_bus=event_bus
        )
        
        # 测试配置保存和事件发布
        test_mapping_data = {
            "field_mappings": {
                "test_field": "测试字段"
            },
            "field_types": {
                "test_field": "string"
            },
            "metadata": {
                "source": "test",
                "created_at": "2025-08-01"
            }
        }
        
        # 测试保存功能
        save_success = config_sync.save_complete_mapping("test_table", test_mapping_data)
        
        if save_success:
            print("   [SUCCESS] 配置同步机制修复成功")
            success_count += 1
        else:
            print("   [ERROR] 配置同步机制修复失败")
    
    except Exception as e:
        print(f"   [ERROR] 配置同步机制测试失败: {e}")
    
    # 4. 测试统一格式管理器
    print("\n4. 测试统一格式管理器修复...")
    total_tests += 1
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 测试事件监听器设置
        format_manager = UnifiedFormatManager(
            config_path="src/modules/format_management/format_config.json",
            field_mapping_path="state/data/field_mappings.json"
        )
        
        # 测试format_display_value方法
        test_values = [
            (None, "增发一次性生活补贴", "retired_employees", "0.00"),
            ("-", "补发", "retired_employees", "0.00"),
            ("", "借支", "retired_employees", "0.00"),
            (None, "备注", "retired_employees", ""),
            ("-", "备注", "retired_employees", ""),
        ]
        
        all_format_manager_correct = True
        for value, column_name, table_type, expected in test_values:
            try:
                actual = format_manager.format_display_value(value, column_name, table_type)
                if actual != expected:
                    print(f"   [FAIL] format_display_value({repr(value)}, {column_name}, {table_type}) = {repr(actual)}, 期望: {repr(expected)}")
                    all_format_manager_correct = False
                else:
                    print(f"   [OK] format_display_value({repr(value)}, {column_name}, {table_type}) = {repr(actual)}")
            except Exception as method_error:
                print(f"   [ERROR] format_display_value测试异常: {method_error}")
                all_format_manager_correct = False
        
        if all_format_manager_correct:
            print("   [SUCCESS] 统一格式管理器修复成功")
            success_count += 1
        else:
            print("   [ERROR] 统一格式管理器修复失败")
    
    except Exception as e:
        print(f"   [ERROR] 统一格式管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("[SUMMARY] 完整修复效果总结")
    print("=" * 60)
    print(f"  成功: {success_count}/{total_tests}")
    print(f"  成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n[SUCCESS] 🎉 所有修复都成功！用户需求应该已经彻底解决")
        return True
    else:
        print(f"\n[WARNING] 还有 {total_tests-success_count} 个修复项需要进一步处理")
        return False

if __name__ == "__main__":
    test_complete_system()