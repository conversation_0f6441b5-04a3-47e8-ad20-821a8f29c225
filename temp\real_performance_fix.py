#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的性能修复 - 直接修复现有代码
解决分页卡顿的根本问题
"""

import os
import sys
import shutil
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.log_config import setup_logger


class RealPerformanceFix:
    """🚀 [真正修复] 直接修复现有代码的性能问题"""
    
    def __init__(self):
        self.logger = setup_logger(__name__ + ".RealPerformanceFix")
        self.backup_dir = f"temp/backups/real_fix_{int(time.time())}"
        os.makedirs(self.backup_dir, exist_ok=True)
        
    def backup_file(self, file_path: str):
        """备份文件"""
        if os.path.exists(file_path):
            backup_path = os.path.join(self.backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            print(f"📦 备份: {file_path} -> {backup_path}")
            return True
        return False
    
    def fix_table_data_service_cache(self):
        """🚀 [核心修复] 修复TableDataService的缓存问题"""
        
        print("🚀 [核心修复] 正在修复TableDataService缓存问题...")
        
        service_file = "src/services/table_data_service.py"
        
        # 备份原文件
        if not self.backup_file(service_file):
            print(f"❌ 文件不存在: {service_file}")
            return False
        
        # 读取原文件
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1: 改进缓存键生成，避免冲突
        old_cache_key_code = '''cache_key = f"{table_name}_{page}_{page_size}_{sort_key}"'''
        new_cache_key_code = '''# 🚀 [性能修复] 生成稳定的缓存键
            import hashlib
            import json
            sort_key_stable = ""
            if sort_columns:
                # 标准化排序参数，确保键的一致性
                normalized_sorts = []
                for col in sort_columns:
                    col_name = str(col.get('column_name', '')).strip()
                    order = str(col.get('order', 'asc')).lower().strip()
                    if col_name:
                        normalized_sorts.append(f"{col_name}:{order}")
                normalized_sorts.sort()  # 确保顺序一致
                sort_key_stable = "|".join(normalized_sorts)
            
            # 使用MD5确保键长度稳定
            key_content = f"{table_name}_{page}_{page_size}_{sort_key_stable}"
            cache_key = f"data_{hashlib.md5(key_content.encode()).hexdigest()[:12]}"'''
        
        content = content.replace(old_cache_key_code, new_cache_key_code)
        
        # 修复2: 添加智能缓存过期机制
        old_cache_check = '''if not force_reload and cache_key in self._data_cache:
                cached_response = self._data_cache[cache_key]
                if self._is_cache_valid(cached_response):
                    self.logger.debug(f"使用缓存数据: {cache_key}")
                    return cached_response'''
        
        new_cache_check = '''if not force_reload and cache_key in self._data_cache:
                cached_response = self._data_cache[cache_key]
                cache_time = getattr(cached_response, '_cache_time', None)
                
                # 🚀 [性能修复] 智能缓存策略
                cache_ttl = 300  # 5分钟缓存
                current_time = time.time()
                
                if cache_time and (current_time - cache_time) < cache_ttl:
                    self.logger.info(f"🚀 [缓存命中] 使用缓存数据: {table_name} 第{page}页")
                    return cached_response
                else:
                    # 缓存过期，移除
                    del self._data_cache[cache_key]
                    self.logger.debug(f"🚀 [缓存过期] 清理过期缓存: {cache_key}")'''
        
        content = content.replace(old_cache_check, new_cache_check)
        
        # 修复3: 添加缓存时间戳
        old_cache_store = '''# 缓存响应
                    self._data_cache[cache_key] = response'''
        
        new_cache_store = '''# 🚀 [性能修复] 缓存响应并添加时间戳
                    setattr(response, '_cache_time', time.time())
                    self._data_cache[cache_key] = response
                    
                    # 🚀 [性能修复] LRU缓存清理 - 限制缓存大小
                    max_cache_size = 50
                    if len(self._data_cache) > max_cache_size:
                        # 移除最老的缓存条目
                        oldest_key = None
                        oldest_time = float('inf')
                        for key, cached_resp in self._data_cache.items():
                            cache_time = getattr(cached_resp, '_cache_time', 0)
                            if cache_time < oldest_time:
                                oldest_time = cache_time
                                oldest_key = key
                        if oldest_key:
                            del self._data_cache[oldest_key]
                            self.logger.debug(f"🚀 [LRU清理] 移除最老缓存: {oldest_key}")'''
        
        content = content.replace(old_cache_store, new_cache_store)
        
        # 修复4: 在文件开头添加time导入（如果没有）
        if 'import time' not in content:
            content = content.replace(
                'import pandas as pd',
                'import pandas as pd\nimport time'
            )
        
        # 写入修改后的文件
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ TableDataService缓存优化完成")
        return True
    
    def fix_main_window_force_reload(self):
        """🔧 [关键修复] 修复主窗口中的force_reload问题"""
        
        print("🔧 [关键修复] 检查主窗口force_reload设置...")
        
        main_window_file = "src/gui/prototype/prototype_main_window.py"
        
        # 备份原文件
        if not self.backup_file(main_window_file):
            print(f"⚠️ 文件不存在: {main_window_file}")
            return False
        
        # 读取原文件
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找和修复force_reload=True的调用
        if 'force_reload=True' in content:
            print("🔧 [发现问题] 找到force_reload=True调用，正在修复...")
            
            # 替换为False以启用缓存
            content = content.replace(
                'force_reload=True',
                'force_reload=False  # 🚀 [性能修复] 启用缓存'
            )
            
            # 写入修改后的文件
            with open(main_window_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已修复force_reload设置")
        else:
            print("✅ 未发现force_reload=True问题")
        
        return True
    
    def optimize_pagination_calls(self):
        """⚡ [分页优化] 优化分页调用逻辑"""
        
        print("⚡ [分页优化] 优化分页调用逻辑...")
        
        # 检查分页组件
        pagination_file = "src/gui/widgets/pagination_widget.py"
        
        if not os.path.exists(pagination_file):
            print(f"⚠️ 分页组件文件不存在: {pagination_file}")
            return False
        
        # 备份原文件
        self.backup_file(pagination_file)
        
        # 读取原文件
        with open(pagination_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加防抖动机制
        debounce_code = '''
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 🚀 [性能修复] 添加防抖动机制
        self._last_page_change_time = 0
        self._debounce_delay = 0.1  # 100ms防抖动
        '''
        
        # 在类定义后查找__init__方法并添加防抖动
        if 'self._last_page_change_time' not in content:
            # 找到类的__init__方法
            init_pos = content.find('def __init__(self')
            if init_pos > 0:
                # 在__init__方法中添加防抖动属性
                init_end = content.find('self.logger.info', init_pos)
                if init_end > 0:
                    insert_pos = content.rfind('\n', init_pos, init_end)
                    content = (content[:insert_pos] + 
                             '\n        # 🚀 [性能修复] 添加防抖动机制\n' +
                             '        self._last_page_change_time = 0\n' +
                             '        self._debounce_delay = 0.1  # 100ms防抖动\n' +
                             content[insert_pos:])
        
        # 添加防抖动检查到set_current_page方法
        if 'def set_current_page(self, page: int):' in content:
            old_set_page = '''def set_current_page(self, page: int):
        """设置当前页码"""'''
            
            new_set_page = '''def set_current_page(self, page: int):
        """设置当前页码"""
        # 🚀 [性能修复] 防抖动检查
        current_time = time.time()
        if hasattr(self, '_last_page_change_time') and (current_time - self._last_page_change_time) < self._debounce_delay:
            self.logger.debug(f"🚀 [防抖动] 忽略快速连续的页面变更请求")
            return
        self._last_page_change_time = current_time'''
            
            content = content.replace(old_set_page, new_set_page)
        
        # 确保导入time模块
        if 'import time' not in content:
            content = 'import time\n' + content
        
        # 写入修改后的文件
        with open(pagination_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 分页防抖动优化完成")
        return True
    
    def add_cache_monitoring(self):
        """📊 [监控] 添加缓存性能监控"""
        
        print("📊 [监控] 添加缓存性能监控...")
        
        service_file = "src/services/table_data_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在类初始化中添加统计
        init_pos = content.find('self._data_cache = {}')
        if init_pos > 0:
            insert_pos = content.find('\n', init_pos) + 1
            stats_code = '''        
        # 🚀 [性能监控] 缓存统计
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
'''
            content = content[:insert_pos] + stats_code + content[insert_pos:]
        
        # 在缓存命中处添加统计
        hit_pos = content.find('🚀 [缓存命中] 使用缓存数据')
        if hit_pos > 0:
            line_start = content.rfind('\n', 0, hit_pos) + 1
            indent = '                    '
            stats_code = f'{indent}self._cache_stats["hits"] += 1\n{indent}self._cache_stats["total_requests"] += 1\n'
            content = content[:line_start] + stats_code + content[line_start:]
        
        # 在缓存未命中处添加统计
        miss_pos = content.find('# 执行请求')
        if miss_pos > 0:
            line_start = content.rfind('\n', 0, miss_pos) + 1
            indent = '                '
            stats_code = f'{indent}self._cache_stats["misses"] += 1\n{indent}self._cache_stats["total_requests"] += 1\n{indent}\n'
            content = content[:line_start] + stats_code + content[line_start:]
        
        # 添加统计报告方法
        class_end = content.rfind('class TableDataService')
        class_block_end = content.find('\n\nclass', class_end)
        if class_block_end == -1:
            class_block_end = len(content)
        
        stats_method = '''
    def get_cache_performance_stats(self) -> dict:
        """🚀 [性能监控] 获取缓存性能统计"""
        total = self._cache_stats["total_requests"]
        hit_rate = (self._cache_stats["hits"] / total * 100) if total > 0 else 0
        
        return {
            "cache_size": len(self._data_cache),
            "total_requests": total,
            "cache_hits": self._cache_stats["hits"],
            "cache_misses": self._cache_stats["misses"],
            "hit_rate_percent": f"{hit_rate:.1f}%",
            "evictions": self._cache_stats["evictions"]
        }
    
    def log_cache_performance(self):
        """🚀 [性能监控] 记录缓存性能"""
        stats = self.get_cache_performance_stats()
        self.logger.info(f"🚀 [缓存统计] {stats}")

'''
        
        content = content[:class_block_end] + stats_method + content[class_block_end:]
        
        # 写入修改后的文件
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 缓存性能监控添加完成")
        return True
    
    def run_all_fixes(self):
        """🚀 [执行] 运行所有真正的性能修复"""
        
        print("🚀 [开始] 应用真正的性能修复")
        print("=" * 60)
        
        success_count = 0
        total_fixes = 4
        
        try:
            # 修复1: TableDataService缓存优化
            if self.fix_table_data_service_cache():
                success_count += 1
            
            # 修复2: force_reload问题
            if self.fix_main_window_force_reload():
                success_count += 1
            
            # 修复3: 分页防抖动
            if self.optimize_pagination_calls():
                success_count += 1
            
            # 修复4: 缓存监控
            if self.add_cache_monitoring():
                success_count += 1
            
            print("\n" + "=" * 60)
            print(f"🎉 [完成] 性能修复应用完成！")
            print(f"✅ 成功修复: {success_count}/{total_fixes}")
            print(f"📦 备份位置: {self.backup_dir}")
            
            if success_count == total_fixes:
                print("\n🚀 [立即生效] 所有修复已应用，现在测试分页性能！")
                print("📊 [监控] 查看日志中的缓存统计信息")
                return True
            else:
                print(f"\n⚠️ [部分完成] {total_fixes - success_count}个修复未完成")
                return False
                
        except Exception as e:
            self.logger.error(f"修复过程发生错误: {e}")
            print(f"❌ [错误] 修复失败: {e}")
            return False


def apply_real_performance_fix():
    """应用真正的性能修复"""
    
    print("🚀 [真正修复] 开始应用解决分页卡顿的真正修复...")
    
    fixer = RealPerformanceFix()
    success = fixer.run_all_fixes()
    
    if success:
        print("""
🎯 [修复完成] 分页性能问题修复总结：

✅ 核心修复：
  1. 智能缓存系统 - 稳定的缓存键 + TTL过期 + LRU清理
  2. 移除强制重载 - 启用数据缓存
  3. 分页防抖动 - 防止快速连续请求
  4. 性能监控 - 实时缓存统计

🚀 立即效果：
  • 重复分页操作应该明显更快
  • 第一次加载稍慢，重复访问瞬时响应
  • 减少数据库查询频率

📊 监控方法：
  • 查看日志中的"🚀 [缓存命中]"信息
  • 观察分页操作响应时间变化
  • 监控"🚀 [缓存统计]"性能数据

⚡ 现在请测试分页功能！
""")
    else:
        print("❌ [失败] 修复未完全成功，请检查错误信息")
    
    return success


if __name__ == "__main__":
    apply_real_performance_fix() 