"""
多Sheet Excel导入示例

展示如何使用MultiSheetImporter处理包含多个Sheet的复杂Excel文件
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import import MultiSheetImporter
from src.utils.log_config import setup_logger

def main():
    """主函数：演示多Sheet Excel导入"""
    
    logger = setup_logger(__name__)
    
    # 1. 创建多Sheet导入器
    print("=== 多Sheet Excel导入示例 ===\n")
    
    # 可以指定配置文件，也可以使用默认配置
    importer = MultiSheetImporter()
    
    # 2. 导出配置模板（用于自定义配置）
    config_template_path = "temp/sheet_config_template.json"
    if importer.export_sheet_config_template(config_template_path):
        print(f"✅ 配置模板已导出到: {config_template_path}")
        print("   你可以修改此文件来自定义Sheet映射规则\n")
    
    # 3. 模拟导入Excel文件
    # 注意：这里使用假设的文件路径，实际使用时替换为真实路径
    excel_file_path = "data/工资表/2025年01月工资数据.xlsx"
    
    print(f"准备导入文件: {excel_file_path}")
    print("假设该文件包含以下Sheet:")
    print("  - 基本信息: 工号、姓名、部门、职位")
    print("  - 工资明细: 工号、基本工资、绩效奖金、加班费")
    print("  - 津贴补贴: 工号、交通补贴、餐补、通讯补贴")
    print("  - 扣款明细: 工号、社保扣款、公积金扣款、个税\n")
    
    # 检查文件是否存在
    if not Path(excel_file_path).exists():
        print("❌ 示例文件不存在，创建模拟导入结果...")
        show_mock_import_result()
        return
    
    # 4. 执行导入
    try:
        result = importer.import_excel_file(
            file_path=excel_file_path,
            year=2025,
            month=1,
            target_table=None  # 使用默认表名: salary_data_2025_01
        )
        
        # 5. 显示导入结果
        show_import_result(result)
        
        # 6. 显示导入统计
        stats = importer.get_import_stats()
        show_import_stats(stats)
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")

def show_mock_import_result():
    """显示模拟的导入结果"""
    
    print("📊 模拟导入结果:")
    print("""
    ✅ 导入成功!
    
    📋 处理详情:
    ├── 目标表: salary_data_2025_01
    ├── 合并记录数: 150
    └── Sheet处理情况:
        ├── 基本信息: 150行 ✅
        ├── 工资明细: 150行 ✅  
        ├── 津贴补贴: 120行 ✅
        └── 扣款明细: 145行 ✅
    
    📈 导入统计:
    ├── 总Sheet数: 4
    ├── 处理Sheet数: 4
    ├── 总记录数: 150
    ├── 失败记录数: 0
    └── 验证错误数: 5
    
    🔧 自动扩展字段:
    ├── transportation_allowance (交通补贴) - REAL
    ├── meal_allowance (餐补) - REAL
    ├── communication_allowance (通讯补贴) - REAL
    ├── social_security_deduction (社保扣款) - REAL
    ├── housing_fund_deduction (公积金扣款) - REAL
    ├── income_tax (个税) - REAL
    ├── data_source (数据源) - TEXT
    └── import_time (导入时间) - TEXT
    """)

def show_import_result(result: dict):
    """显示实际的导入结果"""
    
    print("📊 导入结果:")
    
    if result.get("success"):
        print("✅ 导入成功!")
        print(f"📋 目标表: {result.get('target_table')}")
        print(f"📋 合并记录数: {result.get('merged_records')}")
        
        sheets_data = result.get('sheets_data', {})
        if sheets_data:
            print("📋 Sheet处理情况:")
            for sheet_name, record_count in sheets_data.items():
                print(f"    ├── {sheet_name}: {record_count}行 ✅")
    else:
        print("❌ 导入失败!")
        print(f"错误信息: {result.get('error')}")

def show_import_stats(stats: dict):
    """显示导入统计信息"""
    
    print("\n📈 导入统计:")
    print(f"├── 总Sheet数: {stats.get('total_sheets')}")
    print(f"├── 处理Sheet数: {stats.get('processed_sheets')}")
    print(f"├── 总记录数: {stats.get('total_records')}")
    print(f"├── 失败记录数: {stats.get('failed_records')}")
    print(f"└── 验证错误数: {stats.get('validation_errors')}")

def show_configuration_example():
    """展示配置文件示例"""
    
    print("\n🔧 自定义配置示例:")
    print("""
    你可以创建自己的配置文件来自定义Sheet处理规则:
    
    {
      "import_strategy": "merge_to_single_table",
      "sheet_mappings": {
        "员工基本信息": {
          "enabled": true,
          "field_mappings": {
            "员工编号": "employee_id",
            "员工姓名": "employee_name",
            "所属部门": "department"
          },
          "required_fields": ["employee_id", "employee_name"]
        },
        "薪资明细表": {
          "enabled": true,
          "field_mappings": {
            "员工编号": "employee_id", 
            "基础薪资": "basic_salary",
            "绩效工资": "performance_bonus"
          }
        }
      }
    }
    
    使用自定义配置:
    importer = MultiSheetImporter(config_file="my_config.json")
    """)

if __name__ == "__main__":
    main()
    show_configuration_example() 