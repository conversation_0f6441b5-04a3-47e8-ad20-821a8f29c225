#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的修复验证测试脚本
"""

import sys
import os
import json
from pathlib import Path

def test_unified_state_manager_methods():
    """测试P0-1: UnifiedStateManager方法是否添加成功"""
    print("\n=== P0-1测试: UnifiedStateManager方法修复 ===")
    
    try:
        # 读取文件检查方法是否存在
        state_manager_file = Path("src/core/unified_state_manager.py")
        if not state_manager_file.exists():
            print("ERROR: UnifiedStateManager文件不存在")
            return False
        
        with open(state_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新添加的方法
        methods_to_check = [
            "def save_table_state_by_name",
            "def restore_table_state_by_name"
        ]
        
        for method in methods_to_check:
            if method in content:
                print(f"OK: 找到方法 {method}")
            else:
                print(f"ERROR: 未找到方法 {method}")
                return False
        
        # 检查修复标识
        if "🔧 [P0-1修复]" in content:
            print("OK: 找到P0-1修复标识")
            return True
        else:
            print("ERROR: 未找到P0-1修复标识")
            return False
            
    except Exception as e:
        print(f"ERROR: P0-1测试异常: {e}")
        return False

def test_pagination_ui_update():
    """测试P0-2: 分页UI更新机制修复"""
    print("\n=== P0-2测试: 分页UI更新机制修复 ===")
    
    try:
        # 检查prototype_main_window.py中的修复
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("ERROR: PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes_to_check = [
            "🔧 [P0-2修复] 第一步：先应用字段映射",
            "preserve_headers=False",  # 关键：不保留旧表头
            "_apply_field_mapping_to_dataframe",
            "force_update_all()"
        ]
        
        found_fixes = 0
        for fix in fixes_to_check:
            if fix in content:
                print(f"OK: 找到修复点 {fix}")
                found_fixes += 1
            else:
                print(f"ERROR: 未找到修复点 {fix}")
        
        if found_fixes >= 3:  # 至少找到3个关键修复点
            print(f"OK: P0-2修复完成 ({found_fixes}/{len(fixes_to_check)})")
            return True
        else:
            print(f"ERROR: P0-2修复不完整 ({found_fixes}/{len(fixes_to_check)})")
            return False
            
    except Exception as e:
        print(f"ERROR: P0-2测试异常: {e}")
        return False

def test_field_mapping_config():
    """测试字段映射配置"""
    print("\n=== 字段映射配置测试 ===")
    
    try:
        # 检查字段映射配置文件
        mapping_file = Path("state/data/field_mappings.json")
        if not mapping_file.exists():
            print("ERROR: 字段映射配置文件不存在")
            return False
        
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        
        # 检查目标表的映射
        target_table = "salary_data_2025_08_active_employees"
        if target_table in mappings.get("table_mappings", {}):
            field_mappings = mappings["table_mappings"][target_table].get("field_mappings", {})
            
            # 检查关键字段映射
            key_mappings = {
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称"
            }
            
            for english, chinese in key_mappings.items():
                if english in field_mappings and field_mappings[english] == chinese:
                    print(f"OK: 字段映射正确 {english} -> {chinese}")
                else:
                    print(f"ERROR: 字段映射错误或缺失 {english}")
                    return False
            
            print(f"OK: 找到 {len(field_mappings)} 个字段映射")
            return True
        else:
            print(f"ERROR: 目标表 {target_table} 的映射配置不存在")
            return False
            
    except Exception as e:
        print(f"ERROR: 字段映射测试异常: {e}")
        return False

def test_navigation_panel_retry():
    """测试P1-2: 导航面板重试逻辑"""
    print("\n=== P1-2测试: 导航面板重试逻辑 ===")
    
    try:
        # 检查enhanced_navigation_panel.py中的修复
        nav_panel_file = Path("src/gui/prototype/widgets/enhanced_navigation_panel.py")
        if not nav_panel_file.exists():
            print("ERROR: EnhancedNavigationPanel文件不存在")
            return False
        
        with open(nav_panel_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查重试逻辑修复
        retry_fixes = [
            "🔧 [P1-2修复] 先检查数据库连接状态",
            "is_database_ready()",
            "get_all_table_names()",
            "数据库中暂无表"
        ]
        
        found_fixes = 0
        for fix in retry_fixes:
            if fix in content:
                print(f"OK: 找到重试逻辑修复 {fix}")
                found_fixes += 1
        
        if found_fixes >= 2:
            print(f"OK: P1-2重试逻辑修复完成 ({found_fixes}/{len(retry_fixes)})")
            return True
        else:
            print(f"ERROR: P1-2重试逻辑修复不完整 ({found_fixes}/{len(retry_fixes)})")
            return False
            
    except Exception as e:
        print(f"ERROR: P1-2测试异常: {e}")
        return False

def test_emergency_cleanup():
    """测试P2-1: 紧急清理机制"""
    print("\n=== P2-1测试: 紧急清理机制 ===")
    
    try:
        # 检查prototype_main_window.py中的紧急清理修复
        main_window_file = Path("src/gui/prototype/prototype_main_window.py")
        if not main_window_file.exists():
            print("ERROR: PrototypeMainWindow文件不存在")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查紧急清理修复
        cleanup_fixes = [
            "🔧 [P2-1修复] 紧急状态清理",
            "_setting_data_lock = False",
            "_processing_pagination = False",
            "系统进入安全模式"
        ]
        
        found_fixes = 0
        for fix in cleanup_fixes:
            if fix in content:
                print(f"OK: 找到紧急清理修复 {fix}")
                found_fixes += 1
        
        if found_fixes >= 3:
            print(f"OK: P2-1紧急清理修复完成 ({found_fixes}/{len(cleanup_fixes)})")
            return True
        else:
            print(f"ERROR: P2-1紧急清理修复不完整 ({found_fixes}/{len(cleanup_fixes)})")
            return False
            
    except Exception as e:
        print(f"ERROR: P2-1测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 分页和表头显示修复验证测试 ===")
    print("验证P0-P1-P2优先级修复的代码变更")
    
    tests = [
        ("P0-1: UnifiedStateManager方法修复", test_unified_state_manager_methods),
        ("P0-2: 分页UI更新机制修复", test_pagination_ui_update),
        ("字段映射配置验证", test_field_mapping_config),
        ("P1-2: 导航面板重试逻辑", test_navigation_panel_retry),
        ("P2-1: 紧急清理机制", test_emergency_cleanup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n>>> 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"PASS: {test_name}")
            else:
                print(f"FAIL: {test_name}")
        except Exception as e:
            print(f"EXCEPTION: {test_name} - {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {status} - {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n✓ 所有修复验证通过！")
        print("分页功能应该能够正常工作，表头应该正确显示中文。")
        print("请重新启动系统测试分页和表头显示效果。")
        return True
    else:
        print(f"\n✗ {total - passed} 项测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)