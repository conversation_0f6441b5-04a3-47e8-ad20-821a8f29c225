#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt定时器线程安全问题修复

解决QBasicTimer::stop警告和相关的线程安全问题。

主要问题：
1. 定时器在不同线程中被停止
2. 重复创建定时器实例
3. 对象销毁时定时器未正确清理

创建时间: 2025-07-25
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def fix_timer_issues():
    """修复定时器相关的线程安全问题"""
    
    print("🔧 [Qt修复] 开始修复定时器线程安全问题...")
    
    # 目标文件
    target_file = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1: 清理重复的导入
        fixes_applied = []
        
        # 查找并统计QTimer导入
        import_lines = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if 'from PyQt5.QtCore import' in line and 'QTimer' in line:
                import_lines.append((i + 1, line.strip()))
        
        print(f"📊 发现 {len(import_lines)} 个QTimer相关导入:")
        for line_num, line_content in import_lines:
            print(f"  行 {line_num}: {line_content}")
        
        # 修复2: 检查定时器创建模式
        timer_creations = []
        for i, line in enumerate(lines):
            if '_timer = QTimer()' in line:
                timer_creations.append((i + 1, line.strip()))
        
        print(f"📊 发现 {len(timer_creations)} 个定时器创建:")
        for line_num, line_content in timer_creations:
            print(f"  行 {line_num}: {line_content}")
        
        # 修复3: 检查定时器停止调用
        timer_stops = []
        for i, line in enumerate(lines):
            if '.stop()' in line and ('timer' in line.lower() or 'Timer' in line):
                timer_stops.append((i + 1, line.strip()))
        
        print(f"📊 发现 {len(timer_stops)} 个定时器停止调用:")
        for line_num, line_content in timer_stops:
            print(f"  行 {line_num}: {line_content}")
        
        # 生成修复建议
        print("\n🛠️ [修复建议]:")
        print("1. 将所有QTimer导入移动到文件顶部，避免重复导入")
        print("2. 使用单例模式管理定时器，避免重复创建")
        print("3. 在对象销毁时确保所有定时器被正确清理")
        print("4. 使用QTimer.singleShot的线程安全版本")
        print("5. 添加线程检查，确保定时器操作在主线程进行")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

def create_timer_manager():
    """创建一个线程安全的定时器管理器"""
    
    manager_code = '''
"""
线程安全的定时器管理器

用于管理Qt定时器，确保线程安全性。
"""

from PyQt5.QtCore import QTimer, QThread, QObject, pyqtSignal
from typing import Dict, Callable, Optional
import weakref

class ThreadSafeTimerManager(QObject):
    """线程安全的定时器管理器"""
    
    def __init__(self):
        super().__init__()
        self._timers: Dict[str, QTimer] = {}
        self._callbacks: Dict[str, Callable] = {}
        
    def create_timer(self, timer_id: str, interval: int, callback: Callable, 
                    single_shot: bool = True) -> bool:
        """
        创建或更新定时器
        
        Args:
            timer_id: 定时器唯一标识
            interval: 延迟时间(毫秒)
            callback: 回调函数
            single_shot: 是否单次触发
        
        Returns:
            bool: 是否成功创建
        """
        try:
            # 检查是否在主线程
            if QThread.currentThread() != QThread.currentThread().parent():
                print(f"⚠️ [定时器警告] 尝试在非主线程创建定时器: {timer_id}")
                return False
            
            # 停止现有定时器
            if timer_id in self._timers:
                self.stop_timer(timer_id)
            
            # 创建新定时器
            timer = QTimer()
            timer.setSingleShot(single_shot)
            timer.timeout.connect(callback)
            
            self._timers[timer_id] = timer
            self._callbacks[timer_id] = callback
            
            # 启动定时器
            timer.start(interval)
            
            return True
            
        except Exception as e:
            print(f"❌ [定时器错误] 创建定时器失败 {timer_id}: {e}")
            return False
    
    def stop_timer(self, timer_id: str) -> bool:
        """
        停止指定定时器
        
        Args:
            timer_id: 定时器标识
            
        Returns:
            bool: 是否成功停止
        """
        try:
            if timer_id in self._timers:
                timer = self._timers[timer_id]
                if timer.isActive():
                    timer.stop()
                timer.deleteLater()
                
                del self._timers[timer_id]
                if timer_id in self._callbacks:
                    del self._callbacks[timer_id]
                
                return True
            return False
            
        except Exception as e:
            print(f"❌ [定时器错误] 停止定时器失败 {timer_id}: {e}")
            return False
    
    def stop_all_timers(self):
        """停止所有定时器"""
        timer_ids = list(self._timers.keys())
        for timer_id in timer_ids:
            self.stop_timer(timer_id)
    
    def is_active(self, timer_id: str) -> bool:
        """检查定时器是否活跃"""
        return (timer_id in self._timers and 
                self._timers[timer_id].isActive())

# 全局定时器管理器实例
_timer_manager = None

def get_timer_manager() -> ThreadSafeTimerManager:
    """获取全局定时器管理器实例"""
    global _timer_manager
    if _timer_manager is None:
        _timer_manager = ThreadSafeTimerManager()
    return _timer_manager

def safe_single_shot(delay: int, callback: Callable, timer_id: Optional[str] = None):
    """
    线程安全的单次定时器
    
    Args:
        delay: 延迟时间(毫秒)
        callback: 回调函数
        timer_id: 可选的定时器标识
    """
    if timer_id is None:
        timer_id = f"singleshot_{id(callback)}_{delay}"
    
    manager = get_timer_manager()
    return manager.create_timer(timer_id, delay, callback, single_shot=True)
'''
    
    # 保存定时器管理器
    with open('temp/thread_safe_timer_manager.py', 'w', encoding='utf-8') as f:
        f.write(manager_code)
    
    print("✅ 线程安全定时器管理器已创建: temp/thread_safe_timer_manager.py")

def main():
    """主函数"""
    print("🔧 [Qt定时器修复] 开始诊断和修复...")
    
    # 诊断现有问题
    if fix_timer_issues():
        print("✅ 问题诊断完成")
    else:
        print("❌ 问题诊断失败")
        return
    
    # 创建定时器管理器
    create_timer_manager()
    
    print("\n📋 [下一步建议]:")
    print("1. 在虚拟化表格组件中导入并使用ThreadSafeTimerManager")
    print("2. 替换所有QTimer.singleShot调用为safe_single_shot")
    print("3. 在组件销毁时调用stop_all_timers")
    print("4. 添加线程检查机制")
    
    print("\n✅ Qt定时器安全修复方案已准备完毕")

if __name__ == "__main__":
    main() 