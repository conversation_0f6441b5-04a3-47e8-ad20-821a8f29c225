#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller运行时Hook - 修复bootstrap导入问题

用于解决PyInstaller在Windows环境下的import问题
"""

import sys
import os

# 修复PyQt5路径问题
def fix_pyqt5_path():
    """修复PyQt5导入路径"""
    try:
        # 确保PyQt5可以正确导入
        import PyQt5
        # 添加PyQt5路径到sys.path
        pyqt5_path = os.path.dirname(PyQt5.__file__)
        if pyqt5_path not in sys.path:
            sys.path.insert(0, pyqt5_path)
    except ImportError:
        pass

# 修复pandas导入问题
def fix_pandas_imports():
    """修复pandas相关导入问题"""
    try:
        # 预加载pandas核心模块
        import pandas as pd
        import numpy as np
        # 确保pandas._libs模块正确加载
        import pandas._libs
    except ImportError:
        pass

# 修复编码问题
def fix_encoding():
    """修复Windows下的编码问题"""
    if sys.platform.startswith('win'):
        # 设置默认编码
        import locale
        try:
            # 尝试设置为UTF-8
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except locale.Error:
            try:
                # 如果UTF-8不可用，使用系统默认
                locale.setlocale(locale.LC_ALL, '')
            except locale.Error:
                pass

# 修复资源文件路径
def fix_resource_paths():
    """修复资源文件路径问题"""
    # 获取打包后的资源路径
    if hasattr(sys, '_MEIPASS'):
        # 运行在PyInstaller打包的环境中
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    # 设置环境变量，供应用程序使用
    os.environ['RESOURCE_PATH'] = base_path
    return base_path

# 初始化Hook
def initialize_runtime_hook():
    """初始化运行时Hook"""
    print("🔧 正在初始化运行时Hook...")
    
    try:
        # 执行各种修复
        fix_encoding()
        fix_resource_paths()
        fix_pyqt5_path()
        fix_pandas_imports()
        
        print("✅ 运行时Hook初始化完成")
        
    except Exception as e:
        print(f"⚠️ 运行时Hook初始化警告: {e}")

# 执行初始化
if __name__ == "__main__":
    initialize_runtime_hook()
else:
    # 在导入时自动执行
    initialize_runtime_hook() 