#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试 - 简化版本
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_retired_employees_formatting():
    """测试离休人员表格式化"""
    print("=== 离休人员工资表格式化最终验证 ===")
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 初始化
        format_manager = UnifiedFormatManager(
            config_path="state/format_config.json",
            field_mapping_path="state/data/field_mappings.json"
        )
        
        # 创建测试数据
        test_data = {
            'employee_id': ['001', '002', '003'],
            'employee_name': ['张三', '李四', '王五'],
            'department': ['财务部', '人事部', '技术部'],
            'basic_retirement_salary': [5000.00, None, 0],
            'balance_allowance': [1200.50, np.nan, '0'],
            'living_allowance': [800.25, '', None],
            'supplement': [100.50, 0, ''],
            'total': [7100.75, 0, 0.0],
            'advance': [50.00, None, ''],
            'month': ['202507', '07', '7'],
            'year': ['2025', 2025, '25'],
            'remarks': ['正常', '', None],
            # 应该被隐藏的字段
            'id': [1, 2, 3],
            'sequence_number': [1, 2, 3],
            'created_at': ['2025-07-30', '2025-07-30', '2025-07-30'],
            'updated_at': ['2025-07-30', '2025-07-30', '2025-07-30']
        }
        
        original_df = pd.DataFrame(test_data)
        print(f"原始数据: {len(original_df)} 行 x {len(original_df.columns)} 列")
        
        # 执行格式化
        formatted_headers, formatted_data = format_manager.format_table_complete(
            original_df, "retired_employees"
        )
        
        print(f"格式化后: {len(formatted_data)} 行 x {len(formatted_data.columns)} 列")
        print(f"格式化表头: {formatted_headers}")
        
        # 验证结果
        print("\n=== 验证结果 ===")
        
        # 1. 验证隐藏字段
        hidden_found = [h for h in ['自增主键', '序号', '创建时间', '更新时间'] if h in formatted_headers]
        if not hidden_found:
            print("✓ 隐藏字段功能正常")
        else:
            print(f"✗ 发现未隐藏字段: {hidden_found}")
        
        # 2. 验证浮点数格式化
        salary_columns = [col for col in formatted_data.columns if any(x in col for x in ['离休费', '津贴', '补发', '合计', '借支'])]
        print(f"工资相关列: {salary_columns}")
        
        format_correct = True
        for col in salary_columns:
            if col in formatted_data.columns:
                values = formatted_data[col].tolist()
                print(f"{col}: {values}")
                
                for val in values:
                    if val not in ['0.00', ''] and '.' in str(val):
                        decimal_places = len(str(val).split('.')[-1])
                        if decimal_places != 2:
                            format_correct = False
                            print(f"  ✗ {val} 小数位数不是2位")
        
        if format_correct:
            print("✓ 浮点数格式化正确")
        
        # 3. 验证月份和年份
        if '月份' in formatted_data.columns:
            month_vals = formatted_data['月份'].tolist()
            print(f"月份字段: {month_vals}")
            if all(len(str(v)) == 2 for v in month_vals if v):
                print("✓ 月份格式正确")
            else:
                print("✗ 月份格式不正确")
        
        if '年份' in formatted_data.columns:
            year_vals = formatted_data['年份'].tolist()
            print(f"年份字段: {year_vals}")
            if all(isinstance(v, str) for v in year_vals if v):
                print("✓ 年份格式正确")
            else:
                print("✗ 年份格式不正确")
        
        print("\n=== 格式化完成数据 ===")
        for i, row in formatted_data.iterrows():
            print(f"第{i+1}行: {row.to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_retired_employees_formatting()
    print(f"\n测试结果: {'通过' if success else '失败'}")