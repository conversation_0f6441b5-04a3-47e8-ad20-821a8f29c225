#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入修复测试脚本
测试ConfigSyncManager的设置是否正确修复了数据导入问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_multi_sheet_importer_config_sync():
    """测试MultiSheetImporter的ConfigSyncManager设置"""
    try:
        from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        
        print("🔧 开始测试MultiSheetImporter的ConfigSyncManager设置...")
        
        # 初始化依赖组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建MultiSheetImporter
        multi_sheet_importer = MultiSheetImporter(dynamic_table_manager)
        
        # 检查初始状态
        if multi_sheet_importer.config_sync_manager is None:
            print("✅ 初始状态正确：config_sync_manager为None")
        else:
            print("❌ 初始状态错误：config_sync_manager不为None")
            return False
        
        # 创建ConfigSyncManager
        config_sync_manager = ConfigSyncManager()
        
        # 设置ConfigSyncManager
        multi_sheet_importer.set_config_sync_manager(config_sync_manager)
        
        # 检查设置后的状态
        if multi_sheet_importer.config_sync_manager is not None:
            print("✅ 设置成功：config_sync_manager已设置")
        else:
            print("❌ 设置失败：config_sync_manager仍为None")
            return False
        
        # 测试方法调用
        try:
            # 测试load_mapping方法（应该不会抛出异常）
            result = multi_sheet_importer.config_sync_manager.load_mapping("test_table")
            print(f"✅ load_mapping方法调用成功，结果: {result}")
        except Exception as e:
            print(f"❌ load_mapping方法调用失败: {e}")
            return False
        
        print("🎉 MultiSheetImporter的ConfigSyncManager设置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_import_dialog_integration():
    """测试数据导入对话框的集成"""
    try:
        print("\n🔧 开始测试数据导入对话框集成...")
        
        # 这里只测试导入是否正常，不实际创建对话框
        from src.gui.main_dialogs import DataImportDialog
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        
        # 初始化依赖组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建数据导入对话框（不显示）
        dialog = DataImportDialog(
            parent=None,
            dynamic_table_manager=dynamic_table_manager,
            target_path="工资表 > 2025年 > 07月 > 全部在职人员"
        )
        
        # 检查MultiSheetImporter是否正确创建
        if hasattr(dialog, 'multi_sheet_importer'):
            print("✅ DataImportDialog的multi_sheet_importer创建成功")
        else:
            print("❌ DataImportDialog的multi_sheet_importer创建失败")
            return False
        
        # 检查初始状态
        if dialog.multi_sheet_importer.config_sync_manager is None:
            print("✅ 初始状态正确：multi_sheet_importer的config_sync_manager为None")
        else:
            print("❌ 初始状态错误：multi_sheet_importer的config_sync_manager不为None")
        
        # 测试设置方法是否存在
        if hasattr(dialog.multi_sheet_importer, 'set_config_sync_manager'):
            print("✅ set_config_sync_manager方法存在")
        else:
            print("❌ set_config_sync_manager方法不存在")
            return False
        
        print("🎉 数据导入对话框集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_fields_fix():
    """测试模板字段获取的修复"""
    try:
        print("\n🔧 开始测试模板字段获取修复...")
        
        from src.gui.main_dialogs import DataImportDialog
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        
        # 初始化依赖组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建数据导入对话框
        dialog = DataImportDialog(
            parent=None,
            dynamic_table_manager=dynamic_table_manager,
            target_path="工资表 > 2025年 > 07月 > 全部在职人员"
        )
        
        # 测试_get_template_fields方法（应该不会抛出异常）
        try:
            result = dialog._get_template_fields()
            print(f"✅ _get_template_fields方法调用成功，结果: {result}")
        except Exception as e:
            print(f"❌ _get_template_fields方法调用失败: {e}")
            return False
        
        print("🎉 模板字段获取修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始数据导入修复测试...")
    
    # 测试MultiSheetImporter的ConfigSyncManager设置
    test1_success = test_multi_sheet_importer_config_sync()
    
    # 测试数据导入对话框集成
    test2_success = test_data_import_dialog_integration()
    
    # 测试模板字段获取修复
    test3_success = test_template_fields_fix()
    
    if test1_success and test2_success and test3_success:
        print("\n🎉 所有数据导入修复测试通过！")
        print("✅ ConfigSyncManager设置问题已修复")
        print("✅ 数据导入应该可以正常工作")
        sys.exit(0)
    else:
        print("\n💥 数据导入修复测试失败！")
        sys.exit(1)
