#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证脚本

重启系统后运行此脚本，验证排序和分页修复是否已生效
"""

import sys
import os
from pathlib import Path
import importlib

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def verify_data_updated_event_fix():
    """验证DataUpdatedEvent修复是否生效"""
    print("🔧 [验证1] 检查DataUpdatedEvent修复...")
    
    try:
        # 重新导入模块，确保获取最新代码
        if 'src.services.table_data_service' in sys.modules:
            importlib.reload(sys.modules['src.services.table_data_service'])
        
        from src.services.table_data_service import TableDataService
        from src.core.event_bus import DataUpdatedEvent
        
        # 检查源代码是否包含event_type参数
        import inspect
        source = inspect.getsource(TableDataService._handle_sort_request)
        
        if 'event_type="data_updated"' in source:
            print("   ✅ 排序事件修复已应用")
        else:
            print("   ❌ 排序事件修复未应用")
            return False
            
        source = inspect.getsource(TableDataService._handle_page_request)
        if 'event_type="data_updated"' in source:
            print("   ✅ 分页事件修复已应用")
        else:
            print("   ❌ 分页事件修复未应用")
            return False
            
        # 测试创建DataUpdatedEvent
        event = DataUpdatedEvent(
            event_type="data_updated",
            table_name="test",
            data=[],
            metadata={}
        )
        print("   ✅ DataUpdatedEvent创建成功")
        return True
        
    except Exception as e:
        print(f"   ❌ DataUpdatedEvent验证失败: {e}")
        return False

def verify_pagination_silent_mode_fix():
    """验证分页静默模式修复是否生效"""
    print("\n🔧 [验证2] 检查分页静默模式修复...")
    
    try:
        # 重新导入模块
        if 'src.gui.widgets.pagination_widget' in sys.modules:
            importlib.reload(sys.modules['src.gui.widgets.pagination_widget'])
            
        from src.gui.widgets.pagination_widget import PaginationWidget
        import inspect
        
        # 检查源代码是否包含静默模式方法
        source = inspect.getsource(PaginationWidget)
        
        if 'silent_set_current_page' in source:
            print("   ✅ 静默分页方法已添加")
        else:
            print("   ❌ 静默分页方法未添加")
            return False
            
        if '_silent_update_mode' in source:
            print("   ✅ 静默模式标志已添加")
        else:
            print("   ❌ 静默模式标志未添加")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ 分页静默模式验证失败: {e}")
        return False

def verify_sort_state_cycle_fix():
    """验证排序状态三态循环修复是否生效"""
    print("\n🔧 [验证3] 检查排序状态三态循环修复...")
    
    try:
        # 重新导入模块
        if 'src.gui.prototype.widgets.column_sort_manager' in sys.modules:
            importlib.reload(sys.modules['src.gui.prototype.widgets.column_sort_manager'])
            
        from src.gui.prototype.widgets.column_sort_manager import NewArchitectureColumnSortManager, SortOrder
        import inspect
        
        # 检查源代码是否包含正确的排序循环
        source = inspect.getsource(NewArchitectureColumnSortManager._get_next_sort_order)
        
        if 'SortOrder.DESCENDING: SortOrder.NONE' in source:
            print("   ✅ 排序三态循环修复已应用")
            return True
        else:
            print("   ❌ 排序三态循环修复未应用")
            return False
            
    except Exception as e:
        print(f"   ❌ 排序状态循环验证失败: {e}")
        return False

def check_system_startup_time():
    """检查系统启动时间，确认是否为最新启动"""
    print("\n🕐 [验证4] 检查系统启动时间...")
    
    try:
        with open("logs/salary_system.log", "r", encoding="utf-8") as f:
            lines = f.readlines()
            
        # 查找最新的初始化日志
        latest_init = None
        for line in reversed(lines):
            if "月度工资异动处理系统 v1.0.0 初始化完成" in line:
                latest_init = line.split("|")[0].strip()
                break
                
        if latest_init:
            print(f"   📅 最新系统启动时间: {latest_init}")
            
            # 检查是否是最近启动的（比如在过去30分钟内）
            from datetime import datetime, timedelta
            try:
                startup_time = datetime.strptime(latest_init, "%Y-%m-%d %H:%M:%S.%f")
                now = datetime.now()
                
                if now - startup_time < timedelta(minutes=30):
                    print("   ✅ 系统最近已重启，修复应该已生效")
                    return True
                else:
                    print("   ⚠️ 系统启动时间较早，建议重启系统应用修复")
                    return False
            except:
                print("   ⚠️ 无法解析启动时间格式")
                return False
        else:
            print("   ❌ 未找到系统启动记录")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查启动时间失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 验证排序和分页修复是否已生效...")
    print("="*60)
    
    # 执行各项验证
    test1 = verify_data_updated_event_fix()
    test2 = verify_pagination_silent_mode_fix()
    test3 = verify_sort_state_cycle_fix()
    test4 = check_system_startup_time()
    
    print("\n" + "="*60)
    print("📋 验证结果总结：")
    print(f"   - DataUpdatedEvent修复: {'✅ 已生效' if test1 else '❌ 未生效'}")
    print(f"   - 分页静默模式修复: {'✅ 已生效' if test2 else '❌ 未生效'}")
    print(f"   - 排序三态循环修复: {'✅ 已生效' if test3 else '❌ 未生效'}")
    print(f"   - 系统重启状态: {'✅ 最近重启' if test4 else '⚠️ 需要重启'}")
    
    if test1 and test2 and test3 and test4:
        print("\n🎉 所有修复已成功应用！")
        print("\n🚀 现在可以测试排序和分页功能：")
        print("   1. 启动主程序")
        print("   2. 导入数据")
        print("   3. 切换到第5页")
        print("   4. 点击表头排序（应该立即看到效果）")
        print("   5. 切换分页（应该保持排序状态）")
    elif not test4:
        print("\n🔄 请重启系统以应用修复：")
        print("   1. 关闭当前系统")
        print("   2. 重新运行 python main.py")
        print("   3. 再次运行此验证脚本确认修复生效")
    else:
        print("\n❌ 部分修复可能有问题，请检查代码更新")
        
    print("\n💡 如果修复已生效但功能仍有问题，请运行系统并提供最新的操作日志") 