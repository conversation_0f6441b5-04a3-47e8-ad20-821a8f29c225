#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能分析器 - Performance Analyzer

深度分析分页处理的每个环节，定位5秒延迟的根本原因。

设计目标：
- 识别分页处理链路中的性能瓶颈
- 提供详细的时间分解分析  
- 生成优化建议和修复方案

Author: Claude Code
Created: 2025-07-26
"""

import time
import functools
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
# import pandas as pd  # 可选依赖

@dataclass
class PerformanceMetric:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: float = 0
    duration_ms: float = 0
    memory_before: int = 0
    memory_after: int = 0
    thread_id: str = ""
    stack_depth: int = 0
    params: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.current_operations: Dict[str, PerformanceMetric] = {}
        self.operation_stack: List[str] = []
        self._lock = threading.Lock()
        self.enabled = True
        
    def start_operation(self, operation_name: str, params: Dict[str, Any] = None) -> str:
        """开始性能监控"""
        if not self.enabled:
            return ""
            
        operation_id = f"{operation_name}_{int(time.time() * 1000000)}"
        
        with self._lock:
            metric = PerformanceMetric(
                operation_name=operation_name,
                start_time=time.time(),
                thread_id=threading.current_thread().name,
                stack_depth=len(self.operation_stack),
                params=params or {}
            )
            
            # 尝试获取内存使用情况
            try:
                import psutil
                process = psutil.Process()
                metric.memory_before = process.memory_info().rss // 1024 // 1024  # MB
            except:
                metric.memory_before = 0
            
            self.current_operations[operation_id] = metric
            self.operation_stack.append(operation_name)
            
        return operation_id
    
    def end_operation(self, operation_id: str, error: str = None):
        """结束性能监控"""
        if not self.enabled or not operation_id:
            return
            
        with self._lock:
            if operation_id not in self.current_operations:
                return
                
            metric = self.current_operations[operation_id]
            metric.end_time = time.time()
            metric.duration_ms = (metric.end_time - metric.start_time) * 1000
            metric.error = error
            
            # 尝试获取内存使用情况
            try:
                import psutil
                process = psutil.Process()
                metric.memory_after = process.memory_info().rss // 1024 // 1024  # MB
            except:
                metric.memory_after = metric.memory_before
            
            self.metrics.append(metric)
            del self.current_operations[operation_id]
            
            if self.operation_stack and self.operation_stack[-1] == metric.operation_name:
                self.operation_stack.pop()
    
    def analyze_pagination_performance(self) -> Dict[str, Any]:
        """分析分页性能"""
        pagination_metrics = [m for m in self.metrics if 'page' in m.operation_name.lower()]
        
        if not pagination_metrics:
            return {"error": "没有找到分页相关的性能数据"}
        
        analysis = {
            "total_operations": len(pagination_metrics),
            "total_time_ms": sum(m.duration_ms for m in pagination_metrics),
            "avg_time_ms": sum(m.duration_ms for m in pagination_metrics) / len(pagination_metrics),
            "slowest_operations": [],
            "bottlenecks": [],
            "recommendations": []
        }
        
        # 找出最慢的操作
        sorted_metrics = sorted(pagination_metrics, key=lambda x: x.duration_ms, reverse=True)
        analysis["slowest_operations"] = [
            {
                "operation": m.operation_name,
                "duration_ms": round(m.duration_ms, 2),
                "memory_usage_mb": m.memory_after - m.memory_before,
                "params": m.params
            }
            for m in sorted_metrics[:5]
        ]
        
        # 识别瓶颈
        for metric in sorted_metrics:
            if metric.duration_ms > 1000:  # 超过1秒的操作
                analysis["bottlenecks"].append({
                    "operation": metric.operation_name,
                    "duration_ms": round(metric.duration_ms, 2),
                    "issue": "执行时间过长",
                    "severity": "high" if metric.duration_ms > 3000 else "medium"
                })
        
        # 生成优化建议
        if analysis["avg_time_ms"] > 500:
            analysis["recommendations"].append("分页平均响应时间过长，建议优化数据库查询")
        
        if any(m.memory_after - m.memory_before > 50 for m in pagination_metrics):
            analysis["recommendations"].append("发现内存使用量大的操作，建议优化内存管理")
            
        return analysis
    
    def generate_detailed_report(self) -> str:
        """生成详细报告"""
        if not self.metrics:
            return "没有性能数据可分析"
        
        report = []
        report.append("=" * 60)
        report.append("性能分析报告")
        report.append("=" * 60)
        
        # 总体统计
        total_time = sum(m.duration_ms for m in self.metrics)
        report.append(f"总操作数: {len(self.metrics)}")
        report.append(f"总耗时: {total_time:.2f}ms")
        report.append(f"平均耗时: {total_time / len(self.metrics):.2f}ms")
        
        # 按操作类型分组
        operation_groups = {}
        for metric in self.metrics:
            if metric.operation_name not in operation_groups:
                operation_groups[metric.operation_name] = []
            operation_groups[metric.operation_name].append(metric)
        
        report.append("\n按操作类型统计:")
        report.append("-" * 40)
        
        for op_name, metrics in operation_groups.items():
            total_ms = sum(m.duration_ms for m in metrics)
            avg_ms = total_ms / len(metrics)
            max_ms = max(m.duration_ms for m in metrics)
            
            report.append(f"{op_name:<30} | {len(metrics):>3}次 | 总计{total_ms:>7.1f}ms | 平均{avg_ms:>6.1f}ms | 最大{max_ms:>6.1f}ms")
        
        # 慢操作详情
        slow_operations = [m for m in self.metrics if m.duration_ms > 100]
        if slow_operations:
            report.append("\n慢操作详情 (>100ms):")
            report.append("-" * 40)
            
            for metric in sorted(slow_operations, key=lambda x: x.duration_ms, reverse=True)[:10]:
                report.append(f"{metric.operation_name} - {metric.duration_ms:.1f}ms")
                if metric.params:
                    report.append(f"  参数: {metric.params}")
                if metric.error:
                    report.append(f"  错误: {metric.error}")
        
        return "\n".join(report)

# 全局性能分析器实例
_performance_analyzer = None

def get_performance_analyzer() -> PerformanceAnalyzer:
    """获取全局性能分析器"""
    global _performance_analyzer
    if _performance_analyzer is None:
        _performance_analyzer = PerformanceAnalyzer()
    return _performance_analyzer

def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            analyzer = get_performance_analyzer()
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            # 提取参数信息
            params = {}
            if args:
                if hasattr(args[0], '__class__'):
                    params['class'] = args[0].__class__.__name__
                if len(args) > 1:
                    params['args_count'] = len(args) - 1
            if kwargs:
                params.update({k: str(v)[:50] for k, v in kwargs.items()})
            
            operation_id = analyzer.start_operation(op_name, params)
            
            try:
                result = func(*args, **kwargs)
                analyzer.end_operation(operation_id)
                return result
            except Exception as e:
                analyzer.end_operation(operation_id, str(e))
                raise
        
        return wrapper
    return decorator

# 便捷的上下文管理器
class PerformanceContext:
    """性能监控上下文管理器"""
    
    def __init__(self, operation_name: str, params: Dict[str, Any] = None):
        self.operation_name = operation_name
        self.params = params or {}
        self.operation_id = ""
        self.analyzer = get_performance_analyzer()
    
    def __enter__(self):
        self.operation_id = self.analyzer.start_operation(self.operation_name, self.params)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        error = str(exc_val) if exc_val else None
        self.analyzer.end_operation(self.operation_id, error)

if __name__ == "__main__":
    # 测试性能分析器
    print("性能分析器测试")
    
    analyzer = get_performance_analyzer()
    
    # 模拟分页操作
    with PerformanceContext("pagination_request", {"page": 1, "page_size": 50}):
        time.sleep(0.1)  # 模拟100ms的处理时间
        
        with PerformanceContext("database_query", {"table": "salary_data"}):
            time.sleep(0.05)  # 模拟50ms的数据库查询
        
        with PerformanceContext("data_formatting", {"records": 50}):
            time.sleep(0.03)  # 模拟30ms的数据格式化
    
    # 模拟一个慢操作
    with PerformanceContext("slow_operation", {"type": "complex_calculation"}):
        time.sleep(2.0)  # 模拟2秒的慢操作
    
    # 生成分析报告
    print("\n" + analyzer.generate_detailed_report())
    
    # 分页性能分析
    pagination_analysis = analyzer.analyze_pagination_performance()
    print(f"\n分页性能分析: {pagination_analysis}")