#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的格式渲染调试脚本
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_display_fields_matching():
    """测试显示字段匹配问题"""
    print("=== 测试显示字段匹配问题 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 获取A岗职工的显示字段配置
        table_type = 'a_grade_employees'
        display_fields = field_registry.get_display_fields(table_type)
        
        print(f"配置的显示字段({len(display_fields)}): {display_fields}")
        
        # 模拟实际DataFrame的列名（基于日志分析）
        # 从日志我们知道A岗职工有26列，但display_order只显示22个字段
        mock_df_columns = [
            'employee_id', 'employee_name', 'department', 'employee_type', 
            'employee_type_code', 'position_salary_2025', 'seniority_salary_2025',
            'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee',
            'living_allowance_2025', 'car_allowance', 'performance_bonus_2025',
            'supplement', 'advance', 'total_salary', 'provident_fund_2025',
            'insurance_deduction', 'pension_insurance', 'year', 'month',
            'id', 'created_at', 'updated_at', 'sequence_number'  # 这些可能是额外的列
        ]
        
        print(f"\n模拟DataFrame列({len(mock_df_columns)}): {mock_df_columns}")
        
        # 检查匹配情况
        existing_display_fields = [field for field in display_fields if field in mock_df_columns]
        missing_fields = [field for field in display_fields if field not in mock_df_columns]
        extra_fields = [field for field in mock_df_columns if field not in display_fields]
        
        print(f"\n匹配的字段({len(existing_display_fields)}): {existing_display_fields}")
        print(f"配置中有但DataFrame中没有的字段({len(missing_fields)}): {missing_fields}")
        print(f"DataFrame中有但配置中没有的字段({len(extra_fields)}): {extra_fields}")
        
        # 检查display_order是否会被应用
        if len(existing_display_fields) > 0:
            print("\n✅ display_order应该会被应用（有匹配的字段）")
            
            # 检查字段顺序
            print(f"预期的字段顺序前6个: {existing_display_fields[:6]}")
            
            # 重点检查人员类别代码的位置
            if 'employee_type_code' in existing_display_fields:
                position = existing_display_fields.index('employee_type_code')
                print(f"人员类别代码在显示字段中的位置: {position} (从0开始)")
                if position > 0:
                    prev_field = existing_display_fields[position-1]
                    print(f"人员类别代码前面的字段: {prev_field}")
        else:
            print("\n❌ display_order不会被应用（没有匹配的字段）")
            
        return len(existing_display_fields) > 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_renderer_logic():
    """测试FormatRenderer的逻辑"""
    print("\n=== 测试FormatRenderer逻辑模拟 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        table_type = 'a_grade_employees'
        
        # 模拟format_renderer.render_dataframe中的逻辑
        display_fields = field_registry.get_display_fields(table_type)
        
        # 模拟DataFrame列
        mock_columns = ['employee_id', 'employee_name', 'department', 'employee_type_code', 
                       'employee_type', 'position_salary_2025', 'insurance_deduction', 
                       'total_salary', 'id', 'created_at']
        
        print(f"display_fields: {display_fields}")
        print(f"模拟DataFrame列: {mock_columns}")
        
        if display_fields:
            # 模拟format_renderer中的逻辑
            existing_display_fields = [field for field in display_fields if field in mock_columns]
            print(f"existing_display_fields: {existing_display_fields}")
            
            if existing_display_fields:
                print("✅ 会输出：🎯 [格式渲染] 已按display_order排列字段")
                print(f"重新排列后的字段顺序: {existing_display_fields}")
                return True
            else:
                print("❌ 不会输出display_order日志（existing_display_fields为空）")
                return False
        else:
            print("❌ 不会输出display_order日志（display_fields为空）")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始简化的格式渲染调试")
    print("=" * 60)
    
    # 测试1: 字段匹配
    test1_passed = test_display_fields_matching()
    
    # 测试2: FormatRenderer逻辑
    test2_passed = test_format_renderer_logic()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  显示字段匹配测试: {'通过' if test1_passed else '失败'}")
    print(f"  FormatRenderer逻辑测试: {'通过' if test2_passed else '失败'}")
    
    if test1_passed and test2_passed:
        print("\n分析结论：A岗职工应该会应用display_order")
    else:
        print("\n分析结论：发现了A岗职工不应用display_order的原因")