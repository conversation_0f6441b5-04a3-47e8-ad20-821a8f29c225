# 退休人员工资表格式化实施方案

## 概述

本方案旨在解决退休人员工资表在主界面右侧列表展示区域的字段格式化问题，确保数据按照用户要求进行正确的类型转换和显示格式化。

## 需求分析

### 格式化要求

#### 1. 字符串类型字段
**字段列表：**
- 人员代码
- 姓名
- 部门名称
- 人员类别代码
- 备注

**格式要求：**
- 人员代码：`19990089`（不是 `19990089.0`）
- 人员类别代码：`01`（不是 `1.0`）
- 空值处理：`None、nan、0、0.0、空字符串、空格` 等统一显示为空白

#### 2. 浮点型字段
**字段列表：**
- 基本退休费、津贴、结余津贴、离退休生活补贴、护理费、物业补贴、住房补贴、增资预付
- 2016待遇调整、2017待遇调整、2018待遇调整、2019待遇调整、2020待遇调整、2021待遇调整、2022待遇调整、2023待遇调整
- 补发、借支、应发工资、公积、保险扣款

**格式要求：**
- 小数点后保留两位，不够2位要补齐
- 空值处理：`None、nan、0、0.0、空字符串、空格` 等统一显示为 `0.00`

#### 3. 特殊字段
**月份字段：**
- 提取后两位月份部分，类型转换为字符串

**年份字段：**
- 类型转换为字符串

## 现有架构分析

### 系统架构
项目采用**集中式格式化处理**机制，确保数据显示的一致性：

```
数据导航路径：数据导航区域 → 表类型切换 → SalaryDataFormatter.format_table_data()
分页路径：分页组件 → 页面跳转 → SalaryDataFormatter.format_table_data()
```

### 关键文件
- **核心格式化器：** `src/modules/data_storage/salary_data_formatter.py`
- **主界面组件：** `src/gui/prototype/prototype_main_window.py`
- **字段配置：** `SalaryDataFormatter.FIELD_TYPE_MAPPING`

### 现有问题
1. **配置错误：** 退休人员工资表的字段分类与需求不符
2. **字段缺失：** 部分必要字段未包含在正确的分类中
3. **格式化不准确：** 浮点型字段被错误分类为整型字段

## 技术实施方案

### 1. 修改字段分类配置

**文件：** `src/modules/data_storage/salary_data_formatter.py`

**位置：** `FIELD_TYPE_MAPPING['pension_employees']`

```python
# 退休人员工资表 - 按用户要求重新配置
'pension_employees': {
    'id_fields': [
        # 人员代码需要去除.0格式（如：19990089.0 → 19990089）
        '人员代码', '人员类别代码',
        'employee_code', 'employee_type_code'
    ],
    'string_fields': [
        # 字符串字段：空值显示为空白
        '人员代码', '姓名', '部门名称', '人员类别代码', '备注',
        # 对应英文字段
        'employee_code', 'name', 'department', 'employee_type_code', 'remarks'
    ],
    'integer_fields': [
        # 退休人员工资表不再有整型字段，全部改为浮点型
    ],
    'float_fields': [
        # 浮点型字段：空值显示为0.00，保留两位小数
        '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', 
        '物业补贴', '住房补贴', '增资预付',
        '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', 
        '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', 
        '补发', '借支', '应发工资', '公积', '保险扣款',
        # 对应英文字段
        'basic_retirement_salary', 'allowance', 'balance_allowance', 'retirement_living_allowance',
        'nursing_fee', 'property_allowance', 'housing_allowance', 'salary_advance',
        'adjustment_2016', 'adjustment_2017', 'adjustment_2018', 'adjustment_2019',
        'adjustment_2020', 'adjustment_2021', 'adjustment_2022', 'adjustment_2023',
        'supplement', 'advance', 'total_salary', 'provident_fund', 'insurance_deduction'
    ],
    'special_fields': {
        'month_fields': ['month', '月份'],  # 提取后两位转字符串
        'year_fields': ['year', '年份']    # 转字符串
    }
}
```

### 2. 增强格式化逻辑

#### 人员代码格式化
```python
def format_pension_employee_code(value):
    """退休人员工资表人员代码格式化"""
    if pd.isna(value) or value in ['', '0', '0.0']:
        return ''
    # 去除小数点：19990089.0 → 19990089
    return str(int(float(value)))
```

#### 人员类别代码格式化
```python
def format_pension_employee_type_code(value):
    """退休人员工资表人员类别代码格式化"""
    if pd.isna(value) or value in ['', '0', '0.0']:
        return ''
    # 保持两位格式：1.0 → 01
    return f"{int(float(value)):02d}"
```

#### 浮点型字段格式化
```python
def format_pension_float_field(value):
    """退休人员工资表浮点型字段格式化"""
    if value in [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL'] or pd.isna(value):
        return "0.00"
    return f"{float(value):.2f}"
```

#### 月份字段格式化
```python
def format_pension_month_field(value):
    """退休人员工资表月份字段格式化"""
    if pd.isna(value) or value == '' or value is None:
        return ''
    
    val_str = str(value).strip()
    
    # 处理各种月份格式
    if val_str.isdigit():
        if len(val_str) == 1:
            return f"0{val_str}"  # 单位月份，前补0
        elif len(val_str) == 2:
            return val_str  # 双位月份，直接返回
        elif len(val_str) >= 6:
            return val_str[-2:]  # 类似 202501 格式，提取后两位
        else:
            return val_str[-2:].zfill(2)  # 其他数字格式，取后两位
    
    # 非纯数字，尝试提取数字部分
    import re
    match = re.search(r'(\d{1,2})(?!.*\d)', val_str)
    if match:
        month_num = match.group(1)
        return month_num.zfill(2)  # 确保两位数
    
    return ''
```

#### 年份字段格式化
```python
def format_pension_year_field(value):
    """退休人员工资表年份字段格式化"""
    if pd.isna(value) or value == '' or value is None:
        return ''
    
    val_str = str(value).strip()
    
    # 处理各种年份格式
    if val_str.replace('.', '').isdigit():
        try:
            year_int = int(float(val_str))
            # 验证年份范围的合理性（1900-2100）
            if 1900 <= year_int <= 2100:
                return str(year_int)
        except (ValueError, OverflowError):
            pass
    
    # 非数字格式，尝试提取年份
    import re
    year_match = re.search(r'(20\d{2}|19\d{2})', val_str)
    if year_match:
        return year_match.group(1)
    
    return ''
```

### 3. 空值处理统一标准

#### 字符串字段空值定义
```python
EMPTY_VALUES_STRING = [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL']
```

#### 浮点型字段空值定义
```python
EMPTY_VALUES_FLOAT = [None, 'None', 'nan', 'NaN', 0, 0.0, '', ' ', 'null', 'NULL']
```

### 4. 实现细节

#### 方法修改清单
1. **`_format_id_fields()`** - 确保人员代码去除小数点
2. **`_format_string_fields()`** - 确保人员类别代码为两位格式
3. **`_format_float_fields()`** - 确保浮点数保留两位小数
4. **`_format_special_fields()`** - 确保月份、年份按要求格式化
5. **`format_display_value_by_table()`** - 增强表类型相关的显示格式化

#### 处理流程
```
数据查询 → 表类型识别 → 字段分类 → 格式化处理 → 空值统一处理 → 显示输出
```

## 优势分析

### 1. 架构兼容性
- ✅ **不破坏现有架构**：仅修改配置，不改变核心处理逻辑
- ✅ **独立专用处理**：针对退休人员工资表的专用配置
- ✅ **向后兼容**：不影响其他表类型的格式化

### 2. 实现一致性
- ✅ **两条路径统一**：数据导航和分页都会应用相同的格式化
- ✅ **集中式处理**：统一的格式化入口确保一致性
- ✅ **配置驱动**：通过配置控制格式化规则

### 3. 可维护性
- ✅ **清晰的职责分离**：格式化逻辑集中在专用模块
- ✅ **易于扩展**：可以轻松添加新的表类型支持
- ✅ **便于调试**：日志记录完整的格式化过程

## 实施计划

### 第一阶段：配置修改
1. 修改 `FIELD_TYPE_MAPPING['pension_employees']` 配置
2. 添加退休人员工资表专用的字段分类

### 第二阶段：格式化增强
1. 增强 `_format_id_fields()` 方法
2. 增强 `_format_string_fields()` 方法
3. 增强 `_format_float_fields()` 方法
4. 增强 `_format_special_fields()` 方法

### 第三阶段：测试验证
1. 测试数据导航路径的格式化效果
2. 测试分页路径的格式化效果
3. 验证空值处理的一致性
4. 确认人员代码和人员类别代码的格式正确性

### 第四阶段：部署上线
1. 代码审查和质量检查
2. 部署到测试环境
3. 用户验收测试
4. 正式部署

## 风险评估

### 低风险
- 配置修改不影响其他表类型
- 格式化逻辑增强不破坏现有功能
- 集中式处理确保变更影响可控

### 预防措施
- 完整的单元测试覆盖
- 详细的日志记录
- 渐进式部署策略

## 总结

本方案采用**配置驱动**的方式，通过修改 `SalaryDataFormatter` 的字段分类配置，实现退休人员工资表的专用格式化需求。方案充分利用了现有的集中式格式化架构，确保了实现的简洁性和一致性，同时保持了系统的可维护性和扩展性。

---

**文档版本：** 1.0  
**创建时间：** 2025-07-05  
**最后更新：** 2025-07-05  
**状态：** 待实施