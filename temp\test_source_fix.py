# -*- coding: utf-8 -*-
"""
测试源头修复效果
验证修复后的配置生成逻辑是否正确工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.modules.format_management.field_registry import FieldRegistry
    from src.modules.format_management.format_config import FormatConfig
    from src.modules.format_management.unified_format_manager import get_unified_format_manager
    
    print("=" * 60)
    print("🔧 测试源头修复效果")
    print("=" * 60)
    
    # 1. 测试字段注册系统的默认映射
    print("\n1. 测试FieldRegistry默认映射...")
    field_registry = FieldRegistry("state/data/field_mappings.json")
    
    # 获取默认映射
    default_mappings = field_registry._get_default_mappings()
    retired_config = default_mappings.get("table_mappings", {}).get("retired_employees", {})
    
    print(f"   retired_employees配置存在: {'✓' if retired_config else '✗'}")
    
    if retired_config:
        field_mappings = retired_config.get("field_mappings", {})
        field_types = retired_config.get("field_types", {})
        
        # 检查用户需要的字段
        required_fields = [
            ("one_time_living_allowance", "增发一次性生活补贴"),
            ("supplement", "补发"), 
            ("advance", "借支"),
            ("remarks", "备注")
        ]
        
        print("\n   检查用户需要的新字段:")
        for db_field, display_name in required_fields:
            has_mapping = db_field in field_mappings and field_mappings[db_field] == display_name
            has_type = db_field in field_types
            expected_type = "float" if db_field != "remarks" else "string"
            correct_type = field_types.get(db_field) == expected_type
            
            print(f"     {display_name}: 映射{'✓' if has_mapping else '✗'} 类型{'✓' if has_type and correct_type else '✗'}")
        
        # 检查年份和月份字段类型
        year_type = field_types.get("year") == "year_string"
        month_type = field_types.get("month") == "month_string_extract_last_two"
        
        print(f"\n   特殊字段类型:")
        print(f"     年份 -> year_string: {'✓' if year_type else '✗'}")
        print(f"     月份 -> month_string_extract_last_two: {'✓' if month_type else '✗'}")
    
    # 2. 测试格式配置
    print("\n2. 测试FormatConfig配置...")
    format_config = FormatConfig("src/modules/format_management/format_config.json")
    format_config.load_config()
    
    config_data = format_config._config_data
    retired_format_config = config_data.get("retired_staff_format_config", {})
    
    print(f"   retired_staff_format_config存在: {'✓' if retired_format_config else '✗'}")
    
    if retired_format_config:
        float_fields = retired_format_config.get("field_formats", {}).get("float_fields", [])
        string_fields = retired_format_config.get("field_formats", {}).get("string_fields", [])
        special_fields = retired_format_config.get("field_formats", {}).get("special_string_fields", {})
        
        print(f"   增发一次性生活补贴在float_fields: {'✓' if '增发一次性生活补贴' in float_fields else '✗'}")
        print(f"   补发在float_fields: {'✓' if '补发' in float_fields else '✗'}")
        print(f"   借支在float_fields: {'✓' if '借支' in float_fields else '✗'}")
        print(f"   备注在string_fields: {'✓' if '备注' in string_fields else '✗'}")
        print(f"   月份特殊处理: {'✓' if special_fields.get('月份') == 'month_string_extract_last_two' else '✗'}")
    
    # 3. 测试字段类型推断逻辑
    print("\n3. 测试字段类型推断逻辑...")
    test_mappings = {
        "one_time_living_allowance": "增发一次性生活补贴",
        "supplement": "补发",
        "advance": "借支", 
        "remarks": "备注",
        "year": "年份",
        "month": "月份"
    }
    
    auto_types = field_registry._auto_infer_field_types(test_mappings)
    
    expected_types = {
        "one_time_living_allowance": "float",
        "supplement": "float", 
        "advance": "float",
        "remarks": "string",
        "year": "year_string",
        "month": "month_string_extract_last_two"
    }
    
    print("   自动推断结果:")
    for field, expected_type in expected_types.items():
        actual_type = auto_types.get(field)
        print(f"     {field}: {actual_type} {'✓' if actual_type == expected_type else '✗'}")
    
    # 4. 测试统一格式管理器
    print("\n4. 测试统一格式管理器...")
    format_manager = get_unified_format_manager()
    
    # 测试格式化功能
    test_values = [None, 'nan', '', ' ', '-', '0', '0.0', 3500.5]
    
    print("   测试浮点值格式化(增发一次性生活补贴):")
    for value in test_values[:3]:  # 只测试前几个值
        try:
            result = format_manager.format_display_value(value, '增发一次性生活补贴', 'retired_employees')
            expected = "0.00"
            print(f"     {repr(value)} -> {result} {'✓' if result == expected else '✗'}")
        except Exception as e:
            print(f"     {repr(value)} -> 错误: {e} ✗")
    
    print("\n   测试字符串值格式化(备注):")
    test_string_values = [None, 'nan', '', ' ', '-', '正常备注']
    for value in test_string_values[:3]:  # 只测试前几个值
        try:
            result = format_manager.format_display_value(value, '备注', 'retired_employees')
            expected = ""  # 备注字段空值应显示为空白
            print(f"     {repr(value)} -> {repr(result)} {'✓' if result == expected else '✗'}")
        except Exception as e:
            print(f"     {repr(value)} -> 错误: {e} ✗")
    
    print("\n=" * 60)
    print("🎯 测试完成！")
    print("=" * 60)
    
    # 5. 生成新的field_mappings.json进行验证
    print("\n5. 重新生成配置验证...")
    
    # 强制重新加载映射
    field_registry.reload_mappings()
    
    # 检查最终的配置结果
    final_types = field_registry.get_table_field_types('retired_employees')
    
    print("   最终字段类型配置:")
    for field, expected_type in expected_types.items():
        actual_type = final_types.get(field, "未找到")
        print(f"     {field}: {actual_type} {'✓' if actual_type == expected_type else '✗'}")
    
    print(f"\n   总计字段数: {len(final_types)}")
    print("   修复验证完成！")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()