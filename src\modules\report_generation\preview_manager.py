"""
预览管理器 (Preview Manager)

提供报告预览和打印功能，管理临时文件，支持多种预览方式。
包括系统默认程序预览、文档内容提取预览等。

主要功能：
- 报告文档预览
- 打印功能集成
- 临时文件管理和清理
- 预览历史记录
- 文档转换支持

Author: Development Team
Date: 2025-06-13
"""

import os
import subprocess
import tempfile
import shutil
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging
from datetime import datetime
import platform

try:
    from docx import Document  # type: ignore
    import openpyxl
    OFFICE_AVAILABLE = True
except ImportError:
    OFFICE_AVAILABLE = False

from src.utils.log_config import get_module_logger


class PreviewManager:
    """预览管理器"""
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化预览管理器
        
        Args:
            temp_dir: 临时文件目录，如果为None则使用系统临时目录
        """
        self.logger = get_module_logger(__name__)
        
        # 设置临时目录
        if temp_dir is None:
            self.temp_dir = Path(tempfile.gettempdir()) / "salary_changes_preview"
        else:
            self.temp_dir = Path(temp_dir)
            
        # 确保临时目录存在
        self.temp_dir.mkdir(exist_ok=True)
        
        # 临时文件跟踪
        self.temp_files: set[str] = set()
        self.preview_history: list[Dict[str, Any]] = []
        
        # 检查系统类型
        self.system = platform.system().lower()
        
        self.logger.info(f"预览管理器初始化完成，临时目录: {self.temp_dir}")
        
    def preview_document(self, file_path: str, preview_type: str = 'system') -> bool:
        """
        预览文档
        
        Args:
            file_path: 文档文件路径
            preview_type: 预览类型 ('system', 'extract', 'copy')
                - system: 使用系统默认程序打开
                - extract: 提取文档内容进行预览
                - copy: 复制到临时位置打开
                
        Returns:
            bool: 是否预览成功
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"文档文件不存在: {file_path}")
                return False
                
            self.logger.info(f"开始预览文档: {file_path}, 预览类型: {preview_type}")
            
            if preview_type == 'system':
                return self._preview_with_system(file_path)
            elif preview_type == 'extract':
                return self._preview_with_extract(file_path)
            elif preview_type == 'copy':
                return self._preview_with_copy(file_path)
            else:
                self.logger.error(f"不支持的预览类型: {preview_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"预览文档失败: {str(e)}", exc_info=True)
            return False
            
    def _preview_with_system(self, file_path: str) -> bool:
        """
        使用系统默认程序预览
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            bool: 是否预览成功
        """
        try:
            # 记录预览历史
            self._add_preview_history(file_path, 'system')
            
            if self.system == 'windows':
                # Windows系统
                os.startfile(file_path)
            elif self.system == 'darwin':
                # macOS系统
                subprocess.run(['open', file_path])
            else:
                # Linux系统
                subprocess.run(['xdg-open', file_path])
                
            self.logger.info(f"已使用系统默认程序打开文档: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"系统预览失败: {str(e)}")
            return False
            
    def _preview_with_extract(self, file_path: str) -> bool:
        """
        提取文档内容进行预览
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            bool: 是否预览成功
        """
        try:
            if not OFFICE_AVAILABLE:
                self.logger.warning("缺少Office处理库，无法提取内容，改用系统预览")
                return self._preview_with_system(file_path)
                
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.docx':
                content = self._extract_word_content(file_path)
            elif file_ext == '.xlsx':
                content = self._extract_excel_content(file_path)
            else:
                self.logger.error(f"不支持的文件格式: {file_ext}")
                return False
                
            if content:
                # 创建临时预览文件
                preview_file = self._create_text_preview(content, file_path)
                if preview_file:
                    self._add_preview_history(file_path, 'extract')
                    return self._preview_with_system(preview_file)
                    
            return False
            
        except Exception as e:
            self.logger.error(f"内容提取预览失败: {str(e)}")
            return False
            
    def _preview_with_copy(self, file_path: str) -> bool:
        """
        复制到临时位置后预览
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            bool: 是否预览成功
        """
        try:
            # 生成临时文件路径
            original_name = Path(file_path).name
            temp_file = self.temp_dir / f"preview_{datetime.now().strftime('%H%M%S')}_{original_name}"
            
            # 复制文件
            shutil.copy2(file_path, temp_file)
            self.temp_files.add(str(temp_file))
            
            # 记录预览历史
            self._add_preview_history(file_path, 'copy')
            
            # 使用系统程序打开临时文件
            return self._preview_with_system(str(temp_file))
            
        except Exception as e:
            self.logger.error(f"复制预览失败: {str(e)}")
            return False
            
    def _extract_word_content(self, file_path: str) -> Optional[str]:
        """
        提取Word文档内容
        
        Args:
            file_path: Word文件路径
            
        Returns:
            Optional[str]: 提取的文本内容
        """
        try:
            document = Document(file_path)
            content = []
            
            # 提取段落内容
            for paragraph in document.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text)
                    
            # 提取表格内容
            for table in document.tables:
                content.append("\n--- 表格 ---")
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    content.append(" | ".join(row_text))
                content.append("--- 表格结束 ---\n")
                
            return "\n".join(content)
            
        except Exception as e:
            self.logger.error(f"提取Word内容失败: {str(e)}")
            return None
            
    def _extract_excel_content(self, file_path: str) -> Optional[str]:
        """
        提取Excel文档内容
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            Optional[str]: 提取的文本内容
        """
        try:
            workbook = openpyxl.load_workbook(file_path, read_only=True)
            content = []
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                content.append(f"\n=== 工作表: {sheet_name} ===")
                
                # 提取数据（限制范围避免太大）
                max_row = min(worksheet.max_row, 100)
                max_col = min(worksheet.max_column, 20)
                
                for row in worksheet.iter_rows(min_row=1, max_row=max_row, 
                                             min_col=1, max_col=max_col):
                    row_values = []
                    for cell in row:
                        value = str(cell.value) if cell.value is not None else ""
                        row_values.append(value)
                    if any(row_values):  # 只添加非空行
                        content.append(" | ".join(row_values))
                        
            workbook.close()
            return "\n".join(content)
            
        except Exception as e:
            self.logger.error(f"提取Excel内容失败: {str(e)}")
            return None
            
    def _create_text_preview(self, content: str, original_path: str) -> Optional[str]:
        """
        创建文本预览文件
        
        Args:
            content: 文档内容
            original_path: 原始文件路径
            
        Returns:
            Optional[str]: 预览文件路径
        """
        try:
            # 生成预览文件名
            original_name = Path(original_path).stem
            preview_file = self.temp_dir / f"preview_{original_name}_{datetime.now().strftime('%H%M%S')}.txt"
            
            # 写入内容
            with open(preview_file, 'w', encoding='utf-8') as f:
                f.write(f"文档预览: {original_path}\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")
                f.write(content)
                
            self.temp_files.add(str(preview_file))
            return str(preview_file)
            
        except Exception as e:
            self.logger.error(f"创建文本预览失败: {str(e)}")
            return None
            
    def print_document(self, file_path: str, printer_name: Optional[str] = None, 
                      print_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        打印文档
        
        Args:
            file_path: 文档文件路径
            printer_name: 打印机名称，如果为None则使用默认打印机
            print_options: 打印选项
                - copies: 打印份数
                - duplex: 是否双面打印
                - color: 是否彩色打印
                
        Returns:
            bool: 是否打印成功
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"文档文件不存在: {file_path}")
                return False
                
            self.logger.info(f"开始打印文档: {file_path}")
            
            if self.system == 'windows':
                return self._print_windows(file_path, printer_name, print_options)
            elif self.system == 'darwin':
                return self._print_macos(file_path, printer_name, print_options)
            else:
                return self._print_linux(file_path, printer_name, print_options)
                
        except Exception as e:
            self.logger.error(f"打印文档失败: {str(e)}", exc_info=True)
            return False
            
    def _print_windows(self, file_path: str, printer_name: Optional[str] = None, 
                      print_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        Windows系统打印
        
        Args:
            file_path: 文档文件路径
            printer_name: 打印机名称
            print_options: 打印选项
            
        Returns:
            bool: 是否打印成功
        """
        try:
            # 构建打印命令
            if printer_name:
                # 指定打印机
                cmd = f'powershell -Command "Start-Process -FilePath \\"{file_path}\\" -Verb Print -ArgumentList \\"/p:{printer_name}\\""'
            else:
                # 默认打印机
                cmd = f'powershell -Command "Start-Process -FilePath \\"{file_path}\\" -Verb Print"'
                
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Windows打印命令执行成功: {file_path}")
                return True
            else:
                self.logger.error(f"Windows打印失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Windows打印异常: {str(e)}")
            return False
            
    def _print_macos(self, file_path: str, printer_name: Optional[str] = None, 
                    print_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        macOS系统打印
        
        Args:
            file_path: 文档文件路径
            printer_name: 打印机名称
            print_options: 打印选项
            
        Returns:
            bool: 是否打印成功
        """
        try:
            cmd = ['lpr']
            
            if printer_name:
                cmd.extend(['-P', printer_name])
                
            cmd.append(file_path)
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"macOS打印命令执行成功: {file_path}")
                return True
            else:
                self.logger.error(f"macOS打印失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"macOS打印异常: {str(e)}")
            return False
            
    def _print_linux(self, file_path: str, printer_name: Optional[str] = None, 
                    print_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        Linux系统打印
        
        Args:
            file_path: 文档文件路径
            printer_name: 打印机名称
            print_options: 打印选项
            
        Returns:
            bool: 是否打印成功
        """
        try:
            cmd = ['lp']
            
            if printer_name:
                cmd.extend(['-d', printer_name])
                
            cmd.append(file_path)
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Linux打印命令执行成功: {file_path}")
                return True
            else:
                self.logger.error(f"Linux打印失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Linux打印异常: {str(e)}")
            return False
            
    def get_available_printers(self) -> List[str]:
        """
        获取可用的打印机列表
        
        Returns:
            List[str]: 打印机名称列表
        """
        try:
            printers = []
            
            if self.system == 'windows':
                # Windows: 使用PowerShell获取打印机
                cmd = 'powershell -Command "Get-Printer | Select-Object Name | ForEach-Object { $_.Name }"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    printers = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                    
            elif self.system in ['darwin', 'linux']:
                # macOS/Linux: 使用lpstat
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if line.startswith('printer '):
                            printer_name = line.split()[1]
                            printers.append(printer_name)
                            
            self.logger.debug(f"发现 {len(printers)} 个可用打印机")
            return printers
            
        except Exception as e:
            self.logger.error(f"获取打印机列表失败: {str(e)}")
            return []
            
    def _add_preview_history(self, file_path: str, preview_type: str) -> None:
        """
        添加预览历史记录
        
        Args:
            file_path: 文件路径
            preview_type: 预览类型
        """
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'file_path': file_path,
            'preview_type': preview_type,
            'file_name': Path(file_path).name
        }
        
        self.preview_history.append(history_entry)
        
        # 只保留最近50条记录
        if len(self.preview_history) > 50:
            self.preview_history = self.preview_history[-50:]
            
    def get_preview_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取预览历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 历史记录列表
        """
        return self.preview_history[-limit:] if len(self.preview_history) > limit else self.preview_history
        
    def cleanup_temp_files(self) -> int:
        """
        清理临时文件
        
        Returns:
            int: 清理的文件数量
        """
        removed_count = 0
        
        # 清理跟踪的临时文件
        for temp_file in list(self.temp_files):
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    removed_count += 1
                    self.logger.debug(f"删除临时文件: {temp_file}")
                self.temp_files.discard(temp_file)
            except Exception as e:
                self.logger.warning(f"删除临时文件失败: {temp_file}, {str(e)}")
                
        # 清理临时目录中的旧文件
        try:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(hours=1)  # 1小时前的文件
            
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            removed_count += 1
                            self.logger.debug(f"删除过期临时文件: {file_path}")
                        except Exception as e:
                            self.logger.warning(f"删除过期文件失败: {file_path}, {str(e)}")
                            
        except Exception as e:
            self.logger.error(f"清理临时目录失败: {str(e)}")
            
        self.logger.info(f"清理了 {removed_count} 个临时文件")
        return removed_count 