#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRITICAL排序功能修复验证脚本

验证系统性Qt事件循环和绘制冲突的修复效果
专门测试：
1. QBasicTimer线程安全问题
2. QPaintDevice递归重绘问题
3. 架构工厂初始化问题
4. Qt事件循环稳定性

创建时间: 2025-07-17
测试级别: P0-CRITICAL
"""

import sys
import os
import time
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QThread
from PyQt5.QtTest import QTest

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.log_config import setup_logger
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

logger = setup_logger("critical_sort_fix_test")

class CriticalSortFixTester:
    """CRITICAL排序修复测试器"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.test_results = []
        
    def setup_test_environment(self):
        """设置测试环境"""
        try:
            logger.info("🔧 [CRITICAL测试] 初始化测试环境")
            
            # 创建QApplication
            self.app = QApplication(sys.argv)
            
            # 创建主窗口
            self.main_window = PrototypeMainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTest.qWait(2000)  # 增加等待时间确保完全初始化
            
            logger.info("✅ CRITICAL测试环境初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ CRITICAL测试环境初始化失败: {e}")
            return False
    
    def test_architecture_factory_initialization(self):
        """测试架构工厂初始化"""
        try:
            logger.info("🔧 [CRITICAL测试] 测试架构工厂初始化")
            
            # 检查架构工厂是否正确初始化
            has_factory = hasattr(self.main_window, 'architecture_factory') and self.main_window.architecture_factory
            if not has_factory:
                logger.warning("⚠️ 架构工厂未初始化，测试紧急初始化")
                # 测试紧急初始化
                if hasattr(self.main_window, '_emergency_init_architecture_factory'):
                    result = self.main_window._emergency_init_architecture_factory()
                    if result:
                        logger.info("✅ 紧急初始化架构工厂成功")
                        return True
                    else:
                        logger.error("❌ 紧急初始化架构工厂失败")
                        return False
                else:
                    logger.error("❌ 缺少紧急初始化方法")
                    return False
            else:
                logger.info("✅ 架构工厂已正确初始化")
                return True
                
        except Exception as e:
            logger.error(f"❌ 架构工厂初始化测试失败: {e}")
            return False
    
    def test_repaint_recursion_protection(self):
        """测试重绘递归防护"""
        try:
            logger.info("🔧 [CRITICAL测试] 测试重绘递归防护")
            
            # 查找表格组件
            table_widget = None
            if hasattr(self.main_window, 'main_workspace'):
                workspace = self.main_window.main_workspace
                if hasattr(workspace, 'table_widget'):
                    table_widget = workspace.table_widget
            
            if not table_widget:
                logger.warning("⚠️ 没有找到表格组件，跳过重绘测试")
                return True  # 不算失败
            
            # 检查重绘防护标志
            protection_flags = [
                '_is_painting',
                '_is_repainting', 
                '_repaint_depth',
                '_max_repaint_depth'
            ]
            
            missing_flags = []
            for flag in protection_flags:
                if not hasattr(table_widget, flag):
                    missing_flags.append(flag)
            
            if missing_flags:
                logger.error(f"❌ 缺少重绘防护标志: {missing_flags}")
                return False
            
            # 测试安全更新显示方法
            if hasattr(table_widget, '_safe_update_display'):
                try:
                    table_widget._safe_update_display()
                    logger.info("✅ 安全更新显示方法可用")
                except Exception as update_error:
                    logger.error(f"❌ 安全更新显示方法失败: {update_error}")
                    return False
            else:
                logger.error("❌ 缺少安全更新显示方法")
                return False
                
            logger.info("✅ 重绘递归防护机制正确")
            return True
            
        except Exception as e:
            logger.error(f"❌ 重绘递归防护测试失败: {e}")
            return False
    
    def test_timer_thread_safety(self):
        """测试Timer线程安全"""
        try:
            logger.info("🔧 [CRITICAL测试] 测试Timer线程安全")
            
            # 模拟在主线程外使用QTimer的情况
            current_thread = QThread.currentThread()
            app_thread = self.app.thread()
            
            if current_thread != app_thread:
                logger.warning("⚠️ 检测到非主线程，这可能导致QTimer问题")
                return False
            
            # 测试QTimer的基本使用
            test_timer = QTimer()
            test_timer.setSingleShot(True)
            test_timer.start(10)
            test_timer.stop()
            
            logger.info("✅ QTimer线程安全测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ Timer线程安全测试失败: {e}")
            return False
    
    def test_sort_crash_resistance(self):
        """测试排序崩溃抵抗性"""
        try:
            logger.info("🔧 [CRITICAL测试] 测试排序崩溃抵抗性")
            
            # 查找表格组件
            table_widget = None
            if hasattr(self.main_window, 'main_workspace'):
                workspace = self.main_window.main_workspace
                if hasattr(workspace, 'table_widget'):
                    table_widget = workspace.table_widget
            
            if not table_widget:
                logger.warning("⚠️ 没有找到表格组件，跳过排序测试")
                return True
            
            header = table_widget.horizontalHeader()
            if not header:
                logger.warning("⚠️ 没有找到表头，跳过排序测试")
                return True
            
            # 进行多轮快速排序测试
            test_column = 1  # 避开序号列
            for round_num in range(3):
                logger.info(f"🔧 [CRITICAL测试] 第{round_num + 1}轮排序测试")
                
                for click_num in range(5):
                    try:
                        # 模拟快速点击
                        header.sectionPressed.emit(test_column)
                        QTest.qWait(50)  # 50ms间隔
                        
                        # 检查系统是否仍然响应
                        if not self.main_window.isVisible():
                            logger.error("❌ 主窗口在排序测试中消失")
                            return False
                            
                    except Exception as sort_error:
                        logger.warning(f"⚠️ 排序测试第{click_num + 1}次点击失败: {sort_error}")
                
                # 每轮之间等待
                QTest.qWait(200)
            
            logger.info("✅ 排序崩溃抵抗性测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 排序崩溃抵抗性测试失败: {e}")
            return False
    
    def test_system_stability(self):
        """测试系统稳定性"""
        try:
            logger.info("🔧 [CRITICAL测试] 测试系统稳定性")
            
            # 检查主窗口可见性
            if not self.main_window.isVisible():
                logger.error("❌ 主窗口不可见")
                return False
            
            # 处理挂起事件
            self.app.processEvents()
            QTest.qWait(500)
            
            # 再次检查可见性
            if not self.main_window.isVisible():
                logger.error("❌ 事件处理后主窗口消失")
                return False
            
            logger.info("✅ 系统稳定性测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统稳定性测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有CRITICAL测试"""
        try:
            logger.info("🚀 开始CRITICAL排序修复验证测试")
            
            # 设置测试环境
            if not self.setup_test_environment():
                return False
            
            # 运行所有测试
            test_results = {
                "architecture_factory": self.test_architecture_factory_initialization(),
                "repaint_protection": self.test_repaint_recursion_protection(),
                "timer_thread_safety": self.test_timer_thread_safety(),
                "sort_crash_resistance": self.test_sort_crash_resistance(),
                "system_stability": self.test_system_stability()
            }
            
            # 输出测试结果
            success_count = sum(test_results.values())
            total_tests = len(test_results)
            
            logger.info(f"📊 CRITICAL测试结果总结: {success_count}/{total_tests} 通过")
            
            for test_name, result in test_results.items():
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"  - {test_name}: {status}")
            
            if success_count == total_tests:
                logger.info("🎉 所有CRITICAL测试通过！系统性修复验证成功")
                return True
            else:
                logger.error("⚠️ 部分CRITICAL测试失败，修复可能不完整")
                return False
                
        except Exception as e:
            logger.error(f"❌ CRITICAL测试执行失败: {e}")
            return False
        
        finally:
            # 清理测试环境
            if self.main_window:
                self.main_window.close()
            if self.app:
                self.app.quit()

if __name__ == "__main__":
    tester = CriticalSortFixTester()
    success = tester.run_all_tests()
    
    if success:
        print("✅ CRITICAL排序修复验证成功")
        sys.exit(0)
    else:
        print("❌ CRITICAL排序修复验证失败")
        sys.exit(1)