# -*- coding: utf-8 -*-
"""
分页行号显示问题诊断脚本

用于测试和诊断分页时行号没有正确显示的问题
"""

import sys
import os
import pandas as pd
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt, pyqtSignal

from src.utils.log_config import setup_logger
from src.gui.widgets.pagination_widget import PaginationWidget


class MockConfigSyncManager:
    """模拟的配置同步管理器，用于测试"""
    
    def __init__(self):
        self.logger = setup_logger("MockConfigSyncManager")
        self.memory_cache = {}
        
    def get_field_mapping(self, table_name: str) -> dict:
        """获取字段映射"""
        return self.memory_cache.get(table_name, {})
        
    def save_field_mapping(self, table_name: str, mapping: dict):
        """保存字段映射"""
        self.memory_cache[table_name] = mapping
        
    def backup_mapping(self, table_name: str):
        """备份映射"""
        pass
        
    def restore_mapping(self, table_name: str):
        """恢复映射"""
        pass


class MockHeaderEditManager:
    """模拟的表头编辑管理器"""
    
    mapping_updated = pyqtSignal(str, dict)
    edit_completed = pyqtSignal(str, str, str)
    
    def __init__(self, config_sync_manager):
        self.config_sync_manager = config_sync_manager
        
    def show_edit_dialog(self, *args, **kwargs):
        return False


# 导入表格组件前，先设置Mock
try:
    from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
except ImportError as e:
    print(f"导入表格组件失败: {e}")
    # 创建一个简化的表格组件
    from PyQt5.QtWidgets import QTableWidget
    from PyQt5.QtCore import pyqtSignal
    
    class VirtualizedExpandableTable(QTableWidget):
        """简化的表格组件用于测试"""
        
        def __init__(self, config_sync_manager=None):
            super().__init__()
            self.logger = setup_logger("MockTable")
            self.pagination_state = None
            self._row_header_styled = False
            self.setRowCount(0)
            self.setColumnCount(0)
            
        def set_data(self, data, headers, current_table_name=""):
            """设置表格数据"""
            self.clear()
            if not data or not headers:
                return
                
            self.setRowCount(len(data))
            self.setColumnCount(len(headers))
            self.setHorizontalHeaderLabels(headers)
            
            for row, record in enumerate(data):
                for col, header in enumerate(headers):
                    value = str(record.get(header, ''))
                    self.setItem(row, col, QTableWidgetItem(value))
                    
            # 设置默认行号
            self._setup_row_numbers()
            
        def set_pagination_state(self, pagination_state: dict):
            """设置分页状态"""
            self.pagination_state = pagination_state
            self._update_pagination_row_labels_only()
            self.logger.info(f"分页状态已设置: {pagination_state}")
            
        def _setup_row_numbers(self):
            """设置行号显示"""
            try:
                vertical_header = self.verticalHeader()
                vertical_header.setVisible(True)
                
                row_count = self.rowCount()
                if hasattr(self, 'pagination_state') and self.pagination_state:
                    start_record = self.pagination_state.get('start_record', 1)
                    row_labels = [str(start_record + i) for i in range(row_count)]
                    self.logger.info(f"分页行号设置: 起始{start_record}, 共{row_count}行")
                else:
                    row_labels = [str(i + 1) for i in range(row_count)]
                    self.logger.info(f"普通行号设置: 共{row_count}行")
                
                self.setVerticalHeaderLabels(row_labels)
                
            except Exception as e:
                self.logger.error(f"设置行号失败: {e}")
                
        def _update_pagination_row_labels_only(self):
            """更新分页行号标签"""
            try:
                if not (hasattr(self, 'pagination_state') and self.pagination_state):
                    return
                
                row_count = self.rowCount()
                if row_count <= 0:
                    return
                
                start_record = self.pagination_state.get('start_record', 1)
                row_labels = [str(start_record + i) for i in range(row_count)]
                self.setVerticalHeaderLabels(row_labels)
                
                self.logger.info(f"分页行号标签已更新: 第{start_record}-{start_record + row_count - 1}行")
                
            except Exception as e:
                self.logger.error(f"更新分页行号标签失败: {e}")


# 添加QTableWidgetItem导入
from PyQt5.QtWidgets import QTableWidgetItem


class PaginationRowNumberTestWidget(QWidget):
    """分页行号测试组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger("PaginationRowNumberTest")
        self.init_ui()
        self.create_test_data()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("分页行号显示测试")
        self.setGeometry(100, 100, 1000, 700)
        
        layout = QVBoxLayout()
        
        # 添加测试信息标签
        self.info_label = QLabel("点击按钮测试分页行号显示")
        layout.addWidget(self.info_label)
        
        # 添加测试按钮
        btn_load_data = QPushButton("加载测试数据（200条）")
        btn_load_data.clicked.connect(self.load_test_data)
        layout.addWidget(btn_load_data)
        
        btn_test_page2 = QPushButton("跳转到第2页（应显示51-100）")
        btn_test_page2.clicked.connect(self.go_to_page_2)
        layout.addWidget(btn_test_page2)
        
        btn_test_page3 = QPushButton("跳转到第3页（应显示101-150）")
        btn_test_page3.clicked.connect(self.go_to_page_3)
        layout.addWidget(btn_test_page3)
        
        btn_test_page4 = QPushButton("跳转到第4页（应显示151-200）")
        btn_test_page4.clicked.connect(self.go_to_page_4)
        layout.addWidget(btn_test_page4)
        
        btn_check_state = QPushButton("检查当前分页状态")
        btn_check_state.clicked.connect(self.check_pagination_state)
        layout.addWidget(btn_check_state)
        
        # 添加分页组件
        self.pagination_widget = PaginationWidget()
        self.pagination_widget.page_changed.connect(self.on_page_changed)
        layout.addWidget(self.pagination_widget)
        
        # 添加表格组件
        try:
            # 创建Mock配置管理器
            mock_config_sync_manager = MockConfigSyncManager()
            self.table = VirtualizedExpandableTable(config_sync_manager=mock_config_sync_manager)
        except Exception as e:
            self.logger.error(f"创建表格组件失败: {e}")
            # 使用最简单的QTableWidget作为后备
            self.table = QTableWidget()
            
        layout.addWidget(self.table)
        
        self.setLayout(layout)
        
    def create_test_data(self):
        """创建测试数据"""
        data = []
        for i in range(1, 201):  # 200条测试数据
            data.append({
                '姓名': f'测试人员{i:03d}',
                '部门': f'部门{((i-1) // 20) + 1}',
                '岗位': ['经理', '主管', '员工'][i % 3],
                '基本工资': 5000 + (i % 50) * 100,
                '奖金': (i % 30) * 50,
                '总计': 5000 + (i % 50) * 100 + (i % 30) * 50
            })
        
        self.test_df = pd.DataFrame(data)
        self.logger.info(f"创建测试数据完成：{len(self.test_df)}条记录")
        
    def load_test_data(self):
        """加载测试数据到表格"""
        try:
            # 设置分页组件总记录数
            self.pagination_widget.set_total_records(len(self.test_df))
            
            # 获取第1页数据
            self.load_page_data(1)
            
            self.info_label.setText("测试数据已加载，当前显示第1页")
            self.logger.info("测试数据加载完成")
            
        except Exception as e:
            self.logger.error(f"加载测试数据失败: {e}")
            self.info_label.setText(f"加载数据失败: {e}")
            
    def load_page_data(self, page: int):
        """加载指定页的数据"""
        try:
            state = self.pagination_widget.get_current_state()
            
            # 计算页面数据范围
            start_idx = (page - 1) * state.page_size
            end_idx = start_idx + state.page_size
            page_df = self.test_df.iloc[start_idx:end_idx]
            
            self.logger.info(f"加载第{page}页数据: 索引{start_idx}-{end_idx-1}，实际{len(page_df)}条记录")
            
            # 设置表格数据
            headers = page_df.columns.tolist()
            data = page_df.to_dict('records')
            
            # 检查表格是否有set_data方法
            if hasattr(self.table, 'set_data'):
                self.table.set_data(data, headers)
            else:
                # 后备方案：直接设置QTableWidget
                self._set_simple_table_data(data, headers)
            
            # 🔧 [关键] 设置分页状态
            pagination_state = {
                'current_page': page,
                'page_size': state.page_size,
                'total_records': len(self.test_df),
                'start_record': start_idx + 1,  # 记录号从1开始
                'end_record': min(end_idx, len(self.test_df))
            }
            
            self.logger.info(f"设置分页状态: {pagination_state}")
            
            if hasattr(self.table, 'set_pagination_state'):
                self.table.set_pagination_state(pagination_state)
            else:
                # 手动设置行号
                self._set_simple_row_numbers(pagination_state)
            
            # 更新分页组件当前页
            self.pagination_widget.set_current_page(page)
            
            # 检查行号是否正确设置
            self.check_row_numbers(pagination_state)
            
        except Exception as e:
            self.logger.error(f"加载第{page}页数据失败: {e}")
            import traceback
            traceback.print_exc()
            
    def _set_simple_table_data(self, data, headers):
        """设置简单表格数据（QTableWidget后备方案）"""
        try:
            self.table.clear()
            if not data or not headers:
                return
                
            self.table.setRowCount(len(data))
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            
            for row, record in enumerate(data):
                for col, header in enumerate(headers):
                    value = str(record.get(header, ''))
                    self.table.setItem(row, col, QTableWidgetItem(value))
                    
        except Exception as e:
            self.logger.error(f"设置简单表格数据失败: {e}")
            
    def _set_simple_row_numbers(self, pagination_state):
        """设置简单行号（QTableWidget后备方案）"""
        try:
            start_record = pagination_state.get('start_record', 1)
            row_count = self.table.rowCount()
            
            row_labels = [str(start_record + i) for i in range(row_count)]
            self.table.setVerticalHeaderLabels(row_labels)
            
            self.logger.info(f"简单行号设置完成: 第{start_record}-{start_record + row_count - 1}行")
            
        except Exception as e:
            self.logger.error(f"设置简单行号失败: {e}")
            
    def check_row_numbers(self, expected_state):
        """检查行号是否正确显示"""
        try:
            row_count = self.table.rowCount()
            expected_start = expected_state['start_record']
            
            # 获取实际显示的行号
            actual_labels = []
            for row in range(min(5, row_count)):  # 检查前5行
                header_item = self.table.verticalHeaderItem(row)
                if header_item:
                    actual_labels.append(header_item.text())
                else:
                    # 如果没有设置标签，获取默认的行号
                    actual_labels.append(str(row + 1))
            
            expected_labels = [str(expected_start + i) for i in range(min(5, row_count))]
            
            self.logger.info(f"行号检查 - 期望: {expected_labels}, 实际: {actual_labels}")
            
            if actual_labels == expected_labels:
                result = "✅ 行号显示正确"
            else:
                result = f"❌ 行号显示错误 - 期望: {expected_labels}, 实际: {actual_labels}"
                
            self.info_label.setText(f"第{expected_state['current_page']}页 - {result}")
            
        except Exception as e:
            self.logger.error(f"检查行号失败: {e}")
            self.info_label.setText(f"检查行号失败: {e}")
            
    def go_to_page_2(self):
        """跳转到第2页"""
        self.load_page_data(2)
        
    def go_to_page_3(self):
        """跳转到第3页"""
        self.load_page_data(3)
        
    def go_to_page_4(self):
        """跳转到第4页"""
        self.load_page_data(4)
        
    def check_pagination_state(self):
        """检查当前分页状态"""
        try:
            state = self.pagination_widget.get_current_state()
            table_state = getattr(self.table, 'pagination_state', None)
            
            info = f"""
分页组件状态:
- 当前页: {state.current_page}
- 页面大小: {state.page_size}
- 总记录数: {state.total_records}
- 总页数: {state.total_pages}
- 起始记录: {state.start_record}
- 结束记录: {state.end_record}

表格分页状态: {table_state}

表格行数: {self.table.rowCount()}
"""
            
            self.logger.info(info)
            print(info)
            
        except Exception as e:
            self.logger.error(f"检查分页状态失败: {e}")
            
    def on_page_changed(self, page):
        """分页变化事件处理"""
        self.logger.info(f"分页变化事件: 跳转到第{page}页")
        self.load_page_data(page)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QWidget {
            font-family: "Microsoft YaHei";
            font-size: 12px;
        }
        QPushButton {
            padding: 8px 16px;
            background-color: #f0f0f0;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #e0e0e0;
        }
        QPushButton:pressed {
            background-color: #d0d0d0;
        }
    """)
    
    window = PaginationRowNumberTestWidget()
    window.show()
    
    # 自动加载测试数据
    window.load_test_data()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 