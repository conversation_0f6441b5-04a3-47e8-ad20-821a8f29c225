"""
表头和行号更新管理器 - 防重影专用

本模块实现统一的表头和行号更新管理，解决时序冲突导致的重影问题。

主要功能:
1. 防止并发更新导致的表头重影
2. 统一管理行号更新，避免多种方法混用
3. 简化重绘机制，避免过度重绘
4. 提供线程安全的更新接口

设计原则:
- 单一更新入口，防止并发冲突
- 智能去重，避免不必要的重复设置
- 简化重绘，只使用update()方法
- 状态锁机制，确保更新原子性
"""

import time
from typing import List, Optional, Tuple, Any
from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem
from PyQt5.QtCore import QObject

from src.utils.log_config import setup_logger


class HeaderUpdateManager(QObject):
    """
    统一的表头和行号更新管理器
    
    解决表头重影问题的核心组件，通过以下机制防止重影：
    1. 更新锁机制：防止并发更新
    2. 智能去重：避免重复设置相同内容
    3. 待处理队列：处理更新期间的新请求
    4. 简化重绘：统一使用update()方法
    """
    
    def __init__(self, table: QTableWidget, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 关联的表格组件
        self.table = table
        
        # 更新锁机制
        self._update_lock = False
        self._header_update_lock = False
        self._row_update_lock = False
        
        # 待处理的更新请求
        self._pending_header_update = None
        self._pending_row_update = None
        
        # 最后更新状态缓存
        self._last_headers = []
        self._last_row_config = None  # (start_record, count)
        
        # 统计信息
        self._header_updates = 0
        self._row_updates = 0
        self._skipped_updates = 0

        # 🔧 [P2-重影修复] 原子性状态管理
        self._atomic_operation_lock = False  # 原子操作锁
        self._header_state_lock = False      # 表头状态锁
        self._operation_queue = []           # 操作队列

        self.logger.info(f"HeaderUpdateManager 初始化完成，关联表格: {table.__class__.__name__}")
    
    def update_headers_safe(self, headers: List[str]) -> bool:
        """
        安全的表头更新 - 防重影核心方法
        
        Args:
            headers: 新的表头列表
            
        Returns:
            bool: 是否执行了实际更新
        """
        try:
            # 检查是否正在更新中
            if self._header_update_lock:
                self.logger.debug("表头更新中，将请求加入待处理队列")
                self._pending_header_update = headers
                return False
            
            #智能去重：检查是否真的需要更新
            if self._is_headers_same(headers):
                self._skipped_updates += 1
                self.logger.debug(f"表头无变化，跳过更新 (已跳过 {self._skipped_updates} 次)")
                return False
            
            # 设置更新锁
            self._header_update_lock = True
            
            try:
                # 🔧 [P2-数据同步检查] 获取当前列数并验证数据一致性
                current_columns = self.table.columnCount()
                
                # 确保列数正确，数据-UI同步
                if current_columns != len(headers):
                    self.table.setColumnCount(len(headers))
                    self.logger.debug(f"🔧 [数据同步] 调整列数: {current_columns} -> {len(headers)}")
                
                # 🔧 [P2-原子化更新] 获取水平表头确保原子性操作
                h_header = self.table.horizontalHeader()
                if h_header:
                    # 原子性执行表头更新
                    self.table.setHorizontalHeaderLabels(headers)
                    
                    # 只调用一次update()，避免多次重绘
                    h_header.update()
                else:
                    # 如果没有水平表头，直接设置
                    self.table.setHorizontalHeaderLabels(headers)
                
                # 缓存更新状态
                self._last_headers = headers.copy()
                self._header_updates += 1
                
                self.logger.debug(f"表头更新完成: {len(headers)} 个表头 (第 {self._header_updates} 次更新)")
                return True
                
            finally:
                # 释放更新锁
                self._header_update_lock = False
                
                # 处理待处理的更新请求
                if self._pending_header_update:
                    pending_headers = self._pending_header_update
                    self._pending_header_update = None
                    self.logger.debug("处理待处理的表头更新请求")
                    return self.update_headers_safe(pending_headers)
                    
        except Exception as e:
            self.logger.error(f"表头更新失败: {e}")
            self._header_update_lock = False
            return False
    
    def update_row_numbers_safe(self, start_record: int, count: int) -> bool:
        """
        安全的行号更新 - 防重影核心方法
        
        Args:
            start_record: 起始记录号
            count: 行数
            
        Returns:
            bool: 是否执行了实际更新
        """
        try:
            # 检查是否正在更新中
            if self._row_update_lock:
                self.logger.debug("行号更新中，将请求加入待处理队列")
                self._pending_row_update = (start_record, count)
                return False
            
            # 智能去重：检查是否真的需要更新
            if self._is_row_numbers_same(start_record, count):
                self._skipped_updates += 1
                self.logger.debug(f"行号无变化，跳过更新 (起始: {start_record}, 行数: {count})")
                return False
            
            # 设置更新锁
            self._row_update_lock = True
            
            try:
                # 确保行数正确
                current_rows = self.table.rowCount()
                if current_rows != count:
                    self.logger.warning(f"行数不匹配: 表格有 {current_rows} 行，但要设置 {count} 个行号")
                
                # 生成行号标签
                row_labels = [str(start_record + i) for i in range(count)]
                
                # 🔧 [P2-原子化更新] 统一使用setVerticalHeaderLabels方法
                # 获取垂直表头，确保原子性操作
                v_header = self.table.verticalHeader()
                if v_header:
                    # 先确保表头可见
                    if not v_header.isVisible():
                        v_header.setVisible(True)
                    
                    # 原子性设置行号标签
                    self.table.setVerticalHeaderLabels(row_labels)
                    
                    # 只调用一次update()，避免多次重绘
                    v_header.update()
                else:
                    # 如果没有垂直表头，直接设置
                    self.table.setVerticalHeaderLabels(row_labels)
                
                # 缓存更新状态
                self._last_row_config = (start_record, count)
                self._row_updates += 1
                
                self.logger.debug(f"行号更新完成: 起始 {start_record}, 共 {count} 行 (第 {self._row_updates} 次更新)")
                return True
                
            finally:
                # 释放更新锁
                self._row_update_lock = False
                
                # 处理待处理的更新请求
                if self._pending_row_update:
                    pending_update = self._pending_row_update
                    self._pending_row_update = None
                    self.logger.debug("处理待处理的行号更新请求")
                    return self.update_row_numbers_safe(*pending_update)
                    
        except Exception as e:
            self.logger.error(f"行号更新失败: {e}")
            self._row_update_lock = False
            return False
    
    def update_headers_with_sort_state(self, headers: List[str]) -> bool:
        """
        安全地更新表头并保持排序状态
        
        Args:
            headers: 新的表头列表
            
        Returns:
            bool: 是否执行了实际更新
        """
        try:
            # 保存当前排序状态
            h_header = self.table.horizontalHeader()
            current_sort_column = h_header.sortIndicatorSection()
            current_sort_order = h_header.sortIndicatorOrder()
            
            self.logger.debug(f"保存排序状态: 列 {current_sort_column}, 顺序 {current_sort_order}")
            
            # 更新表头
            updated = self.update_headers_safe(headers)
            
            if updated:
                # 恢复排序状态
                if current_sort_column >= 0 and current_sort_column < len(headers):
                    h_header.setSortIndicator(current_sort_column, current_sort_order)
                    self.logger.debug(f"恢复排序状态: 列 {current_sort_column}, 顺序 {current_sort_order}")
                else:
                    self.logger.debug("排序列索引无效，不恢复排序状态")
            
            return updated
            
        except Exception as e:
            self.logger.error(f"带排序状态的表头更新失败: {e}")
            return False
    
    def _is_headers_same(self, headers: List[str]) -> bool:
        """检查表头是否与当前相同"""
        try:
            # 首先检查缓存
            if self._last_headers == headers:
                return True
            
            # 检查实际表格状态
            if self.table.columnCount() != len(headers):
                return False
            
            current_headers = []
            for i in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(i)
                current_text = header_item.text() if header_item else ""
                current_headers.append(current_text)
            
            return current_headers == headers
            
        except Exception as e:
            self.logger.error(f"检查表头状态失败: {e}")
            return False
    
    def _is_row_numbers_same(self, start_record: int, count: int) -> bool:
        """检查行号是否与当前相同"""
        try:
            # 首先检查缓存
            if self._last_row_config == (start_record, count):
                return True
            
            # 检查实际行数
            if self.table.rowCount() != count:
                return False
            
            # 检查前几行的行号
            check_count = min(3, count)
            for i in range(check_count):
                header_item = self.table.verticalHeaderItem(i)
                if not header_item:
                    return False
                
                expected_text = str(start_record + i)
                if header_item.text() != expected_text:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查行号状态失败: {e}")
            return False
    
    def force_update_all(self, headers: List[str], start_record: int, count: int) -> bool:
        """
        智能强制更新表头和行号（增强版防空表头）
        
        Args:
            headers: 表头列表
            start_record: 起始记录号
            count: 行数
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 🔧 [P0-关键修复] 智能判断是否需要更新表头
            if headers and len(headers) > 0:
                self.logger.info(f"智能强制更新: 表头 {len(headers)} 个, 行号起始 {start_record}, 共 {count} 行")
                
                # 临时清除缓存，强制更新表头和行号
                self._last_headers = []
                self._last_row_config = None
                
                # 执行完整更新
                header_updated = self.update_headers_safe(headers)
                row_updated = self.update_row_numbers_safe(start_record, count)
                
                return header_updated or row_updated
            else:
                # 🔧 [P0-关键修复] 空表头情况：只更新行号
                self.logger.info(f"智能强制更新(仅行号): 跳过空表头, 行号起始 {start_record}, 共 {count} 行")
                
                # 只清除行号缓存
                self._last_row_config = None
                
                # 只执行行号更新
                return self.update_row_numbers_safe(start_record, count)
            
        except Exception as e:
            self.logger.error(f"智能强制更新失败: {e}")
            return False
    
    def get_statistics(self) -> dict:
        """获取更新统计信息"""
        return {
            'header_updates': self._header_updates,
            'row_updates': self._row_updates,
            'skipped_updates': self._skipped_updates,
            'is_header_locked': self._header_update_lock,
            'is_row_locked': self._row_update_lock,
            'has_pending_header': self._pending_header_update is not None,
            'has_pending_row': self._pending_row_update is not None
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self._header_updates = 0
        self._row_updates = 0
        self._skipped_updates = 0
        self.logger.info("HeaderUpdateManager 统计信息已重置")

    def atomic_set_headers(self, headers: List[str]) -> bool:
        """
        🔧 [P2-重影修复] 原子性表头设置，确保操作的完整性和一致性

        Args:
            headers: 要设置的表头列表

        Returns:
            bool: 是否成功设置
        """
        try:
            # 检查原子操作锁
            if self._atomic_operation_lock:
                self.logger.warning("原子操作正在进行中，将请求加入队列")
                self._operation_queue.append(('set_headers', headers))
                return False

            # 设置原子操作锁
            self._atomic_operation_lock = True
            self._header_state_lock = True

            try:
                self.logger.debug(f"🔧 [P2-重影修复] 开始原子性表头设置: {len(headers)} 个表头")

                # 第一步：验证表头数据
                if not headers or not isinstance(headers, list):
                    raise ValueError("表头数据无效")

                # 第二步：检查是否需要更新
                if self._is_headers_same(headers):
                    self.logger.debug("表头无变化，跳过原子性设置")
                    return True

                # 第三步：清理现有状态
                current_columns = self.table.columnCount()
                if current_columns > 0:
                    self.table.clearContents()

                # 第四步：设置列数
                if current_columns != len(headers):
                    self.table.setColumnCount(len(headers))
                    self.logger.debug(f"🔧 [原子设置] 调整列数: {current_columns} -> {len(headers)}")

                # 第五步：原子性设置表头
                self.table.setHorizontalHeaderLabels(headers)

                # 第六步：更新表头显示
                h_header = self.table.horizontalHeader()
                if h_header:
                    h_header.update()

                # 第七步：更新缓存状态
                self._last_headers = headers.copy()
                self._header_updates += 1

                self.logger.info(f"🔧 [P2-重影修复] 原子性表头设置成功: {len(headers)} 个表头")
                return True

            except Exception as e:
                self.logger.error(f"🔧 [P2-重影修复] 原子性表头设置失败: {e}")
                return False

        finally:
            # 释放锁
            self._atomic_operation_lock = False
            self._header_state_lock = False

            # 处理队列中的操作
            self._process_operation_queue()

    def _process_operation_queue(self):
        """处理操作队列中的待处理操作"""
        try:
            while self._operation_queue and not self._atomic_operation_lock:
                operation, data = self._operation_queue.pop(0)
                if operation == 'set_headers':
                    self.atomic_set_headers(data)

        except Exception as e:
            self.logger.error(f"处理操作队列失败: {e}")

    def is_atomic_operation_in_progress(self) -> bool:
        """检查是否有原子操作正在进行"""
        return self._atomic_operation_lock or self._header_state_lock