# Salary Changes System Deployment Script
# Function: Download project from server, clean unnecessary files, deploy locally

param(
    [string]$ServerAlias = "cc",
    [string]$ServerIP = "*************",
    [string]$ServerUser = "ubuntu",
    [string]$ProjectPath = "/home/<USER>/salary_changes",
    [string]$LocalProjectName = "salary_changes"
)

# Set error handling
$ErrorActionPreference = "Stop"

# Generate timestamp
$timestamp = Get-Date -Format "yyyyMMddHH"
$zipFileName = "salary_changes_$timestamp.zip"

Write-Host "=== Salary Changes System Deployment Script ===" -ForegroundColor Green
Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow

try {
    # Stage 1: Remote cleanup and packaging
    Write-Host "`n[Stage 1] Remote server operations..." -ForegroundColor Cyan
    
    $remoteCommands = @(
        "cd $ProjectPath",
        "echo 'Cleaning Python cache...'",
        "find . -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true",
        "find . -name '*.pyc' -delete 2>/dev/null || true",
        "echo 'Creating zip directory...'",
        "mkdir -p /home/<USER>/zip",
        "echo 'Creating zip package...'",
        "zip -r /home/<USER>/zip/$zipFileName $ProjectPath -x '*.git*' 2>/dev/null || echo 'Compression completed (may have warnings)'",
        "ls -lh /home/<USER>/zip/$zipFileName"
    )
    
    $remoteScript = $remoteCommands -join "; "
    Write-Host "Executing remote commands..." -ForegroundColor Yellow
    ssh $ServerAlias $remoteScript
    
    if ($LASTEXITCODE -ne 0) {
        throw "Remote command execution failed"
    }
    
    # Stage 2: Download zip package
    Write-Host "`n[Stage 2] Downloading zip package..." -ForegroundColor Cyan
    Write-Host "Download file: $zipFileName" -ForegroundColor Yellow
    
    scp "${ServerUser}@${ServerIP}:/home/<USER>/zip/$zipFileName" .
    
    if ($LASTEXITCODE -ne 0) {
        throw "File download failed"
    }
    
    # Verify downloaded file
    if (!(Test-Path $zipFileName)) {
        throw "Downloaded zip package does not exist"
    }
    
    $fileSize = (Get-Item $zipFileName).Length / 1MB
    Write-Host "Download successful, file size: $($fileSize.ToString('F2')) MB" -ForegroundColor Green
    
    # Stage 3: Local extraction and processing
    Write-Host "`n[Stage 3] Local extraction and processing..." -ForegroundColor Cyan
    
    # Check if 7-zip is installed
    $sevenZipPath = Get-Command "7z" -ErrorAction SilentlyContinue
    if (-not $sevenZipPath) {
        Write-Host "Checking common 7-zip installation paths..." -ForegroundColor Yellow
        $possiblePaths = @(
            "${env:ProgramFiles}\7-Zip\7z.exe",
            "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                $sevenZipPath = $path
                break
            }
        }
        
        if (-not $sevenZipPath) {
            throw "7-zip not found, please install 7-zip first"
        }
    } else {
        $sevenZipPath = $sevenZipPath.Source
    }
    
    Write-Host "Using 7-zip: $sevenZipPath" -ForegroundColor Yellow
    
    # Create temporary extraction directory
    $tempExtractDir = "temp_extract_$timestamp"
    if (Test-Path $tempExtractDir) {
        Remove-Item $tempExtractDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempExtractDir | Out-Null
    
    # Extract
    Write-Host "Extracting to temporary directory..." -ForegroundColor Yellow
    & $sevenZipPath x $zipFileName -o"$tempExtractDir" -y
    
    if ($LASTEXITCODE -ne 0) {
        throw "Extraction failed"
    }
    
    # Find extracted project directory
    $extractedProjectPath = Join-Path $tempExtractDir "home/ubuntu/salary_changes"
    if (!(Test-Path $extractedProjectPath)) {
        throw "Project directory not found after extraction: $extractedProjectPath"
    }
    
    # Stage 4: Clean unnecessary directories
    Write-Host "`n[Stage 4] Cleaning unnecessary directories..." -ForegroundColor Cyan
    
    $dirsToDelete = @("logs", "state", "data/db")
    foreach ($dir in $dirsToDelete) {
        $fullPath = Join-Path $extractedProjectPath $dir
        if (Test-Path $fullPath) {
            Write-Host "Deleting directory: $dir" -ForegroundColor Yellow
            Remove-Item $fullPath -Recurse -Force
        } else {
            Write-Host "Directory does not exist: $dir" -ForegroundColor Gray
        }
    }
    
    # Stage 5: Deploy locally
    Write-Host "`n[Stage 5] Deploying locally..." -ForegroundColor Cyan
    
    # Backup existing project (if exists)
    if (Test-Path $LocalProjectName) {
        $backupName = "${LocalProjectName}_backup_$timestamp"
        Write-Host "Backing up existing project to: $backupName" -ForegroundColor Yellow
        Rename-Item $LocalProjectName $backupName
    }
    
    # Move new project to current directory
    Write-Host "Deploying new project..." -ForegroundColor Yellow
    Move-Item $extractedProjectPath $LocalProjectName
    
    # Clean temporary files
    Write-Host "Cleaning temporary files..." -ForegroundColor Yellow
    Remove-Item $tempExtractDir -Recurse -Force
    Remove-Item $zipFileName -Force
    
    # Stage 6: Launch admin PowerShell
    Write-Host "`n[Stage 6] Launching admin PowerShell..." -ForegroundColor Cyan
    
    $projectFullPath = Resolve-Path $LocalProjectName
    Write-Host "Project path: $projectFullPath" -ForegroundColor Green
    
    # Create startup script
    $startScript = @"
Write-Host "=== Salary Changes System Admin Environment ===" -ForegroundColor Green
Set-Location '$projectFullPath'
Write-Host "Current directory: `$(Get-Location)" -ForegroundColor Yellow
Write-Host "Project files:" -ForegroundColor Yellow
Get-ChildItem | Format-Table Name, Length, LastWriteTime
Write-Host "`nStarting application..." -ForegroundColor Green
Write-Host "Running: python main.py" -ForegroundColor Yellow
python main.py
Write-Host "`nApplication exited. This window will stay open. Type 'exit' to close." -ForegroundColor Cyan
"@
    
    $startScriptPath = "start_admin_shell.ps1"
    $startScript | Out-File -FilePath $startScriptPath -Encoding UTF8
    
    Write-Host "Starting admin PowerShell..." -ForegroundColor Yellow
    Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy Bypass -NoExit -File `"$startScriptPath`"" -Verb RunAs
    
    Write-Host "`n=== Deployment Completed ===" -ForegroundColor Green
    Write-Host "Project deployed to: $projectFullPath" -ForegroundColor Green
    Write-Host "Admin PowerShell launched" -ForegroundColor Green
    
} catch {
    Write-Host "`n=== Deployment Failed ===" -ForegroundColor Red
    Write-Host "Error message: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check network connection and SSH configuration" -ForegroundColor Yellow
    exit 1
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")